{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 크로스보더 핫셀링 스팟"}, "1688_shi_li_ren_zheng": {"message": "1688 강도 인증"}, "1_jian_qi_pi": {"message": "1개부터 주문 가능"}, "1year_yi_shang": {"message": "1년 이상"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24시간 발송"}, "24H_lan_shou_lv": {"message": "24시간 내 픽업"}, "30D_shang_xin": {"message": "30일내 신상품"}, "30d_sales": {"message": "30일 동안 $amount$ 개 판매된", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3분 내 응답"}, "3Min_xiang_ying_lv__desc": {"message": "고객 서비스 3분 이내 응답 비율"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "48시간 발송"}, "48H_lan_shou_lv": {"message": "48시간 내 픽업"}, "48H_lan_shou_lv__desc": {"message": "최근 30일 주문 이후 48시간 이내에 \n픽업 기록이 있는 주문건수\n(픽업 일시xx픽업 업체xx)"}, "48H_lv_yue_lv": {"message": "이행률"}, "48H_lv_yue_lv__desc": {"message": "\n\n48시간 내 픽업 기록이 있는 주문건수와\n전체 주문 건수 비율"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "7일내 신상품"}, "7D_wu_li_you": {"message": "7일 내 무조건 반품"}, "ABS_title_text": {"message": "이 상품은 브랜드 스토리가 있습니다"}, "AC_title_text": {"message": "이 상품은 Amazon's Choice 표시를 가지고 있습니다"}, "A_title_text": {"message": "이 상품은 A+ 페이지를 가지고 있습니다"}, "BS_title_text": {"message": "이 상품은 $type$ 카테고리의 $num$ 중 인기 상품입니다", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "\"Limited time deal\"의 약어로, 이 상품은 \"7일 프로모션\"에 참여하고 있습니다"}, "NR_title_text": {"message": "이 상품은 $type$ 카테고리의 $num$ 중 인기 신상품입니다", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "이 상품은 동영상 광고가 진행했습니다. PPC 광고의 일종으로, 주로 검색 결과 중간에 표시됩니다"}, "SB_title_text": {"message": "이 상품은 브랜드 광고를 진행했습니다. PPC 광고의 일종으로, 주로 검색 결과 상단이나 하단에 표시됩니다"}, "SP_title_text": {"message": "이 상품은 Sponsored Product 광고를 집행했습니다"}, "V_title_text": {"message": "이 상품은 동영상 소개가 포함되어 있습니다"}, "advanced_research": {"message": "고급 검색"}, "agent_ds1688___my_order": {"message": "내 주문"}, "agent_ds1688__add_to_cart": {"message": "해외사입"}, "agent_ds1688__cart": {"message": "장바구니"}, "agent_ds1688__desc": {"message": "1688에서 제공, 해외사입 가능, 원화결제 가능, 한국직송/포워더로 운송"}, "agent_ds1688__freight": {"message": "국제 운송비 예상"}, "agent_ds1688__help": {"message": "도움"}, "agent_ds1688__packages": {"message": "운송장"}, "agent_ds1688__profile": {"message": "마이"}, "agent_ds1688__warehouse": {"message": "내 창고"}, "ai_comment_analysis_advantage": {"message": "장점"}, "ai_comment_analysis_ai": {"message": "AI 리뷰 분석"}, "ai_comment_analysis_available": {"message": "잔액"}, "ai_comment_analysis_balance": {"message": "코인이 부족합니다. 충전해 주세요"}, "ai_comment_analysis_behavior": {"message": "관심"}, "ai_comment_analysis_characteristic": {"message": "특성"}, "ai_comment_analysis_comment": {"message": "해당 상품의 리뷰 수가 부족하여 정확한 결론을 도출할 수 없습니다. 리뷰 수가 더 많은 상품을 선택하여 분석해 주세요"}, "ai_comment_analysis_consume": {"message": "예상 소모"}, "ai_comment_analysis_default": {"message": "기본 리뷰"}, "ai_comment_analysis_desire": {"message": "고객 기대"}, "ai_comment_analysis_disadvantage": {"message": "단점"}, "ai_comment_analysis_free": {"message": "무료 횟수"}, "ai_comment_analysis_freeNum": {"message": "무료 사용 횟수가 1회 감소됩니다"}, "ai_comment_analysis_go_recharge": {"message": "충전하기"}, "ai_comment_analysis_intelligence": {"message": "스마트 리뷰 분석"}, "ai_comment_analysis_location": {"message": "사용 장소"}, "ai_comment_analysis_motive": {"message": "구매 동기"}, "ai_comment_analysis_network_error": {"message": "네트워크 오류, 다시 시도해 주세요"}, "ai_comment_analysis_normal": {"message": "사진과 텍스트 리뷰"}, "ai_comment_analysis_number_reviews": {"message": "리뷰 수 $num$, 예상 비용 $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "일반 댓글"}, "ai_comment_analysis_percentage": {"message": "비율"}, "ai_comment_analysis_problem": {"message": "결제 오류 발생"}, "ai_comment_analysis_reanalysis": {"message": "다시 분석"}, "ai_comment_analysis_reason": {"message": "이유"}, "ai_comment_analysis_recharge": {"message": "충전"}, "ai_comment_analysis_recharged": {"message": "충전 완료"}, "ai_comment_analysis_retry": {"message": "다시 시도"}, "ai_comment_analysis_scene": {"message": "사용 시나리오"}, "ai_comment_analysis_start": {"message": "분석 시작"}, "ai_comment_analysis_subject": {"message": "주제"}, "ai_comment_analysis_time": {"message": "사용 시간"}, "ai_comment_analysis_tool": {"message": "인공지능 도구"}, "ai_comment_analysis_user_portrait": {"message": "사용자 프로필"}, "ai_comment_analysis_welcome": {"message": "AI 리뷰 분석을 사용해 주셔서 감사합니다"}, "ai_comment_analysis_year": {"message": "지난 1년간의 댓글"}, "ai_listing_Exclude_keywords": {"message": "제외할 키워드"}, "ai_listing_Login_the_feature": {"message": "이 기능은 로그인이 필요합니다"}, "ai_listing_aI_generation": {"message": "AI 생성"}, "ai_listing_add_automatic": {"message": "자동 추가"}, "ai_listing_add_dictionary_new": {"message": "새로운 용어집 생성"}, "ai_listing_add_enter_keywords": {"message": "키워드 입력"}, "ai_listing_add_inputkey_selling": {"message": "판매 포인트를 입력하고 $key$ 키를 눌러 추가 완료", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "한을 초과하여 $amount$개의 판매 포인트까지만 추가 가능합니다", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "키워드 추가"}, "ai_listing_add_manually": {"message": "수동 추가"}, "ai_listing_add_selling": {"message": "판매 포인트 추가"}, "ai_listing_added_keywords": {"message": "추가된 키워드"}, "ai_listing_added_successfully": {"message": "추가 완료"}, "ai_listing_addexcluded_keywords": {"message": "제외할 키워드를 입력하고 Enter 키를 눌러 추가하세요"}, "ai_listing_adding_selling": {"message": "추가된 판매 포인트"}, "ai_listing_addkeyword_enter": {"message": "키 속성 단어를 입력하고 엔터 키를 눌러 추가하세요"}, "ai_listing_ai_description": {"message": "AI 설명 용어집"}, "ai_listing_ai_dictionary": {"message": "AI 제목 용어집"}, "ai_listing_ai_title": {"message": "AI 제목"}, "ai_listing_aidescription_repeated": {"message": "AI 설명 용어집 이름이 중복될 수 없습니다"}, "ai_listing_aititle_repeated": {"message": "AI 제목 용어집 이름이 중복될 수 없습니다"}, "ai_listing_data_comes_from": {"message": "이 데이터는 다음에서 제공되었습니다"}, "ai_listing_deleted_successfully": {"message": "삭제 완료"}, "ai_listing_dictionary_name": {"message": "용어집 명"}, "ai_listing_edit_dictionary": {"message": "용어집 수정"}, "ai_listing_edit_word_library": {"message": "용어집 편집"}, "ai_listing_enter_keywords": {"message": "키워드를 입력하고 $key$ 키를 눌러 추가하세요", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "최대 $amount$개의 키워드로 제한을 초과했습니다.", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "제외할 용어집"}, "ai_listing_generate_characters": {"message": "문자 생성 "}, "ai_listing_generation_platform": {"message": "플랫폼"}, "ai_listing_help_optimize": {"message": "제품 제목을 최적화해주세요. 원제는 다음과 같습니다."}, "ai_listing_include_selling": {"message": "다른 판매 포인트 포함"}, "ai_listing_included_keyword": {"message": "포함할 키워드"}, "ai_listing_included_keywords": {"message": "추가된 키워드"}, "ai_listing_input_selling": {"message": "판매 포인트 입력"}, "ai_listing_input_selling_fit": {"message": "제목에 맞는 판매 포인트를 입력하세요"}, "ai_listing_input_selling_please": {"message": "판매 포인트를 입력하세요"}, "ai_listing_intelligently_title": {"message": "위에 원하는 내용 입력하여 제목을 지능적으로 생성하세요."}, "ai_listing_keyword_product_title": {"message": "키워드 제품 제목"}, "ai_listing_keywords_repeated": {"message": "키워드가 중복될 수 없습니다"}, "ai_listing_listed_selling_points": {"message": "수록된 판매 포인트"}, "ai_listing_long_title_1": {"message": "브랜드 이름, 제품 유형, 제품 특징 등 기본 정보 포함."}, "ai_listing_long_title_2": {"message": "표준 제품 제목에는 검색 엔진 최적화에 유리한 키워드가 추가됩니다"}, "ai_listing_long_title_3": {"message": "브랜드 이름, 제품 유형, 제품 특징 및 키워드 외에도 구체적이고 세분화된 검색 쿼리에서 더 높은 순위를 얻기 위해 롱테일 키워드가 포함됩니다."}, "ai_listing_longtail_keyword_product_title": {"message": "롱테일 키워드 제품 제목"}, "ai_listing_manually_enter": {"message": "수동 입력 ..."}, "ai_listing_network_not_working": {"message": "네트워크 연결이 되지 않아 ChatGPT에 접속하려면 과학적 인터넷 연결이 필요합니다"}, "ai_listing_new_dictionary": {"message": "새 용어집 만들기"}, "ai_listing_new_generate": {"message": "생성"}, "ai_listing_optional_words": {"message": "선택 가능한 단어"}, "ai_listing_original_title": {"message": "원제"}, "ai_listing_other_keywords_included": {"message": "다른 키워드에는 다음이 포함됩니다:"}, "ai_listing_please_again": {"message": "다시 시도해 주세요"}, "ai_listing_please_select": {"message": "다음과 같은 제목을 생성해드렸습니다. 선택하세요:"}, "ai_listing_product_category": {"message": "제품 분류"}, "ai_listing_product_category_is": {"message": "제품 분류는"}, "ai_listing_product_category_to": {"message": "제품은 어떤 분류에 속합니까?"}, "ai_listing_random_keywords": {"message": "무작위 $amount$개의 키워드", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "무작위로 $amount$개의 판매 포인트", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "용어집에서 무작위로"}, "ai_listing_search_selling": {"message": "판매 포인트로 검색"}, "ai_listing_select_product_categories": {"message": "제품 분류 자동 선택"}, "ai_listing_select_product_selling_points": {"message": "제품의 판매 포인트 자동 선택"}, "ai_listing_select_word_library": {"message": "용어집 선택"}, "ai_listing_selling": {"message": "판매 포인트"}, "ai_listing_selling_ask": {"message": "제목에 추가할 다른 판매 포인트 요구사항이 있습니까?"}, "ai_listing_selling_optional": {"message": "선택적 판매 포인트"}, "ai_listing_selling_repeat": {"message": "판매 포인트는 중복될 수 없습니다"}, "ai_listing_set_excluded": {"message": "용어집 제외 설정"}, "ai_listing_set_include_selling_points": {"message": "포함된 판매 포인트"}, "ai_listing_set_included": {"message": "용어집 포함 설정"}, "ai_listing_set_selling_dictionary": {"message": "판매 포인트 용어집으로 설정"}, "ai_listing_standard_product_title": {"message": "표준 제품 제목"}, "ai_listing_translated_title": {"message": "제목 번역"}, "ai_listing_visit_chatGPT": {"message": "ChatGPT 에 접속"}, "ai_listing_what_other_keywords": {"message": "제목에 추가 키워드 요구사항이 있습니까?"}, "aliprice_coupons_apply_again": {"message": "다시 신청"}, "aliprice_coupons_apply_coupons": {"message": "쿠폰 적용"}, "aliprice_coupons_apply_success": {"message": "쿠폰을 찾았습니다: $amount$를 저장", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "최선의 거래를 위해 코드를 테스트하는 중..."}, "aliprice_coupons_applying_desc": {"message": "체크아웃 중: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "체크아웃을 계속합니다"}, "aliprice_coupons_found_coupons": {"message": "$amount$ 쿠폰을 찾았습니다", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "체크아웃하시겠습니까? 당신이 가장 좋은 가격을 받을 수 있도록 보장합시다!"}, "aliprice_coupons_no_coupon_aviable": {"message": "이 코드들은 작동하지 않습니다. 괜찮습니다. 이미 가장 좋은 가격을 받았습니다."}, "aliprice_coupons_toolbar_btn": {"message": "쿠폰 받기"}, "aliww_translate": {"message": "알리왕왕 대화 번역"}, "aliww_translate_supports": {"message": "지원:1688 & 타오바오"}, "amazon_extended_keywords_Keywords": {"message": "검색어"}, "amazon_extended_keywords_copy_all": {"message": "모두 복사"}, "amazon_extended_keywords_more": {"message": "더 보기"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "총판매량 순 정렬"}, "an_lei_xing_cha_kan": {"message": "유형별 보기"}, "an_yue_dai_xiao_pai_xu": {"message": "월별 드랍쉬핑 매출 기준으로 정렬"}, "apra_btn__cat_name": {"message": "리뷰 분석"}, "apra_chart__name": {"message": "국가별 제품 판매 비율"}, "apra_chart__update_at": {"message": "업데이트 시간 $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "국가별 판매 통계"}, "auto_opening": {"message": "$num$초 후에 자동으로 열립니다.", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "다음 $autoPaging$ 페이지", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "평균 등록 일수"}, "average_hui_fu_lv": {"message": "평균 응답률"}, "average_ping_gong_ying_shang_deng_ji": {"message": "평균 공급업체 등급"}, "average_price": {"message": "평균 가격"}, "average_qi_ding_liang": {"message": "평균 최소 주문 수량"}, "average_rating": {"message": "평균 평점"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "평균 인증 공급업체 연수"}, "average_revenue": {"message": "평균 수익"}, "average_revenue_per_product": {"message": "총 수익 ÷ 상품 수"}, "average_sales": {"message": "평균 판매량"}, "average_sales_per_product": {"message": "총 판매량 ÷ 상품 수"}, "bao_han": {"message": "포함"}, "bao_zheng_jin": {"message": "보증금"}, "bian_ti_shu": {"message": "옵션 수"}, "biao_ti": {"message": "제목"}, "blacklist_add_blacklist": {"message": "이 상점 차단"}, "blacklist_address_incorrect": {"message": "주소가 잘못되었습니다. 확인해주세요."}, "blacklist_blacked_out": {"message": "이미 차단된 상점"}, "blacklist_blacklist": {"message": "블랙리스트"}, "blacklist_no_records_yet": {"message": "아직 기록이 없습니다!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "브랜드"}, "btn_aliprice_agent__daigou": {"message": "나를 위해 구매 에이전트"}, "btn_aliprice_agent__dropshipping": {"message": "드랍쉬핑"}, "btn_have_a_try": {"message": "지금 사용해보십시오"}, "btn_refresh": {"message": "새롭게 하다"}, "btn_try_it_now": {"message": "지금 사용해보십시오"}, "btn_txt_view_on_aliprice": {"message": "AliPrice에서보기"}, "bu_bao_han": {"message": "포함되지 않음"}, "bulk_copy_links": {"message": "링크 일괄 복사"}, "bulk_copy_products": {"message": "제품 일괄 복사"}, "cai_gou_zi_xun": {"message": "고객 서비스"}, "cai_gou_zi_xun__desc": {"message": "고객 서비스 3분 응답률"}, "can_ping_lei_xing": {"message": "유형"}, "cao_zuo": {"message": "액션"}, "chan_pin_ID": {"message": "제품 ID"}, "chan_pin_e_wai_xin_xi": {"message": "제품 추가 정보"}, "chan_pin_lian_jie": {"message": "제품 링크"}, "cheng_li_shi_jian": {"message": "설립 시간"}, "city__aba": {"message": "Aba", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__altai": {"message": "Altai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__alxaleague": {"message": "Alxa League", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ankang": {"message": "Ankan<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__anqing": {"message": "Anqing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__anshan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__anyang": {"message": "Anyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baicheng": {"message": "Baicheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baise": {"message": "Baise", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baisha": {"message": "Baisha", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baishan": {"message": "Baishan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baoding": {"message": "<PERSON>od<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baoting": {"message": "Baoting", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bayannur": {"message": "Bayannur", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bayinguoleng": {"message": "Bayinguoleng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bazhong": {"message": "Bazhong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__beihai": {"message": "Beihai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__benxi": {"message": "Benxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bijie": {"message": "Bijie", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__binzhou": {"message": "Binzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__bozhou": {"message": "Bozhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__cangzhou": {"message": "Cangzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changde": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changhua": {"message": "Changhua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changji": {"message": "Changji", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changjiang": {"message": "Changjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changsha": {"message": "Changsha", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changzhi": {"message": "Changzhi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__changzhou": {"message": "Changzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chaohu": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chaoyang": {"message": "Chaoyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chaozhou": {"message": "Chaozhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chengde": {"message": "Chengde", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chengdu": {"message": "Chengdu", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chenzhou": {"message": "Chenzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chifeng": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chizhou": {"message": "Chizhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chuxiong": {"message": "Chuxiong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__chuzhou": {"message": "Chuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dali": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dalian": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dandong": {"message": "Dandong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__danzhou": {"message": "Danzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__daqing": {"message": "Daqing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dazhou": {"message": "Dazhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dehong": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__deyang": {"message": "Deyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dezhou": {"message": "Dezhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dingan": {"message": "Ding'an", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dingxi": {"message": "Dingxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__diqing": {"message": "Diqing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dongfang": {"message": "<PERSON><PERSON>g", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dongguan": {"message": "Dongguan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__dongying": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__enshi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ezhou": {"message": "Ezhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__fangchenggang": {"message": "Fangchenggang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__fuxin": {"message": "Fuxin", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__fuyang": {"message": "Fuyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__fuzhou": {"message": "Fuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ganzhou": {"message": "Ganzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ganzi": {"message": "Ganzi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guangyuan": {"message": "Guangyuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guangzhou": {"message": "Guangzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guiyang": {"message": "Guiyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guolok": {"message": "Guolok", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__guyuan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__haibei": {"message": "Haibei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__haidong": {"message": "Haidong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__haikou": {"message": "Hai<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hainan": {"message": "Hainan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__haixi": {"message": "Haixi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hami": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__handan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hangzhou": {"message": "Hangzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hanzhong": {"message": "Hanzhong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hebi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hechi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hengyang": {"message": "Hengyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__heyuan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__heze": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hezhou": {"message": "Hezhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hohhot": {"message": "Hohhot", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__honghe": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hongkongisland": {"message": "Hong Kong Island", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hotan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huaihua": {"message": "Huaihua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huaisouth": {"message": "Huai South", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huangnan": {"message": "Huangnan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huangshan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huangshi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huizhou": {"message": "Huizhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__hulunbuir": {"message": "Hulunbuir", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__huzhou": {"message": "Huzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiangmen": {"message": "Jiangmen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiaxing": {"message": "Jiaxing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiayuguan": {"message": "Jiayuguan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jilin": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jinan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jinchang": {"message": "Jinchang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jincheng": {"message": "Jincheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jingdezhen": {"message": "Jingdez<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jingmen": {"message": "Jingmen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jingzhou": {"message": "Jingzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jinhua": {"message": "Jinhua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jining": {"message": "Jining", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jinzhong": {"message": "Jinzhong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jinzhou": {"message": "Jinzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiujiang": {"message": "Jiujiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__jixi": {"message": "Ji<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kaifeng": {"message": "Kaifeng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__keelung": {"message": "Keelung", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kinmen": {"message": "Kinmen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kowloon": {"message": "Kowloon", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__kunming": {"message": "Ku<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__laibin": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__laiwu": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lanzhou": {"message": "Lanzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ledong": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__leshan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lhasa": {"message": "Lhasa", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liangshan": {"message": "Liangshan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lianyungang": {"message": "Lianyungang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lijiang": {"message": "Lijiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lincang": {"message": "Lincang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__linfen": {"message": "Linfen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lingao": {"message": "Lingao", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__linxia": {"message": "Lin<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__linyi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lishui": {"message": "Li<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liupanshui": {"message": "Liupanshu<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__liuzhou": {"message": "Liuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__longnan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__longyan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__loudi": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__luzhou": {"message": "Luzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__lüliang": {"message": "<PERSON>眉liang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__macaupeninsula": {"message": "Macau Peninsula", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__maoming": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__meishan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__meizhou": {"message": "Meizhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__mianyang": {"message": "Mianyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__miaoli": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__mudanjiang": {"message": "Mudanjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanchang": {"message": "Nanchang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanjing": {"message": "Nanjing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanning": {"message": "Nanning", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanping": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nantong": {"message": "Nantong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nanyang": {"message": "Nanyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__neijiang": {"message": "Neijiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__newterritories": {"message": "New Territories", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ngari": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ningbo": {"message": "Ningbo", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nujiang": {"message": "Nujiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__nyingchi": {"message": "Nyingchi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ordos": {"message": "Ordos", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__panjin": {"message": "Panjin", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__panzhihua": {"message": "Pan Zhihua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__pingdingshan": {"message": "Pingdingshan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__puer": {"message": "<PERSON>u'er", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__putian": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__puyang": {"message": "P<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qiandongnan": {"message": "Qiandongnan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qianjiang": {"message": "Qianjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qingdao": {"message": "Qingdao", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qingyang": {"message": "Qingyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qingyuan": {"message": "Qingyuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qinhuangdao": {"message": "Qinhuangdao", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qinzhou": {"message": "Qinzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qionghai": {"message": "Qionghai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qiongzhong": {"message": "Qiongzhong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qiqihar": {"message": "Qiqihar", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__quanzhou": {"message": "Quanzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__quzhou": {"message": "Quzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__sanmenxia": {"message": "Sanmenxia", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__sanming": {"message": "Sanming", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__sanya": {"message": "Sanya", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shannan": {"message": "Shannan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shanwei": {"message": "Shanwei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shaoguan": {"message": "Shaoguan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shaoxing": {"message": "Shaoxing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shaoyang": {"message": "Shaoyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shenyang": {"message": "Shenyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shenzhen": {"message": "Shenzhen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shigatse": {"message": "Shigat<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shijiazhuang": {"message": "Shijiazhuang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shiyan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__shuozhou": {"message": "Shuozhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__siping": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__songyuan": {"message": "Songyuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__suihua": {"message": "Su<PERSON>ua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__suining": {"message": "Suining", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__suzhou": {"message": "Suzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tacheng": {"message": "Tacheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taian": {"message": "Tai'an", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taichung": {"message": "Taichung", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tainan": {"message": "Tainan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taipei": {"message": "Taipei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taiyuan": {"message": "Taiyuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taizhou": {"message": "Taizhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tangshan": {"message": "Tangshan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__taoyuan": {"message": "Taoyuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tianmen": {"message": "Tianmen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tieling": {"message": "Tieling", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tonghua": {"message": "Tonghua", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tongling": {"message": "Tongling", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tongren": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__tunchang": {"message": "Tunchang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ulanqab": {"message": "Ulanqab", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__urumqi": {"message": "Urumqi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wanning": {"message": "Wanning", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__weihai": {"message": "Weihai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__weinan": {"message": "Weinan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wenchang": {"message": "Wenchang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wenshan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wenzhou": {"message": "Wenzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuhai": {"message": "Wu<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuhan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuhu": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuwei": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuxi": {"message": "Wuxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuzhishan": {"message": "Wuzhishan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuzhong": {"message": "Wuzhong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__wuzhou": {"message": "Wuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiamen": {"message": "Xiamen", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xian": {"message": "Xi'<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiangtan": {"message": "Xiangtan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiangxi": {"message": "Xiangxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xianning": {"message": "Xianning", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiantao": {"message": "Xiantao", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xiaogan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xilingol": {"message": "Xilingol", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xinganleague": {"message": "Xing'an League", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xingtai": {"message": "Xingtai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xining": {"message": "Xining", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xinxiang": {"message": "Xinxiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xinyang": {"message": "Xinyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xinyu": {"message": "Xinyu", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xinzhou": {"message": "Xinzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xishuangbanna": {"message": "Xishuangbanna", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xuancheng": {"message": "Xuancheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xuchang": {"message": "Xuchang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__xuzhou": {"message": "Xuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yaan": {"message": "Ya'an", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yanan": {"message": "Yan'<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yanbian": {"message": "Yanbian", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yancheng": {"message": "Yancheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yangjiang": {"message": "Yangjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yangzhou": {"message": "Yangzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yantai": {"message": "Yantai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yibin": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yichang": {"message": "Yichang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yilan": {"message": "Yilan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yili": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yingtan": {"message": "Yingtan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yiwu": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yiyang": {"message": "Yiyang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yongzhou": {"message": "Yongzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yulin": {"message": "Yulin", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yuncheng": {"message": "Yuncheng", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yunfu": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yunlin": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yushu": {"message": "Yushu", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__yuxi": {"message": "Yuxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zaozhuang": {"message": "Zaozhuang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhangye": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhangzhou": {"message": "Zhangzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhanjiang": {"message": "Zhanjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhaoqing": {"message": "Zhaoqing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhaotong": {"message": "Zhaotong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhengzhou": {"message": "Zhengzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhenjiang": {"message": "Zhenjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhongshan": {"message": "Zhongshan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhongwei": {"message": "Zhongwei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhoushan": {"message": "Zhoushan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhuhai": {"message": "Zhu<PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhumadian": {"message": "Zhumadian", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zhuzhou": {"message": "Zhuzhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zibo": {"message": "Zibo", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zigong": {"message": "Zigong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "click_copy_product_info": {"message": "그 다음 제품 정보 복사 클릭"}, "commmon_txt_expired": {"message": "만료 됨"}, "common__date_range_12m": {"message": "일년"}, "common__date_range_1m": {"message": "1 개월"}, "common__date_range_1w": {"message": "일주"}, "common__date_range_2w": {"message": "이주"}, "common__date_range_3m": {"message": "3 개월"}, "common__date_range_3w": {"message": "3 주"}, "common__date_range_6m": {"message": "6 개월"}, "common_btn_cancel": {"message": " 취소"}, "common_btn_close": {"message": "닫기"}, "common_btn_save": {"message": " 저장"}, "common_btn_setting": {"message": "설정"}, "common_email": {"message": "이메일"}, "common_error_msg_no_data": {"message": " 데이터 없음"}, "common_error_msg_no_result": {"message": " 죄송합니다. 결과를 찾을 수 없습니다."}, "common_favorites": {"message": "즐겨 찾기"}, "common_feedback": {"message": "피드백"}, "common_help": {"message": "도움"}, "common_loading": {"message": " 로딩중"}, "common_login": {"message": " 로그인"}, "common_logout": {"message": " 로그아웃"}, "common_no": {"message": "아니"}, "common_powered_by_aliprice": {"message": "AliPrice.com 제공"}, "common_setting": {"message": "환경"}, "common_sign_up": {"message": " 가입  "}, "common_system_upgrading_title": {"message": "시스템 업그레이드"}, "common_system_upgrading_txt": {"message": "나중에 시도하십시오"}, "common_txt__currency": {"message": "통화"}, "common_txt__video_tutorial": {"message": "비디오 튜토리얼"}, "common_txt_ago_time": {"message": "$time$일 전", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "전체 상품"}, "common_txt_analysis": {"message": "분석"}, "common_txt_basically_used": {"message": "사용감 없음"}, "common_txt_biaoti_link": {"message": "상품명+링크"}, "common_txt_biaoti_link_dian_pu": {"message": "상품명+링크+ 상점명"}, "common_txt_blacklist": {"message": "차단리스트"}, "common_txt_cancel": {"message": "종료"}, "common_txt_category": {"message": "카테고리"}, "common_txt_chakan": {"message": "확인하다"}, "common_txt_colors": {"message": "그림 물감"}, "common_txt_confirm": {"message": "확인하다"}, "common_txt_copied": {"message": "복사 됨"}, "common_txt_copy": {"message": "복사"}, "common_txt_copy_link": {"message": "링크 복사"}, "common_txt_copy_title": {"message": "제목 복사"}, "common_txt_copy_title__link": {"message": "제목과 링크 복사"}, "common_txt_day": {"message": "일"}, "common_txt_day__short": {"message": "일"}, "common_txt_delete": {"message": "지우다"}, "common_txt_dian_pu_link": {"message": "매장 이름 + 링크 복사"}, "common_txt_download": {"message": "다운로드"}, "common_txt_downloaded": {"message": "다운로드 계속하기"}, "common_txt_export_as_csv": {"message": "Excel 내보내기"}, "common_txt_export_as_txt": {"message": "텍스트 내보내기"}, "common_txt_fail": {"message": "실패하다"}, "common_txt_format": {"message": "체재"}, "common_txt_get": {"message": "얻다"}, "common_txt_incert_selection": {"message": "선택 반전"}, "common_txt_install": {"message": "설치"}, "common_txt_load_failed": {"message": "불러 오지 못했습니다"}, "common_txt_month": {"message": "개월"}, "common_txt_more": {"message": "더"}, "common_txt_new_unused": {"message": "미개봉、미사용"}, "common_txt_next": {"message": "다음"}, "common_txt_no_limit": {"message": "제한 없는"}, "common_txt_no_noticeable": {"message": "사용감 거의 없음"}, "common_txt_on_sale": {"message": "판매 중"}, "common_txt_opt_in_out": {"message": "온/오프"}, "common_txt_order": {"message": "주문"}, "common_txt_others": {"message": "기타"}, "common_txt_overall_poor_condition": {"message": "상태 불량"}, "common_txt_patterns": {"message": "모양"}, "common_txt_platform": {"message": "플랫폼"}, "common_txt_please_select": {"message": "선택해 주세요"}, "common_txt_prev": {"message": "이전"}, "common_txt_price": {"message": "가격"}, "common_txt_privacy_policy": {"message": "개인 정보 정책"}, "common_txt_product_condition": {"message": "상품 상태"}, "common_txt_rating": {"message": "점수"}, "common_txt_ratings": {"message": "평점 인원수"}, "common_txt_reload": {"message": "새로 고침"}, "common_txt_reset": {"message": "초기화"}, "common_txt_retail": {"message": "소매"}, "common_txt_review": {"message": "리뷰"}, "common_txt_sale": {"message": "판매 중"}, "common_txt_same": {"message": "같은"}, "common_txt_scratches_and_dirt": {"message": "사용감 보통"}, "common_txt_search_title": {"message": "검색 제목"}, "common_txt_select_all": {"message": "전체 선택"}, "common_txt_selected": {"message": "선택된"}, "common_txt_share": {"message": "그것을 공유"}, "common_txt_sold": {"message": "매진"}, "common_txt_sold_out": {"message": "품절"}, "common_txt_some_scratches": {"message": "사용감 적음"}, "common_txt_sort_by": {"message": "정렬 기준"}, "common_txt_state": {"message": "상태"}, "common_txt_success": {"message": "성공"}, "common_txt_sys_err": {"message": "시스템 오류"}, "common_txt_today": {"message": "오늘"}, "common_txt_total": {"message": "총"}, "common_txt_unselect_all": {"message": "역선택"}, "common_txt_upload_image": {"message": "이미지 업로드"}, "common_txt_visit": {"message": "방문"}, "common_txt_whitelist": {"message": "화이트리스트"}, "common_txt_wholesale": {"message": "모조리"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "년도"}, "common_yes": {"message": "예"}, "compare_tool_btn_clear_all": {"message": "모두 지우기"}, "compare_tool_btn_compare": {"message": "비교"}, "compare_tool_btn_contact": {"message": "접촉"}, "compare_tool_tips_max_compared": {"message": "최대 $maxComparedCount$ 개 추가", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "알림 구성"}, "contact_us": {"message": "문의하기"}, "context_menu_screenshot_search": {"message": "캡처하여 이미지로 검색"}, "context_menus_aliprice_search_by_image": {"message": " AliPrice에서 이미지 검색"}, "context_menus_goote_trans": {"message": "웹 번역/원문 보기"}, "context_menus_search_by_image": {"message": "$storeName$에서 이미지로 검색", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$로 캡처", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "스크린샷 번역"}, "converter_modal_amount_placeholder": {"message": "여기에 금액을 입력하세요"}, "converter_modal_btn_convert": {"message": "전환"}, "converter_modal_exchange_rate_source": {"message": "데이터는 다음과 같습니다 $boc$ 환율. 업데이트 시간: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "통화 전환기"}, "converter_modal_search_placeholder": {"message": "화폐 종류 검색"}, "copy_all_contact_us_notice": {"message": "현재 이 사이트는 지원되지 않습니다. 저희에게 연락해 주세요"}, "copy_product_info": {"message": "제품 정보 복사"}, "copy_suggest_search_kw": {"message": "자동완성사 복사"}, "country__han_gou": {"message": "한국"}, "country__ri_ben": {"message": "일본"}, "country__yue_nan": {"message": "베트남"}, "currency_convert__custom": {"message": "맞춤 환율"}, "currency_convert__sync_server": {"message": "서버 동기화"}, "dang_ri_fa_huo": {"message": "당일발송"}, "dao_chu_quan_dian_shang_pin": {"message": "상점 상품 내보내기"}, "dao_chu_wei_CSV": {"message": "내보내다"}, "dao_chu_zi_duan": {"message": "필드 내보내기"}, "delivery_address": {"message": "발송 주소"}, "delivery_company": {"message": "배송 회사"}, "di_zhi": {"message": "주소"}, "dian_ji_cha_xun": {"message": "문의하려면 클릭"}, "dian_pu_ID": {"message": "상점 ID"}, "dian_pu_di_zhi": {"message": "상점 주소"}, "dian_pu_lian_jie": {"message": "상점 링크"}, "dian_pu_ming": {"message": "상점 이름"}, "dian_pu_ming_cheng": {"message": "상점 이름"}, "dian_pu_shang_pin_zong_hsu": {"message": "상점 상품 총 수: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "스토어 정보"}, "ding_zai_zuo_ce": {"message": "왼쪽에 고정"}, "download_image__SKU_variant_images": {"message": "SKU 이미지"}, "download_image__assume": {"message": "예를 들어product1.jpg、product2.gif 두 장의 이미지\nimg_{$no$} 는 img_01.jpg、img_02.gif로 이름이 변경됩니다.\n{$group$}_{$no$} 는 주요_01.jpg、주요_02.gif로 이름이 변경됩니다.", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "일괄 다운로드"}, "download_image__combined_image": {"message": "결합된 제품 상세 이미지"}, "download_image__continue_downloading": {"message": "다운로드 계속하기"}, "download_image__description_images": {"message": "상세설명 이미지"}, "download_image__download_combined_image": {"message": "결합된 제품 상세 이미지 다운로드"}, "download_image__download_zip": {"message": "압축 파일로 다운로드"}, "download_image__enlarge_check": {"message": "JPEG, JPG, GIF 및 PNG 형식의 이미지 파일만 지원되며, 한 장의 이미지 최대 크기: 1600 * 1600"}, "download_image__enlarge_image": {"message": "고화질 확대"}, "download_image__export": {"message": "링크 내보내기"}, "download_image__height": {"message": "높이"}, "download_image__ignore_videos": {"message": "동영상을 내보낼 수 없으므로 무시되었습니다."}, "download_image__img_translate": {"message": "이미지 번역"}, "download_image__main_image": {"message": "메인 이미지"}, "download_image__multi_folder": {"message": "여러 폴더"}, "download_image__name": {"message": "이미지 다운로드"}, "download_image__notice_content": {"message": "브라우저의 다운로드 설정에서 \"다운로드 전에 각 파일의 저장 위치 확인\"를 선택하지 마세요!!! 그렇지 않으면 많은 대화 상자가 나타날 것입니다 ."}, "download_image__notice_ignore": {"message": "더 이상 이 정보를 알리지 않음"}, "download_image__order_number": {"message": "{$no$} 순번; {$group$} 그룹명; {$date$} 타임스탬프", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "개요"}, "download_image__prompt_download_zip": {"message": "이미지가 너무 많습니다. 압축 파일로 다운로드하는 것을 제안드립니다"}, "download_image__rename": {"message": "이름 변경"}, "download_image__rule": {"message": "명명 규칙"}, "download_image__single_folder": {"message": "단일 폴더"}, "download_image__sku_image": {"message": "SKU 이미지"}, "download_image__video": {"message": "비디오"}, "download_image__width": {"message": "너비"}, "download_reviews__download_images": {"message": "댓글 이미지 다운로드"}, "download_reviews__dropdown_title": {"message": "리뷰 이미지 다운로드"}, "download_reviews__export_csv": {"message": "CSV 내보내기"}, "download_reviews__no_images": {"message": "다운로드 가능한 이미지 0장"}, "download_reviews__no_reviews": {"message": "다운로드할 댓글이 없습니다!"}, "download_reviews__notice": {"message": "힌트:"}, "download_reviews__notice__chrome_settings": {"message": "다운로드하기 전에 각 파일을 저장할 위치를 묻도록 크롬을 설정하려면 \"끄기\"로 설정하세요"}, "download_reviews__notice__wait": {"message": "리뷰 개수에 따라 대기 시간이 길어질 수 있습니다"}, "download_reviews__pages_list__all": {"message": "전부"}, "download_reviews__pages_list__page": {"message": "이전$page$페이지", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "선택 범위"}, "export_shopping_cart__csv_filed__details_url": {"message": "제품 링크"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "SKU 다시 표시"}, "export_shopping_cart__csv_filed__images": {"message": "이미지 링크"}, "export_shopping_cart__csv_filed__quantity": {"message": "수량"}, "export_shopping_cart__csv_filed__sale_price": {"message": "가격"}, "export_shopping_cart__csv_filed__specs": {"message": "규격"}, "export_shopping_cart__csv_filed__store_name": {"message": "스토어 이름"}, "export_shopping_cart__csv_filed__store_url": {"message": "스토어 링크"}, "export_shopping_cart__csv_filed__title": {"message": "제품 이름"}, "export_shopping_cart__export_btn": {"message": "내보내다"}, "export_shopping_cart__export_empty": {"message": "제품을 선택해주세요!"}, "fa_huo_shi_jian": {"message": "배솧 기간"}, "favorite_add_email": {"message": "이메일 추가"}, "favorite_add_favorites": {"message": "즐겨찾기 추가"}, "favorite_added": {"message": "추가됨"}, "favorite_btn_add": {"message": " 가격 인하 알림."}, "favorite_btn_notify": {"message": " 가격 추적"}, "favorite_cate_name_all": {"message": "모든 상품"}, "favorite_current_price": {"message": "추적가"}, "favorite_due_date": {"message": "기한"}, "favorite_enable_notification": {"message": "이메일 알림을 활성화해 주세요."}, "favorite_expired": {"message": "판매종료"}, "favorite_go_to_enable": {"message": "활성화로 이동하세요."}, "favorite_msg_add_success": {"message": " 즐겨 찾기에 추가됨"}, "favorite_msg_del_success": {"message": " 즐겨 찾기에서 삭제됨"}, "favorite_msg_failure": {"message": " 실패했습니다! 페이지를 새로 고침하고 다시 시도해 주십시오."}, "favorite_please_add_email": {"message": "이메일을 추가해 주세요"}, "favorite_price_drop": {"message": "하락"}, "favorite_price_rise": {"message": "상승"}, "favorite_price_untracked": {"message": "가격 미추적"}, "favorite_saved_price": {"message": "찜한 가격"}, "favorite_stop_tracking": {"message": "추적 취소"}, "favorite_sub_email_address": {"message": "이메일 주소"}, "favorite_tracking_period": {"message": "추적 기간"}, "favorite_tracking_prices": {"message": "현재가"}, "favorite_verify_email": {"message": "이메일 주소를 확인하세요."}, "favorites_list_remove_prompt_msg": {"message": "삭제 하시겠습니까?"}, "favorites_update_button": {"message": "지금 가격 업데이트"}, "fen_lei": {"message": "카테고리"}, "fen_xia_yan_xuan": {"message": "도매 엄선"}, "find_similar": {"message": "유사 찾기"}, "first_ali_price_date": {"message": "AliPrice 크롤러에 처음으로 캡처된 날짜"}, "fooview_coupons_modal_no_data": {"message": "쿠폰 없음"}, "fooview_coupons_modal_title": {"message": "쿠폰"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "가격 < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "또는 가격 > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "마감 시간"}, "fooview_favorites_error_msg_no_favorites": {"message": " 여기에서 즐겨 찾는 제품으로 등록해서 가격 인하 알림을 받으십시오."}, "fooview_favorites_filter_latest": {"message": " 최근"}, "fooview_favorites_filter_price_drop": {"message": "가격 하락"}, "fooview_favorites_filter_price_up": {"message": "가격 상승"}, "fooview_favorites_modal_title": {"message": " 내 즐겨 찾기 "}, "fooview_favorites_modal_title_title": {"message": "AliPrice 즐겨 찾기로 이동"}, "fooview_favorites_track_price": {"message": " 가격 추적하기 "}, "fooview_price_history_app_price": {"message": " App 가격 :"}, "fooview_price_history_title": {"message": "가격 기록"}, "fooview_product_list_feedback": {"message": " 피드백 "}, "fooview_product_list_orders": {"message": " 주문  "}, "fooview_product_list_price": {"message": " 가격"}, "fooview_reviews_error_msg_no_review": {"message": " 이 제품에 대한 어떤 리뷰도 찾지 못했습니다."}, "fooview_reviews_filter_buyer_reviews": {"message": "구매자의 사진"}, "fooview_reviews_modal_title": {"message": " 리뷰"}, "fooview_same_product_choose_category": {"message": " 카테고리 선택"}, "fooview_same_product_filter_feedback": {"message": " 피드백 "}, "fooview_same_product_filter_orders": {"message": " 주문  "}, "fooview_same_product_filter_price": {"message": " 가격"}, "fooview_same_product_filter_rating": {"message": "등급"}, "fooview_same_product_modal_title": {"message": " 같은 제품을 찾습니다 "}, "fooview_same_product_search_by_image": {"message": " 이미지로 검색합니다"}, "fooview_seller_analysis_modal_title": {"message": " 판매자 분석"}, "for_12_months": {"message": "1 년"}, "for_12_months_list_pro": {"message": "12개월"}, "for_12_months_nei": {"message": "12개월 내"}, "for_1_months": {"message": "1개월"}, "for_1_months_nei": {"message": "1개월 내"}, "for_3_months": {"message": "3 개월 동안"}, "for_3_months_nei": {"message": "3개월 내"}, "for_6_months": {"message": "6 개월 동안"}, "for_6_months_nei": {"message": "6개월 내"}, "for_9_months": {"message": "9개월"}, "for_9_months_nei": {"message": "9개월 내"}, "fu_gou_lv": {"message": "재구매율"}, "gao_liang_bu_tong_dian": {"message": "차이점 강조 표시"}, "gao_liang_guang_gao_chan_pin": {"message": "광고 제품 강조"}, "geng_duo_xin_xi": {"message": "더 많은 정보"}, "geng_xin_shi_jian": {"message": "업데이트"}, "get_store_products_fail_tip": {"message": "확인을 클릭하여 정상적인 접속을 위해 인증을 완료하세요"}, "gong_x_kuan_shang_pin": {"message": "총 $amount$ 제품 수", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "공급업체"}, "gong_ying_shang_ID": {"message": "공급업체 ID"}, "gong_ying_shang_deng_ji": {"message": "공급업체 레벨"}, "gong_ying_shang_nian_zhan": {"message": "공급업체가 나이가 많음"}, "gong_ying_shang_xin_xi": {"message": "공급업체 정보"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "공급업체 홈 링크"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "예상 판매 가격"}, "guan_jian_zi": {"message": "키워드"}, "guang_gao_chan_pin": {"message": "광고 상품"}, "guang_gao_zhan_bi": {"message": "광고 비율"}, "guo_ji_wu_liu_yun_fei": {"message": "국제 물류 운임"}, "guo_lv_tiao_jian": {"message": "필터"}, "hao_ping_lv": {"message": "호평률"}, "highest_price": {"message": "최고가"}, "historical_trend": {"message": "역사적 추세"}, "how_to_screenshot": {"message": "마우스 왼쪽 버튼을 눌러 영역을 선택하고 마우스 오른쪽 버튼 또는 Esc 버튼를 눌러 스크린샷을 종료합니다"}, "howt_it_works": {"message": "작동 원리"}, "hui_fu_lv": {"message": "응답률"}, "hui_tou_lv": {"message": "재구매율"}, "inquire_freightFee": {"message": "운임 조회"}, "inquire_freightFee_Yuan": {"message": "운임/Yuan"}, "inquire_freightFee_province": {"message": "성"}, "inquire_freightFee_the": {"message": "운임은 $num$이며, 해당 지역은 무료 배송을 의미합니다.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "광고 여부"}, "jia_ge": {"message": "가격"}, "jia_ge_dan_wei": {"message": "단위"}, "jia_ge_qu_shi": {"message": "추세"}, "jia_zai_n_ge_shang_pin": {"message": "상품 $num$개 로드됨", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "최근 30일 판매량 비율"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "최근 30일 매출액 비율"}, "jin_30d_xiao_liang": {"message": "판매량(30일)"}, "jin_30d_xiao_liang__desc": {"message": "최근 30일 판매량 제목"}, "jin_30d_xiao_shou_e": {"message": "매출액(30일)"}, "jin_30d_xiao_shou_e__desc": {"message": "최근 30일 매출액 제목"}, "jin_90_tian_mai_jia_shu": {"message": "지난 90일 동안 구매자 수"}, "jin_90_tian_xiao_shou_liang": {"message": "지난 90일 동안 판매량"}, "jing_xuan_huo_yuan": {"message": "엄선된 공급원"}, "jing_ying_mo_shi": {"message": "비즈니스 모드"}, "jing_ying_mo_shi__gong_chang": {"message": "생산자"}, "jiu_fen_jie_jue": {"message": "분쟁 해결"}, "jiu_fen_jie_jue__desc": {"message": "소비자와 사업자 사이의 분쟁"}, "jiu_fen_lv": {"message": "분쟁률"}, "jiu_fen_lv__desc": {"message": "사업자 책임이나 쌍방 책임으로 인한 분쟁 발생 비율"}, "kai_dian_ri_qi": {"message": "개점 시간"}, "keywords": {"message": "키워드"}, "kua_jin_Select_pan_huo": {"message": "셀렉트 공급마켓"}, "last15_days": {"message": "최근 15일"}, "last180_days": {"message": "최근 180일"}, "last30_days": {"message": "지난 30일 동안"}, "last360_days": {"message": "최근 360일"}, "last45_days": {"message": "최근 45일"}, "last60_days": {"message": "최근 60일"}, "last7_days": {"message": "최근 7일"}, "last90_days": {"message": "최근 90일"}, "last_30d_sales": {"message": "지난 30일 판매량"}, "lei_ji": {"message": "누적"}, "lei_ji_xiao_liang": {"message": "총 판매량"}, "lei_ji_xiao_liang__desc": {"message": "제품 출시 후 전체 판매량"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "최근 30일 동안의 누적 판매량을 높은 순으로 정렬합니다."}, "lian_xi_fang_shi": {"message": "연락처 세부 정보"}, "list_time": {"message": "진열 시간"}, "load_more": {"message": "더 보기"}, "login_to_aliprice": {"message": "AliPrice에 로그인"}, "long_link": {"message": "긴 링크"}, "lowest_price": {"message": "최저가"}, "mai_jia_shu": {"message": "매자수"}, "mao_li_lv": {"message": "마진율"}, "mobile_view__dkxbqy": {"message": "새 탭 열기"}, "mobile_view__sjdxq": {"message": "모바일 상세"}, "mobile_view__sjdxqy": {"message": "모바일 상세 페이지"}, "mobile_view__smck": {"message": "스캔하여 보기"}, "mobile_view__smckms": {"message": "카메라나 앱을 사용하여 QR 코드를 스캔하여 확인하세요"}, "modified_failed": {"message": "수정 실패"}, "modified_successfully": {"message": "수정 완료"}, "nav_btn_favorites": {"message": "내 컬렉션"}, "nav_btn_package": {"message": "꾸러미"}, "nav_btn_product_info": {"message": "제품에 대해"}, "nav_btn_viewed": {"message": "조회"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "없음"}, "normal_link": {"message": "일반 링크"}, "notice": {"message": "제시"}, "number_reviews": {"message": "댓글 수"}, "only_show_num": {"message": "총 상품: $allnum$, 숨김: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "선택된 상품만 표시"}, "open": {"message": "열기"}, "open_links": {"message": "링크 열기"}, "options_page_tab_check_links": {"message": "링크 확인"}, "options_page_tab_gernal": {"message": "일반"}, "options_page_tab_notifications": {"message": "알림"}, "options_page_tab_others": {"message": "기타"}, "options_page_tab_sbi": {"message": " 이미지로 검색합니다"}, "options_page_tab_shortcuts": {"message": "바로 가기"}, "options_page_tab_shortcuts_title": {"message": "단축키의 글꼴 크기"}, "options_page_tab_similar_products": {"message": "같은"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "곧 여러 상품 상세 페이지가 열립니다"}, "order_list_sku_show_title": {"message": "SKU 다시 표시"}, "orders_last30_days": {"message": "지난 30일 주문 건수"}, "pTutorial_favorites_block1_desc1": {"message": "추적 한 제품이 여기에 나열됩니다"}, "pTutorial_favorites_block1_title": {"message": "즐겨 찾기"}, "pTutorial_popup_block1_desc1": {"message": "녹색 라벨은 가격 인하 된 제품이 있음을 의미합니다"}, "pTutorial_popup_block1_title": {"message": "바로 가기 및 즐겨 찾기"}, "pTutorial_price_history_block1_desc1": {"message": "가격 추적을 클릭하고 즐겨 찾기에 제품을 추가하십시오. 가격이 떨어지면 알림을받습니다."}, "pTutorial_price_history_block1_title": {"message": "가격 추적"}, "pTutorial_reviews_block1_desc1": {"message": "Itao의 구매자 리뷰와 AliExpress 피드백의 실제 사진"}, "pTutorial_reviews_block1_title": {"message": "리뷰"}, "pTutorial_reviews_block2_desc1": {"message": "다른 구매자의 리뷰를 확인하는 것이 항상 도움이됩니다"}, "pTutorial_same_products_block1_desc1": {"message": "당신은 최선의 선택을하기 위해 그들을 비교할 수 있습니다"}, "pTutorial_same_products_block1_desc2": {"message": "'이미지로 검색'하려면 '더보기'를 클릭하십시오"}, "pTutorial_same_products_block1_title": {"message": "동일한 제품"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "거기에 제품 이미지를 드롭하고 카테고리를 선택하십시오"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "이미지로 검색"}, "pTutorial_seller_analysis_block1_desc1": {"message": "판매자의 긍정적 인 피드백 속도, 피드백 점수 및 판매자가 시장에 출시 된 기간"}, "pTutorial_seller_analysis_block1_title": {"message": "판매자 등급"}, "pTutorial_seller_analysis_block2_desc2": {"message": "판매자 등급은 3 가지 색인 (설명 된 항목, 통신 배송 속도)을 기준으로합니다."}, "pTutorial_seller_analysis_block3_desc3": {"message": "우리는 판매자의 신뢰 수준을 나타내는 3 가지 색상과 아이콘을 사용합니다"}, "page_count": {"message": "페이지 수"}, "pai_chu": {"message": "제외"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "홍콩 판매 부적합 제외"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "일본 판매 부적합 제외"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "한국 판매 부적합 제외"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "카자흐스탄 판매 부적합 제외"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "마카오 판매 부적합 제외"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "동유럽 판매 부적합 제외"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "사우디 아라비아 판매 부적합 제외"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "대만 판매 부적합 제외"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "미국 판매 부적합 제외"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "베트남 판매 부적합 제외"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "판매 부적합 제외"}, "payable_price_formula": {"message": "가격 + 운송비 + 할인"}, "pdd_check_retail_btn_txt": {"message": "소매점 확인"}, "pdd_pifa_to_retail_btn_txt": {"message": "소매 구매"}, "pdp_copy_fail": {"message": "복사 실폐!"}, "pdp_copy_success": {"message": "복사 성공!"}, "pdp_share_modal_subtitle": {"message": "스크리샵 공유하면 타인은 당신의 선택을 볼 것이다."}, "pdp_share_modal_title": {"message": "선택 공유"}, "pdp_share_screenshot": {"message": "스크리샵 공유"}, "pei_song": {"message": "배송"}, "pin_lei": {"message": "카테고리"}, "pin_zhi_ti_yan": {"message": "제품 품질"}, "pin_zhi_ti_yan__desc": {"message": "제품 품질"}, "pin_zhi_tui_kuan_lv": {"message": "환불율"}, "pin_zhi_tui_kuan_lv__desc": {"message": "환불 및 반품 주문 건수와 전체 주문 건수 비율"}, "ping_fen": {"message": "평점"}, "ping_jun_fa_huo_su_du": {"message": "평균 배송 속도"}, "pkgInfo_hide": {"message": "물류 정보: 온/오프"}, "pkgInfo_no_trace": {"message": "물류 정보 없음"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "알리바바 인터내셔널"}, "platform_name__aliexpress": {"message": "알리익스프레스"}, "platform_name__amazon": {"message": "아마존"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "둔황왕"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "이베이"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "네이버"}, "platform_name__pinduoduo": {"message": "핀둬둬"}, "platform_name__pinduoduo_pifa": {"message": "핀둬둬 도매"}, "platform_name__shopee": {"message": "쇼피"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "타오바오"}, "platform_name__taobao_lite": {"message": "타오바오 간이판"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "월마트"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "이우고닷컴"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "홈페이지"}, "popup_go_to_platform": {"message": "$name$로 이동", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": " 이런 것을 쇼핑하고 있습니다 ..."}, "popup_track_package_btn_track": {"message": " 트랙 "}, "popup_track_package_desc": {"message": " 올인원 패키지 추적"}, "popup_track_package_search_placeholder": {"message": " 추적 번호"}, "popup_translate_search_placeholder": {"message": "$searchOn$에서 변환 및 검색", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "가격 기록"}, "price_history_chart_tip_ae": {"message": "알림: 주문 수는 출시 이후부터 현재까지의 누적 주문 수를 나타냅니다."}, "price_history_chart_tip_coupang": {"message": "알림: 쿠팡 공식은 부정 주문의 수를 삭제합니다."}, "price_history_inm_1688_l1": {"message": "설치하십시오"}, "price_history_inm_1688_l2": {"message": "1688 용 AliPrice 쇼핑 도우미"}, "price_history_panel_lowest_price": {"message": " 최저가 "}, "price_history_panel_tab_price_tracking": {"message": "가격 기록"}, "price_history_panel_tab_seller_analysis": {"message": " 판매자 분석"}, "price_history_pro_modal_title": {"message": "가격 내역 및 주문 내역"}, "privacy_consent__btn_agree": {"message": "데이터 수집 동의 재 방문"}, "privacy_consent__btn_disable_all": {"message": "수락하지 않음"}, "privacy_consent__btn_enable_all": {"message": "모두 활성화"}, "privacy_consent__btn_uninstall": {"message": "없애다"}, "privacy_consent__desc_privacy": {"message": "데이터 나 쿠키가 없으면 일부 기능은 데이터 나 쿠키에 대한 설명이 필요하기 때문에 꺼지지 만 다른 기능은 계속 사용할 수 있습니다."}, "privacy_consent__desc_privacy_L1": {"message": "불행히도 데이터 나 쿠키가 없으면 데이터 나 쿠키에 대한 설명이 필요하기 때문에 작동하지 않습니다."}, "privacy_consent__desc_privacy_L2": {"message": "이러한 정보를 수집 할 수없는 경우 제거하십시오."}, "privacy_consent__item_cookies_desc": {"message": "쿠키, 우리는 가격 기록을 보여주기 위해 온라인 쇼핑을 할 때만 쿠키에 귀하의 통화 데이터를 가져옵니다."}, "privacy_consent__item_cookies_title": {"message": "필수 쿠키"}, "privacy_consent__item_functional_desc_L1": {"message": "1. 귀하의 컴퓨터 또는 장치를 익명으로 식별하기 위해 브라우저에 쿠키를 추가합니다."}, "privacy_consent__item_functional_desc_L2": {"message": "2. 추가 기능에 기능 데이터를 추가하여 기능을 사용합니다."}, "privacy_consent__item_functional_title": {"message": "기능 및 분석 쿠키"}, "privacy_consent__more_desc": {"message": "당사는 귀하의 개인 데이터를 다른 회사와 공유하지 않으며 광고 회사가 당사 서비스를 통해 데이터를 수집하지 않습니다."}, "privacy_consent__options__btn__desc": {"message": "모든 기능을 사용하려면이 기능을 켜야합니다."}, "privacy_consent__options__btn__label": {"message": "전원을 켜십시오"}, "privacy_consent__options__desc_L1": {"message": "당사는 귀하를 개인적으로 식별하는 다음 데이터를 수집합니다."}, "privacy_consent__options__desc_L2": {"message": "-쿠키, 가격 내역을 표시하기 위해 온라인 쇼핑을 할 때만 통화 데이터를 쿠키로 가져옵니다."}, "privacy_consent__options__desc_L3": {"message": "-브라우저에 쿠키를 추가하여 컴퓨터 나 장치를 익명으로 식별합니다."}, "privacy_consent__options__desc_L4": {"message": "-다른 익명 데이터는이 확장을보다 편리하게 만듭니다."}, "privacy_consent__options__desc_L5": {"message": "당사는 귀하의 개인 데이터를 다른 회사와 공유하지 않으며 광고 회사는 당사 서비스를 통해 데이터를 수집하지 않습니다."}, "privacy_consent__privacy_preferences": {"message": "개인 정보 기본 설정"}, "privacy_consent__read_more": {"message": "더 읽기 >>"}, "privacy_consent__title_privacy": {"message": "은둔"}, "product_info": {"message": "제품 정보"}, "product_recommend__name": {"message": "같은"}, "product_research": {"message": "제품 연구"}, "product_sub__email_desc": {"message": "가격 알림 메일"}, "product_sub__email_edit": {"message": "편집"}, "product_sub__email_not_verified": {"message": "이메일을 인정해 주세요"}, "product_sub__email_required": {"message": "이메일을 제공해 주세요"}, "product_sub__form_countdown": {"message": "$seconds$초 후 자동으로 닫기", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "알림 추가 실패!"}, "product_sub__form_input_price": {"message": "입력 가격"}, "product_sub__form_item_country": {"message": "나라"}, "product_sub__form_item_current_price": {"message": "현재가격"}, "product_sub__form_item_duration": {"message": "추적 대상"}, "product_sub__form_item_higher_price": {"message": "혹은 가격이 >"}, "product_sub__form_item_invalid_higher_price": {"message": "가격은 $price$보다 커야 합니다", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "가격은 $price$보다 낮아야 합니다", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "가격이 < 때"}, "product_sub__form_submit": {"message": "제출하다"}, "product_sub__form_success": {"message": "알림 추가 성공!"}, "product_sub__high_price_notify": {"message": "가격 인상 알림"}, "product_sub__low_price_notify": {"message": "가격 인하 알림"}, "product_sub__modal_title": {"message": "서브스크립션 가격 변경 알림"}, "province__an_hui": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__ao_men": {"message": "Macau", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__bei_jing": {"message": "Beijing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__chong_qing": {"message": "Chongqing", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__fu_jian": {"message": "Fujian", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__gan_su": {"message": "Gansu", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__guang_dong": {"message": "Guangdong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__guang_xi": {"message": "Guangxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__gui_zhou": {"message": "Guizhou", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__hai_nan": {"message": "Hainan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__he_bei": {"message": "Hebei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__he_nan": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__hei_long_jiang": {"message": "Heilongjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__hu_bei": {"message": "Hubei", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__hu_nan": {"message": "Hunan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__ji_lin": {"message": "<PERSON><PERSON>", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__jiang_su": {"message": "Jiangsu", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__jiang_xi": {"message": "Jiangxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__liao_ning": {"message": "Liaoning", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__nei_meng_gu": {"message": "Inner Mongolia", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__ning_xia": {"message": "Ningxia", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__qing_hai": {"message": "Qinghai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__shan_dong": {"message": "Shandong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__shan_xi": {"message": "Shaanxi", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__shang_hai": {"message": "Shanghai", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__si_chuan": {"message": "Sichuan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__tai_wan": {"message": "Taiwan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__tian_jin": {"message": "Tianjin", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__xi_zhang": {"message": "Tibet", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__xiang_gang": {"message": "Hong Kong", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__xin_jiang": {"message": "Xinjiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__yun_nan": {"message": "Yunnan", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "province__zhe_jiang": {"message": "Zhejiang", "desperate_desc": "该语言使用复用英语的语言包", "desperate_at": "2025-01-21"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "최소 주문 수량"}, "qi_ding_liang_qi_ding_jia": {"message": "최소 주문 수량 & 가격"}, "qi_ye_mian_ji": {"message": "기업 면적"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "제품을 하나 이상 선택해 주세요"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "필드를 하나 이상 선택해 주세요"}, "qu_deng_lu": {"message": "로그인"}, "quan_guo_yan_xuan": {"message": "글로벌 엄선"}, "recommendation_popup_banner_btn_install": {"message": "그것을 설치하십시오"}, "recommendation_popup_banner_desc": {"message": "3/6 개월 내 가격 내역 표시 및 가격 하락 알림"}, "region__all": {"message": "모든 지역"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "오른쪽 클릭 활성화"}, "ren_zheng_gong_chang": {"message": "인증공장"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "인증 공급업체로 활동한 기간"}, "required_to_aliprice_login": {"message": "AliPrice에 로그인해야 함"}, "revenue_last30_days": {"message": "지난 30일 판매액"}, "review_counts": {"message": "수집가 수"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "입점 연한"}, "sales_amount_last30_days": {"message": "지난 30일 총 판매액"}, "sales_last30_days": {"message": "지난 30일 판매량"}, "sbi_alibaba_cate__accessories": {"message": "부속품"}, "sbi_alibaba_cate__aqfk": {"message": "안전보호"}, "sbi_alibaba_cate__bags_cases": {"message": "가방 및 케이스"}, "sbi_alibaba_cate__beauty": {"message": "아름다움"}, "sbi_alibaba_cate__beverage": {"message": "마실 것"}, "sbi_alibaba_cate__bgwh": {"message": "사무 문화"}, "sbi_alibaba_cate__bz": {"message": "포장하다."}, "sbi_alibaba_cate__ccyj": {"message": "주방 식기"}, "sbi_alibaba_cate__clothes": {"message": "의복"}, "sbi_alibaba_cate__cmgd": {"message": "매스컴, 방송"}, "sbi_alibaba_cate__coat_jacket": {"message": "코트 & 재킷"}, "sbi_alibaba_cate__consumer_electronics": {"message": "가전"}, "sbi_alibaba_cate__cryp": {"message": "성인용품"}, "sbi_alibaba_cate__csyp": {"message": "침구류"}, "sbi_alibaba_cate__cwyy": {"message": "애견 원예"}, "sbi_alibaba_cate__cysx": {"message": "요리, 생선"}, "sbi_alibaba_cate__dgdq": {"message": "전기 기술자 전기"}, "sbi_alibaba_cate__dl": {"message": "대리"}, "sbi_alibaba_cate__dress_suits": {"message": "드레스 & 수트"}, "sbi_alibaba_cate__dszm": {"message": "장식 조명"}, "sbi_alibaba_cate__dzqj": {"message": "전자 부품"}, "sbi_alibaba_cate__essb": {"message": "중고 설비"}, "sbi_alibaba_cate__food": {"message": "음식"}, "sbi_alibaba_cate__fspj": {"message": "의류 액세서리"}, "sbi_alibaba_cate__furniture": {"message": "가구"}, "sbi_alibaba_cate__fzpg": {"message": "직물 가죽입니다."}, "sbi_alibaba_cate__ghjq": {"message": "퍼스널 케어, 가정 청결"}, "sbi_alibaba_cate__gt": {"message": "강철"}, "sbi_alibaba_cate__gyp": {"message": "공예품"}, "sbi_alibaba_cate__hb": {"message": "환경 보호"}, "sbi_alibaba_cate__hfcz": {"message": "스킨케어, 메이크업"}, "sbi_alibaba_cate__hg": {"message": "화학공업"}, "sbi_alibaba_cate__jg": {"message": "가공"}, "sbi_alibaba_cate__jianccai": {"message": "건축 자재"}, "sbi_alibaba_cate__jichuang": {"message": "공작기계"}, "sbi_alibaba_cate__jjry": {"message": "재택 일용품"}, "sbi_alibaba_cate__jtys": {"message": "교통운수"}, "sbi_alibaba_cate__jxsb": {"message": "기계 설비"}, "sbi_alibaba_cate__jxwj": {"message": "기계 하드웨어"}, "sbi_alibaba_cate__jydq": {"message": "가전제품"}, "sbi_alibaba_cate__jzjc": {"message": "인테리어, 건축 자재"}, "sbi_alibaba_cate__jzjf": {"message": "집물림"}, "sbi_alibaba_cate__mj": {"message": "수건"}, "sbi_alibaba_cate__myyp": {"message": "영유아 용품"}, "sbi_alibaba_cate__nanz": {"message": "남성복"}, "sbi_alibaba_cate__nvz": {"message": "여성복"}, "sbi_alibaba_cate__ny": {"message": "에너지"}, "sbi_alibaba_cate__others": {"message": "기타"}, "sbi_alibaba_cate__qcyp": {"message": "자동차 용품"}, "sbi_alibaba_cate__qmpj": {"message": "자동차 오토바이 부품"}, "sbi_alibaba_cate__shoes": {"message": "신발"}, "sbi_alibaba_cate__smdn": {"message": "디지털 컴퓨터"}, "sbi_alibaba_cate__snqj": {"message": "수납, 청결"}, "sbi_alibaba_cate__spjs": {"message": "식품, 주류"}, "sbi_alibaba_cate__swfw": {"message": "비즈니스 서비스입니다."}, "sbi_alibaba_cate__toys_hobbies": {"message": "장난감"}, "sbi_alibaba_cate__trousers_skirt": {"message": "바지 & 스커트"}, "sbi_alibaba_cate__txcp": {"message": "통신 상품"}, "sbi_alibaba_cate__tz": {"message": "아동복"}, "sbi_alibaba_cate__underwear": {"message": "속옷"}, "sbi_alibaba_cate__wjgj": {"message": "철물공구"}, "sbi_alibaba_cate__xgpi": {"message": "가방, 가죽 제품"}, "sbi_alibaba_cate__xmhz": {"message": "프로젝트 협력"}, "sbi_alibaba_cate__xs": {"message": "오크 플라스틱"}, "sbi_alibaba_cate__ydfs": {"message": "트레이닝복"}, "sbi_alibaba_cate__ydhw": {"message": "아웃도어 스포츠"}, "sbi_alibaba_cate__yjkc": {"message": "야금 광산"}, "sbi_alibaba_cate__yqyb": {"message": "계기"}, "sbi_alibaba_cate__ys": {"message": "인쇄"}, "sbi_alibaba_cate__yyby": {"message": "의약 보양"}, "sbi_alibaba_cn_kj_90mjs": {"message": "지난 90 일 동안의 구매자 수"}, "sbi_alibaba_cn_kj_90xsl": {"message": "지난 90 일 동안의 판매량"}, "sbi_alibaba_cn_kj_gjsj": {"message": "추정 가격"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "국제 배송료"}, "sbi_alibaba_cn_kj_gjyf": {"message": "배송비"}, "sbi_alibaba_cn_kj_gssj": {"message": "추정 가격"}, "sbi_alibaba_cn_kj_lr": {"message": "이익"}, "sbi_alibaba_cn_kj_lrgs": {"message": "이익 = 예상 가격 x 이익 마진"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "평균 배송 속도"}, "sbi_alibaba_cn_kj_qtfy": {"message": "기타 수수료"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "기타 비용 = 예상 가격 x 기타 비용 비율"}, "sbi_alibaba_cn_kj_spjg": {"message": "가격"}, "sbi_alibaba_cn_kj_spzl": {"message": "무게"}, "sbi_alibaba_cn_kj_szd": {"message": "위치"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "조각"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "조각"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "그램"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "구매자"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "조각"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "세트"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "일"}, "sbi_alibaba_cn_kj_zwbj": {"message": "가격 없음"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "가격"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "판매 가능"}, "sbi_aliprice_alibaba_cn__moren": {"message": "기본"}, "sbi_aliprice_alibaba_cn__queding": {"message": "확신하는"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "매상"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "가구"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "간식"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "메이크업"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "속옷"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "부속품"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "병에 든 음료"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "기타"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "치마"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "재킷"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "전자제품"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "장난감"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "수화물"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "하의"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "구두"}, "sbi_aliprice_cate__apparel": {"message": "의복"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "자동차 & 오토바이"}, "sbi_aliprice_cate__beauty_health": {"message": "미용과 건강"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "휴대폰 및 통신"}, "sbi_aliprice_cate__computer_office": {"message": "컴퓨터 및 오피스"}, "sbi_aliprice_cate__consumer_electronics": {"message": "가전"}, "sbi_aliprice_cate__education_office_supplies": {"message": "교육 및 사무용품"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "전자 부품 및 소모품"}, "sbi_aliprice_cate__furniture": {"message": "가구"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "헤어 익스텐션 및 가발"}, "sbi_aliprice_cate__home_garden": {"message": "집과 마당"}, "sbi_aliprice_cate__home_improvement": {"message": "홈 개선"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "보석 및 액세서리"}, "sbi_aliprice_cate__luggage_bags": {"message": "수하물 및 가방"}, "sbi_aliprice_cate__mother_kids": {"message": "엄마와 아이"}, "sbi_aliprice_cate__novelty_special_use": {"message": "참신함과 특별한 사용"}, "sbi_aliprice_cate__security_protection": {"message": "보안 및 보호"}, "sbi_aliprice_cate__shoes": {"message": "신발"}, "sbi_aliprice_cate__sports_entertainment": {"message": "스포츠 및 엔터테인먼트"}, "sbi_aliprice_cate__toys_hobbies": {"message": "장난감 및 취미"}, "sbi_aliprice_cate__watches": {"message": "시계"}, "sbi_aliprice_cate__weddings_events": {"message": "결혼식 및 이벤트"}, "sbi_btn_capture_txt": {"message": "포착"}, "sbi_btn_source_now_txt": {"message": "지금 소스"}, "sbi_button__chat_with_me": {"message": "나와 대화하기"}, "sbi_button__contact_supplier": {"message": "접촉"}, "sbi_button__hide_on_this_site": {"message": "이 사이트에 표시하지 마십시오"}, "sbi_button__open_settings": {"message": "이미지로 검색 구성"}, "sbi_capture_shortcut_tip": {"message": "또는 키보드에서 \"Enter\"키를 누르십시오"}, "sbi_capturing_tip": {"message": "캡처"}, "sbi_composed_rating_45": {"message": "4.5성-5.0성"}, "sbi_crop_and_search": {"message": "검색"}, "sbi_crop_start": {"message": "스크린 샷 사용"}, "sbi_err_captcha_action": {"message": "인증하기"}, "sbi_err_captcha_for_alibaba_cn": {"message": "확인이 필요합니다. 확인하려면 사진을 업로드하세요. ($video_tutorial$을 보거나 쿠키를 삭제해 보세요)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "비정상적인 트래픽을 확인하십시오."}, "sbi_err_captcha_for_taobao": {"message": "Taobao가 확인을 요청합니다. 수동으로 사진을 업로드하고 검색하여 확인하십시오. 이 오류는 \"TaoBao 이미지로 검색\"의 새로운 확인 정책으로 인해 발생합니다. Taobao $feedback$에서 불만 사항이 너무 자주 확인되는 것이 좋습니다.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "피드백"}, "sbi_err_captcha_msg": {"message": "$platform$ 웹사이트에 방문하여 이미지 검색을 한 번 수행하거나 보안 인증을 완료하여 검색 제한을 해제해 주시기 바랍니다.", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "최신 버전인지 확인"}, "sbi_err_cookie_btn_clear": {"message": "쿠키 삭제"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 쿠키를 삭제하시겠습니까?(다시 로그인해야 함)"}, "sbi_err_desperate_feature_pdd": {"message": "이미지 검색 기능이 핀둬둬 이미지 확장자별 검색으로 이동되었습니다."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "이미지 검색의 성공률을 향상시키는 방법은 무엇입니까?"}, "sbi_err_img_undersize": {"message": "이미지 > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "로그인 $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "로그인"}, "sbi_err_low_version": {"message": "최신 버전 ($latestVersion$)을 설치합니다.", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "다운로드"}, "sbi_err_need_help": {"message": "도움이 필요하십니까"}, "sbi_err_network": {"message": "인터넷이 안 되는데 웹사이트에 접속할 수 있는지 확인해 주세요\n"}, "sbi_err_not_low_version": {"message": "최신 버전이 설치되었습니다($latestVersion$).", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "다시 시도"}, "sbi_err_try_again_action": {"message": "다시 시도"}, "sbi_err_visit_and_try": {"message": "다시 시도하거나 $website$를 방문하여 다시 시도하세요.", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ 홈 페이지를 방문하십시오", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "로드하지 못했습니다. 페이지를 새로 고침 한 후 다시 시도하십시오."}, "sbi_kuajing_filter_area": {"message": "지역"}, "sbi_kuajing_filter_au": {"message": "호주"}, "sbi_kuajing_filter_btn_confirm": {"message": "확인하다"}, "sbi_kuajing_filter_de": {"message": "독일"}, "sbi_kuajing_filter_destination_country": {"message": "대상 국가"}, "sbi_kuajing_filter_es": {"message": "스페인"}, "sbi_kuajing_filter_estimate": {"message": "추정"}, "sbi_kuajing_filter_estimate_price": {"message": "추정 가격"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "예상 가격 공식 = (상품 가격 + 국제 물류 운임)/(1 - 이윤 - 기타 비용 비율)"}, "sbi_kuajing_filter_fr": {"message": "프랑스"}, "sbi_kuajing_filter_kw_placeholder": {"message": "제목과 일치하는 키워드를 입력하세요."}, "sbi_kuajing_filter_logistics": {"message": "물류 템플릿"}, "sbi_kuajing_filter_logistics_china_post": {"message": "중국 우편 항공 우편"}, "sbi_kuajing_filter_logistics_discount": {"message": "물류 할인"}, "sbi_kuajing_filter_logistics_epacket": {"message": "전자 패킷"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "국제물류운임 = (무게 x 배송비 + 등록비) x (1 - 할인)"}, "sbi_kuajing_filter_others_fee": {"message": "기타 수수료"}, "sbi_kuajing_filter_profit_percent": {"message": "이익 마진"}, "sbi_kuajing_filter_prop": {"message": "속성"}, "sbi_kuajing_filter_ru": {"message": "러시아"}, "sbi_kuajing_filter_total": {"message": "$count$ 유사 항목 일치", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "영국"}, "sbi_kuajing_filter_usa": {"message": "미국"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo 도매 버전"}, "sbi_msg_no_result": {"message": "결과가 없습니다. $loginSite$에 로그인하거나 다른 사진을 시도해보십시오.", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "일시적으로 Safari에서 사용할 수 없습니다. $supportPage$를 사용하십시오.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome 브라우저 및 확장"}, "sbi_msg_no_result_reinstall_l1": {"message": "결과가 없습니다. $loginSite$에 로그인하거나 다른 사진을 시도하거나 최신 버전 $latestExtUrl$을 다시 설치하십시오.", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "최신 버전", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "선택된 지역"}, "sbi_shipping_": {"message": "당일발송"}, "sbi_specify_category": {"message": "카테고리를 지정하십시오."}, "sbi_start_crop": {"message": "영역 선택"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 해외"}, "sbi_tutorial_btn_more": {"message": "사용법 2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "타오바오에서 쿠폰 발견"}, "sbi_txt__empty_retry": {"message": "죄송합니다. 검색된 결과가 없습니다. 다시 시도해 주세요."}, "sbi_txt__min_order": {"message": "최소 주문"}, "sbi_visiting": {"message": "찾아보는 중"}, "sbi_yiwugo__jiagexiangtan": {"message": "가격은 판매자에게 문의"}, "sbi_yiwugo__qigou": {"message": "$num$ 조각(MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "별"}, "searchByImage_screenshot": {"message": "스크린샷으로 검세"}, "searchByImage_search": {"message": "같은 제품을 검색"}, "searchByImage_size_type": {"message": "파일 크기는 $num$MB 이하여야 하며, $type$ 만 가능합니다", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "이미지 분석"}, "search_by_image_progress_searching": {"message": "제품 검색"}, "search_by_image_progress_sending": {"message": "이미지 전송"}, "search_by_image_response_rate": {"message": "응답률 :이 공급 업체에 연락 한 구매자의 $responseRate$은 $responseInHour$ 시간 내에 응답을 받았습니다.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "키워드로 검색하다:"}, "select_country_language_modal_title_country": {"message": "국가"}, "select_country_language_modal_title_language": {"message": "언어"}, "select_country_region_modal_title": {"message": "국가 / 지역 선택"}, "select_language_modal_title": {"message": "언어 선택:"}, "select_shop": {"message": "매장 선택"}, "sellers_count": {"message": "현재 페이지의 판매자 수"}, "sellers_count_per_page": {"message": "현재 페이지의 판매자 수"}, "service_score": {"message": "종합 서비스 평점"}, "set_shortcut_keys": {"message": "단축키 설정"}, "setting_logo_title": {"message": " 쇼핑 도우미"}, "setting_modal_options_position_title": {"message": "위치"}, "setting_modal_options_position_value_left": {"message": "왼쪽에"}, "setting_modal_options_position_value_right": {"message": "오른쪽에"}, "setting_modal_options_theme_title": {"message": " 테마 색상"}, "setting_modal_options_theme_value_dark": {"message": " 검은색"}, "setting_modal_options_theme_value_light": {"message": " 밝기"}, "setting_modal_title": {"message": " 설정"}, "setting_options_country_title": {"message": "국가 / 지역"}, "setting_options_hover_zoom_desc": {"message": " 확대하려면 마우스를 올리세요"}, "setting_options_hover_zoom_title": {"message": "호버 및 줌"}, "setting_options_jd_coupon_desc": {"message": "JD.com에서 찾은 쿠폰"}, "setting_options_jd_coupon_title": {"message": "제이디닷컴 쿠폰"}, "setting_options_language_title": {"message": " 언어 "}, "setting_options_price_drop_alert_desc": {"message": " 즐겨 찾기에 등록된 제품 가격이 인하되면 푸시 알람을 받게 됩니다."}, "setting_options_price_drop_alert_title": {"message": " 가격 인하 알람"}, "setting_options_price_history_on_list_page_desc": {"message": " 제품 검색 페이지에 가격 내역 표시"}, "setting_options_price_history_on_list_page_title": {"message": "가격 내역(목록 페이지)"}, "setting_options_price_history_on_produt_page_desc": {"message": " 제품 세부 정보 페이지에 제품 내역 표시"}, "setting_options_price_history_on_produt_page_title": {"message": "가격 내역(상세 페이지)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ 제품 목록 페이지에서 가격, 판매량, 판매자 수, 매장 판매율 통계 지원", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "판매 분석"}, "setting_options_save_success_msg": {"message": " 성공"}, "setting_options_tacking_price_title": {"message": "가격변동경보"}, "setting_options_value_off": {"message": " 끄기"}, "setting_options_value_on": {"message": " 켜기"}, "setting_pkg_quick_view_desc": {"message": "지원 : 1688 & 타오바오"}, "setting_saved_message": {"message": "변경 사항이 성공적으로 저장되었습니다"}, "setting_section_enable_platform_title": {"message": "온 오프"}, "setting_section_setting_title": {"message": " 설정"}, "setting_section_shortcuts_title": {"message": " 바로 가기"}, "settings_aliprice_agent__desc": {"message": "$platforms$ 제품 세부정보 페이지에 표시됨", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "나를 위한 구매"}, "settings_copy_link__desc": {"message": "제품 상세정보 페이지에 표시"}, "settings_copy_link__title": {"message": "복사 버튼&제목 검색"}, "settings_currency_desc__for_detail": {"message": "1688 제품 상세 페이지 지원"}, "settings_currency_desc__for_list": {"message": "이미지로 검색(해외 1688/1688/타오바오 포함)"}, "settings_currency_desc__for_sbi": {"message": "가격 선택"}, "settings_currency_desc_display_for_list": {"message": "이미지 검색에 표시됨 (1688/1688 해외/타오바오 포함)"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\"에서 환율 업데이트 중", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "중국은행"}, "settings_download_images__desc": {"message": "$platforms$에서 이미지 다운로드 지원", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "이미지 다운로드 버튼"}, "settings_download_reviews__desc": {"message": "$platforms$ 제품 세부정보 페이지에 표시됨", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "리뷰 이미지 다운로드"}, "settings_google_translate_desc": {"message": "Google 번역 바를 받으려면 마우스 오른쪽 버튼을 클릭하십시오."}, "settings_google_translate_title": {"message": "웹페이지 번역"}, "settings_historical_trend_desc": {"message": "상품 목록 페이지 이미지 우측 하단에 표시"}, "settings_modal_btn_more": {"message": "자세한 설정"}, "settings_productInfo_desc": {"message": "검색 목록 페이지에서 제품의 더 많은 상세 정보를 표시합니다. 이 기능을 켜면 컴퓨터 부하가 증가하여 페이지가 지연될 수 있습니다. 영향이 있을 경우, 기능을 끄는 것이 좋습니다."}, "settings_product_recommend__desc": {"message": "$platforms$ 상품 상세페이지 메인 그림 아래에 보이기", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "제품 추천"}, "settings_research_desc": {"message": "검색 목록 페이지에서 제품의 더 많은 상세 정보를 표시합니다."}, "settings_sbi_add_to_list": {"message": "$listType$에 추가", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "이미지 검색 결과 미리보기 이미지"}, "settings_sbi_remove_from_list": {"message": "$listType$에서 삭제", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "블랙리스트에 추가"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "입구 위치 조정"}, "settings_search_by_image_blacklist_desc": {"message": "블랙리스트에 있는 웹사이트에 아이콘을 표시하지 마십시오."}, "settings_search_by_image_blacklist_title": {"message": "블랙리스트"}, "settings_search_by_image_bottom_left": {"message": "왼쪽 아래"}, "settings_search_by_image_bottom_right": {"message": "오른쪽 아래"}, "settings_search_by_image_clear_blacklist": {"message": "블랙리스트 지우기"}, "settings_search_by_image_detail_page_icon_title": {"message": "썸네일"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "크게"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "작게"}, "settings_search_by_image_display_button_desc": {"message": "아이콘을 한 번 클릭하여 이미지로 검색"}, "settings_search_by_image_display_button_title": {"message": "이미지 아이콘"}, "settings_search_by_image_sourece_websites_desc": {"message": "이 웹 사이트에서 소스 제품 찾기"}, "settings_search_by_image_sourece_websites_title": {"message": "이미지 결과로 검색"}, "settings_search_by_image_top_left": {"message": "왼쪽 위"}, "settings_search_by_image_top_right": {"message": "오른쪽 위"}, "settings_search_keyword_on_x__desc": {"message": "$platform$에서 단어 검색", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "단어를 선택하면 $platform$ 아이콘 표시", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "이러한 웹 사이트에서 동일한 제품을 찾아보세요 (최대 5 개)"}, "settings_similar_products_title": {"message": "동일한 제품 찾기"}, "settings_toolbar_expand_title": {"message": "플러그인 최소화"}, "settings_top_toolbar_desc": {"message": "페이지 상단의 검색 창"}, "settings_top_toolbar_title": {"message": "검색 창"}, "settings_translate_search_desc": {"message": "중국어로 번역하고 검색하세요."}, "settings_translate_search_title": {"message": "다중 랭크 검색"}, "settings_translator_contextmenu_title": {"message": "스크린샷 번역"}, "settings_translator_title": {"message": "번역"}, "shai_xuan_dao_chu": {"message": "필터 내보내기"}, "shai_xuan_zi_duan": {"message": "필터링"}, "shang_jia_shi_jian": {"message": "제품 추가 시간"}, "shang_pin_biao_ti": {"message": "제품 제목"}, "shang_pin_dui_bi": {"message": "제품 비교"}, "shang_pin_lian_jie": {"message": "제품 링크"}, "shang_pin_xin_xi": {"message": "제품 정보"}, "share_modal__content": {"message": "친구들과 공유"}, "share_modal__disable_for_while": {"message": "나는 아무것도 공유하고 싶지 않다"}, "share_modal__title": {"message": "$extensionName$ 좋아하세요?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "남은 수량"}, "shi_fou_ke_ding_zhi": {"message": "맞춤설정이 가능한가요?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "공급자 인증 여부"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "인증된 공급업체 총 수"}, "shi_fou_you_mao_yi_dan_bao": {"message": "무역담보여부"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "무역 보증 총 수"}, "shipping_fee": {"message": "배송비"}, "shop_followers": {"message": "상점 팔로워 수"}, "shou_qi": {"message": "접기"}, "similar_products_warn_max_platforms": {"message": "최대 5"}, "sku_calc_price": {"message": "계산된 가격"}, "sku_calc_price_settings": {"message": "계산된 가격 설정"}, "sku_formula": {"message": "수식"}, "sku_formula_desc": {"message": "수식 설명"}, "sku_formula_desc_text": {"message": "원래 가격을 A로, 운임을 B로 나타내는 복잡한 수학 공식을 지원합니다.\n\n<br/>\n\n괄호(), 더하기 +, 빼기 -, 곱하기 *, 나누기 /를 지원합니다.\n\n<br/>\n\n예:\n\n<br/>\n\n1. 원래 가격의 1.2배를 구하고 운임을 더하는 공식은 다음과 같습니다. A*1.2+B\n\n<br/>\n\n2. 원래 가격에 1위안을 더한 후 1.2배를 곱하는 공식은 다음과 같습니다. (A+1)*1.2\n\n<br/>\n\n3. 원래 가격에 10위안을 더한 후 1.2배를 곱하고 3위안을 빼는 공식은 다음과 같습니다. (A+10)*1.2-3"}, "sku_in_stock": {"message": "재고"}, "sku_invalid_formula_format": {"message": "잘못된 수식 형식"}, "sku_inventory": {"message": "재고"}, "sku_link_copy_fail": {"message": "복사가 완료되었습니다. SKU 사양 및 속성이 선택되지 않았습니다."}, "sku_link_copy_success": {"message": "성공적으로 복사되었습니다. SKU 사양 및 속성이 선택되었습니다."}, "sku_list": {"message": "SKU 목록"}, "sku_min_qrder_qty": {"message": "최소 주문 수량"}, "sku_name": {"message": "SKU 이름"}, "sku_no": {"message": "수량"}, "sku_original_price": {"message": "원래 가격"}, "sku_price": {"message": "SKU 가격"}, "stop_track_time_label": {"message": "추적 마감일 :"}, "suo_zai_di_qu": {"message": "위치"}, "tab_pkg_quick_view": {"message": "물류 정보 모니터링"}, "tab_product_details_price_history": {"message": "역사"}, "tab_product_details_reviews": {"message": "리뷰"}, "tab_product_details_seller_analysis": {"message": "분석"}, "tab_product_details_similar_products": {"message": "같은"}, "total_days_listed_per_product": {"message": "등록 일수 총합 ÷ 상품 수"}, "total_items": {"message": "총 상품 수"}, "total_price_per_product": {"message": "가격 총합 ÷ 상품 수"}, "total_rating_per_product": {"message": "평점 총합 ÷ 상품 수"}, "total_revenue": {"message": "총 수익"}, "total_revenue40_items": {"message": "현재 페이지의 $amount$개 상품의 총 수익", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "총 판매량"}, "total_sales40_items": {"message": "현재 페이지의 $amount$개 상품의 총 판매량", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "추적 : 1 년"}, "track_for_3_months": {"message": "추적 : 3 개월"}, "track_for_6_months": {"message": "추적 : 6 개월"}, "tracking_price_email_add_btn": {"message": "이메일 추가"}, "tracking_price_email_edit_btn": {"message": "이메일 수정"}, "tracking_price_email_intro": {"message": "이메일로 알려드립니다."}, "tracking_price_email_invalid": {"message": "유효한 이메일을 제공하십시오"}, "tracking_price_email_verified_desc": {"message": "이제 가격 인하 알림을받을 수 있습니다."}, "tracking_price_email_verified_title": {"message": "성공적으로 확인되었습니다"}, "tracking_price_email_verify_desc_line1": {"message": "귀하의 이메일 주소로 확인 링크를 보냈습니다."}, "tracking_price_email_verify_desc_line2": {"message": "이메일받은 편지함을 확인하십시오."}, "tracking_price_email_verify_title": {"message": "이메일 확인"}, "tracking_price_web_push_notification_intro": {"message": "데스크탑 : AliPrice는 제품을 모니터링하고 가격이 변경되면 웹 푸시 알림을 보낼 수 있습니다."}, "tracking_price_web_push_notification_title": {"message": "웹 푸시 알림"}, "translate_im__login_required": {"message": "AliPrice가 번역 지원을 제공합니다. $loginUrl$에 로그인하세요.", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "번역되어 클립 보드에 복사되었지만, 알리 왕왕 제한으로 인해 \n수동으로 붙여넣어야 합니다!"}, "translate_im__send": {"message": "번역 & 전송"}, "translate_search": {"message": "번역하고 검색하십시오"}, "translation_originals_translated": {"message": "원문과 중국어"}, "translation_translated": {"message": "중국어"}, "translator_btn_capture_txt": {"message": "번역"}, "translator_language_auto_detect": {"message": "자동 검출"}, "translator_language_detected": {"message": "감지된"}, "translator_language_search_placeholder": {"message": "언어 검색"}, "try_again": {"message": "다시 시도하십시오"}, "tu_pian_chi_cun": {"message": "이미지 크기"}, "tu_pian_lian_jie": {"message": "이미지 링크"}, "tui_huan_ti_yan": {"message": "반품 서비스"}, "tui_huan_ti_yan__desc": {"message": "판매자의 애프터세일즈"}, "tutorial__show_all": {"message": "모든 기능"}, "tutorial_ae_popup_title": {"message": "확장 프로그램 고정, Aliexpress 열기"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress는 분석을 검토합니다."}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "KRW 결제 지원"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "한국·일본·중국 경내로 운송할 수 있다"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688직구지원"}, "tutorial_auto_apply_coupon_title": {"message": "자동 적용 쿠폰"}, "tutorial_btn_end": {"message": "끝"}, "tutorial_btn_example": {"message": "예시"}, "tutorial_btn_have_a_try": {"message": "좋아, 시도해라."}, "tutorial_btn_next": {"message": "다음 것"}, "tutorial_btn_see_more": {"message": "더보기"}, "tutorial_compare_products": {"message": "제품 비교"}, "tutorial_currency_convert_title": {"message": "통화 변환"}, "tutorial_export_shopping_cart": {"message": "CSV로 내보내기, 타오바오 및 1688 지원"}, "tutorial_export_shopping_cart_title": {"message": "장바구니 내보내기"}, "tutorial_price_history_pro": {"message": "제품 상세 페이지에 표시\n쇼피, 라자다, 아마존, 이베이 지원"}, "tutorial_price_history_pro_title": {"message": "1년 가격 내역 및 주문 내역"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "캡처하여 이미지로 검색"}, "tutorial_translate_search": {"message": "검색으로 번역"}, "tutorial_translate_search_and_package_tracking": {"message": "번역 검색과 택배 추적"}, "unit_bao": {"message": "개"}, "unit_ben": {"message": "개"}, "unit_bi": {"message": "건"}, "unit_chuang": {"message": "개"}, "unit_dai": {"message": "개"}, "unit_dui": {"message": "쌍"}, "unit_fen": {"message": "개"}, "unit_ge": {"message": "개"}, "unit_he": {"message": "개"}, "unit_jian": {"message": "개"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "개"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "쌍"}, "unit_tai": {"message": "개"}, "unit_ti": {"message": "개"}, "unit_tiao": {"message": "개"}, "unit_xiang": {"message": "개"}, "unit_zhang": {"message": "개"}, "unit_zhi": {"message": "개"}, "verify_contact_support": {"message": "상담원에게 문의하기"}, "verify_human_verification": {"message": "인증 절차"}, "verify_unusual_access": {"message": "귀하의 접근이 비정상적입니다"}, "view_history_clean_all": {"message": "모두 청소"}, "view_history_clean_all_warring": {"message": "조회 한 레코드를 모두 정리 하시겠습니까?"}, "view_history_clean_all_warring_title": {"message": "경고"}, "view_history_viewd": {"message": "조회"}, "website": {"message": "웹사이트"}, "weight": {"message": "무게"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "데이터를 가져올 수 없습니다"}, "wu_liu_shi_xiao": {"message": "정시 배송"}, "wu_liu_shi_xiao__desc": {"message": "판매자 48시간 내 정시 출하와 30일 내 이행률"}, "xia_dan_jia": {"message": "주문 가격"}, "xian_xuan_ze_product_attributes": {"message": "먼저 제품 속성 선택"}, "xiao_liang": {"message": "판매량"}, "xiao_liang_zhan_bi": {"message": "판매량 비율"}, "xiao_shi": {"message": "$num$시간", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "매출액"}, "xiao_shou_e_zhan_bi": {"message": "매출액 비율"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ 개의 레코드를 선택하십시오", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "엄선"}, "yi_ding_zai_zuo_ce": {"message": "고정됨"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "모든 상품이 로드되었습니다"}, "yi_nian_xiao_liang": {"message": "연간 판매량"}, "yi_nian_xiao_liang_zhan_bi": {"message": "연간 판매량 비중"}, "yi_nian_xiao_shou_e": {"message": "연간 매출액"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "연간 매출액 비중"}, "yi_shua_xin": {"message": "새로 고침됨"}, "yin_cang_xiang_tong_dian": {"message": "유사점 숨기기"}, "you_xiao_liang": {"message": "판매량 있음"}, "yu_ji_dao_da_shi_jian": {"message": "예상 도착 시간"}, "yuan_gong_ren_shu": {"message": "직원 수"}, "yue_cheng_jiao": {"message": "월별 거래량"}, "yue_dai_xiao": {"message": "대리 판매"}, "yue_dai_xiao__desc": {"message": "최근 30일간 드랍쉬핑 판매량"}, "yue_dai_xiao_pai_xu__desc": {"message": "최근 30일간 드랍쉬핑 판매량은 높은 것부터 낮은 것 순으로 정렬"}, "yue_xiao_liang__desc": {"message": "최근 30일간 판매량"}, "zhan_kai": {"message": "더보기"}, "zhe_kou": {"message": "할인"}, "zhi_chi_yi_jian_dai_fa": {"message": "드랍쉬핑 지원"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "무료 배송 지원 (드랍쉬핑 배송 무료)"}, "zhi_fu_ding_dan_shu": {"message": "결제 완료 주문"}, "zhi_fu_ding_dan_shu__desc": {"message": "지난 30 일 동안 결제 완료 주문"}, "zhu_ce_xing_zhi": {"message": "등록 성질"}, "zi_ding_yi_tiao_jian": {"message": "사용자 정의 조건"}, "zi_duan": {"message": "항목"}, "zi_ti_xiao_liang": {"message": "옵션 판매량"}, "zong_he_fu_wu_fen": {"message": "스토어 총 평점\n"}, "zong_he_fu_wu_fen__desc": {"message": "판매자 서비스에 대한 종합 평점"}, "zong_he_fu_wu_fen__short": {"message": "점수"}, "zong_he_ti_yan_fen": {"message": "종합 체험 점수"}, "zong_he_ti_yan_fen_3": {"message": "4성 이하"}, "zong_he_ti_yan_fen_4": {"message": "4성-4.5성"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5성-5.0성"}, "zong_he_ti_yan_fen_5": {"message": "5성"}, "zong_ku_cun": {"message": "총 재고"}, "zong_xiao_liang": {"message": "총 판매량"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "지난 30일 3분 응답률"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "지난 30일 48시간 수금률"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "지난 30일 48시간 주문 처리율"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "거래 기록 (30일)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "지난 30 일 동안 거래 기록"}, "zui_jin_30D_jiu_fen_lv": {"message": "지난 30일 분쟁 발생률"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "지난 30일 품질 환불 비율"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "지난 30일 동안 결제된 주문 수"}}