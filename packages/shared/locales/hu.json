{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Hat<PERSON>ron <PERSON> Hot Selling Spot"}, "1688_shi_li_ren_zheng": {"message": "1688 szil<PERSON><PERSON><PERSON><PERSON> tanúsítvány"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Több mint 1 éve"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Szállítás 24 órán be<PERSON>"}, "24H_lan_shou_lv": {"message": "24 órás Csomagolási sebesség"}, "30D_shang_xin": {"message": "<PERSON><PERSON>"}, "30d_sales": {"message": "$amount$ eladva 30 nap alatt", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Válasz 3 percen belül."}, "3Min_xiang_ying_lv__desc": {"message": "Wangwang hatékony válaszainak aránya a vásárlói megkeresésekre 3 percen belül az elmúlt 30 napban"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Szállítás 48 <PERSON><PERSON><PERSON>"}, "48H_lan_shou_lv": {"message": "48 ó<PERSON><PERSON> Csomagolási sebesség"}, "48H_lan_shou_lv__desc": {"message": "A 48 órán belül felvett rendelésszám aránya az összes rendelés számához viszonyítva"}, "48H_lv_yue_lv": {"message": "48 órás teljesítménymutató"}, "48H_lv_yue_lv__desc": {"message": "A 48 órán belül felvett vagy kiszállított rendelésszám aránya az összes rendelés számához viszonyítva"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "<PERSON><PERSON>"}, "7D_wu_li_you": {"message": "7 nap gondozásmentes"}, "ABS_title_text": {"message": "Ez a lista egy márkatörténetet tartalmaz"}, "AC_title_text": {"message": "Ezen a listán az Amazon's Choice jelvény található"}, "A_title_text": {"message": "Ennek az adatlapnak A+ tartalomoldala van"}, "BS_title_text": {"message": "Ez az adatlap a $num$ legjobb eladóként szerepel a $type$ kategóriában", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) azt jelenti, hogy ez a lista egy \"7 napos promóciós\" esemény része"}, "NR_title_text": {"message": "Ez az adatlap a $num$ új kiadása a $type$ kategóriában", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Ez a hirdetés tartalmaz egy v<PERSON><PERSON>t, egy olyan típusú PPC-hirdetést, amely <PERSON> a keresési eredmények közepén jelenik meg"}, "SB_title_text": {"message": "Ez az adatlap márkahirdet<PERSON>t tartalmaz, egyfajta PPC-hirdetést, amely <PERSON> a keresési eredmények tetején vagy alján jelenik meg."}, "SP_title_text": {"message": "Ez az adatlap szponzorált termék hirdetést tartalmaz"}, "V_title_text": {"message": "Ez az adatlap bemutatkozó videót tartalmaz"}, "advanced_research": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "agent_ds1688___my_order": {"message": "rendeléseim"}, "agent_ds1688__add_to_cart": {"message": "Tengerentúli vásárlás"}, "agent_ds1688__cart": {"message": "Bevás<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__desc": {"message": "Omogućuje 1688. Podržava izravnu kupnju iz inozemstva, plaćanje u USD i dostavu do vašeg tranzitnog skladišta u Kini."}, "agent_ds1688__freight": {"message": "Szállítási költség kalkulátor"}, "agent_ds1688__help": {"message": "Seg<PERSON><PERSON><PERSON>g"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__warehouse": {"message": "Az én raktárom"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "AI felülvizsgálati elemzés"}, "ai_comment_analysis_available": {"message": "Elérhető"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON> <PERSON>, kérjük töltse fel"}, "ai_comment_analysis_behavior": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_characteristic": {"message": "A tömeg jellemzői"}, "ai_comment_analysis_comment": {"message": "A termékről nincs elég vélemény a pontos következtetések levonásához, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon több véleményt tartalmazó terméket."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON>lt <PERSON>"}, "ai_comment_analysis_default": {"message": "Alapértelmezett vélemények"}, "ai_comment_analysis_desire": {"message": "Az ügyfelek elvárásai"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "Ingyenes próbálkozások"}, "ai_comment_analysis_freeNum": {"message": "1 ingyenes kredit kerül felhasználásra"}, "ai_comment_analysis_go_recharge": {"message": "Ugrás a feltöltéshez"}, "ai_comment_analysis_intelligence": {"message": "Intelligens felülvizsgálati elemzés"}, "ai_comment_analysis_location": {"message": "Elhelyezkedés"}, "ai_comment_analysis_motive": {"message": "Vásárlási motiváció"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_normal": {"message": "Fotó vélemények"}, "ai_comment_analysis_number_reviews": {"message": "Vélemények száma: $num$, Becsült fogyasztás: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Általános me<PERSON>jegyzések"}, "ai_comment_analysis_percentage": {"message": "Százalék"}, "ai_comment_analysis_problem": {"message": "Problémák a fizetéssel"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "Ok"}, "ai_comment_analysis_recharge": {"message": "Felt<PERSON>lt"}, "ai_comment_analysis_recharged": {"message": "feltöltöttem"}, "ai_comment_analysis_retry": {"message": "Prób<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_scene": {"message": "Használati forgatókönyv"}, "ai_comment_analysis_start": {"message": "Kezdje el az elemzést"}, "ai_comment_analysis_subject": {"message": "Témák"}, "ai_comment_analysis_time": {"message": "Használat ideje"}, "ai_comment_analysis_tool": {"message": "AI eszköz"}, "ai_comment_analysis_user_portrait": {"message": "Felhasználói profil"}, "ai_comment_analysis_welcome": {"message": "Üdvözöljük az AI felülvizsgálati elemzésében"}, "ai_comment_analysis_year": {"message": "Megjegyzések az elmúlt évről"}, "ai_listing_Exclude_keywords": {"message": "Kulcsszavak kizárása"}, "ai_listing_Login_the_feature": {"message": "A funkció használatához bejelentkezés szükséges"}, "ai_listing_aI_generation": {"message": "AI generáció"}, "ai_listing_add_automatic": {"message": "Automatikus"}, "ai_listing_add_dictionary_new": {"message": "Hozzon létre egy új könyvtárat"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON> be a kulcssz<PERSON>kat"}, "ai_listing_add_inputkey_selling": {"message": "Adjon meg egy értékesítési pontot, és nyomja meg a $key$ gombot a hozzáadás befejezéséhez", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "A korlát túllépve, maximum $amount$ értékesítési pont", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "<PERSON>jon hozzá k<PERSON>csszavakat"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_add_selling": {"message": "Adjon hozzá értékesítési pontokat"}, "ai_listing_added_keywords": {"message": "Kulcsszavak hozzáadva"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "Adja meg a kiz<PERSON><PERSON> k<PERSON>, majd nyomja meg az Enter bill<PERSON>űt a hozzáadás befejezéséhez."}, "ai_listing_adding_selling": {"message": "Hozzáadott értékesítési pontok"}, "ai_listing_addkeyword_enter": {"message": "Írja be a kulcsattribútum szavait, és nyomja meg az Enter bill<PERSON>űt a hozzáadás befejezéséhez"}, "ai_listing_ai_description": {"message": "AI leírás szótár"}, "ai_listing_ai_dictionary": {"message": "AI címszótár"}, "ai_listing_ai_title": {"message": "AI cím"}, "ai_listing_aidescription_repeated": {"message": "Az AI leíró szókönyvtár neve nem ismételhető meg"}, "ai_listing_aititle_repeated": {"message": "Az AI címszó könyvtárának neve nem ismételhető meg"}, "ai_listing_data_comes_from": {"message": "Ezek az adatok innen származnak:"}, "ai_listing_deleted_successfully": {"message": "Sikeresen törölve"}, "ai_listing_dictionary_name": {"message": "Könyv<PERSON><PERSON><PERSON> neve"}, "ai_listing_edit_dictionary": {"message": "Könyvt<PERSON><PERSON>..."}, "ai_listing_edit_word_library": {"message": "Szerkessze a szótárat"}, "ai_listing_enter_keywords": {"message": "Írja be a kulcsszavakat, és nyomja meg a $key$ gombot a hozzáadás befejezéséhez", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "A korlát túllépve, maximum $amount$ kulcsszó", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_generate_characters": {"message": "Karakterek generálása"}, "ai_listing_generation_platform": {"message": "Generációs platform"}, "ai_listing_help_optimize": {"message": "Segítsen optimaliz<PERSON>lni a termék <PERSON>, az eredeti cím az"}, "ai_listing_include_selling": {"message": "További értékesítési pontok:"}, "ai_listing_included_keyword": {"message": "Tartalmazott kulcsszavak"}, "ai_listing_included_keywords": {"message": "Tartalmazott kulcsszavak"}, "ai_listing_input_selling": {"message": "Adjon meg egy értékesítési pontot"}, "ai_listing_input_selling_fit": {"message": "Adja meg az eladási pontokat, hogy megfeleljen a címnek"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg az értékesítési pontokat"}, "ai_listing_intelligently_title": {"message": "Írja be a szükséges tartalmat fent a cím intelligens generálásához"}, "ai_listing_keyword_product_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cí<PERSON>"}, "ai_listing_keywords_repeated": {"message": "A kulcsszavak nem ismételhetők"}, "ai_listing_listed_selling_points": {"message": "Tartalmazza az értékesítési pontokat"}, "ai_listing_long_title_1": {"message": "<PERSON>lyan alap<PERSON> információkat tartalmaz, mint a márkanév, a termék típusa, a termék jellemzői stb."}, "ai_listing_long_title_2": {"message": "A szabványos termékcím alapján a SEO-t elősegítő kulcsszavak kerülnek hozzáadásra."}, "ai_listing_long_title_3": {"message": "<PERSON><PERSON><PERSON>, hogy m<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, termékjellemzőket és kulcsszavakat tartalmaznak, hoss<PERSON><PERSON> farkú kulcsszavakat is tarta<PERSON><PERSON><PERSON><PERSON>, hogy magas<PERSON><PERSON> he<PERSON>ez<PERSON>t érjenek el az adott, szegmentált keresési lekérdezésekben."}, "ai_listing_longtail_keyword_product_title": {"message": "<PERSON><PERSON><PERSON><PERSON> fark<PERSON> k<PERSON><PERSON><PERSON>í<PERSON>"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON><PERSON><PERSON>rja be..."}, "ai_listing_network_not_working": {"message": "Internet nem érhető el, a ChatGPT eléréséhez VPN szükséges"}, "ai_listing_new_dictionary": {"message": "Hozzon létre egy új szótárat..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Választható s<PERSON>"}, "ai_listing_original_title": {"message": "<PERSON><PERSON><PERSON> cí<PERSON>"}, "ai_listing_other_keywords_included": {"message": "<PERSON><PERSON><PERSON><PERSON>:"}, "ai_listing_please_again": {"message": "Kérlek pr<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_please_select": {"message": "A következő címeket generáltuk Önnek, kérjük, válasszon:"}, "ai_listing_product_category": {"message": "Termékkategória"}, "ai_listing_product_category_is": {"message": "A termékkategória az"}, "ai_listing_product_category_to": {"message": "<PERSON><PERSON><PERSON> ka<PERSON> tartozik a termék?"}, "ai_listing_random_keywords": {"message": "Véletlenszerű $amount$ kulcsszavak", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Véletlenszerű $amount$ eladási pontok", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Véletlenszerűsítse a könyvtár szóból"}, "ai_listing_search_selling": {"message": "Keressen értékesítési pont szerint"}, "ai_listing_select_product_categories": {"message": "Termékkategóriák automatikus kiválasztása."}, "ai_listing_select_product_selling_points": {"message": "A termék értékesítési pontjainak automatikus kiválasztása"}, "ai_listing_select_word_library": {"message": "Válassza ki a szótárat"}, "ai_listing_selling": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_selling_ask": {"message": "<PERSON><PERSON><PERSON> e<PERSON>rtékesítési szempontok vonatkoznak a címre?"}, "ai_listing_selling_optional": {"message": "Választható értékesítési pontok"}, "ai_listing_selling_repeat": {"message": "A pontokat nem lehet megkettőzni"}, "ai_listing_set_excluded": {"message": "Beállítás kiz<PERSON> s<PERSON>árk<PERSON>t"}, "ai_listing_set_include_selling_points": {"message": "Tartalmazza az értékesítési pontokat"}, "ai_listing_set_included": {"message": "Beáll<PERSON><PERSON><PERSON> me<PERSON> szótárként"}, "ai_listing_set_selling_dictionary": {"message": "Beállítás értékesítési pont könyvtárként"}, "ai_listing_standard_product_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_translated_title": {"message": "Le<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Látogassa meg a ChatGPT-t"}, "ai_listing_what_other_keywords": {"message": "<PERSON><PERSON><PERSON> kulcsszavak szükségesek a címhez?"}, "aliprice_coupons_apply_again": {"message": "Alkalmazza <PERSON>"}, "aliprice_coupons_apply_coupons": {"message": "Alkalmazzon kuponokat"}, "aliprice_coupons_apply_success": {"message": "Talált kupon: Mentse $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Kódok tesztelése a legjobb ajánlatokhoz..."}, "aliprice_coupons_applying_desc": {"message": "Kijelentkezés: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Tovább a Checkouthoz"}, "aliprice_coupons_found_coupons": {"message": "$amount$ kuponokat találtunk", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "<PERSON><PERSON>zen áll a fizetésre? Győződjön meg róla, hogy a legjobb árat kapja!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Ezek a kódok nem működtek. Nem nagy dolog – máris a legjobb árat ka<PERSON>ja."}, "aliprice_coupons_toolbar_btn": {"message": "Szerezzen kuponokat"}, "aliww_translate": {"message": "Aliwangwang Chat fordító"}, "aliww_translate_supports": {"message": "Támogatás: 1688 és Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Kulcsszavak"}, "amazon_extended_keywords_copy_all": {"message": "Az összes másolása"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Rendezés összesített eladások szerint"}, "an_lei_xing_cha_kan": {"message": "<PERSON><PERSON><PERSON>"}, "an_yue_dai_xiao_pai_xu": {"message": "Rangsorolás dropshipping értékesítés alapján"}, "apra_btn__cat_name": {"message": "Vélemények elemzése"}, "apra_chart__name": {"message": "A termékértékesítés százalékos aránya országonként"}, "apra_chart__update_at": {"message": "Frissítési idő: $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Az országok értékesítési statisztikái"}, "auto_opening": {"message": "Automatikusan megnyílik $num$ másodpercen belül", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Következő $autoPaging$ oldal", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Átlagos a tárolási napokon"}, "average_hui_fu_lv": {"message": "Átlagos válaszadási arány"}, "average_ping_gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>t"}, "average_price": {"message": "<PERSON><PERSON><PERSON>"}, "average_qi_ding_liang": {"message": "Átlagos MOQ"}, "average_rating": {"message": "Átlagos <PERSON>"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Átlagos év tanúsí<PERSON>ván<PERSON>"}, "average_revenue": {"message": "<PERSON>tl<PERSON><PERSON> be<PERSON>"}, "average_revenue_per_product": {"message": "Teljes bevétel ÷ Termékek száma"}, "average_sales": {"message": "Átlagos elad<PERSON>ok"}, "average_sales_per_product": {"message": "Összes értékesítés ÷ Termékek száma"}, "bao_han": {"message": "Tartalmaz"}, "bao_zheng_jin": {"message": "<PERSON><PERSON><PERSON>"}, "bian_ti_shu": {"message": "Variációk"}, "biao_ti": {"message": "Cím"}, "blacklist_add_blacklist": {"message": "Blokkolja ezt az üzletet"}, "blacklist_address_incorrect": {"message": "Helytelen a cím. <PERSON>, ellenőrizd."}, "blacklist_blacked_out": {"message": "Az üzletet letiltották"}, "blacklist_blacklist": {"message": "Feketelista"}, "blacklist_no_records_yet": {"message": "Még nincs rekord!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Vásárlási közvetítő"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "<PERSON><PERSON><PERSON><PERSON> ki most"}, "btn_txt_view_on_aliprice": {"message": "Megtekintés az AliPrice <PERSON>"}, "bu_bao_han": {"message": "<PERSON><PERSON>"}, "bulk_copy_links": {"message": "Linkek tömeges másolása"}, "bulk_copy_products": {"message": "Tömeges másolási termékek"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cai_gou_zi_xun__desc": {"message": "<PERSON>z eladó háromperces válaszadási aránya"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON><PERSON> be"}, "cao_zuo": {"message": "Művelet"}, "chan_pin_ID": {"message": "term<PERSON>k <PERSON>"}, "chan_pin_e_wai_xin_xi": {"message": "Termék extra információ"}, "chan_pin_lian_jie": {"message": "Termék link"}, "cheng_li_shi_jian": {"message": "Létrehozás ideje"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Kattintson a Termékadatok másolása lehetőségre"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 év"}, "common__date_range_1m": {"message": "1 hónap"}, "common__date_range_1w": {"message": "1 hét"}, "common__date_range_2w": {"message": "2 hét"}, "common__date_range_3m": {"message": "3 hónap"}, "common__date_range_3w": {"message": "3 hét"}, "common__date_range_6m": {"message": "6 hónap"}, "common_btn_cancel": {"message": "Meg<PERSON><PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "Bezárás"}, "common_btn_save": {"message": "Megment"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "Nincs adat"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ninc<PERSON>."}, "common_favorites": {"message": "Kedvencek"}, "common_feedback": {"message": "Visszacsatolás"}, "common_help": {"message": "Seg<PERSON><PERSON><PERSON>g"}, "common_loading": {"message": "Betöltés"}, "common_login": {"message": "Belépés"}, "common_logout": {"message": "Kijelentkezés"}, "common_no": {"message": "Nem"}, "common_powered_by_aliprice": {"message": "Az AliPrice.com üzemelteti"}, "common_setting": {"message": "Be<PERSON>llít<PERSON>"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_system_upgrading_title": {"message": "A rendszer frissítése"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbálkozzon később"}, "common_txt__currency": {"message": "Valuta"}, "common_txt__video_tutorial": {"message": "Oktatóvideó"}, "common_txt_ago_time": {"message": "$time$ napja", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "minden"}, "common_txt_analysis": {"message": "Elemzés"}, "common_txt_basically_used": {"message": "Szinte soha nem has<PERSON>nált"}, "common_txt_biaoti_link": {"message": "Cím+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Cím+Link+<PERSON>zle<PERSON> neve"}, "common_txt_blacklist": {"message": "Tiltólista"}, "common_txt_cancel": {"message": "M<PERSON>gs<PERSON>"}, "common_txt_category": {"message": "Kategória"}, "common_txt_chakan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> be"}, "common_txt_colors": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_confirm": {"message": "megerősít"}, "common_txt_copied": {"message": "M<PERSON>ol<PERSON>"}, "common_txt_copy": {"message": "Másolat"}, "common_txt_copy_link": {"message": "<PERSON>"}, "common_txt_copy_title": {"message": "<PERSON><PERSON>m <PERSON>"}, "common_txt_copy_title__link": {"message": "Cím és link másolása"}, "common_txt_day": {"message": "ég"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "T<PERSON>r<PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "Másolja az üzlet nevét + linket"}, "common_txt_download": {"message": "Letöltés"}, "common_txt_downloaded": {"message": "Letöltés"}, "common_txt_export_as_csv": {"message": "Excel exportálása"}, "common_txt_export_as_txt": {"message": "Txt exportálása"}, "common_txt_fail": {"message": "<PERSON><PERSON><PERSON> vall"}, "common_txt_format": {"message": "Formátum"}, "common_txt_get": {"message": "kap"}, "common_txt_incert_selection": {"message": "Fordított <PERSON>"}, "common_txt_install": {"message": "Telepítés"}, "common_txt_load_failed": {"message": "<PERSON><PERSON>"}, "common_txt_month": {"message": "hónap"}, "common_txt_more": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_new_unused": {"message": "<PERSON><PERSON><PERSON>j, nem has<PERSON>"}, "common_txt_next": {"message": "Következő"}, "common_txt_no_limit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_noticeable": {"message": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> karc vagy szennyeződés"}, "common_txt_on_sale": {"message": "Elérhető"}, "common_txt_opt_in_out": {"message": "Be ki"}, "common_txt_order": {"message": "Rendelés"}, "common_txt_others": {"message": "Mások"}, "common_txt_overall_poor_condition": {"message": "Összességében r<PERSON>z <PERSON>"}, "common_txt_patterns": {"message": "minták"}, "common_txt_platform": {"message": "Platformok"}, "common_txt_please_select": {"message": "K<PERSON><PERSON><PERSON><PERSON>k, válasszon"}, "common_txt_prev": {"message": "Előző"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Adatvédelmi <PERSON>"}, "common_txt_product_condition": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_rating": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "Újratöltés"}, "common_txt_reset": {"message": "Visszaállítás"}, "common_txt_review": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>"}, "common_txt_sale": {"message": "Elérhető"}, "common_txt_same": {"message": "Azonos"}, "common_txt_scratches_and_dirt": {"message": "Karcokkal és szennyeződésekkel"}, "common_txt_search_title": {"message": "Keresési cím"}, "common_txt_select_all": {"message": "Mindet kiválaszt"}, "common_txt_selected": {"message": "kiválasztott"}, "common_txt_share": {"message": "Részvény"}, "common_txt_sold": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sold_out": {"message": "Elfogyott"}, "common_txt_some_scratches": {"message": "Néhány karc <PERSON>s szennyeződés"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_state": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_success": {"message": "Siker"}, "common_txt_sys_err": {"message": "rendszer hiba"}, "common_txt_today": {"message": "Ma"}, "common_txt_total": {"message": "minden"}, "common_txt_unselect_all": {"message": "Fordított <PERSON>"}, "common_txt_upload_image": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_visit": {"message": "Látogat<PERSON>"}, "common_txt_whitelist": {"message": "Engedélyezőlista"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "Igen"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON>"}, "compare_tool_btn_compare": {"message": "Összehasonlítás"}, "compare_tool_btn_contact": {"message": "Kapcsolatba lépni"}, "compare_tool_tips_max_compared": {"message": "Adjon össze $maxComparedCount$-at", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Értesítések konfigurálása"}, "contact_us": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>ü<PERSON>"}, "context_menu_screenshot_search": {"message": "Rögzítse a kép alapján történő kereséshez"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON>ép keresése az AliPrice-on"}, "context_menus_goote_trans": {"message": "Oldal lefordítása/<PERSON><PERSON><PERSON>"}, "context_menus_search_by_image": {"message": "Kép szerint kereshet a $storeName$ oldalon", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Rögzítés a $storeName$ helyre", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Rögzítse a fordításhoz"}, "converter_modal_amount_placeholder": {"message": "Ide írja be az összeget"}, "converter_modal_btn_convert": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "converter_modal_exchange_rate_source": {"message": "Az adatok $boc$ devizaárfolyamból származnak Frissítés időpontja: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "valutaváltás"}, "converter_modal_search_placeholder": {"message": "keresési pénznem"}, "copy_all_contact_us_notice": {"message": "<PERSON>z az oldal jelenleg nem t<PERSON>, kérjük lépjen kapcsolatba velünk"}, "copy_product_info": {"message": "Termékadatok másolása"}, "copy_suggest_search_kw": {"message": "Legördülő listák másolása"}, "country__han_gou": {"message": "Dél-Korea"}, "country__ri_ben": {"message": "Japán"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "currency_convert__sync_server": {"message": "Szerver szinkronizálása"}, "dang_ri_fa_huo": {"message": "Szállítás aznap"}, "dao_chu_quan_dian_shang_pin": {"message": "Exportálja az összes bolti terméket"}, "dao_chu_wei_CSV": {"message": "Export"}, "dao_chu_zi_duan": {"message": "Export Mezők"}, "delivery_address": {"message": "Szállítási cím"}, "delivery_company": {"message": "Szállítócég"}, "di_zhi": {"message": "cím"}, "dian_ji_cha_xun": {"message": "Kattintson a lekérdezéshez"}, "dian_pu_ID": {"message": "Az üzlet azonosítója"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON> c<PERSON>"}, "dian_pu_lian_jie": {"message": "Store link"}, "dian_pu_ming": {"message": "<PERSON>z üzlet neve"}, "dian_pu_ming_cheng": {"message": "<PERSON>z üzlet neve"}, "dian_pu_shang_pin_zong_hsu": {"message": "Az áruházban található termékek teljes száma: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Raktárinformáció"}, "ding_zai_zuo_ce": {"message": "balra szegezve"}, "disable_old_version_tips_disable_btn_title": {"message": "A régi verzió let<PERSON>a"}, "download_image__SKU_variant_images": {"message": "SKU-változat képek"}, "download_image__assume": {"message": "Például 2 képünk van, termék1.jpg és termék2.gif.\nimg_{$no$} át lesz nevezve erre: img_01.jpg, img_02.gif;\nA(z) {$group$}_{$no$} átnevezése main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Kö<PERSON>gelt letöltés"}, "download_image__combined_image": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> termékrészlet képe"}, "download_image__continue_downloading": {"message": "Folytassa a letöltést"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__download_combined_image": {"message": "Kombinált termékrészlet képének letöltése"}, "download_image__download_zip": {"message": "zip letöltése"}, "download_image__enlarge_check": {"message": "Csak a JPEG, JPG, GIF és PNG képeket támogatja, egyetlen kép maximális mérete: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON> nagyí<PERSON>"}, "download_image__export": {"message": "Export"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "A videót figyelmen kívül hagyták, mivel nem exportálható"}, "download_image__img_translate": {"message": "Képfordítás"}, "download_image__main_image": {"message": "fő kép"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON> map<PERSON>"}, "download_image__name": {"message": "kép letöltése"}, "download_image__notice_content": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ne jelölje be a \"Letöltés előtt kérdezze meg az egyes fájlokat hova mentse\" a böngésző letöltési beállításai között!!! Ellenkező esetben sok párbeszédpanel lesz."}, "download_image__notice_ignore": {"message": "Ne kérje újra ezt az üzenetet"}, "download_image__order_number": {"message": "{$no$} sorozatszám; {$group$} csoport neve; {$date$} időbélyeg", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__prompt_download_zip": {"message": "<PERSON><PERSON> sok kép van, jobb, ha zip mappaként töltöd le."}, "download_image__rename": {"message": "Átnevezés"}, "download_image__rule": {"message": "Elnevezési <PERSON>"}, "download_image__single_folder": {"message": "Egyetlen mappa"}, "download_image__sku_image": {"message": "SKU képek"}, "download_image__video": {"message": "videó-"}, "download_image__width": {"message": "Szélesség"}, "download_reviews__download_images": {"message": "Töltse le az értékelési képet"}, "download_reviews__dropdown_title": {"message": "Töltse le az értékelési képet"}, "download_reviews__export_csv": {"message": "CSV exportálása"}, "download_reviews__no_images": {"message": "0 letölthető kép"}, "download_reviews__no_reviews": {"message": "Nincs letölthető értékelés!"}, "download_reviews__notice": {"message": "Tipp:"}, "download_reviews__notice__chrome_settings": {"message": "Áll<PERSON>tsa be a Chrome böngés<PERSON>t, hogy a letöltés előtt megkérdezze, hová kell menteni az egyes fájlokat, <PERSON><PERSON><PERSON><PERSON><PERSON> „Ki” értékre."}, "download_reviews__notice__wait": {"message": "Az értékelések számától függően a várakozási idő hosszabb lehet"}, "download_reviews__pages_list__all": {"message": "Minden"}, "download_reviews__pages_list__page": {"message": "Korábbi $page$ oldal", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Kiválasztási tartomány"}, "export_shopping_cart__csv_filed__details_url": {"message": "Termék <PERSON>"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU link"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__quantity": {"message": "Mennyiség"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Műszaki adatok"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON>z üzlet neve"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__title": {"message": "Termék név"}, "export_shopping_cart__export_btn": {"message": "Export"}, "export_shopping_cart__export_empty": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON>lasszon terméket!"}, "fa_huo_shi_jian": {"message": "Szállítás"}, "favorite_add_email": {"message": "E-mail hozzáadása"}, "favorite_add_favorites": {"message": "Hozzáadás a kedvencekhez"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Árcsökkenés riasztás."}, "favorite_btn_notify": {"message": "<PERSON><PERSON> <PERSON><PERSON> n<PERSON>"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON>"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_due_date": {"message": "Esedékesség"}, "favorite_enable_notification": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, engedélyezze az e-mail értesítéseket"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "Engedélyezés"}, "favorite_msg_add_success": {"message": "Hozzáadva a kedvencekhez"}, "favorite_msg_del_success": {"message": "Törölve a kedvencek közül"}, "favorite_msg_failure": {"message": "Nem si<PERSON>ü<PERSON>! Frissítse az oldalt, és próbálja újra."}, "favorite_please_add_email": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ad<PERSON> hozzá e-mailt"}, "favorite_price_drop": {"message": "Le"}, "favorite_price_rise": {"message": "<PERSON><PERSON>"}, "favorite_price_untracked": {"message": "<PERSON><PERSON> <PERSON>r nem követhető"}, "favorite_saved_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_stop_tracking": {"message": "Állítsa le a követést"}, "favorite_sub_email_address": {"message": "Előfizetési e-mail cím"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON>vet<PERSON>"}, "favorite_tracking_prices": {"message": "<PERSON><PERSON> n<PERSON>"}, "favorite_verify_email": {"message": "E-mail cím el<PERSON>"}, "favorites_list_remove_prompt_msg": {"message": "Biztosan törli?"}, "favorites_update_button": {"message": "Frissítse az árakat most"}, "fen_lei": {"message": "Kategória"}, "fen_xia_yan_xuan": {"message": "A forgalmazó választása"}, "find_similar": {"message": "<PERSON><PERSON>"}, "first_ali_price_date": {"message": "<PERSON><PERSON> <PERSON>, amikor az AliPrice be<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>z<PERSON>"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ok"}, "fooview_coupons_modal_title": {"message": "Kuponok"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Ár < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "vagy ár > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_error_msg_no_favorites": {"message": "Adja hozzá kedvenc termékeit ide, hogy értesítést kapjon az árcsökkenésről."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "árcsökkenés"}, "fooview_favorites_filter_price_up": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title": {"message": "<PERSON> ked<PERSON><PERSON>im"}, "fooview_favorites_modal_title_title": {"message": "Lépjen az AliPrice Kedvenc oldalra"}, "fooview_favorites_track_price": {"message": "<PERSON><PERSON> <PERSON><PERSON> n<PERSON>"}, "fooview_price_history_app_price": {"message": "APP ár:"}, "fooview_price_history_title": {"message": "Árelőzmények"}, "fooview_product_list_feedback": {"message": "Visszacsatolás"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "Nem talá<PERSON>unk véleményt erről a termékről."}, "fooview_reviews_filter_buyer_reviews": {"message": "Vevők fotói"}, "fooview_reviews_modal_title": {"message": "Vélemények"}, "fooview_same_product_choose_category": {"message": "V<PERSON>lass<PERSON> kategó<PERSON>"}, "fooview_same_product_filter_feedback": {"message": "Visszacsatolás"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_modal_title": {"message": "<PERSON><PERSON><PERSON> meg ugyanazt a terméket"}, "fooview_same_product_search_by_image": {"message": "Keresés kép szerint"}, "fooview_seller_analysis_modal_title": {"message": "Eladó elemzése"}, "for_12_months": {"message": "1 évig"}, "for_12_months_list_pro": {"message": "12 hónap"}, "for_12_months_nei": {"message": "12 h<PERSON>apon be<PERSON>l"}, "for_1_months": {"message": "1 hónap"}, "for_1_months_nei": {"message": "1 hónapon belül"}, "for_3_months": {"message": "3 hónapig"}, "for_3_months_nei": {"message": "3 hónapon be<PERSON>ül"}, "for_6_months": {"message": "6 hónapig"}, "for_6_months_nei": {"message": "6 h<PERSON>apon be<PERSON>l"}, "for_9_months": {"message": "9 hónap"}, "for_9_months_nei": {"message": "9 h<PERSON>apon be<PERSON>l"}, "fu_gou_lv": {"message": "Visszavásárlási árfolyam"}, "gao_liang_bu_tong_dian": {"message": "kiemelni a különbségeket"}, "gao_liang_guang_gao_chan_pin": {"message": "Jelölje ki a hirdetési termékeket"}, "geng_duo_xin_xi": {"message": "<PERSON><PERSON><PERSON>"}, "geng_xin_shi_jian": {"message": "Frissítési idő"}, "get_store_products_fail_tip": {"message": "Kattintson az OK gombra az ellenőrzéshez a normál hozzáférés biztosításához"}, "gong_x_kuan_shang_pin": {"message": "Összesen $amount$ termék", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "T<PERSON>mo<PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "Szállítói azonosító"}, "gong_ying_shang_deng_ji": {"message": "Beszállítói <PERSON>"}, "gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_xin_xi": {"message": "szállítói információk"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON><PERSON>ad<PERSON>"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "guang_gao_chan_pin": {"message": "Hirdetés. termékek"}, "guang_gao_zhan_bi": {"message": "Hirdetés. hányados"}, "guo_ji_wu_liu_yun_fei": {"message": "Nemzetközi szállítási díj"}, "guo_lv_tiao_jian": {"message": "Szűrők"}, "hao_ping_lv": {"message": "Pozitív <PERSON>"}, "highest_price": {"message": "Magas"}, "historical_trend": {"message": "<PERSON><PERSON>rt<PERSON><PERSON><PERSON>"}, "how_to_screenshot": {"message": "Tartsa lenyomva a bal egérgombot a terület kiválasztásához, érintse meg a jobb egérgombot vagy az Esc billentyűt a képernyőképből való kilépéshez"}, "howt_it_works": {"message": "<PERSON><PERSON><PERSON>"}, "hui_fu_lv": {"message": "Válaszadási arány"}, "hui_tou_lv": {"message": "visszaté<PERSON><PERSON> a<PERSON>"}, "inquire_freightFee": {"message": "Fuvarozási vizsgálat"}, "inquire_freightFee_Yuan": {"message": "Fuvarozás/jüan"}, "inquire_freightFee_province": {"message": "Tartomány"}, "inquire_freightFee_the": {"message": "A fuvardíj $num$, ami azt jelenti, hogy a régióban ingyenes a szállítás.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Egység"}, "jia_ge_qu_shi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ termékek betöltése", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Az értékesítési mennyiség százalékos aránya az elmúlt 30 napban"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "A bevétel százalékos aránya az elmúlt 30 napban"}, "jin_30d_xiao_liang": {"message": "Értékes<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Teljes értékesítés az elmúlt 30 napban"}, "jin_30d_xiao_shou_e": {"message": "Forgalom"}, "jin_30d_xiao_shou_e__desc": {"message": "Teljes forgalom az elmúlt 30 napban"}, "jin_90_tian_mai_jia_shu": {"message": "Vásárlók az elmúlt 90 napban"}, "jin_90_tian_xiao_shou_liang": {"message": "Eladások az elmúlt 90 napban"}, "jing_xuan_huo_yuan": {"message": "Válogatott források"}, "jing_ying_mo_shi": {"message": "Üzleti modell"}, "jing_ying_mo_shi__gong_chang": {"message": "G<PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue": {"message": "Vitarendezés"}, "jiu_fen_jie_jue__desc": {"message": "Az eladók üzletjogi vitáinak elszámolása"}, "jiu_fen_lv": {"message": "A vita aránya"}, "jiu_fen_lv__desc": {"message": "Az elmúlt 30 napban teljesített, panaszos megrendelések aránya, amelyekről úgy ítélték meg, hogy az eladó vagy mindkét fél felelőssége"}, "kai_dian_ri_qi": {"message": "Nyitás <PERSON>"}, "keywords": {"message": "Kulcsszavak"}, "kua_jin_Select_pan_huo": {"message": "Határokon átnyúló választás"}, "last15_days": {"message": "Az elmúlt 15 nap"}, "last180_days": {"message": "<PERSON>z elmúlt 180 nap"}, "last30_days": {"message": "Az elmúlt 30 napban"}, "last360_days": {"message": "Az elmúlt 360 nap"}, "last45_days": {"message": "Az elmúlt 45 nap"}, "last60_days": {"message": "Az elmúlt 60 nap"}, "last7_days": {"message": "Az elmúlt 7 nap"}, "last90_days": {"message": "Az elmúlt 90 nap"}, "last_30d_sales": {"message": "Az elmúlt 30 nap értékesítése"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang__desc": {"message": "Minden értékesítés a termék után a polcon"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Összesített értékesítési mennyiség az elmúlt 30 napban, a legmagasabbtól a legalacsonyabbig rendezve"}, "lian_xi_fang_shi": {"message": "Elérhetőség"}, "list_time": {"message": "A polc dátuma"}, "load_more": {"message": "Load More"}, "login_to_aliprice": {"message": "Jelentkezzen be az AliPrice-be"}, "long_link": {"message": "Hosszú link"}, "lowest_price": {"message": "Alacsony"}, "mai_jia_shu": {"message": "Eladók száma"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "<PERSON><PERSON><PERSON> meg egy <PERSON>j lapot"}, "mobile_view__sjdxq": {"message": "Részletek az App"}, "mobile_view__sjdxqy": {"message": "Részletes oldal az alkalmazásban"}, "mobile_view__smck": {"message": "Szkennelés megtekintésre"}, "mobile_view__smckms": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, használja a kamerát vagy az alkalmazást a beolvasáshoz és megtekintéshez"}, "modified_failed": {"message": "A módosítás si<PERSON>telen"}, "modified_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_btn_favorites": {"message": "Saját gyűjtemények"}, "nav_btn_package": {"message": "Csomag"}, "nav_btn_product_info": {"message": "A termékről"}, "nav_btn_viewed": {"message": "Megtekintve"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON><PERSON>"}, "normal_link": {"message": "Normál Link"}, "notice": {"message": "célzás"}, "number_reviews": {"message": "Vélemények"}, "only_show_num": {"message": "Összes termék: $allnum$, Rejtett: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Távolítsa el a Kijelöletlen elemet"}, "open": {"message": "Nyitott"}, "open_links": {"message": "Nyissa meg a hivatkozásokat"}, "options_page_tab_check_links": {"message": "Ellenőrizze a linkeket"}, "options_page_tab_gernal": {"message": "T<PERSON>born<PERSON>"}, "options_page_tab_notifications": {"message": "Értesítések"}, "options_page_tab_others": {"message": "Mások"}, "options_page_tab_sbi": {"message": "Keresés kép szerint"}, "options_page_tab_shortcuts": {"message": "Parancsikonok"}, "options_page_tab_shortcuts_title": {"message": "Betűméret a hivatkozásokhoz"}, "options_page_tab_similar_products": {"message": "Ugyanazok a termékek"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Több terméklink megnyílik"}, "order_list_sku_show_title": {"message": "A kiválasztott változatok megjelenítése a megosztott linkekben"}, "orders_last30_days": {"message": "Megrendelések száma az elmúlt 30 napban"}, "pTutorial_favorites_block1_desc1": {"message": "Az Ön által követett termékek itt vannak felsorolva"}, "pTutorial_favorites_block1_title": {"message": "Kedvencek"}, "pTutorial_popup_block1_desc1": {"message": "A zöld címke a<PERSON>t jelenti, hogy vannak árcsökkentett termékek"}, "pTutorial_popup_block1_title": {"message": "Parancsikonok és Kedvencek"}, "pTutorial_price_history_block1_desc1": {"message": "Kattintson az \"Ár követése\" elemre, és adjon termékeket a Kedvencekhez. Amint az <PERSON>ra c<PERSON>, értesítéseket kap"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON> <PERSON><PERSON> n<PERSON>"}, "pTutorial_reviews_block1_desc1": {"message": "Az Itao vásárlói véleményei és valódi fotók az AliExpress visszajelzései alapján"}, "pTutorial_reviews_block1_title": {"message": "Vélemények"}, "pTutorial_reviews_block2_desc1": {"message": "Mindig hasznos ellenőrizni a többi vásárló véleményét"}, "pTutorial_same_products_block1_desc1": {"message": "Összehasonlíthatja őket a legjobb választás érdekében"}, "pTutorial_same_products_block1_desc2": {"message": "Kattintson a \"Tovább\" elemre a \"Keresés kép szerint\" elemre"}, "pTutorial_same_products_block1_title": {"message": "Ugyanazok a termékek"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Do<PERSON><PERSON> oda a term<PERSON>pet, és válasszon kategóriát"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Keresés kép szerint"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Az eladó pozitív visszajelzési aránya, a visszajelzési pontszámok és az eladó meddig tartózkodott a piacon"}, "pTutorial_seller_analysis_block1_title": {"message": "Eladó <PERSON>"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Az eladó értékelése 3 indexen alapul: a leírt tétel, a kommunikáció szállítási sebessége"}, "pTutorial_seller_analysis_block3_desc3": {"message": "3 színt és ikont használunk az eladók bizalmi szintjének jelzésére"}, "page_count": {"message": "Oldalszám"}, "pai_chu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Hongkong-korlátozások kizárása"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Kizárja a Japán által korlátozott"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Kizárja a Korea által korlátozott"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kizárja a Kazahsztán által korlátozott"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Makaói korlátozások kizárása"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Kizárja a Kelet-Európa által korlátozott"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Kizárja a Szaúd-Arábia által korlátozott"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "A tajvani korlátozások kizárása"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Kizárja az Egyesült Államokban korlátozott"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Kizárja a Vietnam által korlátozott"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Tiltott elemek kizárása"}, "payable_price_formula": {"message": "Ár + szállítás + kedvezmény"}, "pdd_check_retail_btn_txt": {"message": "<PERSON> k<PERSON>"}, "pdd_pifa_to_retail_btn_txt": {"message": "Vásárlás a kiskereskedelemben"}, "pdp_copy_fail": {"message": "A másolás nem si<PERSON>ült!"}, "pdp_copy_success": {"message": "A másolás si<PERSON>ült!"}, "pdp_share_modal_subtitle": {"message": "Ossza meg a képernyőképet, ő látni fogja az Ön választását."}, "pdp_share_modal_title": {"message": "Ossza meg választását"}, "pdp_share_screenshot": {"message": "Képernyőkép megosztása"}, "pei_song": {"message": "Szállítási mód"}, "pin_lei": {"message": "Kategória"}, "pin_zhi_ti_yan": {"message": "Termékminőség"}, "pin_zhi_ti_yan__desc": {"message": "<PERSON>z eladó <PERSON>zletének minőségi visszatérítési aránya"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pin_zhi_tui_kuan_lv__desc": {"message": "<PERSON>zon <PERSON>delések a<PERSON>án<PERSON>, amelyeket csak az elmúlt 30 napban térítettek vissza és küldtek vissza"}, "ping_fen": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ping_jun_fa_huo_su_du": {"message": "Átlagos szállítási sebesség"}, "pkgInfo_hide": {"message": "Logisztikai információ: be/ki"}, "pkgInfo_no_trace": {"message": "Nincs logisztikai információ"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo nagykereskedés"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "itthon"}, "popup_go_to_platform": {"message": "Menjen az $name$ oldalra", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Vásárolok ..."}, "popup_track_package_btn_track": {"message": "VÁGÁNY"}, "popup_track_package_desc": {"message": "MINDEN EGYBEN CSOMAG KÖVETÉS"}, "popup_track_package_search_placeholder": {"message": "Nyomkövetési szám"}, "popup_translate_search_placeholder": {"message": "Fordítson és keressen a $searchOn$ webhelyen", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Árelőzmények"}, "price_history_chart_tip_ae": {"message": "Tipp: A rendelések száma az indulás óta történt összesített rendelésszám"}, "price_history_chart_tip_coupang": {"message": "Tipp: A Coupang törli a csaló rendelések számát"}, "price_history_inm_1688_l1": {"message": "Kérjük telepítse"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant az <PERSON> webhel<PERSON>z"}, "price_history_panel_lowest_price": {"message": "Legal<PERSON><PERSON><PERSON><PERSON> ár:"}, "price_history_panel_tab_price_tracking": {"message": "Árelőzmények"}, "price_history_panel_tab_seller_analysis": {"message": "Eladó elemzése"}, "price_history_pro_modal_title": {"message": "Ártörténet és rendelési előzmények"}, "privacy_consent__btn_agree": {"message": "Látogassa meg az adatgyűjtési hozzáj<PERSON><PERSON><PERSON>"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "Az összes engedélyezése"}, "privacy_consent__btn_uninstall": {"message": "Eltávolítás"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON> <PERSON>, hogy adatok vagy sütik nélkül egyes funkciók ki lesznek kapcsolva, mivel ezekhez a funkciókhoz meg kell magyarázni az adatokat vagy a sütiket, de a többi funkciót továbbra is használhatja."}, "privacy_consent__desc_privacy_L1": {"message": "Sajnos adatok vagy sütik nélkül nem fog működni, mert szükségünk van az adatok vagy a sütik magyarázatára."}, "privacy_consent__desc_privacy_L2": {"message": "Ha nem engedi, hogy összegyűjtsük ezeket az információkat, k<PERSON><PERSON><PERSON><PERSON><PERSON>, távolítsa el azokat."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, az Ön pénznem adatait csak akkor kapjuk meg cookie-kban, amikor online vásárolunk, hogy megmutassuk az árelőzményeket."}, "privacy_consent__item_cookies_title": {"message": "Szükséges sütik"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON> hozz<PERSON> cookie-kat a böngészőbe a számítógép vagy eszköz névtelen azonosításához."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON> a funkcionális adatokat a kiegészítőhöz, hogy működjön a funkcióval."}, "privacy_consent__item_functional_title": {"message": "Funkcionális és Analytics sütik"}, "privacy_consent__more_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>, hogy az Ön s<PERSON> adatait nem osztjuk meg más társaságokkal, és egyetlen reklámcég sem gyűjti az adatokat a szolgáltatásunkon keresztül."}, "privacy_consent__options__btn__desc": {"message": "Az összes funkció használatához be kell kapcsolnia."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> be"}, "privacy_consent__options__desc_L1": {"message": "A következő adatokat g<PERSON>ű<PERSON>k, <PERSON><PERSON><PERSON> azonosít<PERSON><PERSON>:"}, "privacy_consent__options__desc_L2": {"message": "- <PERSON><PERSON><PERSON><PERSON>, az Ön valutaadatait sütikben csak akkor kapjuk meg, ha <PERSON> vásárolunk, hogy ábrázoljuk az árat."}, "privacy_consent__options__desc_L3": {"message": "- és adjon hozzá sütiket a böngészőbe a számítógép vagy eszköz névtelen azonosításához."}, "privacy_consent__options__desc_L4": {"message": "- más névtelen adatok megkönnyítik ezt a kiterjesztést."}, "privacy_consent__options__desc_L5": {"message": "Felhív<PERSON><PERSON>, hogy az Ön s<PERSON> adatait nem osztjuk meg más társaságokkal, és egyetlen reklámcég sem gyűjti az adatokat a szolgáltatásunkon keresztül."}, "privacy_consent__privacy_preferences": {"message": "Adatvédel<PERSON>"}, "privacy_consent__read_more": {"message": "Bővebben >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_info": {"message": "Termék információ"}, "product_recommend__name": {"message": "Ugyanazok a termékek"}, "product_research": {"message": "Termékkutatás"}, "product_sub__email_desc": {"message": "Árértesítés e-mail"}, "product_sub__email_edit": {"message": "szerkeszteni"}, "product_sub__email_not_verified": {"message": "K<PERSON><PERSON><PERSON><PERSON>k, igazolja az e-mail-címet"}, "product_sub__email_required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg e-mailt"}, "product_sub__form_countdown": {"message": "Automatikus bezárás $seconds$ másodperc után", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "<PERSON>em si<PERSON>ü<PERSON> emlékeztetőt hozzáadni!"}, "product_sub__form_input_price": {"message": "be<PERSON><PERSON>r"}, "product_sub__form_item_country": {"message": "ne<PERSON>zet"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_item_duration": {"message": "vágány"}, "product_sub__form_item_higher_price": {"message": "<PERSON><PERSON><PERSON> ár>"}, "product_sub__form_item_invalid_higher_price": {"message": "Az árnak nagyobbnak kell lennie, mint $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Az árnak alacsonyabbnak kell lennie, mint $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Am<PERSON>r az <PERSON>r <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_success": {"message": "Sikerült emlékeztetőt hozzáadni!"}, "product_sub__high_price_notify": {"message": "Értesítsen áremelésről"}, "product_sub__low_price_notify": {"message": "Értesítsen <PERSON>llí<PERSON>ásról"}, "product_sub__modal_title": {"message": "Emlékeztető az előfizetési ár változására"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ és MOP"}, "qi_ye_mian_ji": {"message": "Vállalkozási terület"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon legalább egy terméket"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON>lasszon legalább egy mezőt"}, "qu_deng_lu": {"message": "Jelentkezzen be"}, "quan_guo_yan_xuan": {"message": "Globális választás"}, "recommendation_popup_banner_btn_install": {"message": "Telepítse"}, "recommendation_popup_banner_desc": {"message": "Az árelőzmények megjelenítése 3/6 h<PERSON><PERSON> be<PERSON>, és értesítés az árcsökkenésről"}, "region__all": {"message": "<PERSON><PERSON> r<PERSON>"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "<PERSON><PERSON> engedélyez<PERSON>e"}, "ren_zheng_gong_chang": {"message": "Minősített g<PERSON>"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON> tanúsí<PERSON>tt <PERSON>"}, "required_to_aliprice_login": {"message": "Be kell jelentkezni az AliPrice-be"}, "revenue_last30_days": {"message": "Eladási összeg az elmúlt 30 napban"}, "review_counts": {"message": "Gyűjtők száma"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Belépési időszak"}, "sales_amount_last30_days": {"message": "Teljes értékesítés az elmúlt 30 napban"}, "sales_last30_days": {"message": "Eladások az elmúlt 30 napban"}, "sbi_alibaba_cate__accessories": {"message": "kiegészítők"}, "sbi_alibaba_cate__aqfk": {"message": "Biztonság"}, "sbi_alibaba_cate__bags_cases": {"message": "Táskák és tokok"}, "sbi_alibaba_cate__beauty": {"message": "Szépség"}, "sbi_alibaba_cate__beverage": {"message": "Ital"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON>rod<PERSON> k<PERSON>ú<PERSON>"}, "sbi_alibaba_cate__bz": {"message": "Csomag"}, "sbi_alibaba_cate__ccyj": {"message": "Konyhaedények"}, "sbi_alibaba_cate__clothes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Médiaközvetítés"}, "sbi_alibaba_cate__coat_jacket": {"message": "<PERSON><PERSON><PERSON><PERSON> és dzseki"}, "sbi_alibaba_cate__consumer_electronics": {"message": "A fogyasztói elektronika"}, "sbi_alibaba_cate__cryp": {"message": "Felnőtt termékek"}, "sbi_alibaba_cate__csyp": {"message": "Ágybélések"}, "sbi_alibaba_cate__cwyy": {"message": "Kis<PERSON>llat k<PERSON>"}, "sbi_alibaba_cate__cysx": {"message": "Vend<PERSON>g<PERSON><PERSON><PERSON><PERSON> f<PERSON>sen"}, "sbi_alibaba_cate__dgdq": {"message": "Villanyszerelő"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Ruhák és öltönyök"}, "sbi_alibaba_cate__dszm": {"message": "Világítás"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronikai eszköz"}, "sbi_alibaba_cate__essb": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__food": {"message": "Étel"}, "sbi_alibaba_cate__fspj": {"message": "Ruházat és Kiegészítők"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "Textilbőr"}, "sbi_alibaba_cate__ghjq": {"message": "Testá<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gt": {"message": "Acél"}, "sbi_alibaba_cate__gyp": {"message": "Iparművészet"}, "sbi_alibaba_cate__hb": {"message": "Környezetbarát"}, "sbi_alibaba_cate__hfcz": {"message": "Bőrápoló smink"}, "sbi_alibaba_cate__hg": {"message": "Vegyipar"}, "sbi_alibaba_cate__jg": {"message": "Feldolgozás"}, "sbi_alibaba_cate__jianccai": {"message": "Építőanyagok"}, "sbi_alibaba_cate__jichuang": {"message": "Szerszámgép"}, "sbi_alibaba_cate__jjry": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jtys": {"message": "Szállítás"}, "sbi_alibaba_cate__jxsb": {"message": "Felszerelés"}, "sbi_alibaba_cate__jxwj": {"message": "Mechanikus <PERSON>ver"}, "sbi_alibaba_cate__jydq": {"message": "Háztartási gépek"}, "sbi_alibaba_cate__jzjc": {"message": "Lakásfelújítási építőanyagok"}, "sbi_alibaba_cate__jzjf": {"message": "Lakástextil"}, "sbi_alibaba_cate__mj": {"message": "Törülköző"}, "sbi_alibaba_cate__myyp": {"message": "Babatermékek"}, "sbi_alibaba_cate__nanz": {"message": "Férfiak"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "Energia"}, "sbi_alibaba_cate__others": {"message": "Mások"}, "sbi_alibaba_cate__qcyp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qmpj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__shoes": {"message": "Cipők"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON><PERSON><PERSON><PERSON> számítógép"}, "sbi_alibaba_cate__snqj": {"message": "Tárolás és tisztítás"}, "sbi_alibaba_cate__spjs": {"message": "Étel ital"}, "sbi_alibaba_cate__swfw": {"message": "Üzleti szolgáltatások"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Nadrág és szoknya"}, "sbi_alibaba_cate__txcp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__tz": {"message": "Gyermek ruházat"}, "sbi_alibaba_cate__underwear": {"message": "Fehérnemű"}, "sbi_alibaba_cate__wjgj": {"message": "Hardver eszközök"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "projekt együttműködés"}, "sbi_alibaba_cate__xs": {"message": "Rad<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportruházat"}, "sbi_alibaba_cate__ydhw": {"message": "Szabadtéri sport"}, "sbi_alibaba_cate__yjkc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yqyb": {"message": "Hangszerelés"}, "sbi_alibaba_cate__ys": {"message": "Nyomtatás"}, "sbi_alibaba_cate__yyby": {"message": "Egészségügyi ellátás"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Vásárlók száma az elmúlt 90 napban"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Értékesítési mennyiség az elmúlt 90 napban"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Nemzetközi szállítási díj"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Szállítási költség"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lr": {"message": "Nyereség"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Nyereség = becsült ár x haszonkulcs"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Átlagos szállítási sebesség"}, "sbi_alibaba_cn_kj_qtfy": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Egyéb költség = becsült ár x egyéb költségar<PERSON>"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "Súly"}, "sbi_alibaba_cn_kj_szd": {"message": "Elhelyezkedés"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Darabok"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Darabok"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gramm"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Vevők"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Darabok"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Készletek"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Napok"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "<PERSON>r"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Eladó"}, "sbi_aliprice_alibaba_cn__moren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "biztos"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Értékes<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "b<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "falatozás"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "sminkek"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Fehérnemű"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "kiegészítők"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Palackozott ital"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Mások"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Szoknya"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "d<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronika"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "cipő"}, "sbi_aliprice_cate__apparel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Gépkocsik és motorkerékpárok"}, "sbi_aliprice_cate__beauty_health": {"message": "szépség és egészség"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobiltelefonok és telekommunikáció"}, "sbi_aliprice_cate__computer_office": {"message": "Számítógép és Iroda"}, "sbi_aliprice_cate__consumer_electronics": {"message": "A fogyasztói elektronika"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Oktatási és irodai kellékek"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektronikus alkatrészek és kellékek"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Hajhosszabbítás és paróka"}, "sbi_aliprice_cate__home_garden": {"message": "Otthon és kert"}, "sbi_aliprice_cate__home_improvement": {"message": "Lakásfelújítás"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Ékszerek és kiegészítők"}, "sbi_aliprice_cate__luggage_bags": {"message": "Poggyász és táskák"}, "sbi_aliprice_cate__mother_kids": {"message": "<PERSON>"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Újdonság és speciális f<PERSON>"}, "sbi_aliprice_cate__security_protection": {"message": "Biztonság és védelem"}, "sbi_aliprice_cate__shoes": {"message": "Cipő"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport és szórakozás"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Játékok és hobbik"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Esküvők és események"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON><PERSON> most"}, "sbi_button__chat_with_me": {"message": "Csevegjünk"}, "sbi_button__contact_supplier": {"message": "Ka<PERSON><PERSON><PERSON>t<PERSON> "}, "sbi_button__hide_on_this_site": {"message": "Ne jelenjen meg ezen a webhelyen"}, "sbi_button__open_settings": {"message": "Konfigurálja a keresést kép szerint"}, "sbi_capture_shortcut_tip": {"message": "vagy nyo<PERSON>ja meg az \"<PERSON><PERSON>\" billent<PERSON> a billentyűzeten"}, "sbi_capturing_tip": {"message": "Rög<PERSON><PERSON><PERSON><PERSON>"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 csillag"}, "sbi_crop_and_search": {"message": "Keresés"}, "sbi_crop_start": {"message": "Használja a Képernyőképet"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Ellen<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> fel egy képet az ellenőrzéshez. (Nézze meg a $video_tutorial$-t, vagy próbálja meg törölni a cookie-kat)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Szokatlan forgalom, kérjük, ellen<PERSON>ze"}, "sbi_err_captcha_for_taobao": {"message": "Taobao kéri, hogy <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON>ön fel kézzel egy képet, <PERSON>s keressen rá annak igazolására. Ez a hiba a \"TaoBao kép szerinti keresés\" új ellenőrzési irányelvnek tudható be, javasoljuk, hogy a panaszt túl gyakran ellenőrizze a Taobao $feedback$-n.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "Visszacsatolás"}, "sbi_err_captcha_msg": {"message": "$platform$ meg<PERSON><PERSON><PERSON><PERSON>, hogy egy képet töltsön fel a kereséshez, vagy végezzen biztonsági ellenőrzést a keresési korlátozások eltávolításához", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON><PERSON>, hogy ez a legújabb verzió"}, "sbi_err_cookie_btn_clear": {"message": "Cookie-k törlése"}, "sbi_err_cookie_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON><PERSON> a 1688 cookie-kat? (<PERSON><PERSON><PERSON> be kell j<PERSON>nt<PERSON>z<PERSON>)"}, "sbi_err_desperate_feature_pdd": {"message": "A képkereső funkció átkerült a Pinduoduo Search by Image extension szolgáltatásba."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Hogyan javítható a képkeresés sikerességi a<PERSON>ánya?"}, "sbi_err_img_undersize": {"message": "Kép > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Jelentkezzen be $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Belépés"}, "sbi_err_low_version": {"message": "Telepítse a legújabb verziót ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Letöltés"}, "sbi_err_need_help": {"message": "Segítségre van szüksége"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> meg a<PERSON>, hogy fel tudja látogatni a webhelyet"}, "sbi_err_not_low_version": {"message": "A legújabb verzió telepítve van ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_visit_and_try": {"message": "Prób<PERSON><PERSON><PERSON>, vagy keresse fel a $website$ webhelyet az újrapróbálkozáshoz", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Látogassa meg a $siteName$ kezdőlapját", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "A betöltés nem si<PERSON>ült, frissítse az oldalt, és próbálja <PERSON>."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Ausztrália"}, "sbi_kuajing_filter_btn_confirm": {"message": "megerősít"}, "sbi_kuajing_filter_de": {"message": "Németország"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON><PERSON>sz<PERSON><PERSON>"}, "sbi_kuajing_filter_es": {"message": "Spanyolország"}, "sbi_kuajing_filter_estimate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Becsült árképlet = (nyersanyagár + nemzetközi logisztikai áruszállítás)/(1 - haszonkulcs - egy<PERSON><PERSON>)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON><PERSON> be a címhez tartozó kulcsszavakat"}, "sbi_kuajing_filter_logistics": {"message": "Logisztikai sablon"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logisztikai kedvezmény"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Nemzetközi logisztikai áruszállítás = (súly x szállítási ár + regisztrá<PERSON><PERSON> díj) x (1 - <PERSON><PERSON><PERSON><PERSON>)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "Haszonkulcs"}, "sbi_kuajing_filter_prop": {"message": "Tulajdonságok"}, "sbi_kuajing_filter_ru": {"message": "Oroszország"}, "sbi_kuajing_filter_total": {"message": "Párosítson $count$ hasonló elemeket", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo nagykereskedelem"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,jelentkezzen be a $loginSite$ webhelyre,vagy pr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> másik képpel", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Átmenetileg nem érhető el a Safari számára, kérjük, használja az $supportPage$-t.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome böngésző és annak bővítményei"}, "sbi_msg_no_result_reinstall_l1": {"message": "<PERSON><PERSON><PERSON> tal<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jelentkezzen be az $loginSite$ webhelyre, vagy pr<PERSON><PERSON><PERSON> ki egy másik képet, vagy telepítse újra a $latestExtUrl$ legújabb verzióját", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Leg<PERSON><PERSON><PERSON> verzi<PERSON>", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Kiválasztott terület"}, "sbi_shipping_": {"message": "Szállítás aznap"}, "sbi_specify_category": {"message": "Adja meg a kategóriát:"}, "sbi_start_crop": {"message": "Válasszon területet"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 tengerentúlon"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON><PERSON><PERSON> kuponokat"}, "sbi_txt__empty_retry": {"message": "<PERSON>jn<PERSON> ninc<PERSON>, pró<PERSON><PERSON><PERSON><PERSON>zon újra."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON>"}, "sbi_visiting": {"message": "Böngészés"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON>z árért forduljon az eladóhoz"}, "sbi_yiwugo__qigou": {"message": "$num$ Pieces (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Csillagok"}, "searchByImage_screenshot": {"message": "<PERSON><PERSON> katti<PERSON> képernyőkép"}, "searchByImage_search": {"message": "Egykattintásos keresés ugyanazokra az elemekre"}, "searchByImage_size_type": {"message": "A fájl mérete nem lehet nagyobb $num$ MB-nál, csak $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON>p el<PERSON>"}, "search_by_image_progress_searching": {"message": "Termékek keresése"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON>"}, "search_by_image_response_rate": {"message": "Válaszadási arány: $responseRate$ azon vásárl<PERSON>k közül, akik kapcsolatba léptek ezzel a szállítóval, $responseInHour$ órán belül kaptak választ.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Keresés k<PERSON>sz<PERSON>:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Nyelv"}, "select_country_region_modal_title": {"message": "V<PERSON><PERSON><PERSON> / ré<PERSON>"}, "select_language_modal_title": {"message": "Válasszon nyelvet:"}, "select_shop": {"message": "Válassza ki a boltot"}, "sellers_count": {"message": "Eladók száma az aktuális oldalon"}, "sellers_count_per_page": {"message": "Eladók száma az aktuális oldalon"}, "service_score": {"message": "Átfogó szolgáltatás minősítés"}, "set_shortcut_keys": {"message": "Gyorsbillentyűk beállítása"}, "setting_logo_title": {"message": "Vásárlási asszisztens"}, "setting_modal_options_position_title": {"message": "Dugas<PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_position_value_left": {"message": "Bal sarok"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON> sa<PERSON>"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "Beállítások"}, "setting_options_country_title": {"message": "Ország / régió"}, "setting_options_hover_zoom_desc": {"message": "A nagyításhoz vigye az egérmutatót"}, "setting_options_hover_zoom_title": {"message": "Mutasson a nagyításra"}, "setting_options_jd_coupon_desc": {"message": "Talált kupont a JD.com oldalon"}, "setting_options_jd_coupon_title": {"message": "JD.com kupon"}, "setting_options_language_title": {"message": "Nyelv"}, "setting_options_price_drop_alert_desc": {"message": "Amikor a Kedvencemben szereplő termékek ára c<PERSON>, push értesítést kap."}, "setting_options_price_drop_alert_title": {"message": "Figyelem az árcsökkenésről"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON>z árelőzmények megjelenítése a termékkereső oldalon"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (listaoldal)"}, "setting_options_price_history_on_produt_page_desc": {"message": "A termékelőzmények megjelenítése a termék részletes oldalán"}, "setting_options_price_history_on_produt_page_title": {"message": "Árelőz<PERSON><PERSON>ek (részletek oldal)"}, "setting_options_sales_analysis_desc": {"message": "Támogassa a<PERSON>, az értékesítési mennyiségre, az eladók számára és a bolti értékesítési arányra vonatkozó statisztikákat a $platforms$ terméklista oldalán", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Értékesítési elemzés"}, "setting_options_save_success_msg": {"message": "Siker"}, "setting_options_tacking_price_title": {"message": "Árváltozás figyelmeztetés"}, "setting_options_value_off": {"message": "<PERSON>"}, "setting_options_value_on": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_pkg_quick_view_desc": {"message": "Támogatás: 1688 és Taobao"}, "setting_saved_message": {"message": "A változtatások mentése sikeres volt"}, "setting_section_enable_platform_title": {"message": "Be ki"}, "setting_section_setting_title": {"message": "Beállítások"}, "setting_section_shortcuts_title": {"message": "Parancsikonok"}, "settings_aliprice_agent__desc": {"message": "Megjelenik a $platforms$ termékrészletes oldalon", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Vásárol<PERSON>"}, "settings_copy_link__desc": {"message": "Megjelenítés a termék részletes oldalán"}, "settings_copy_link__title": {"message": "Másolás gomb és a Keresés címe"}, "settings_currency_desc__for_detail": {"message": "Támogassa az 1688 termék részleteit"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON> szerinti kere<PERSON> (bele<PERSON>rtve a tengerentúli / Taobao 1688/1688 számot)"}, "settings_currency_desc__for_sbi": {"message": "Válassza ki az árat"}, "settings_currency_desc_display_for_list": {"message": "Megjelenik a képkeresőben (beleértve a 1688/1688 tengerentúli/Taobaót)"}, "settings_currency_rate_desc": {"message": "Az árfolyam frissítése a \"$currencyRateFrom$\" értékről", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "kínai bank"}, "settings_download_images__desc": {"message": "Támogatja a képek letöltését a $platforms$ webhelyről", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "<PERSON><PERSON><PERSON>e gomb"}, "settings_download_reviews__desc": {"message": "Megjelenik a $platforms$ termékrészletes oldalon", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Töltse le az értékelési képeket"}, "settings_google_translate_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> a jobb gombbal a Google fordítósáv megjelenítéséhez"}, "settings_google_translate_title": {"message": "weboldal fordítása"}, "settings_historical_trend_desc": {"message": "Megjelenítés a terméklista oldalon a kép jobb alsó sa<PERSON>"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_productInfo_desc": {"message": "Részletesebb termékinformáció megjelenítése a terméklista oldalon. Ennek engedélyezése növelheti a számítógép terhelését, és oldalelmaradást okozhat. Ha ez befolyásolja a teljesítményt, a<PERSON><PERSON><PERSON><PERSON> letiltani."}, "settings_product_recommend__desc": {"message": "Megjelenik a fő kép alatt a $platforms$ termékrészletek oldalán", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Termékek ajánlott"}, "settings_research_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> információkat a terméklista oldalon"}, "settings_sbi_add_to_list": {"message": "Adja hozzá a $listType$ elemhez", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "A képkeresési eredmény miniatűrje"}, "settings_sbi_remove_from_list": {"message": "Eltávolítás a következő helyről: $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Hozzáadás a tiltólistához"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Á<PERSON><PERSON>ts<PERSON> be a bejárati pozíciót"}, "settings_search_by_image_blacklist_desc": {"message": "Ne jelenítsen meg ikont a feketelistán szereplő webhelyeken."}, "settings_search_by_image_blacklist_title": {"message": "Tiltólista"}, "settings_search_by_image_bottom_left": {"message": "<PERSON><PERSON><PERSON> lent"}, "settings_search_by_image_bottom_right": {"message": "Job<PERSON> lent"}, "settings_search_by_image_clear_blacklist": {"message": "Törölje a tiltólistát"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatűr"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Egy képre történő kereséshez kattintson az ikonra"}, "settings_search_by_image_display_button_title": {"message": "Ikon a képeken"}, "settings_search_by_image_sourece_websites_desc": {"message": "<PERSON><PERSON><PERSON> meg a forrás terméket ezeken a webhelyeken"}, "settings_search_by_image_sourece_websites_title": {"message": "Keresés képeredmény <PERSON>apján"}, "settings_search_by_image_top_left": {"message": "<PERSON>l fels<PERSON>"}, "settings_search_by_image_top_right": {"message": "Jobbra fent"}, "settings_search_keyword_on_x__desc": {"message": "Keressen szavakat a $platform$ webhelyen", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "A $platform$ ikon megjelenítése szavak kiválasztásakor", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Próbálja meg megtalálni ugyanazt a terméket ezeken a webhelyeken (legfeljebb 5)"}, "settings_similar_products_title": {"message": "<PERSON><PERSON><PERSON> meg ugyanazt a terméket"}, "settings_toolbar_expand_title": {"message": "Plug-in minimalizálása"}, "settings_top_toolbar_desc": {"message": "Keresősáv az oldal tetején"}, "settings_top_toolbar_title": {"message": "Keres<PERSON> sáv"}, "settings_translate_search_desc": {"message": "Fordítsa le kínaira és keressen"}, "settings_translate_search_title": {"message": "Többnyelvű keresés"}, "settings_translator_contextmenu_title": {"message": "Rögzítse a fordításhoz"}, "settings_translator_title": {"message": "for<PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Szűrés az exportáláshoz"}, "shai_xuan_zi_duan": {"message": "Mezők szűrése"}, "shang_jia_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shang_pin_biao_ti": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "shang_pin_dui_bi": {"message": "Termékek összehasonlítása"}, "shang_pin_lian_jie": {"message": "<PERSON><PERSON><PERSON>"}, "shang_pin_xin_xi": {"message": "Termék információ"}, "share_modal__content": {"message": "Oszd meg a barátaiddal"}, "share_modal__disable_for_while": {"message": "<PERSON><PERSON> semmit"}, "share_modal__title": {"message": "Szereted az $extensionName$-t?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "Testreszabható?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Minősített szállító"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Minősített <PERSON>"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Kereskedelmi biztosítás"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Szállítási költség"}, "shop_followers": {"message": "<PERSON><PERSON> k<PERSON>i"}, "shou_qi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Max. 5"}, "sku_calc_price": {"message": "Sz<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sku_calc_price_settings": {"message": "Számí<PERSON>tt <PERSON>í<PERSON>"}, "sku_formula": {"message": "K<PERSON><PERSON><PERSON>"}, "sku_formula_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_formula_desc_text": {"message": "Támogatja az összetett matematikai képleteket, ahol az eredeti árat A, a fuvart pedig B képviseli\n\n<br/>\n\nTámogatja a zárójeleket (), plusz +, mínusz -, szorzás * és osztás /\n\n<br/>\n\n<PERSON><PERSON><PERSON>:\n\n<br/>\n\n1. <PERSON>z eredeti ár 1,2-s<PERSON><PERSON><PERSON><PERSON> eléréséhez, majd ho<PERSON>ad<PERSON>k a fuvardíjat, a képlet a következő: A*1,2+B\n\n<br/>\n\n2. Az eredeti ár plusz 1 jüan el<PERSON>z, majd 1,2-szeres szorozásához a képlet a következő: (A+1)*1,2\n\n<br/>\n\n3. Az eredeti ár plusz 10 jüan elé<PERSON>z, majd 1,2-s<PERSON><PERSON> szorozásához, majd 3 jüan levon<PERSON> a képlet a következő: (A+10)*1,2-3"}, "sku_in_stock": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_invalid_formula_format": {"message": "Érvénytelen képlet formátum"}, "sku_inventory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_link_copy_fail": {"message": "<PERSON><PERSON><PERSON> m<PERSON>, a termékváltozat-specifikációk és attribútumok nincsenek kiválasztva"}, "sku_link_copy_success": {"message": "<PERSON><PERSON><PERSON> m<PERSON>, a termékváltozat-specifikációk és attribútumok kiválasztva"}, "sku_list": {"message": "Cikkszám Lista"}, "sku_min_qrder_qty": {"message": "<PERSON><PERSON><PERSON><PERSON> re<PERSON> men<PERSON>"}, "sku_name": {"message": "Cikkszám neve"}, "sku_no": {"message": "Szám"}, "sku_original_price": {"message": "<PERSON><PERSON><PERSON>"}, "sku_price": {"message": "Cikkszá<PERSON>"}, "stop_track_time_label": {"message": "Követési hatá<PERSON>ő:"}, "suo_zai_di_qu": {"message": "elhelyezkedés"}, "tab_pkg_quick_view": {"message": "Logisztikai Monitor"}, "tab_product_details_price_history": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_reviews": {"message": "Fotók véleménye"}, "tab_product_details_seller_analysis": {"message": "Eladó elemzése"}, "tab_product_details_similar_products": {"message": "Ugyanazok a termékek"}, "total_days_listed_per_product": {"message": "Raktári napok összege ÷ Termékek száma"}, "total_items": {"message": "Termékek teljes s<PERSON>áma"}, "total_price_per_product": {"message": "Árak összege ÷ Termékek száma"}, "total_rating_per_product": {"message": "Értékelések összege ÷ Termékek száma"}, "total_revenue": {"message": "Összes bevétel"}, "total_revenue40_items": {"message": "Az aktuális oldalon található $amount$ termék összbevétele", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Összes értékesítés"}, "total_sales40_items": {"message": "Az aktuális oldalon található $amount$ termék összértékesítése", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Pálya: 1 évig"}, "track_for_3_months": {"message": "Pálya: 3 hónapig"}, "track_for_6_months": {"message": "Pálya: 6 hónapig"}, "tracking_price_email_add_btn": {"message": "E-mail hozzáadása"}, "tracking_price_email_edit_btn": {"message": "E-mail szerkesztése"}, "tracking_price_email_intro": {"message": "E-mailben é<PERSON>sítjük."}, "tracking_price_email_invalid": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes e-mailt"}, "tracking_price_email_verified_desc": {"message": "Most megkaphatja az árcsökkenésről szóló figyelmeztetésünket."}, "tracking_price_email_verified_title": {"message": "<PERSON><PERSON><PERSON>n el<PERSON>"}, "tracking_price_email_verify_desc_line1": {"message": "Ellenőrző linket küldtünk az e-mail címére,"}, "tracking_price_email_verify_desc_line2": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizze az e-mail postaládáját."}, "tracking_price_email_verify_title": {"message": "E-mail megerősítés"}, "tracking_price_web_push_notification_intro": {"message": "Asztalon: <PERSON><PERSON> AliPrice bármely terméket figyelemmel kísérhet az Ön <PERSON>, és az ár megváltozása után webes értesítést küldhet Önnek."}, "tracking_price_web_push_notification_title": {"message": "Web Push értesítések"}, "translate_im__login_required": {"message": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jelentkezzen be a $loginUrl$ címen", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Lefordítva és a vágólapra másolva, de az Aliwangwang korlátozása miatt manuálisan kell beillesztenie!"}, "translate_im__send": {"message": "Fordítás és küldés"}, "translate_search": {"message": "Fordítás és keresés"}, "translation_originals_translated": {"message": "Eredeti és kínai"}, "translation_translated": {"message": "<PERSON><PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "for<PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Automatikus é<PERSON>lés"}, "translator_language_detected": {"message": "Észlelve"}, "translator_language_search_placeholder": {"message": "Keresés nyelve"}, "try_again": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tu_pian_chi_cun": {"message": "Képméret:"}, "tu_pian_lian_jie": {"message": "Kép link"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tui_huan_ti_yan__desc": {"message": "Értékelje az eladók értékesítés utáni mutatóit"}, "tutorial__show_all": {"message": "<PERSON><PERSON>"}, "tutorial_ae_popup_title": {"message": "Rögzítse a bővítményt, nyissa meg az Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress felülvizsgálati elemzés"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Támogatás USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Szállí<PERSON><PERSON>/Japánba/K<PERSON>ai <PERSON>öldre"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Támogatja a tengerentúli vásárlást"}, "tutorial_auto_apply_coupon_title": {"message": "Kupon automatikus alkalmazása"}, "tutorial_btn_end": {"message": "Vége"}, "tutorial_btn_example": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_compare_products": {"message": "termékek összehasonlitása"}, "tutorial_currency_convert_title": {"message": "Pénznem átváltása"}, "tutorial_export_shopping_cart": {"message": "Exportálás CSV-ként, a Taobao és a 1688 támogatása"}, "tutorial_export_shopping_cart_title": {"message": "Export kosár"}, "tutorial_price_history_pro": {"message": "Megjelenik a termék részletes oldalán.\nTámogatás a Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "<PERSON><PERSON>sz é<PERSON>et és rendelési előzmények"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Rögzítse a kép alapján történő kereséshez"}, "tutorial_translate_search": {"message": "Fordítás kere<PERSON>"}, "tutorial_translate_search_and_package_tracking": {"message": "Fordításkeresés és csomagkövetés"}, "unit_bao": {"message": "db"}, "unit_ben": {"message": "db"}, "unit_bi": {"message": "paranc<PERSON><PERSON>"}, "unit_chuang": {"message": "db"}, "unit_dai": {"message": "db"}, "unit_dui": {"message": "<PERSON><PERSON><PERSON>"}, "unit_fen": {"message": "db"}, "unit_ge": {"message": "db"}, "unit_he": {"message": "db"}, "unit_jian": {"message": "db"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "db"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "<PERSON><PERSON><PERSON>"}, "unit_tai": {"message": "db"}, "unit_ti": {"message": "db"}, "unit_tiao": {"message": "db"}, "unit_xiang": {"message": "db"}, "unit_zhang": {"message": "db"}, "unit_zhi": {"message": "db"}, "verify_contact_support": {"message": "Lépjen ka<PERSON>csolatba az ügyfélszolgálattal"}, "verify_human_verification": {"message": "<PERSON><PERSON><PERSON>"}, "verify_unusual_access": {"message": "Szokatlan hozzáférés észlelve"}, "view_history_clean_all": {"message": "Tiszta Mind"}, "view_history_clean_all_warring": {"message": "Tisztítsa meg az összes megtekintett rekordot?"}, "view_history_clean_all_warring_title": {"message": "Figyelem"}, "view_history_viewd": {"message": "Megtekintve"}, "website": {"message": "weboldal"}, "weight": {"message": "Súly"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON><PERSON> beszerezni az adatokat"}, "wu_liu_shi_xiao": {"message": "Időben történő szállítás"}, "wu_liu_shi_xiao__desc": {"message": "Az eladó üzletének 48 órás begyűjtési és teljesítési aránya"}, "xia_dan_jia": {"message": "Végs<PERSON> á<PERSON>"}, "xian_xuan_ze_product_attributes": {"message": "Válassza ki a termék tulajdonságait"}, "xiao_liang": {"message": "Értékesítési volumen"}, "xiao_liang_zhan_bi": {"message": "Az értékesítési mennyiség százalékos aránya"}, "xiao_shi": {"message": "$num$ óra", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Bevétel"}, "xiao_shou_e_zhan_bi": {"message": "A bevétel százaléka"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Válasszon $amount$ rekordot", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Választás"}, "yi_ding_zai_zuo_ce": {"message": "Rögzítve"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Minden termék betöltve"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "<PERSON><PERSON>íté<PERSON> részesedés"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON>"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON> forgalmi részesedés"}, "yi_shua_xin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yin_cang_xiang_tong_dian": {"message": "elrejteni a hasonlóságokat"}, "you_xiao_liang": {"message": "Eladási <PERSON>"}, "yu_ji_dao_da_shi_jian": {"message": "Becsült érkezési idő"}, "yuan_gong_ren_shu": {"message": "Alkalmazottak száma"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON>"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping értékesítések az elmúlt 30 napban"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping értékesítések az elmúlt 30 napban, a legmagasabbtól a legalacsonyabbig rendezve"}, "yue_xiao_liang__desc": {"message": "Eladási mennyiség az elmúlt 30 napban"}, "zhan_kai": {"message": "<PERSON><PERSON><PERSON>"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Ingy<PERSON>s s<PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "Fizetett megrendelések"}, "zhi_fu_ding_dan_shu__desc": {"message": "A <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> megrendelések szá<PERSON> (30 nap)"}, "zhu_ce_xing_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jellege"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "zi_duan": {"message": "Mezők"}, "zi_ti_xiao_liang": {"message": "Variá<PERSON><PERSON> elad<PERSON>"}, "zong_he_fu_wu_fen": {"message": "Összesített <PERSON>és"}, "zong_he_fu_wu_fen__desc": {"message": "Az eladói szolgáltatás általános értékelése"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen_3": {"message": "4 csillag alatt"}, "zong_he_ti_yan_fen_4": {"message": "4-4,5 csillag"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 csillag"}, "zong_he_ti_yan_fen_5": {"message": "5 csillag"}, "zong_ku_cun": {"message": "<PERSON><PERSON><PERSON>"}, "zong_xiao_liang": {"message": "Összes értékesítés"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3 perces válaszadási arány az elmúlt 30 napban"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48 órás gyógyulási arány az elmúlt 30 napban"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48 órás teljesítmény az elmúlt 30 napban"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Kereskedési rekord (30 nap)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Kereskedési rekord (30 nap)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Viták aránya az elmúlt 30 napban"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Minőségi visszatérítési arány az elmúlt 30 napban"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Fizetési megbízások száma az elmúlt 30 napban"}}