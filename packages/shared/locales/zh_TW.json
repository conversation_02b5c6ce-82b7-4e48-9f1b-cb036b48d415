{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688跨國熱賣現貨"}, "1688_shi_li_ren_zheng": {"message": "1688實力認證"}, "1_jian_qi_pi": {"message": "1件起批"}, "1year_yi_shang": {"message": "一年以上"}, "24H": {"message": "24小時"}, "24H_fa_huo": {"message": "24小時出貨"}, "24H_lan_shou_lv": {"message": "24小時攬收率"}, "30D_shang_xin": {"message": "30天上新"}, "30d_sales": {"message": "30天內$amount$個成交", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3分鐘回應率"}, "3Min_xiang_ying_lv__desc": {"message": "近30天旺旺3分鐘內有效回覆買家諮詢訊息的比例"}, "48H": {"message": "48小時"}, "48H_fa_huo": {"message": "48小時出貨"}, "48H_lan_shou_lv": {"message": "48小時攬收率"}, "48H_lan_shou_lv__desc": {"message": "48小時內有攬收記錄的訂單數與總訂單數的比率"}, "48H_lv_yue_lv": {"message": "48小時履約率"}, "48H_lv_yue_lv__desc": {"message": "48小時內有攬收記錄或出貨記錄的訂單數與總訂單數的比率"}, "72H": {"message": "72小時"}, "7D_shang_xin": {"message": "7天上新"}, "7D_wu_li_you": {"message": "7天無理由"}, "ABS_title_text": {"message": "該商品有品牌故事"}, "AC_title_text": {"message": "該商品有Amazon's Choice標識"}, "A_title_text": {"message": "該商品有A+頁面"}, "BS_title_text": {"message": "該商品是$type$類目下的$num$ 熱銷品", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "Limited time deal的縮寫，表示該商品參加了「7天促銷」活動"}, "NR_title_text": {"message": "該商品是$type$類目下的$num$ 熱銷新品", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "該商品投放了影片廣告。 PPC廣告的一種，一般在搜尋結果頁的中間位置出現"}, "SB_title_text": {"message": "該商品投放了品牌廣告。 PPC廣告的一種，一般在搜尋結果頁的頂部或底部出現"}, "SP_title_text": {"message": "該商品投放了Sponsored Product廣告"}, "V_title_text": {"message": "該商品有影片介紹"}, "advanced_research": {"message": "進階查詢"}, "agent_ds1688___my_order": {"message": "我的訂單"}, "agent_ds1688__add_to_cart": {"message": "跨境直購"}, "agent_ds1688__cart": {"message": "跨境購物車"}, "agent_ds1688__desc": {"message": "1688支持直購,美元、新台幣及港幣支付,送貨至你在中國內地的中轉倉"}, "agent_ds1688__freight": {"message": "國際運費試算"}, "agent_ds1688__help": {"message": "幫助"}, "agent_ds1688__packages": {"message": "配送單"}, "agent_ds1688__profile": {"message": "個人中心"}, "agent_ds1688__warehouse": {"message": "我的倉庫"}, "ai_comment_analysis_advantage": {"message": "優點"}, "ai_comment_analysis_ai": {"message": "AI評論分析"}, "ai_comment_analysis_available": {"message": "可用"}, "ai_comment_analysis_balance": {"message": "金幣不足，請充值"}, "ai_comment_analysis_behavior": {"message": "行為"}, "ai_comment_analysis_characteristic": {"message": "人群特徵"}, "ai_comment_analysis_comment": {"message": "該商品的評論數量不足，無法得出準確的結論，請選擇評論數更多的商品進行分析"}, "ai_comment_analysis_consume": {"message": "預計消耗"}, "ai_comment_analysis_default": {"message": "預設評論"}, "ai_comment_analysis_desire": {"message": "客戶期望"}, "ai_comment_analysis_disadvantage": {"message": "缺點"}, "ai_comment_analysis_free": {"message": "免費次數"}, "ai_comment_analysis_freeNum": {"message": "將消耗 1 次免費次數"}, "ai_comment_analysis_go_recharge": {"message": "去儲值"}, "ai_comment_analysis_intelligence": {"message": "智慧評論分析"}, "ai_comment_analysis_location": {"message": "使用地點"}, "ai_comment_analysis_motive": {"message": "購買動機"}, "ai_comment_analysis_network_error": {"message": "網路出錯，請重試"}, "ai_comment_analysis_normal": {"message": "圖文評論"}, "ai_comment_analysis_number_reviews": {"message": "評論數 $num$, 預計消耗 $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "普通評論"}, "ai_comment_analysis_percentage": {"message": "百分比"}, "ai_comment_analysis_problem": {"message": "支付遇到問題"}, "ai_comment_analysis_reanalysis": {"message": "重新分析"}, "ai_comment_analysis_reason": {"message": "原因"}, "ai_comment_analysis_recharge": {"message": "儲值"}, "ai_comment_analysis_recharged": {"message": "我已儲值"}, "ai_comment_analysis_retry": {"message": "重試"}, "ai_comment_analysis_scene": {"message": "使用場景"}, "ai_comment_analysis_start": {"message": "開始分析"}, "ai_comment_analysis_subject": {"message": "話題"}, "ai_comment_analysis_time": {"message": "使用時刻"}, "ai_comment_analysis_tool": {"message": "AI工具"}, "ai_comment_analysis_user_portrait": {"message": "使用者畫像"}, "ai_comment_analysis_welcome": {"message": "歡迎使用AI評論分析"}, "ai_comment_analysis_year": {"message": "近一年的評論"}, "ai_listing_Exclude_keywords": {"message": "排除關鍵字"}, "ai_listing_Login_the_feature": {"message": "此功能需要登入"}, "ai_listing_aI_generation": {"message": "AI生成"}, "ai_listing_add_automatic": {"message": "自動"}, "ai_listing_add_dictionary_new": {"message": "新建詞庫"}, "ai_listing_add_enter_keywords": {"message": "輸入關鍵字"}, "ai_listing_add_inputkey_selling": {"message": "輸入賣點，按$key$鍵完成新增", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "已超出限制，最多$amount$個賣點", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "新增關鍵字"}, "ai_listing_add_manually": {"message": "手動新增"}, "ai_listing_add_selling": {"message": "添加賣點"}, "ai_listing_added_keywords": {"message": "已新增的關鍵字"}, "ai_listing_added_successfully": {"message": "添加成功"}, "ai_listing_addexcluded_keywords": {"message": "輸入排除關鍵字，按回車鍵完成新增"}, "ai_listing_adding_selling": {"message": "已加入的賣點"}, "ai_listing_addkeyword_enter": {"message": "輸入關鍵屬性詞，按回車鍵完成新增"}, "ai_listing_ai_description": {"message": "AI描述詞庫"}, "ai_listing_ai_dictionary": {"message": "AI標題詞庫"}, "ai_listing_ai_title": {"message": "AI標題"}, "ai_listing_aidescription_repeated": {"message": "AI描述詞庫名稱不能重複"}, "ai_listing_aititle_repeated": {"message": "AI標題詞庫名稱不能重複"}, "ai_listing_data_comes_from": {"message": "此數據源自："}, "ai_listing_deleted_successfully": {"message": "刪除成功"}, "ai_listing_dictionary_name": {"message": "詞庫名"}, "ai_listing_edit_dictionary": {"message": "修改詞庫 ..."}, "ai_listing_edit_word_library": {"message": "編輯詞庫"}, "ai_listing_enter_keywords": {"message": "輸入關鍵字，按$key$鍵完成新增", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "已超出限制，最多$amount$個關鍵字", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "排除詞庫"}, "ai_listing_generate_characters": {"message": "生成字符"}, "ai_listing_generation_platform": {"message": "生成平台"}, "ai_listing_help_optimize": {"message": "幫我優化產品標題，原標題為"}, "ai_listing_include_selling": {"message": "其他賣點包括："}, "ai_listing_included_keyword": {"message": "包含關鍵字"}, "ai_listing_included_keywords": {"message": "已收錄的關鍵字"}, "ai_listing_input_selling": {"message": "輸入賣點"}, "ai_listing_input_selling_fit": {"message": "輸入賣點對標題進行匹配"}, "ai_listing_input_selling_please": {"message": "請輸入賣點"}, "ai_listing_intelligently_title": {"message": "上方輸入所需內容 即可智慧產生標題"}, "ai_listing_keyword_product_title": {"message": "關鍵字產品標題"}, "ai_listing_keywords_repeated": {"message": "關鍵字不能重複"}, "ai_listing_listed_selling_points": {"message": "已收錄的賣點"}, "ai_listing_long_title_1": {"message": "包含品牌名稱、產品類型、產品特徵等基本資訊。"}, "ai_listing_long_title_2": {"message": "在標準產品標題的基礎上，添加了有利於搜尋引擎優化的關鍵字。"}, "ai_listing_long_title_3": {"message": "除了包含品牌名稱、產品類型、產品特徵和關鍵字外，還包含了長尾關鍵字，以便在具體、細分的搜尋查詢中獲得更高的排名。"}, "ai_listing_longtail_keyword_product_title": {"message": "長尾關鍵字產品標題"}, "ai_listing_manually_enter": {"message": "手動輸入 ..."}, "ai_listing_network_not_working": {"message": "網路不通，存取ChatGPT需要科學上網"}, "ai_listing_new_dictionary": {"message": "新建詞庫 ..."}, "ai_listing_new_generate": {"message": "產生"}, "ai_listing_optional_words": {"message": "可選詞"}, "ai_listing_original_title": {"message": "原標題"}, "ai_listing_other_keywords_included": {"message": "其他關鍵字包括："}, "ai_listing_please_again": {"message": "請重試"}, "ai_listing_please_select": {"message": "已幫您產生以下標題，請選擇："}, "ai_listing_product_category": {"message": "產品分類"}, "ai_listing_product_category_is": {"message": "產品分類是"}, "ai_listing_product_category_to": {"message": "請問產品屬於什麼分類？"}, "ai_listing_random_keywords": {"message": "隨機$amount$個關鍵字", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "隨機$amount$個賣點", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "從詞庫中隨機"}, "ai_listing_search_selling": {"message": "按賣點搜尋"}, "ai_listing_select_product_categories": {"message": "自動選擇產品分類，"}, "ai_listing_select_product_selling_points": {"message": "自動選擇產品賣點"}, "ai_listing_select_word_library": {"message": "選擇詞庫"}, "ai_listing_selling": {"message": "賣點"}, "ai_listing_selling_ask": {"message": "標題還有什麼其他賣點要求？"}, "ai_listing_selling_optional": {"message": "可選賣點"}, "ai_listing_selling_repeat": {"message": "賣點不能重複"}, "ai_listing_set_excluded": {"message": "設為排除詞庫"}, "ai_listing_set_include_selling_points": {"message": "包含賣點"}, "ai_listing_set_included": {"message": "設為包含詞庫"}, "ai_listing_set_selling_dictionary": {"message": "設為賣點詞庫"}, "ai_listing_standard_product_title": {"message": "標準產品標題"}, "ai_listing_translated_title": {"message": "翻譯標題"}, "ai_listing_visit_chatGPT": {"message": "訪問ChatGPT"}, "ai_listing_what_other_keywords": {"message": "標題還有什麼其他關鍵字要求？"}, "aliprice_coupons_apply_again": {"message": "再試一次"}, "aliprice_coupons_apply_coupons": {"message": "使用優惠券"}, "aliprice_coupons_apply_success": {"message": "找到優惠券：节省 $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "測試最優惠的代碼..."}, "aliprice_coupons_applying_desc": {"message": "確認中：$code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "繼續付款"}, "aliprice_coupons_found_coupons": {"message": "我們找到了 $amount$ 優惠券", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "準備好付款了嗎？讓我們幫你確認最優惠的價格！"}, "aliprice_coupons_no_coupon_aviable": {"message": "這些代碼無法使用。沒關係——你已經得到了最優惠的價格。"}, "aliprice_coupons_toolbar_btn": {"message": "獲取優惠券"}, "aliww_translate": {"message": "阿里旺旺聊天翻譯"}, "aliww_translate_supports": {"message": "支持：1688 & 淘寶"}, "amazon_extended_keywords_Keywords": {"message": "搜尋詞"}, "amazon_extended_keywords_copy_all": {"message": "複製所有"}, "amazon_extended_keywords_more": {"message": "查看更多"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "按累計銷量排序"}, "an_lei_xing_cha_kan": {"message": "類型"}, "an_yue_dai_xiao_pai_xu": {"message": "按月代銷排序"}, "apra_btn__cat_name": {"message": "評論分析"}, "apra_chart__name": {"message": "產品銷量國家地區百分比"}, "apra_chart__update_at": {"message": "更新時間 $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "國家地區銷售統計"}, "auto_opening": {"message": "$num$秒後自動開啟", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "下$autoPaging$ 頁", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "平均上架天數"}, "average_hui_fu_lv": {"message": "平均回覆率"}, "average_ping_gong_ying_shang_deng_ji": {"message": "平均供應商等級"}, "average_price": {"message": "平均價格"}, "average_qi_ding_liang": {"message": "平均起訂量"}, "average_rating": {"message": "平均評分"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "平均認證供應年長"}, "average_revenue": {"message": "平均銷售額"}, "average_revenue_per_product": {"message": "總銷售額 ÷ 產品數"}, "average_sales": {"message": "平均銷量"}, "average_sales_per_product": {"message": "總銷售量 ÷ 產品數"}, "bao_han": {"message": "包含"}, "bao_zheng_jin": {"message": "保證金"}, "bian_ti_shu": {"message": "變體數"}, "biao_ti": {"message": "標題"}, "blacklist_add_blacklist": {"message": "封鎖此店鋪"}, "blacklist_address_incorrect": {"message": "地址不正確。請檢查。"}, "blacklist_blacked_out": {"message": "已封鎖此店鋪"}, "blacklist_blacklist": {"message": "黑名单"}, "blacklist_no_records_yet": {"message": "暫無記錄！"}, "blue_rocket": {"message": "蓝色火箭"}, "brand_name": {"message": "品牌名"}, "btn_aliprice_agent__daigou": {"message": "代購"}, "btn_aliprice_agent__dropshipping": {"message": "一件代發"}, "btn_have_a_try": {"message": "試一試"}, "btn_refresh": {"message": "刷新"}, "btn_try_it_now": {"message": "馬上試試"}, "btn_txt_view_on_aliprice": {"message": "在AliPrice上查看"}, "bu_bao_han": {"message": "不包含"}, "bulk_copy_links": {"message": "批量複製連結"}, "bulk_copy_products": {"message": "批量複製產品"}, "cai_gou_zi_xun": {"message": "採購諮詢"}, "cai_gou_zi_xun__desc": {"message": "商家的三分鐘回應率"}, "can_ping_lei_xing": {"message": "商品類型"}, "cao_zuo": {"message": "操作"}, "chan_pin_ID": {"message": "產品ID"}, "chan_pin_e_wai_xin_xi": {"message": "產品額外信息"}, "chan_pin_lian_jie": {"message": "產品連結"}, "cheng_li_shi_jian": {"message": "成立時間"}, "city__aba": {"message": "阿壩藏族羌族自治州"}, "city__aksu": {"message": "阿克蘇市"}, "city__alaer": {"message": "阿拉爾市"}, "city__altai": {"message": "阿勒泰"}, "city__alxaleague": {"message": "阿拉善盟"}, "city__ankang": {"message": "安康市"}, "city__anqing": {"message": "安慶市"}, "city__anshan": {"message": "鞍山市"}, "city__anshun": {"message": "安順市"}, "city__anyang": {"message": "安陽市"}, "city__baicheng": {"message": "白城市"}, "city__baise": {"message": "百色市"}, "city__baisha": {"message": "白沙黎族自治縣"}, "city__baishan": {"message": "白山市"}, "city__baiyin": {"message": "銀市"}, "city__baoding": {"message": "保訂市"}, "city__baoji": {"message": "寶雞市"}, "city__baoshan": {"message": "保山市"}, "city__baoting": {"message": "保亭黎族苗族自治縣"}, "city__baotou": {"message": "包頭市"}, "city__bayannur": {"message": "巴彥淖爾市"}, "city__bayinguoleng": {"message": "巴音郭楞蒙古自治州"}, "city__bazhong": {"message": "巴中市"}, "city__beihai": {"message": "北海市"}, "city__bengbu": {"message": "蚌埠市"}, "city__benxi": {"message": "本溪市"}, "city__bijie": {"message": "畢節市"}, "city__binzhou": {"message": "濱州市"}, "city__bortala": {"message": "博爾塔拉蒙古自治州"}, "city__bozhou": {"message": "亳州市"}, "city__cangzhou": {"message": "滄州市"}, "city__chamdo": {"message": "昌都市"}, "city__changchun": {"message": "長春市"}, "city__changde": {"message": "常德市"}, "city__changhua": {"message": "彰化縣"}, "city__changji": {"message": "昌吉回族自治州"}, "city__changjiang": {"message": "昌江黎族自治縣"}, "city__changsha": {"message": "長沙市"}, "city__changzhi": {"message": "長治市"}, "city__changzhou": {"message": "常州市"}, "city__chaohu": {"message": "巢湖市"}, "city__chaoyang": {"message": "朝陽市"}, "city__chaozhou": {"message": "潮州市"}, "city__chengde": {"message": "承德市"}, "city__chengdu": {"message": "成都市"}, "city__chengmai": {"message": "澄邁縣"}, "city__chenzhou": {"message": "郴州市"}, "city__chiayi": {"message": "嘉義市"}, "city__chifeng": {"message": "赤峰市"}, "city__chizhou": {"message": "池州市"}, "city__chongzuo": {"message": "崇左市"}, "city__chuxiong": {"message": "楚雄彝族自治州"}, "city__chuzhou": {"message": "滁州市"}, "city__dali": {"message": "大理白族自治州"}, "city__dalian": {"message": "大連市"}, "city__dandong": {"message": "丹東市"}, "city__danzhou": {"message": "儋州市"}, "city__daqing": {"message": "大慶市"}, "city__datong": {"message": "大同市"}, "city__daxinganling": {"message": "大興安嶺"}, "city__dazhou": {"message": "達州市"}, "city__dehong": {"message": "德宏傣族景頗族自治州"}, "city__deyang": {"message": "德陽市"}, "city__dezhou": {"message": "德州市"}, "city__dingan": {"message": "定安縣"}, "city__dingxi": {"message": "訂西市"}, "city__diqing": {"message": "迪慶藏族自治州"}, "city__dongfang": {"message": "東方市"}, "city__dongguan": {"message": "東莞市"}, "city__dongying": {"message": "東營市"}, "city__enshi": {"message": "恩施土家族苗族自治州"}, "city__ezhou": {"message": "鄂州市"}, "city__fangchenggang": {"message": "防城港市"}, "city__foshan": {"message": "佛山市"}, "city__fushun": {"message": "撫順市"}, "city__fuxin": {"message": "阜新市"}, "city__fuyang": {"message": "阜陽市"}, "city__fuzhou": {"message": "撫州市"}, "city__gannan": {"message": "甘南藏族自治州"}, "city__ganzhou": {"message": "贛州市"}, "city__ganzi": {"message": "甘孜藏族自治州"}, "city__guangan": {"message": "廣安市"}, "city__guangyuan": {"message": "廣元市"}, "city__guangzhou": {"message": "廣州市"}, "city__guigang": {"message": "貴港市"}, "city__guilin": {"message": "桂林市"}, "city__guiyang": {"message": "貴陽市"}, "city__guolok": {"message": "果洛藏族自治州"}, "city__guyuan": {"message": "固原市"}, "city__haibei": {"message": "海北藏族自治州"}, "city__haidong": {"message": "海東市"}, "city__haikou": {"message": "海口市"}, "city__hainan": {"message": "海南藏族自治州"}, "city__haixi": {"message": "海西蒙古藏族自治州"}, "city__hami": {"message": "哈密​​市"}, "city__handan": {"message": "邯鄲市"}, "city__hangzhou": {"message": "杭州市"}, "city__hanzhong": {"message": "漢中市"}, "city__harbin": {"message": "哈爾濱市"}, "city__hebi": {"message": "鶴壁市"}, "city__hechi": {"message": "河池市"}, "city__hefei": {"message": "合肥市"}, "city__hegang": {"message": "鶴崗市"}, "city__heihe": {"message": "黑河市"}, "city__hengshui": {"message": "衡水市"}, "city__hengyang": {"message": "衡陽市"}, "city__heyuan": {"message": "河源市"}, "city__heze": {"message": "菏澤市"}, "city__hezhou": {"message": "賀州市"}, "city__hohhot": {"message": "呼和浩特市"}, "city__honghe": {"message": "紅河哈尼族彝族自治州"}, "city__hongkongisland": {"message": "香港島"}, "city__hotan": {"message": "和田市"}, "city__hsinchu": {"message": "新竹縣"}, "city__huaian": {"message": "淮安市"}, "city__huaibei": {"message": "淮北市"}, "city__huaihua": {"message": "懷化市"}, "city__huaisouth": {"message": "淮南市"}, "city__hualien": {"message": "花蓮縣"}, "city__huanggang": {"message": "黃岡市"}, "city__huangnan": {"message": "黃南藏族自治州"}, "city__huangshan": {"message": "黃山市"}, "city__huangshi": {"message": "黃石市"}, "city__huizhou": {"message": "惠州市"}, "city__huludao": {"message": "葫蘆島市"}, "city__hulunbuir": {"message": "呼倫貝爾市"}, "city__huzhou": {"message": "湖州市"}, "city__jiamusi": {"message": "佳木斯市"}, "city__jian": {"message": "吉安市"}, "city__jiangmen": {"message": "江門市"}, "city__jiaozuo": {"message": "焦作市"}, "city__jiaxing": {"message": "嘉興市"}, "city__jiayuguan": {"message": "嘉峪關市"}, "city__jieyang": {"message": "揭陽市"}, "city__jilin": {"message": "吉林市"}, "city__jinan": {"message": "濟南市"}, "city__jinchang": {"message": "金昌市"}, "city__jincheng": {"message": "晉城市"}, "city__jingdezhen": {"message": "景德鎮市"}, "city__jingmen": {"message": "荊門市"}, "city__jingzhou": {"message": "荊州市"}, "city__jinhua": {"message": "金華市"}, "city__jining": {"message": "濟寧市"}, "city__jinzhong": {"message": "晉中市"}, "city__jinzhou": {"message": "錦州市"}, "city__jiujiang": {"message": "九江市"}, "city__jiuquan": {"message": "酒泉市"}, "city__jixi": {"message": "雞西市"}, "city__kaifeng": {"message": "開封市"}, "city__kaohsiung": {"message": "高雄市"}, "city__karamay": {"message": "克拉瑪依市"}, "city__kashgar": {"message": "喀什市"}, "city__keelung": {"message": "基隆市"}, "city__kinmen": {"message": "金門縣"}, "city__kizilsu": {"message": "克孜勒蘇柯爾克孜自治州"}, "city__kowloon": {"message": "九龍"}, "city__kunming": {"message": "昆明市"}, "city__laibin": {"message": "來賓市"}, "city__laiwu": {"message": "萊蕪市"}, "city__langfang": {"message": "廊坊市"}, "city__lanzhou": {"message": "蘭州市"}, "city__ledong": {"message": "樂東黎族自治縣"}, "city__leshan": {"message": "樂山市"}, "city__lhasa": {"message": "拉薩市"}, "city__liangshan": {"message": "涼山彝族自治州"}, "city__lianyungang": {"message": "連雲港市"}, "city__liaocheng": {"message": "聊城市"}, "city__liaoyang": {"message": "遼陽市"}, "city__liaoyuan": {"message": "遼源市"}, "city__lienchiang": {"message": "連江縣"}, "city__lijiang": {"message": "麗江市"}, "city__lincang": {"message": "臨滄市"}, "city__linfen": {"message": "臨汾市"}, "city__lingao": {"message": "臨高縣"}, "city__lingshui": {"message": "陵水黎族自治縣"}, "city__linxia": {"message": "臨夏回族自治州"}, "city__linyi": {"message": "臨沂市"}, "city__lishui": {"message": "麗水市"}, "city__liupanshui": {"message": "六盤水市"}, "city__liuzhou": {"message": "柳州市"}, "city__longnan": {"message": "隴南市"}, "city__longyan": {"message": "龍岩市"}, "city__loudi": {"message": "婁底市"}, "city__luan": {"message": "六安市"}, "city__luohe": {"message": "漯河市"}, "city__luoyang": {"message": "洛陽市"}, "city__luzhou": {"message": "瀘州市"}, "city__lüliang": {"message": "呂梁市"}, "city__maanshan": {"message": "馬鞍山市"}, "city__macauoutlyingislands": {"message": "澳門離島"}, "city__macaupeninsula": {"message": "澳門半島"}, "city__maoming": {"message": "茂名市"}, "city__meishan": {"message": "眉山市"}, "city__meizhou": {"message": "梅州市"}, "city__mianyang": {"message": "綿陽市"}, "city__miaoli": {"message": "苗栗縣"}, "city__mudanjiang": {"message": "牡丹江市"}, "city__nagqu": {"message": "那曲"}, "city__nanchang": {"message": "南昌市"}, "city__nanchong": {"message": "南充市"}, "city__nanjing": {"message": "南京市"}, "city__nanning": {"message": "南寧市"}, "city__nanping": {"message": "南平市"}, "city__nantong": {"message": "南通市"}, "city__nantou": {"message": "南投縣"}, "city__nanyang": {"message": "南陽市"}, "city__neijiang": {"message": "內江市"}, "city__newterritories": {"message": "新界"}, "city__ngari": {"message": "阿里"}, "city__ningbo": {"message": "寧波市"}, "city__ningde": {"message": "寧德市"}, "city__nujiang": {"message": "怒江傈傈族自治州"}, "city__nyingchi": {"message": "林芝市"}, "city__ordos": {"message": "鄂爾多斯市"}, "city__panjin": {"message": "盤錦市"}, "city__panzhihua": {"message": "攀枝花市"}, "city__penghu": {"message": "澎湖縣"}, "city__pingdingshan": {"message": "平頂山市"}, "city__pingliang": {"message": "平涼市"}, "city__pingtung": {"message": "屏東縣"}, "city__pingxiang": {"message": "萍鄉市"}, "city__puer": {"message": "普洱市"}, "city__putian": {"message": "莆田市"}, "city__puyang": {"message": "濮陽市"}, "city__qiandongnan": {"message": "黔東南苗族侗族自治州"}, "city__qianjiang": {"message": "潛江市"}, "city__qiannan": {"message": "黔南布依族苗族自治州"}, "city__qianxinan": {"message": "黔西南布依族苗族自治州"}, "city__qingdao": {"message": "青島市"}, "city__qingyang": {"message": "慶陽市"}, "city__qingyuan": {"message": "清遠市"}, "city__qinhuangdao": {"message": "秦皇島市"}, "city__qinzhou": {"message": "欽州市"}, "city__qionghai": {"message": "瓊海市"}, "city__qiongzhong": {"message": "瓊中黎族苗族自治縣"}, "city__qiqihar": {"message": "齊齊哈爾市"}, "city__qitaihe": {"message": "七台河市"}, "city__quanzhou": {"message": "泉州市"}, "city__qujing": {"message": "曲靖市"}, "city__quzhou": {"message": "衢州市"}, "city__rizhao": {"message": "日照市"}, "city__sanmenxia": {"message": "三門峽市"}, "city__sanming": {"message": "三明市"}, "city__sanya": {"message": "三亞市"}, "city__shangluo": {"message": "商洛市"}, "city__shangqiu": {"message": "商丘市"}, "city__shangrao": {"message": "上饒市"}, "city__shannan": {"message": "山南市"}, "city__shantou": {"message": "汕頭市"}, "city__shanwei": {"message": "汕尾市"}, "city__shaoguan": {"message": "韶關市"}, "city__shaoxing": {"message": "紹興市"}, "city__shaoyang": {"message": "邵陽市"}, "city__shennongjiaforestarea": {"message": "神農架林區"}, "city__shenyang": {"message": "瀋陽市"}, "city__shenzhen": {"message": "深圳市"}, "city__shigatse": {"message": "日喀則市"}, "city__shihezi": {"message": "石河子市"}, "city__shijiazhuang": {"message": "石家莊市"}, "city__shiyan": {"message": "十堰市"}, "city__shizuishan": {"message": "石嘴山市"}, "city__shuangyashan": {"message": "雙鴨山市"}, "city__shuozhou": {"message": "朔州市"}, "city__siping": {"message": "四平市"}, "city__songyuan": {"message": "松原市"}, "city__suihua": {"message": "綏化市"}, "city__suining": {"message": "遂寧市"}, "city__suizhou": {"message": "隨州市"}, "city__suqian": {"message": "宿遷市"}, "city__suzhou": {"message": "宿州市"}, "city__tacheng": {"message": "塔城"}, "city__taian": {"message": "泰安市"}, "city__taichung": {"message": "臺中市"}, "city__tainan": {"message": "台南市"}, "city__taipei": {"message": "台北市"}, "city__taitung": {"message": "台東縣"}, "city__taiyuan": {"message": "太原市"}, "city__taizhou": {"message": "泰州市"}, "city__tangshan": {"message": "唐山市"}, "city__taoyuan": {"message": "桃園市"}, "city__tianmen": {"message": "天門市"}, "city__tianshui": {"message": "天水市"}, "city__tieling": {"message": "鐵嶺市"}, "city__tongchuan": {"message": "銅川市"}, "city__tonghua": {"message": "通化市"}, "city__tongliao": {"message": "通遼市"}, "city__tongling": {"message": "銅陵市"}, "city__tongren": {"message": "銅仁市"}, "city__tumushuke": {"message": "圖木舒克市"}, "city__tunchang": {"message": "屯昌縣"}, "city__turpan": {"message": "吐魯番市"}, "city__ulanqab": {"message": "烏蘭察布盟"}, "city__urumqi": {"message": "烏魯木齊市"}, "city__wanning": {"message": "萬寧市"}, "city__weifang": {"message": "濰坊市"}, "city__weihai": {"message": "威海市"}, "city__weinan": {"message": "渭南市"}, "city__wenchang": {"message": "文昌市"}, "city__wenshan": {"message": "文山壯族苗族自治州"}, "city__wenzhou": {"message": "溫州市"}, "city__wuhai": {"message": "烏海市"}, "city__wuhan": {"message": "武漢市"}, "city__wuhu": {"message": "蕪湖市"}, "city__wujiaqu": {"message": "五家渠市"}, "city__wuwei": {"message": "武威市"}, "city__wuxi": {"message": "無錫市"}, "city__wuzhishan": {"message": "五指山市"}, "city__wuzhong": {"message": "吳忠市"}, "city__wuzhou": {"message": "梧州市"}, "city__xiamen": {"message": "廈門市"}, "city__xian": {"message": "西安市"}, "city__xiangfan": {"message": "襄樊市"}, "city__xiangtan": {"message": "湘潭市"}, "city__xiangxi": {"message": "湘西土家族苗族自治州"}, "city__xianning": {"message": "鹹寧市"}, "city__xiantao": {"message": "仙桃市"}, "city__xianyang": {"message": "鹹陽市"}, "city__xiaogan": {"message": "孝感市"}, "city__xilingol": {"message": "錫林郭勒盟"}, "city__xinganleague": {"message": "興安盟"}, "city__xingtai": {"message": "邢台市"}, "city__xining": {"message": "西寧市"}, "city__xinxiang": {"message": "新鄉市"}, "city__xinyang": {"message": "信陽市"}, "city__xinyu": {"message": "新餘市"}, "city__xinzhou": {"message": "忻州市"}, "city__xishuangbanna": {"message": "西雙版納傣族自治州"}, "city__xuancheng": {"message": "宣城市"}, "city__xuchang": {"message": "許昌市"}, "city__xuzhou": {"message": "徐州市"}, "city__yaan": {"message": "雅安市"}, "city__yanan": {"message": "延安市"}, "city__yanbian": {"message": "延邊朝鮮族自治州"}, "city__yancheng": {"message": "鹽城市"}, "city__yangjiang": {"message": "陽江市"}, "city__yangquan": {"message": "陽泉市"}, "city__yangzhou": {"message": "揚州市"}, "city__yantai": {"message": "煙台市"}, "city__yibin": {"message": "宜賓市"}, "city__yichang": {"message": "宜昌市"}, "city__yichun": {"message": "宜春市"}, "city__yilan": {"message": "宜蘭縣"}, "city__yili": {"message": "伊犁哈薩克自治州"}, "city__yinchuan": {"message": "銀川市"}, "city__yingkou": {"message": "營口市"}, "city__yingtan": {"message": "鷹潭市"}, "city__yiwu": {"message": "義烏市"}, "city__yiyang": {"message": "益陽市"}, "city__yongzhou": {"message": "永州市"}, "city__yueyang": {"message": "岳陽市"}, "city__yulin": {"message": "榆林市"}, "city__yuncheng": {"message": "運城市"}, "city__yunfu": {"message": "雲浮市"}, "city__yunlin": {"message": "雲林縣"}, "city__yushu": {"message": "玉樹藏族自治州"}, "city__yuxi": {"message": "玉溪市"}, "city__zaozhuang": {"message": "棗莊市"}, "city__zhangjiajie": {"message": "張家界市"}, "city__zhangjiakou": {"message": "張家口市"}, "city__zhangye": {"message": "張掖市"}, "city__zhangzhou": {"message": "漳州市"}, "city__zhanjiang": {"message": "湛江市"}, "city__zhaoqing": {"message": "肇慶市"}, "city__zhaotong": {"message": "昭通市"}, "city__zhengzhou": {"message": "鄭州市"}, "city__zhenjiang": {"message": "鎮江市"}, "city__zhongshan": {"message": "中山市"}, "city__zhongwei": {"message": "中衛市"}, "city__zhoukou": {"message": "週口市"}, "city__zhoushan": {"message": "舟山市"}, "city__zhuhai": {"message": "珠海市"}, "city__zhumadian": {"message": "駐馬店市"}, "city__zhuzhou": {"message": "株洲市"}, "city__zibo": {"message": "淄博市"}, "city__zigong": {"message": "自貢市"}, "city__ziyang": {"message": "資陽市"}, "city__zunyi": {"message": "遵義市"}, "click_copy_product_info": {"message": "再點選複製產品訊息"}, "commmon_txt_expired": {"message": "已過期"}, "common__date_range_12m": {"message": "一年"}, "common__date_range_1m": {"message": "一個月"}, "common__date_range_1w": {"message": "一個星期"}, "common__date_range_2w": {"message": "兩個星期"}, "common__date_range_3m": {"message": "三個月"}, "common__date_range_3w": {"message": "三個星期"}, "common__date_range_6m": {"message": "半年"}, "common_btn_cancel": {"message": "取消"}, "common_btn_close": {"message": "關閉"}, "common_btn_save": {"message": "保存"}, "common_btn_setting": {"message": "設置"}, "common_email": {"message": "電郵"}, "common_error_msg_no_data": {"message": "無數據"}, "common_error_msg_no_result": {"message": "抱歉，未找到結果。"}, "common_favorites": {"message": "收藏夾"}, "common_feedback": {"message": "反饋"}, "common_help": {"message": "幫助"}, "common_loading": {"message": "載入中"}, "common_login": {"message": "登錄"}, "common_logout": {"message": "登出"}, "common_no": {"message": "否"}, "common_powered_by_aliprice": {"message": "由AliPrice.com提供技術支持"}, "common_setting": {"message": "設定"}, "common_sign_up": {"message": "註冊"}, "common_system_upgrading_title": {"message": "系統升級"}, "common_system_upgrading_txt": {"message": "請稍後再試"}, "common_txt__currency": {"message": "貨幣"}, "common_txt__video_tutorial": {"message": "視頻教程"}, "common_txt_ago_time": {"message": "$time$日前", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "全部"}, "common_txt_analysis": {"message": "賣家"}, "common_txt_basically_used": {"message": "幾乎沒用過"}, "common_txt_biaoti_link": {"message": "標題+連結"}, "common_txt_biaoti_link_dian_pu": {"message": "標題+連結+店鋪名"}, "common_txt_blacklist": {"message": "黑名單"}, "common_txt_cancel": {"message": "退出"}, "common_txt_category": {"message": "類別"}, "common_txt_chakan": {"message": "查看"}, "common_txt_colors": {"message": "顏色"}, "common_txt_confirm": {"message": "確定"}, "common_txt_copied": {"message": "已復制"}, "common_txt_copy": {"message": "複製"}, "common_txt_copy_link": {"message": "複製鏈接"}, "common_txt_copy_title": {"message": "複製標題"}, "common_txt_copy_title__link": {"message": "複製標題+鏈接"}, "common_txt_day": {"message": "天"}, "common_txt_day__short": {"message": "天"}, "common_txt_delete": {"message": "刪除"}, "common_txt_dian_pu_link": {"message": "複製店舖名+鏈接"}, "common_txt_download": {"message": "下載"}, "common_txt_downloaded": {"message": "下载"}, "common_txt_export_as_csv": {"message": "匯出Excel"}, "common_txt_export_as_txt": {"message": "導出文字Txt"}, "common_txt_fail": {"message": "失敗"}, "common_txt_format": {"message": "格式"}, "common_txt_get": {"message": "獲取"}, "common_txt_incert_selection": {"message": "反轉選擇"}, "common_txt_install": {"message": "安裝"}, "common_txt_load_failed": {"message": "加載失敗"}, "common_txt_month": {"message": "月"}, "common_txt_more": {"message": "更多"}, "common_txt_new_unused": {"message": "全新、未使用"}, "common_txt_next": {"message": "下一頁"}, "common_txt_no_limit": {"message": "不限"}, "common_txt_no_noticeable": {"message": "沒有明顯的刮痕或污垢"}, "common_txt_on_sale": {"message": "在售"}, "common_txt_opt_in_out": {"message": "開/關"}, "common_txt_order": {"message": "訂單"}, "common_txt_others": {"message": "其他"}, "common_txt_overall_poor_condition": {"message": "整體狀況不佳"}, "common_txt_patterns": {"message": "款式"}, "common_txt_platform": {"message": "平台"}, "common_txt_please_select": {"message": "請選擇"}, "common_txt_prev": {"message": "上一頁"}, "common_txt_price": {"message": "價格"}, "common_txt_privacy_policy": {"message": "隱私政策"}, "common_txt_product_condition": {"message": "產品狀況"}, "common_txt_rating": {"message": "評分"}, "common_txt_ratings": {"message": "評分人數"}, "common_txt_reload": {"message": "重新載入"}, "common_txt_reset": {"message": "重置"}, "common_txt_retail": {"message": "零售"}, "common_txt_review": {"message": "曬圖"}, "common_txt_sale": {"message": "在售"}, "common_txt_same": {"message": "同款"}, "common_txt_scratches_and_dirt": {"message": "有刮痕和污垢"}, "common_txt_search_title": {"message": "搜標題"}, "common_txt_select_all": {"message": "全選"}, "common_txt_selected": {"message": "已選中"}, "common_txt_share": {"message": "分享"}, "common_txt_sold": {"message": "已售"}, "common_txt_sold_out": {"message": "售罄"}, "common_txt_some_scratches": {"message": "有一些刮痕和污垢"}, "common_txt_sort_by": {"message": "排序方式"}, "common_txt_state": {"message": "狀態"}, "common_txt_success": {"message": "成功"}, "common_txt_sys_err": {"message": "系統錯誤"}, "common_txt_today": {"message": "今天"}, "common_txt_total": {"message": "全部"}, "common_txt_unselect_all": {"message": "反選"}, "common_txt_upload_image": {"message": "上傳圖片"}, "common_txt_visit": {"message": "訪問"}, "common_txt_whitelist": {"message": "白名單"}, "common_txt_wholesale": {"message": "批發"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "年"}, "common_yes": {"message": "是"}, "compare_tool_btn_clear_all": {"message": "全部清除"}, "compare_tool_btn_compare": {"message": "對比"}, "compare_tool_btn_contact": {"message": "聯繫"}, "configure_notifiactions": {"message": "設置通知"}, "contact_us": {"message": "聯繫我們"}, "context_menu_screenshot_search": {"message": "截圖搜索同款"}, "context_menus_aliprice_search_by_image": {"message": "在AliPrice上用图片搜同款"}, "context_menus_goote_trans": {"message": "翻譯網頁/顯示原文"}, "context_menus_search_by_image": {"message": "在$storeName$以圖搜同款", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "截圖去$storeName$搜同款", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "截圖翻譯"}, "converter_modal_amount_placeholder": {"message": "在此輸入金額"}, "converter_modal_btn_convert": {"message": "換算"}, "converter_modal_exchange_rate_source": {"message": "數據來源於$boc$外匯牌價 更新時間: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "匯率換算"}, "converter_modal_search_placeholder": {"message": "搜索貨幣"}, "copy_all_contact_us_notice": {"message": "暫不支援此網站，請聯絡我們"}, "copy_product_info": {"message": "複製產品資訊"}, "copy_suggest_search_kw": {"message": "複製下拉詞"}, "country__han_gou": {"message": "韓國"}, "country__ri_ben": {"message": "日本"}, "country__yue_nan": {"message": "越南"}, "currency_convert__custom": {"message": "自定義匯率"}, "currency_convert__sync_server": {"message": "同步服務器"}, "dang_ri_fa_huo": {"message": "當日出貨"}, "dao_chu_quan_dian_shang_pin": {"message": "匯出全店商品"}, "dao_chu_wei_CSV": {"message": "導出為CSV"}, "dao_chu_zi_duan": {"message": "導出字段"}, "delivery_address": {"message": "出貨地址"}, "delivery_company": {"message": "配送公司"}, "di_zhi": {"message": "地址"}, "dian_ji_cha_xun": {"message": "點擊查詢"}, "dian_pu_ID": {"message": "店家ID"}, "dian_pu_di_zhi": {"message": "店鋪地址"}, "dian_pu_lian_jie": {"message": "店鋪連結"}, "dian_pu_ming": {"message": "店舖名"}, "dian_pu_ming_cheng": {"message": "店鋪名稱"}, "dian_pu_shang_pin_zong_hsu": {"message": "店面商品總數$num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "店鋪資訊"}, "ding_zai_zuo_ce": {"message": "釘在左側"}, "disable_old_version_tips_disable_btn_title": {"message": "禁用舊版本"}, "download_image__SKU_variant_images": {"message": "SKU 属性图"}, "download_image__assume": {"message": "例如有product1.jpg、product2.gif 2張圖片\n\nimg_{$no$} 會重新命名為 img_01.jpg、img_02.gif;\n\n{$group$}_{$no$} 會重新命名為 主圖_01.jpg、主圖_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "大量下載"}, "download_image__combined_image": {"message": "詳情拼接長圖"}, "download_image__continue_downloading": {"message": "继续下载"}, "download_image__description_images": {"message": "描述图"}, "download_image__download_combined_image": {"message": "下載詳情拼接長圖"}, "download_image__download_zip": {"message": "下載為壓縮包"}, "download_image__enlarge_check": {"message": "僅支援 JPEG、JPG、GIF 和 PNG 格式的圖片，單張圖片最大尺寸: 1600 * 1600"}, "download_image__enlarge_image": {"message": "高清放大"}, "download_image__export": {"message": "導出鏈接"}, "download_image__height": {"message": "高度"}, "download_image__ignore_videos": {"message": "視頻不能導出，已為你忽略視頻"}, "download_image__img_translate": {"message": "圖片翻譯"}, "download_image__main_image": {"message": "主圖"}, "download_image__multi_folder": {"message": "多文件夹"}, "download_image__name": {"message": "下載圖片"}, "download_image__notice_content": {"message": "請不要在瀏覽器下載設定中選取「下載前詢問每個檔案的儲存位置\"!!!否則會彈出非常多的對話框。"}, "download_image__notice_ignore": {"message": "不再提示此信息"}, "download_image__order_number": {"message": "{$no$} 序號; {$group$} 分組名稱; {$date$} 時間戳", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "概述"}, "download_image__prompt_download_zip": {"message": "图片数量过多，建议下载为压缩包。"}, "download_image__rename": {"message": "重新命名"}, "download_image__rule": {"message": "命名規則"}, "download_image__single_folder": {"message": "单文件夹"}, "download_image__sku_image": {"message": "SKU 圖片"}, "download_image__video": {"message": "視頻"}, "download_image__width": {"message": "寬度"}, "download_reviews__download_images": {"message": "下載評論圖片"}, "download_reviews__dropdown_title": {"message": "下載評論圖片"}, "download_reviews__export_csv": {"message": "導出評論CSV"}, "download_reviews__no_images": {"message": "0張圖片可下載"}, "download_reviews__no_reviews": {"message": "沒有評論可以下載！"}, "download_reviews__notice": {"message": "提示："}, "download_reviews__notice__chrome_settings": {"message": "設置Chrome瀏覽器下載前詢問每個文件的保存位置，設為“關閉”"}, "download_reviews__notice__wait": {"message": "根據評論數量，等待時間可能較長"}, "download_reviews__pages_list__all": {"message": "全部"}, "download_reviews__pages_list__page": {"message": "前$page$頁", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "選擇範圍"}, "export_shopping_cart__csv_filed__details_url": {"message": "產品鏈接"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "回顯SKU鏈接"}, "export_shopping_cart__csv_filed__images": {"message": "圖片鏈接"}, "export_shopping_cart__csv_filed__quantity": {"message": "數量"}, "export_shopping_cart__csv_filed__sale_price": {"message": "價格"}, "export_shopping_cart__csv_filed__specs": {"message": "規格"}, "export_shopping_cart__csv_filed__store_name": {"message": "店鋪名稱"}, "export_shopping_cart__csv_filed__store_url": {"message": "商店鏈接"}, "export_shopping_cart__csv_filed__title": {"message": "產品名稱"}, "export_shopping_cart__export_btn": {"message": "導出"}, "export_shopping_cart__export_empty": {"message": "請選擇產品！"}, "fa_huo_shi_jian": {"message": "出貨時間"}, "favorite_add_email": {"message": "新增信箱"}, "favorite_add_favorites": {"message": "新增收藏"}, "favorite_added": {"message": "已收藏"}, "favorite_btn_add": {"message": "降價提醒"}, "favorite_btn_notify": {"message": "追蹤價格"}, "favorite_cate_name_all": {"message": "全部商品"}, "favorite_current_price": {"message": "當前價格"}, "favorite_due_date": {"message": "截止日期"}, "favorite_enable_notification": {"message": "請開啟郵件通知"}, "favorite_expired": {"message": "過期"}, "favorite_go_to_enable": {"message": "去開啟"}, "favorite_msg_add_success": {"message": "已加入收藏"}, "favorite_msg_del_success": {"message": "已從收藏中移除"}, "favorite_msg_failure": {"message": "失敗！刷新頁面並重試"}, "favorite_please_add_email": {"message": "請新增郵箱"}, "favorite_price_drop": {"message": "降價"}, "favorite_price_rise": {"message": "漲價"}, "favorite_price_untracked": {"message": "價格未追蹤"}, "favorite_saved_price": {"message": "收藏價格"}, "favorite_stop_tracking": {"message": "取消追蹤"}, "favorite_sub_email_address": {"message": "訂閱郵箱"}, "favorite_tracking_period": {"message": "追蹤時長"}, "favorite_tracking_prices": {"message": "追蹤價格"}, "favorite_verify_email": {"message": "驗證信箱"}, "favorites_list_remove_prompt_msg": {"message": "確定要刪除它嗎？"}, "favorites_update_button": {"message": "立即更新價格"}, "fen_lei": {"message": "分類"}, "fen_xia_yan_xuan": {"message": "分銷嚴選"}, "find_similar": {"message": "找同款"}, "first_ali_price_date": {"message": "首次被AliPrice爬蟲抓取到的日期"}, "fooview_coupons_modal_no_data": {"message": "暫無優惠券"}, "fooview_coupons_modal_title": {"message": "優惠"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "價格 < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "或價格 > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "截止日"}, "fooview_favorites_error_msg_no_favorites": {"message": "點亮小心心將產品加入我的最愛，以收到降價提醒"}, "fooview_favorites_filter_latest": {"message": "最新的"}, "fooview_favorites_filter_price_drop": {"message": "降價"}, "fooview_favorites_filter_price_up": {"message": "漲價"}, "fooview_favorites_modal_title": {"message": "我的最愛"}, "fooview_favorites_modal_title_title": {"message": "去AliPrice我的最愛"}, "fooview_favorites_track_price": {"message": "以跟蹤價格"}, "fooview_price_history_app_price": {"message": "APP端價格:"}, "fooview_price_history_title": {"message": "價格歷史"}, "fooview_product_list_feedback": {"message": "評分"}, "fooview_product_list_orders": {"message": "訂單"}, "fooview_product_list_price": {"message": "價格"}, "fooview_reviews_error_msg_no_review": {"message": "我們未找到對此產品的評論。"}, "fooview_reviews_filter_buyer_reviews": {"message": "買家秀"}, "fooview_reviews_modal_title": {"message": "評論"}, "fooview_same_product_choose_category": {"message": "選擇類目"}, "fooview_same_product_filter_feedback": {"message": "評分"}, "fooview_same_product_filter_orders": {"message": "訂單"}, "fooview_same_product_filter_price": {"message": "價格"}, "fooview_same_product_filter_rating": {"message": "評分"}, "fooview_same_product_modal_title": {"message": "搜索同款產品"}, "fooview_same_product_search_by_image": {"message": "以圖搜同款"}, "fooview_seller_analysis_modal_title": {"message": "賣家分析"}, "for_12_months": {"message": "全年"}, "for_12_months_list_pro": {"message": "12個月"}, "for_12_months_nei": {"message": "12個月內"}, "for_1_months": {"message": "1個月"}, "for_1_months_nei": {"message": "1個月內"}, "for_3_months": {"message": "3個月"}, "for_3_months_nei": {"message": "3個月內"}, "for_6_months": {"message": "6個月"}, "for_6_months_nei": {"message": "6個月內"}, "for_9_months": {"message": "9個月"}, "for_9_months_nei": {"message": "9個月內"}, "fu_gou_lv": {"message": "復購率"}, "gao_liang_bu_tong_dian": {"message": "高亮不同點"}, "gao_liang_guang_gao_chan_pin": {"message": "高亮廣告產品"}, "geng_duo_xin_xi": {"message": "更多資訊"}, "geng_xin_shi_jian": {"message": "更新時間"}, "get_store_products_fail_tip": {"message": "點擊確定，前往通過驗證以確保正常訪問"}, "gong_x_kuan_shang_pin": {"message": "共$amount$款商品", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "供應商"}, "gong_ying_shang_ID": {"message": "供應商ID"}, "gong_ying_shang_deng_ji": {"message": "供應商等級"}, "gong_ying_shang_nian_zhan": {"message": "供應商年長"}, "gong_ying_shang_xin_xi": {"message": "供應商信息"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "供應商主頁鏈接"}, "green_rocket": {"message": "绿色火箭"}, "grey_rocket": {"message": "灰色火箭"}, "gu_ji_shou_jia": {"message": "估計售價"}, "guan_jian_zi": {"message": "關鍵字"}, "guang_gao_chan_pin": {"message": "廣告產品"}, "guang_gao_zhan_bi": {"message": "廣告佔比"}, "guo_ji_wu_liu_yun_fei": {"message": "國際物流運費"}, "guo_lv_tiao_jian": {"message": "過濾條件"}, "hao_ping_lv": {"message": "好評率"}, "highest_price": {"message": "最高價"}, "historical_trend": {"message": "歷史趨勢"}, "how_to_screenshot": {"message": "按住滑鼠左鍵選擇區域，點選滑鼠右鍵或Esc鍵退出截圖"}, "howt_it_works": {"message": "怎麼用"}, "hui_fu_lv": {"message": "回覆率"}, "hui_tou_lv": {"message": "回頭率"}, "inquire_freightFee": {"message": "運費查詢"}, "inquire_freightFee_Yuan": {"message": "運費/元起"}, "inquire_freightFee_province": {"message": "省份"}, "inquire_freightFee_the": {"message": "運費為$num$ ,表示該地區包郵", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "是否為廣告"}, "isf": {"message": "我要買..."}, "jia_ge": {"message": "價格"}, "jia_ge_dan_wei": {"message": "商品的價格單位"}, "jia_ge_qu_shi": {"message": "趨勢"}, "jia_zai_n_ge_shang_pin": {"message": "載入 $num$ 個商品", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "近30天銷售佔比"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "近30天銷售額佔比"}, "jin_30d_xiao_liang": {"message": "近30天銷量"}, "jin_30d_xiao_liang__desc": {"message": "最近30天的銷售"}, "jin_30d_xiao_shou_e": {"message": "30天銷售額"}, "jin_30d_xiao_shou_e__desc": {"message": "最近30天的銷售額"}, "jin_90_tian_mai_jia_shu": {"message": "近90天買家數"}, "jin_90_tian_xiao_shou_liang": {"message": "近90天銷售量"}, "jing_xuan_huo_yuan": {"message": "精選貨源"}, "jing_ying_mo_shi": {"message": "經營模式"}, "jing_ying_mo_shi__gong_chang": {"message": "生產廠商"}, "jiu_fen_jie_jue": {"message": "糾紛解決"}, "jiu_fen_jie_jue__desc": {"message": "商家店舖維權糾紛的計入情況"}, "jiu_fen_lv": {"message": "糾紛率"}, "jiu_fen_lv__desc": {"message": "近30天投訴結束且判定為賣家責任或雙方責任的訂單比例"}, "kai_dian_ri_qi": {"message": "開店日期"}, "keywords": {"message": "關鍵字"}, "kua_jin_Select_pan_huo": {"message": "跨境Select貨盤"}, "last15_days": {"message": "近15天"}, "last180_days": {"message": "近180天"}, "last30_days": {"message": "近30天"}, "last360_days": {"message": "近360天"}, "last45_days": {"message": "近45天"}, "last60_days": {"message": "近60天"}, "last7_days": {"message": "近7天"}, "last90_days": {"message": "近90天"}, "last_30d_sales": {"message": "最近30天成交量"}, "lei_ji": {"message": "累計"}, "lei_ji_xiao_liang": {"message": "累計銷量"}, "lei_ji_xiao_liang__desc": {"message": "產品上架後的所有銷售量"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "最近30天的累計銷售量, 從高到低排序"}, "lian_xi_fang_shi": {"message": "聯繫方式"}, "list_time": {"message": "上架日期"}, "load_more": {"message": "加載更多"}, "login_to_aliprice": {"message": "登錄AliPrice"}, "long_link": {"message": "長連結"}, "lowest_price": {"message": "最低價"}, "mai_jia_shu": {"message": "賣家數"}, "mao_li_lv": {"message": "毛利率"}, "mobile_view__dkxbqy": {"message": "開啟新分頁"}, "mobile_view__sjdxq": {"message": "手機端詳情"}, "mobile_view__sjdxqy": {"message": "手機端詳情頁"}, "mobile_view__smck": {"message": "掃碼查看"}, "mobile_view__smckms": {"message": "請使用相機或APP掃描查看"}, "modified_failed": {"message": "修改失敗"}, "modified_successfully": {"message": "修改成功"}, "nav_btn_favorites": {"message": "我的收藏"}, "nav_btn_package": {"message": "包裹"}, "nav_btn_product_info": {"message": "關於產品"}, "nav_btn_viewed": {"message": "瀏覽歷史"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "無"}, "normal_link": {"message": "普通連結"}, "notice": {"message": "提示"}, "number_reviews": {"message": "評論數"}, "only_show_num": {"message": "商品總數: $allnum$, 已隱藏: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "去掉未勾選"}, "open": {"message": "打開"}, "open_links": {"message": "打開連結"}, "options_page_tab_check_links": {"message": "檢查鏈接"}, "options_page_tab_gernal": {"message": "通用"}, "options_page_tab_notifications": {"message": "通知"}, "options_page_tab_others": {"message": "其他"}, "options_page_tab_sbi": {"message": "以圖搜同款"}, "options_page_tab_shortcuts": {"message": "快捷方式"}, "options_page_tab_shortcuts_title": {"message": "快捷方式字體大小"}, "options_page_tab_similar_products": {"message": "同款產品"}, "orange_rocket": {"message": "橙色火箭"}, "order_list_open_links_tip": {"message": "即將開啟多個商品詳情頁"}, "order_list_sku_show_title": {"message": "支持回顯sku"}, "orders_last30_days": {"message": "最近30天的訂單數"}, "pTutorial_favorites_block1_desc1": {"message": "加入追踪價格的產品會顯示在這裡"}, "pTutorial_favorites_block1_title": {"message": "收藏夾"}, "pTutorial_popup_block1_desc1": {"message": "綠色標籤表示有降價產品，數字表示新降價產品數量"}, "pTutorial_popup_block1_title": {"message": "快捷入口和收藏夾"}, "pTutorial_price_history_block1_desc1": {"message": "單擊“追踪價格”，將產品添加到收藏夾。價格下降後，您會收到通知"}, "pTutorial_price_history_block1_title": {"message": "追踪價格"}, "pTutorial_reviews_block1_desc1": {"message": "來自Itao的買家點評和速賣通買家的實拍曬圖"}, "pTutorial_reviews_block1_title": {"message": "點評"}, "pTutorial_reviews_block2_desc1": {"message": "下單前看下其他買家的評論總是很有幫助的"}, "pTutorial_same_products_block1_desc1": {"message": "您可以對比同款以做出最佳選擇"}, "pTutorial_same_products_block1_desc2": {"message": "點擊“更多”以使用“以圖像同款”"}, "pTutorial_same_products_block1_title": {"message": "同款產品"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "將產品圖片拖到此處，然後選擇產品所屬類目"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "以圖搜同款"}, "pTutorial_seller_analysis_block1_desc1": {"message": "賣家的好評率，評分以及賣家的開店時間"}, "pTutorial_seller_analysis_block1_title": {"message": "賣家分析"}, "pTutorial_seller_analysis_block2_desc2": {"message": "賣家評分基於3個指數：產品描述相符度，發貨速度和客服能力"}, "pTutorial_seller_analysis_block3_desc3": {"message": "我們使用3種不同的顏色和圖標表示不同的賣家可信任等級"}, "page_count": {"message": "頁數"}, "pai_chu": {"message": "排除"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "排除中國香港不宜銷售商品"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "排除日本不宜銷售商品"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "排除韓國不宜銷售商品"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "排除哈薩克不宜銷售商品"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "排除中國澳門不宜銷售商品"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "排除東歐不宜銷售商品"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "排除沙烏地阿拉伯不宜銷售商品"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "排除中國台灣不宜販售商品"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "排除美國不宜銷售商品"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "排除越南不宜銷售商品"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "排除不宜銷售"}, "payable_price_formula": {"message": "價格+運費+優惠"}, "pdd_check_retail_btn_txt": {"message": "查看零售"}, "pdd_pifa_to_retail_btn_txt": {"message": "零售購買"}, "pdp_copy_fail": {"message": "複製失敗！"}, "pdp_copy_success": {"message": "複製成功！"}, "pdp_share_modal_subtitle": {"message": "分享截圖，他/她會看到你的選擇。"}, "pdp_share_modal_title": {"message": "分享你的選擇"}, "pdp_share_screenshot": {"message": "分享截圖"}, "pei_song": {"message": "配送"}, "pin_lei": {"message": "類別"}, "pin_zhi_ti_yan": {"message": "品質體驗"}, "pin_zhi_ti_yan__desc": {"message": "商家店舖的品質退款率"}, "pin_zhi_tui_kuan_lv": {"message": "品質退款率"}, "pin_zhi_tui_kuan_lv__desc": {"message": "近30天僅退款和退款退貨結束的訂單比例"}, "ping_fen": {"message": "評分"}, "ping_jun_fa_huo_su_du": {"message": "平均出貨速度"}, "pkgInfo_hide": {"message": "顯示物流：開/關"}, "pkgInfo_no_trace": {"message": "無物流資訊"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17網"}, "platform_name__51taoyang": {"message": "淘羊網"}, "platform_name__5ts": {"message": "童商網"}, "platform_name__91jf": {"message": "91家紡網"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "速賣通"}, "platform_name__amazon": {"message": "亞馬遜"}, "platform_name__bao66": {"message": "包牛牛"}, "platform_name__chinagoods": {"message": "義烏小商品城"}, "platform_name__dhgate": {"message": "敦煌網"}, "platform_name__e3hui": {"message": "衣衫匯"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "杭州女裝網"}, "platform_name__jd": {"message": "京東"}, "platform_name__juyi5": {"message": "聚衣網"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "拼多多"}, "platform_name__pinduoduo_pifa": {"message": "拼多多批發"}, "platform_name__shopee": {"message": "蝦皮"}, "platform_name__sjxzw": {"message": "四季星座網"}, "platform_name__sooxie": {"message": "愛搜鞋網"}, "platform_name__taobao": {"message": "淘寶"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "搜款网"}, "platform_name__walmart": {"message": "沃爾瑪"}, "platform_name__wsy": {"message": "網商園"}, "platform_name__xingfujie": {"message": "新款網"}, "platform_name__yiwugo": {"message": "義烏購"}, "platform_name__yunchepin": {"message": "雲車品"}, "platform_name__zhaojiafang": {"message": "找家紡"}, "popup_go_to_home": {"message": "主頁"}, "popup_go_to_platform": {"message": "去$name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "我要購買..."}, "popup_track_package_btn_track": {"message": "查詢"}, "popup_track_package_desc": {"message": "多合一包裹查快遞神器"}, "popup_track_package_search_placeholder": {"message": "快遞號"}, "popup_translate_search_placeholder": {"message": "在 $searchOn$ 上翻譯和搜索", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "價格歷史"}, "price_history_chart_tip_ae": {"message": "提示：訂單數是從上架至今的累計訂單數"}, "price_history_chart_tip_coupang": {"message": "提示： Coupang官方會刪除作弊訂單的訂單數"}, "price_history_inm_1688_l1": {"message": "請先安裝"}, "price_history_inm_1688_l2": {"message": "1688購物助手"}, "price_history_panel_lowest_price": {"message": "最低價:"}, "price_history_panel_tab_price_tracking": {"message": "價格歷史"}, "price_history_panel_tab_seller_analysis": {"message": "賣家分析"}, "price_history_pro_modal_title": {"message": "價格歷史和訂單歷史"}, "privacy_consent__btn_agree": {"message": "同意"}, "privacy_consent__btn_disable_all": {"message": "不接受"}, "privacy_consent__btn_enable_all": {"message": "全部啟用"}, "privacy_consent__btn_uninstall": {"message": "移除"}, "privacy_consent__desc_privacy": {"message": "請注意，如果沒有數據或Cookie，某些功能將關閉，因為這些功能需要數據或Cookie，但您仍可以使用其他功能。"}, "privacy_consent__desc_privacy_L1": {"message": "如果不允許收集必要數據或添加cookie，挿件將無法正常工作。"}, "privacy_consent__desc_privacy_L2": {"message": "請同意我們收集必要數據或添加cookie。否則，可以移除挿件。"}, "privacy_consent__item_cookies_desc": {"message": "Cookie，我們僅獲取您購物時Cookie中的貨幣數據用以顯示價格歷史。"}, "privacy_consent__item_cookies_title": {"message": "所需的<PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1.在瀏覽器中添加Cookie以匿名標識您的計算機或設備。"}, "privacy_consent__item_functional_desc_L2": {"message": "2.在插件中添加功能數據以啟用此功能。"}, "privacy_consent__item_functional_title": {"message": "功能和分析Cookie"}, "privacy_consent__more_desc": {"message": "請注意，我們不會與其他公司共享您的個人數據，也沒有廣告公司會通過我們的服務收集數據。"}, "privacy_consent__options__btn__desc": {"message": "要使用所有功能，您需要開啟它。"}, "privacy_consent__options__btn__label": {"message": "開啟"}, "privacy_consent__options__desc_L1": {"message": "我們將收集以下可識別您個人身份的資料："}, "privacy_consent__options__desc_L2": {"message": "- Cookie，我們只會在您線上購物時顯示Cookie中的貨幣資料，以顯示產品價格歷史。"}, "privacy_consent__options__desc_L3": {"message": "- 並在流覽器中添加Cookie以匿名識別您的電腦或設備。"}, "privacy_consent__options__desc_L4": {"message": "- 其他匿名資料使本外掛程式更加方便使用。"}, "privacy_consent__options__desc_L5": {"message": "請注意，我們不會與其他公司共用您的個人資料，也沒有廣告公司通過我們的服務收集資料。"}, "privacy_consent__privacy_preferences": {"message": "隱私偏好"}, "privacy_consent__read_more": {"message": "閱讀更多>>"}, "privacy_consent__title_privacy": {"message": "隱私"}, "product_info": {"message": "產品資訊"}, "product_recommend__name": {"message": "同款產品"}, "product_research": {"message": "產品查詢"}, "product_sub__email_desc": {"message": "價格警報郵箱"}, "product_sub__email_edit": {"message": "編輯"}, "product_sub__email_not_verified": {"message": "請驗證郵箱"}, "product_sub__email_required": {"message": "請提供郵箱"}, "product_sub__form_countdown": {"message": "$seconds$ 秒後自動關閉", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "添加提醒失敗！"}, "product_sub__form_input_price": {"message": "輸入價格"}, "product_sub__form_item_country": {"message": "國家"}, "product_sub__form_item_current_price": {"message": "當前價"}, "product_sub__form_item_duration": {"message": "跟踪"}, "product_sub__form_item_higher_price": {"message": "或價格 >"}, "product_sub__form_item_invalid_higher_price": {"message": "價格必須大於 $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "價格必須低於 $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "當價格 <"}, "product_sub__form_submit": {"message": "提交"}, "product_sub__form_success": {"message": "添加提醒成功！"}, "product_sub__high_price_notify": {"message": "漲價時通知我"}, "product_sub__low_price_notify": {"message": "降價時通知我"}, "product_sub__modal_title": {"message": "訂閱價格變動提醒"}, "province__an_hui": {"message": "安徽省"}, "province__ao_men": {"message": "澳門"}, "province__bei_jing": {"message": "北京市"}, "province__chong_qing": {"message": "重慶市"}, "province__fu_jian": {"message": "福建省"}, "province__gan_su": {"message": "甘肅省"}, "province__guang_dong": {"message": "廣東省"}, "province__guang_xi": {"message": "廣西壯族自治區"}, "province__gui_zhou": {"message": "貴州省"}, "province__hai_nan": {"message": "海南省"}, "province__he_bei": {"message": "河北省"}, "province__he_nan": {"message": "河南省"}, "province__hei_long_jiang": {"message": "黑龍江省"}, "province__hu_bei": {"message": "湖北省"}, "province__hu_nan": {"message": "湖南省"}, "province__ji_lin": {"message": "吉林省"}, "province__jiang_su": {"message": "江蘇省"}, "province__jiang_xi": {"message": "江西省"}, "province__liao_ning": {"message": "遼寧省"}, "province__nei_meng_gu": {"message": "內蒙古自治區"}, "province__ning_xia": {"message": "寧夏回族自治區"}, "province__qing_hai": {"message": "青海省"}, "province__shan_dong": {"message": "山東省"}, "province__shan_xi": {"message": "陝西省"}, "province__shang_hai": {"message": "上海市"}, "province__si_chuan": {"message": "四川省"}, "province__tai_wan": {"message": "台灣"}, "province__tian_jin": {"message": "天津市"}, "province__xi_zhang": {"message": "西藏自治區"}, "province__xiang_gang": {"message": "香港"}, "province__xin_jiang": {"message": "新疆維吾爾自治區"}, "province__yun_nan": {"message": "雲南省"}, "province__zhe_jiang": {"message": "浙江省"}, "purple_rocket": {"message": "紫色火箭"}, "qi_ding_liang": {"message": "起訂量"}, "qi_ding_liang_qi_ding_jia": {"message": "起訂量&起訂價"}, "qi_ye_mian_ji": {"message": "企業面積"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "請至少選擇一個產品"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "請至少選擇一個字段"}, "qu_deng_lu": {"message": "去登入"}, "quan_guo_yan_xuan": {"message": "全球嚴選"}, "recommendation_popup_banner_btn_install": {"message": "安裝"}, "recommendation_popup_banner_desc": {"message": "顯示3/6個月內的價格歷史和降價通知"}, "region__all": {"message": "所有地區"}, "region__hai_wai": {"message": "海外"}, "region__hua_bei_qu": {"message": "華北區"}, "region__hua_dong_qu": {"message": "華東區"}, "region__hua_nan_qu": {"message": "華南區"}, "region__hua_zhong_qu": {"message": "華中區"}, "region__jiang_zhe_lu": {"message": "江浙滬"}, "remove_web_limits": {"message": "解除右鍵限制"}, "ren_zheng_gong_chang": {"message": "認證工廠"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "認證供應商年長"}, "required_to_aliprice_login": {"message": "需要登錄AliPrice"}, "revenue_last30_days": {"message": "30天銷售額"}, "review_counts": {"message": "收藏人數"}, "rocket": {"message": "小火箭"}, "ru_zhu_nian_xian": {"message": "入駐年限"}, "sales_amount_last30_days": {"message": "最近30天的銷售額"}, "sales_last30_days": {"message": "近30天銷量"}, "sbi_alibaba_cate__accessories": {"message": "配飾"}, "sbi_alibaba_cate__aqfk": {"message": "安全防護"}, "sbi_alibaba_cate__bags_cases": {"message": "箱包"}, "sbi_alibaba_cate__beauty": {"message": "美妝"}, "sbi_alibaba_cate__beverage": {"message": "瓶飲"}, "sbi_alibaba_cate__bgwh": {"message": "辦公文化"}, "sbi_alibaba_cate__bz": {"message": "包裝"}, "sbi_alibaba_cate__ccyj": {"message": "餐廚飲具"}, "sbi_alibaba_cate__clothes": {"message": "服飾"}, "sbi_alibaba_cate__cmgd": {"message": "傳媒廣電"}, "sbi_alibaba_cate__coat_jacket": {"message": "上衣"}, "sbi_alibaba_cate__consumer_electronics": {"message": "數碼"}, "sbi_alibaba_cate__cryp": {"message": "成人用品"}, "sbi_alibaba_cate__csyp": {"message": "床上用品"}, "sbi_alibaba_cate__cwyy": {"message": "寵物園藝"}, "sbi_alibaba_cate__cysx": {"message": "餐飲生鮮"}, "sbi_alibaba_cate__dgdq": {"message": "電工電氣"}, "sbi_alibaba_cate__dl": {"message": "代理"}, "sbi_alibaba_cate__dress_suits": {"message": "裙裝"}, "sbi_alibaba_cate__dszm": {"message": "燈飾照明"}, "sbi_alibaba_cate__dzqj": {"message": "電子器件"}, "sbi_alibaba_cate__essb": {"message": "二手設備"}, "sbi_alibaba_cate__food": {"message": "零食"}, "sbi_alibaba_cate__fspj": {"message": "服飾配件"}, "sbi_alibaba_cate__furniture": {"message": "家具"}, "sbi_alibaba_cate__fzpg": {"message": "紡織皮革"}, "sbi_alibaba_cate__ghjq": {"message": "個護家清"}, "sbi_alibaba_cate__gt": {"message": "鋼鐵"}, "sbi_alibaba_cate__gyp": {"message": "工藝品"}, "sbi_alibaba_cate__hb": {"message": "環保"}, "sbi_alibaba_cate__hfcz": {"message": "護膚彩妝"}, "sbi_alibaba_cate__hg": {"message": "化工"}, "sbi_alibaba_cate__jg": {"message": "加工"}, "sbi_alibaba_cate__jianccai": {"message": "建材"}, "sbi_alibaba_cate__jichuang": {"message": "機床"}, "sbi_alibaba_cate__jjry": {"message": "居家日用"}, "sbi_alibaba_cate__jtys": {"message": "交通運輸"}, "sbi_alibaba_cate__jxsb": {"message": "機械設備"}, "sbi_alibaba_cate__jxwj": {"message": "機械五金"}, "sbi_alibaba_cate__jydq": {"message": "家用電器"}, "sbi_alibaba_cate__jzjc": {"message": "家裝建材"}, "sbi_alibaba_cate__jzjf": {"message": "家紡家飾"}, "sbi_alibaba_cate__mj": {"message": "毛巾"}, "sbi_alibaba_cate__myyp": {"message": "母嬰用品"}, "sbi_alibaba_cate__nanz": {"message": "男裝"}, "sbi_alibaba_cate__nvz": {"message": "女裝"}, "sbi_alibaba_cate__ny": {"message": "能源"}, "sbi_alibaba_cate__others": {"message": "其他"}, "sbi_alibaba_cate__qcyp": {"message": "汽車用品"}, "sbi_alibaba_cate__qmpj": {"message": "汽摩配件"}, "sbi_alibaba_cate__shoes": {"message": "鞋"}, "sbi_alibaba_cate__smdn": {"message": "數碼電腦"}, "sbi_alibaba_cate__snqj": {"message": "收納清潔"}, "sbi_alibaba_cate__spjs": {"message": "食品酒水"}, "sbi_alibaba_cate__swfw": {"message": "商務服務"}, "sbi_alibaba_cate__toys_hobbies": {"message": "玩具"}, "sbi_alibaba_cate__trousers_skirt": {"message": "下裝"}, "sbi_alibaba_cate__txcp": {"message": "通信產品"}, "sbi_alibaba_cate__tz": {"message": "童裝"}, "sbi_alibaba_cate__underwear": {"message": "內衣"}, "sbi_alibaba_cate__wjgj": {"message": "五金工具"}, "sbi_alibaba_cate__xgpi": {"message": "箱包皮具"}, "sbi_alibaba_cate__xmhz": {"message": "項目合作"}, "sbi_alibaba_cate__xs": {"message": "橡塑"}, "sbi_alibaba_cate__ydfs": {"message": "運動服飾"}, "sbi_alibaba_cate__ydhw": {"message": "運動戶外"}, "sbi_alibaba_cate__yjkc": {"message": "冶金礦產"}, "sbi_alibaba_cate__yqyb": {"message": "儀器儀表"}, "sbi_alibaba_cate__ys": {"message": "印刷"}, "sbi_alibaba_cate__yyby": {"message": "醫藥保養"}, "sbi_alibaba_cn_kj_90mjs": {"message": "過去 90 天的買家數量"}, "sbi_alibaba_cn_kj_90xsl": {"message": "過去 90 天的銷售額"}, "sbi_alibaba_cn_kj_gjsj": {"message": "預估價格"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "國際運費"}, "sbi_alibaba_cn_kj_gjyf": {"message": "運輸費用"}, "sbi_alibaba_cn_kj_gssj": {"message": "預估價格"}, "sbi_alibaba_cn_kj_lr": {"message": "利潤"}, "sbi_alibaba_cn_kj_lrgs": {"message": "利潤 = 預估價格 x 利潤率"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "平均交貨速度"}, "sbi_alibaba_cn_kj_qtfy": {"message": "其他費用"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "其他成本 = 預估價格 x 其他成本比率"}, "sbi_alibaba_cn_kj_spjg": {"message": "價格"}, "sbi_alibaba_cn_kj_spzl": {"message": "重量"}, "sbi_alibaba_cn_kj_szd": {"message": "地點"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "件"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "件"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "公克"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "買家"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "件"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "套"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "天"}, "sbi_alibaba_cn_kj_zwbj": {"message": "沒有價格"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "價格"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "可售"}, "sbi_aliprice_alibaba_cn__moren": {"message": "默認"}, "sbi_aliprice_alibaba_cn__queding": {"message": "確定"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "銷量"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "家具"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "零食"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "美妝"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "內衣"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "配飾"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "瓶飲"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "其他"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "裙裝"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "上衣"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "數碼"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "玩具"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "箱包"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "下裝"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "鞋子"}, "sbi_aliprice_cate__apparel": {"message": "服飾"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "汽車和摩托車"}, "sbi_aliprice_cate__beauty_health": {"message": "美容與健康"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "手機與通訊"}, "sbi_aliprice_cate__computer_office": {"message": "電腦與辦公"}, "sbi_aliprice_cate__consumer_electronics": {"message": "消費類電子產品"}, "sbi_aliprice_cate__education_office_supplies": {"message": "教育和辦公用品"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "電子元件及用品"}, "sbi_aliprice_cate__furniture": {"message": "家具類"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "假髮"}, "sbi_aliprice_cate__home_garden": {"message": "家居與園藝"}, "sbi_aliprice_cate__home_improvement": {"message": "家居裝修"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "珠寶及配飾"}, "sbi_aliprice_cate__luggage_bags": {"message": "行李箱包"}, "sbi_aliprice_cate__mother_kids": {"message": "母嬰用品"}, "sbi_aliprice_cate__novelty_special_use": {"message": "新奇產品和特殊用品"}, "sbi_aliprice_cate__security_protection": {"message": "安全防護"}, "sbi_aliprice_cate__shoes": {"message": "鞋類"}, "sbi_aliprice_cate__sports_entertainment": {"message": "體育與娛樂"}, "sbi_aliprice_cate__toys_hobbies": {"message": "玩具與愛好"}, "sbi_aliprice_cate__watches": {"message": "手錶"}, "sbi_aliprice_cate__weddings_events": {"message": "婚禮與活動"}, "sbi_btn_capture_txt": {"message": "截圖"}, "sbi_btn_source_now_txt": {"message": "立刻搜索"}, "sbi_button__chat_with_me": {"message": "與我聊天"}, "sbi_button__contact_supplier": {"message": "聯繫"}, "sbi_button__hide_on_this_site": {"message": "不要在這個網站上顯示"}, "sbi_button__open_settings": {"message": "設置圖片搜索"}, "sbi_capture_shortcut_tip": {"message": "或按鍵盤上的“回車”鍵"}, "sbi_capturing_tip": {"message": "正在截取"}, "sbi_composed_rating_45": {"message": "4.5星-5.0星"}, "sbi_crop_and_search": {"message": "搜索"}, "sbi_crop_start": {"message": "使用截圖"}, "sbi_err_captcha_action": {"message": "驗證"}, "sbi_err_captcha_for_alibaba_cn": {"message": "1688要求您驗證,請手動上傳圖片驗證。 (查看$video_tutorial$或嘗試清除Cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "異常流量，請驗證"}, "sbi_err_captcha_for_taobao": {"message": "淘寶要求您驗證，請手動上傳圖片並蒐索進行驗證。這個錯誤源於“淘寶圖片搜索”新的驗證政策，我們建議您在淘寶$feedback$投訴過於頻繁的驗證。", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "反饋"}, "sbi_err_captcha_msg": {"message": "$platform$需要您前往網站操作一次圖片搜尋或完成安全驗證，以解除搜尋限制", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "檢查是否是最新版"}, "sbi_err_cookie_btn_clear": {"message": "清除Cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "試下清除1688上的cookie？ （需要重新登錄）"}, "sbi_err_desperate_feature_pdd": {"message": "此圖搜功能已移至拼多多以圖搜同款插件"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "如何提高圖搜成功率？"}, "sbi_err_img_undersize": {"message": "圖片 > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "登錄$loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "登錄"}, "sbi_err_low_version": {"message": "安裝最新版本($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "下載"}, "sbi_err_need_help": {"message": "需要幫助"}, "sbi_err_network": {"message": "網路不通，請確保您能造訪網站"}, "sbi_err_not_low_version": {"message": "已安裝了最新版（$latestVersion$）", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "再試一次"}, "sbi_err_try_again_action": {"message": "再試一次"}, "sbi_err_visit_and_try": {"message": "再試一次，或造訪$website$重試", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "訪問下$siteName$首頁", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "加載失敗，請刷新頁面後重試。"}, "sbi_kuajing_filter_area": {"message": "地區"}, "sbi_kuajing_filter_au": {"message": "澳大利亞"}, "sbi_kuajing_filter_btn_confirm": {"message": "確定"}, "sbi_kuajing_filter_de": {"message": "德國"}, "sbi_kuajing_filter_destination_country": {"message": "目的地國家"}, "sbi_kuajing_filter_es": {"message": "西班牙"}, "sbi_kuajing_filter_estimate": {"message": "估算"}, "sbi_kuajing_filter_estimate_price": {"message": "售價估算"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "估算售價公式 = （商品價格 + 國際物流運費）/（1 - 利潤率 - 其他費用比例）"}, "sbi_kuajing_filter_fr": {"message": "法國"}, "sbi_kuajing_filter_kw_placeholder": {"message": "輸入關鍵詞對標題進行匹配"}, "sbi_kuajing_filter_logistics": {"message": "物流模板"}, "sbi_kuajing_filter_logistics_china_post": {"message": "中國郵政掛號小包"}, "sbi_kuajing_filter_logistics_discount": {"message": "物流折扣"}, "sbi_kuajing_filter_logistics_epacket": {"message": "國際E郵寶"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "國際物流運費 = （重量 x 目的地國家運費單價 + 掛號費）x（1 - 折扣）"}, "sbi_kuajing_filter_others_fee": {"message": "其他費用"}, "sbi_kuajing_filter_profit_percent": {"message": "利潤率"}, "sbi_kuajing_filter_prop": {"message": "屬性"}, "sbi_kuajing_filter_ru": {"message": "俄羅斯"}, "sbi_kuajing_filter_total": {"message": "匹配$count$個同款貨源", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "英國"}, "sbi_kuajing_filter_usa": {"message": "美國"}, "sbi_login_punish_title__pdd_pifa": {"message": "拼多多商家版"}, "sbi_msg_no_result": {"message": "未找到結果,請先登錄$loginSite$或換張圖試試", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "暫時不支持Safari, 請使用$supportPage$。", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome瀏覽器及其擴展"}, "sbi_msg_no_result_reinstall_l1": {"message": "未找到結果,請先登錄$loginSite$或換張圖試試,或重新安裝$latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "最新版本", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "調整區域搜索"}, "sbi_shipping_": {"message": "當日出貨"}, "sbi_specify_category": {"message": "指定類別："}, "sbi_start_crop": {"message": "調整截圖"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688跨境"}, "sbi_tutorial_btn_more": {"message": "更多用法"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "查找淘寶優惠券"}, "sbi_txt__empty_retry": {"message": "抱歉，未找到結果，請重試。"}, "sbi_txt__min_order": {"message": "最小訂單量"}, "sbi_visiting": {"message": "正在瀏覽"}, "sbi_yiwugo__jiagexiangtan": {"message": "價格聯繫商家"}, "sbi_yiwugo__qigou": {"message": "$num$個起購", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "星"}, "searchByImage_screenshot": {"message": "一鍵截圖"}, "searchByImage_search": {"message": "一鍵搜尋同款"}, "searchByImage_size_type": {"message": "檔案大小不能高於$num$MB，僅支援$type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "分析圖片"}, "search_by_image_progress_searching": {"message": "搜索產品"}, "search_by_image_progress_sending": {"message": "正在發送圖片"}, "search_by_image_response_rate": {"message": "答复率：$responseRate$的買家聯繫供應商後，在$responseInHour$時內收到了答复。", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "按關鍵詞搜索"}, "select_country_language_modal_title_country": {"message": "國家"}, "select_country_language_modal_title_language": {"message": "語言"}, "select_country_region_modal_title": {"message": "選擇國家/地區"}, "select_language_modal_title": {"message": "選擇語言:"}, "select_shop": {"message": "選擇店鋪"}, "sellers_count": {"message": "目前頁賣家數"}, "sellers_count_per_page": {"message": "目前頁面的賣家數"}, "service_score": {"message": "綜合服務評分"}, "set_shortcut_keys": {"message": "設定快速鍵"}, "setting_logo_title": {"message": "購物助手"}, "setting_modal_options_position_title": {"message": "外掛程式圖示位置"}, "setting_modal_options_position_value_left": {"message": "左下角"}, "setting_modal_options_position_value_right": {"message": "右下角"}, "setting_modal_options_theme_title": {"message": "主題顏色"}, "setting_modal_options_theme_value_dark": {"message": "黑色"}, "setting_modal_options_theme_value_light": {"message": "白色"}, "setting_modal_title": {"message": "設置"}, "setting_options_country_title": {"message": "國家/地區"}, "setting_options_hover_zoom_desc": {"message": "游標懸停在圖片上以放大圖片"}, "setting_options_hover_zoom_title": {"message": "懸停放大"}, "setting_options_jd_coupon_desc": {"message": "找到優惠券"}, "setting_options_jd_coupon_title": {"message": "京東優惠券"}, "setting_options_language_title": {"message": "語言"}, "setting_options_price_drop_alert_desc": {"message": "當我的最愛中的產品價格下降時，您會收到降價推送提醒"}, "setting_options_price_drop_alert_title": {"message": "降價提醒"}, "setting_options_price_history_on_list_page_desc": {"message": "在產品搜索清單頁顯示價格歷史"}, "setting_options_price_history_on_list_page_title": {"message": "價格歷史(列表頁)"}, "setting_options_price_history_on_produt_page_desc": {"message": "在產品詳情頁顯示價格歷史"}, "setting_options_price_history_on_produt_page_title": {"message": "價格歷史(詳情頁)"}, "setting_options_sales_analysis_desc": {"message": "支持統計$platforms$產品列表頁的價格、銷量、賣家數和店鋪銷量佔比等信息", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "銷售統計分析"}, "setting_options_save_success_msg": {"message": "成功"}, "setting_options_tacking_price_title": {"message": "價格變動提醒"}, "setting_options_value_off": {"message": "關"}, "setting_options_value_on": {"message": "開"}, "setting_pkg_quick_view_desc": {"message": "支持：1688 & 淘寶"}, "setting_saved_message": {"message": "更改已成功保存"}, "setting_section_enable_platform_title": {"message": "主開關"}, "setting_section_setting_title": {"message": "設置"}, "setting_section_shortcuts_title": {"message": "快捷入口"}, "settings_aliprice_agent__desc": {"message": "顯示在$platforms$產品詳情頁", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "代購"}, "settings_copy_link__desc": {"message": "顯示在產品詳情頁"}, "settings_copy_link__title": {"message": "複製按鈕和搜尋標題"}, "settings_currency_desc__for_detail": {"message": "支持1688產品詳情頁"}, "settings_currency_desc__for_list": {"message": "圖片搜索（包括1688/1688海外/淘寶）"}, "settings_currency_desc__for_sbi": {"message": "選中價格"}, "settings_currency_desc_display_for_list": {"message": "顯示在圖片搜尋（包括1688/1688海外/淘寶）"}, "settings_currency_rate_desc": {"message": "匯率更新來自\"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "中國銀行"}, "settings_download_images__desc": {"message": "支持下載$platforms$的圖片", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "下載圖像按鈕"}, "settings_download_reviews__desc": {"message": "顯示在$platforms$產品詳情頁。", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "下載評論圖片"}, "settings_google_translate_desc": {"message": "右鍵單擊以獲取谷歌翻譯條"}, "settings_google_translate_title": {"message": "網頁翻譯"}, "settings_historical_trend_desc": {"message": "顯示在產品清單頁的圖片右下角"}, "settings_modal_btn_more": {"message": "更多設置"}, "settings_productInfo_desc": {"message": "在清單頁顯示產品的更多詳細信息，開啟後可能會增加電腦負荷，導致頁面卡頓。如有影響建議關閉"}, "settings_product_recommend__desc": {"message": "顯示在$platforms$產品詳情頁主圖下方", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "產品推薦"}, "settings_research_desc": {"message": "在列表頁查詢產品的更多詳細信息"}, "settings_sbi_add_to_list": {"message": "加入$listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "圖搜結果縮略圖"}, "settings_sbi_remove_from_list": {"message": "清除$listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "加入黑名單"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "調整入口位置"}, "settings_search_by_image_blacklist_desc": {"message": "不在黑名單中的網站上顯示圖標。"}, "settings_search_by_image_blacklist_title": {"message": "黑名單"}, "settings_search_by_image_bottom_left": {"message": "左下"}, "settings_search_by_image_bottom_right": {"message": "右下"}, "settings_search_by_image_clear_blacklist": {"message": "清除黑名單"}, "settings_search_by_image_detail_page_icon_title": {"message": "縮略圖"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "變大"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "變小"}, "settings_search_by_image_display_button_desc": {"message": "一鍵按圖搜同款"}, "settings_search_by_image_display_button_title": {"message": "圖像上的圖標"}, "settings_search_by_image_sourece_websites_desc": {"message": "在這些網站上搜索同款貨源"}, "settings_search_by_image_sourece_websites_title": {"message": "圖片搜索結果"}, "settings_search_by_image_top_left": {"message": "左上"}, "settings_search_by_image_top_right": {"message": "右上"}, "settings_search_keyword_on_x__desc": {"message": "在$platform$上搜索文字", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "選擇文字時顯示$platform$圖標", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "試著在這些網站（最多5個）上搜索同款產品"}, "settings_similar_products_title": {"message": "搜索同款產品"}, "settings_toolbar_expand_title": {"message": "插件最小化"}, "settings_top_toolbar_desc": {"message": "頁面頂部的搜索欄"}, "settings_top_toolbar_title": {"message": "搜索欄"}, "settings_translate_search_desc": {"message": "翻譯成中文並搜索"}, "settings_translate_search_title": {"message": "多語言搜索"}, "settings_translator_contextmenu_title": {"message": "截圖翻譯"}, "settings_translator_title": {"message": "翻譯"}, "shai_xuan_dao_chu": {"message": "篩選導出"}, "shai_xuan_zi_duan": {"message": "篩選字段"}, "shang_jia_shi_jian": {"message": "上架時間"}, "shang_pin_biao_ti": {"message": "商品標題"}, "shang_pin_dui_bi": {"message": "商品對比"}, "shang_pin_lian_jie": {"message": "商品鏈接"}, "shang_pin_xin_xi": {"message": "商品資訊"}, "share_modal__content": {"message": "與你的朋友們分享"}, "share_modal__disable_for_while": {"message": "近日不显示"}, "share_modal__title": {"message": "你喜歡$extensionName$嗎？", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "商品剩餘的件數"}, "shi_fou_ke_ding_zhi": {"message": "是否可自訂"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "是否認證供應商"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "是否認證供應商總數"}, "shi_fou_you_mao_yi_dan_bao": {"message": "是否有貿易擔保"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "是否有貿易擔保總數"}, "shipping_fee": {"message": "運費"}, "shop_followers": {"message": "店鋪粉絲數"}, "shou_qi": {"message": "收起"}, "similar_products_warn_max_platforms": {"message": "最多5個"}, "sku_calc_price": {"message": "計算價格"}, "sku_calc_price_settings": {"message": "計算價格設定"}, "sku_formula": {"message": "公式"}, "sku_formula_desc": {"message": "公式說明"}, "sku_formula_desc_text": {"message": "支援複雜的數學公式，A表示原價，B表示運費\n<br/>\n支援括號()、加號+、減號-、乘號*、除號/\n<br/>\n範例：\n<br/>\n1. 實現原價的1.2倍，再加運費，公式為：A*1.2+B\n<br/>\n2. 實現原價加1元，再乘以1.2倍，公式為：(A+1)*1.2\n<br/>\n3. 實現原價加10元，再乘以1.2倍，再減3元，公式為：(A+10)*1.2-3"}, "sku_in_stock": {"message": "有庫存"}, "sku_invalid_formula_format": {"message": "計算公式格式錯誤"}, "sku_inventory": {"message": "庫存"}, "sku_link_copy_fail": {"message": "複製成功, 未選取sku規格與屬性"}, "sku_link_copy_success": {"message": "複製成功, 已選取sku規格與屬性"}, "sku_list": {"message": "SKU列表"}, "sku_min_qrder_qty": {"message": "起大量"}, "sku_name": {"message": "SKU名稱"}, "sku_no": {"message": "序號"}, "sku_original_price": {"message": "原價"}, "sku_price": {"message": "SKU價格"}, "stop_track_time_label": {"message": "跟踪截止日期："}, "suo_zai_di_qu": {"message": "所在地區"}, "tab_pkg_quick_view": {"message": "監控物流資訊"}, "tab_product_details_price_history": {"message": "價格歷史"}, "tab_product_details_reviews": {"message": "曬圖評論"}, "tab_product_details_seller_analysis": {"message": "賣家分析"}, "tab_product_details_similar_products": {"message": "同款產品"}, "total_days_listed_per_product": {"message": "上架天數總和 ÷ 產品數"}, "total_items": {"message": "商品總數"}, "total_price_per_product": {"message": "價格總和 ÷ 產品數"}, "total_rating_per_product": {"message": "評分總和 ÷ 產品數"}, "total_revenue": {"message": "總銷售額"}, "total_revenue40_items": {"message": "當前頁$amount$個商品的總銷售額", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "總銷量"}, "total_sales40_items": {"message": "當前頁$amount$個商品的總銷量", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "追踪時長：1年"}, "track_for_3_months": {"message": "追踪時長：3個月"}, "track_for_6_months": {"message": "追踪時長：6個月"}, "tracking_price_email_add_btn": {"message": "新增電郵"}, "tracking_price_email_edit_btn": {"message": "編輯電郵"}, "tracking_price_email_intro": {"message": "我們將通過電子郵件通知您。"}, "tracking_price_email_invalid": {"message": "請提供有效的電郵"}, "tracking_price_email_verified_desc": {"message": "現在您可以收到我們的降價提醒。"}, "tracking_price_email_verified_title": {"message": "驗證成功"}, "tracking_price_email_verify_desc_line1": {"message": "我們已將驗證鏈接發送到您的電子郵箱，"}, "tracking_price_email_verify_desc_line2": {"message": "請檢查您的收件箱。"}, "tracking_price_email_verify_title": {"message": "驗證電郵"}, "tracking_price_web_push_notification_intro": {"message": "在桌面端：AliPrice可以為您監視任何產品，並在價格變化後向您發送Web推送通知。"}, "tracking_price_web_push_notification_title": {"message": "Web推送通知"}, "translate_im__login_required": {"message": "由AliPrice提供翻譯支援, 請登入$loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "已翻譯並複製到剪貼板，但由於阿里旺旺的限制，需要您手動貼上！"}, "translate_im__send": {"message": "翻譯&發送"}, "translate_search": {"message": "翻譯和搜索"}, "translation_originals_translated": {"message": "原文和中文"}, "translation_translated": {"message": "中文"}, "translator_btn_capture_txt": {"message": "翻譯"}, "translator_language_auto_detect": {"message": "自動檢測"}, "translator_language_detected": {"message": "自动检测"}, "translator_language_search_placeholder": {"message": "搜索語言"}, "try_again": {"message": "再試一次"}, "tu_pian_chi_cun": {"message": "圖片尺寸:"}, "tu_pian_lian_jie": {"message": "圖片連結"}, "tui_huan_ti_yan": {"message": "退換體驗"}, "tui_huan_ti_yan__desc": {"message": "考核商家的售後指標"}, "tutorial__show_all": {"message": "所有功能"}, "tutorial_ae_popup_title": {"message": "固定擴展程序，打開速賣通詳情頁"}, "tutorial_aliexpress_reviews_analysis": {"message": "速賣通評論分析"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "支援TWD HKD USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "運送至韓國/日本/中國內地"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688支持直購"}, "tutorial_auto_apply_coupon_title": {"message": "自動填寫優惠券"}, "tutorial_btn_end": {"message": "結束"}, "tutorial_btn_example": {"message": "範例"}, "tutorial_btn_have_a_try": {"message": "好的,試一試"}, "tutorial_btn_next": {"message": "下一頁"}, "tutorial_btn_see_more": {"message": "更多功能"}, "tutorial_compare_products": {"message": "對比同款"}, "tutorial_currency_convert_title": {"message": "匯率轉換"}, "tutorial_export_shopping_cart": {"message": "導出CSV，支持淘寶和1688"}, "tutorial_export_shopping_cart_title": {"message": "導出購物車"}, "tutorial_price_history_pro": {"message": "顯示在產品詳情頁上。\n支持Shopee，Lazada，Amazon和Ebay"}, "tutorial_price_history_pro_title": {"message": "全年價格歷史和訂單歷史"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "截圖搜索同款"}, "tutorial_translate_search": {"message": "翻譯即搜索"}, "tutorial_translate_search_and_package_tracking": {"message": "翻譯搜索和包裹跟踪"}, "unit_bao": {"message": "包"}, "unit_ben": {"message": "本"}, "unit_bi": {"message": "筆"}, "unit_chuang": {"message": "床"}, "unit_dai": {"message": "袋"}, "unit_dui": {"message": "對"}, "unit_fen": {"message": "份"}, "unit_ge": {"message": "個"}, "unit_he": {"message": "盒"}, "unit_jian": {"message": "件"}, "unit_li_fang_mi": {"message": "立方米"}, "unit_ping": {"message": "瓶"}, "unit_ping_fang_mi": {"message": "平方米"}, "unit_shuang": {"message": "雙"}, "unit_tai": {"message": "台"}, "unit_ti": {"message": "提"}, "unit_tiao": {"message": "條"}, "unit_xiang": {"message": "箱"}, "unit_zhang": {"message": "張"}, "unit_zhi": {"message": "只"}, "verify_contact_support": {"message": "聯絡客服"}, "verify_human_verification": {"message": "人機驗證"}, "verify_unusual_access": {"message": "檢測到異常訪問"}, "view_history_clean_all": {"message": "全部清理"}, "view_history_clean_all_warring": {"message": "清除所有瀏覽記錄？"}, "view_history_clean_all_warring_title": {"message": "警告"}, "view_history_viewd": {"message": "瀏覽記錄"}, "website": {"message": "網站"}, "weight": {"message": "重量"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "無法取得該數據"}, "wu_liu_shi_xiao": {"message": "物流時效"}, "wu_liu_shi_xiao__desc": {"message": "商家店舖的48小時攬收率及履約率"}, "xia_dan_jia": {"message": "下單價"}, "xian_xuan_ze_product_attributes": {"message": "先選擇產品屬性"}, "xiao_liang": {"message": "銷量"}, "xiao_liang_zhan_bi": {"message": "銷量佔比"}, "xiao_shi": {"message": "$num$小時", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "銷售額"}, "xiao_shou_e_zhan_bi": {"message": "銷售額佔比"}, "xuan_zhong_x_tiao_ji_lu": {"message": "選中$amount$條記錄", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "嚴選"}, "yi_ding_zai_zuo_ce": {"message": "已釘住"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "已載入完所有商品"}, "yi_nian_xiao_liang": {"message": "年銷量"}, "yi_nian_xiao_liang_zhan_bi": {"message": "年銷售佔比"}, "yi_nian_xiao_shou_e": {"message": "年銷售額"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "年銷售額佔比"}, "yi_shua_xin": {"message": "已重新整理"}, "yin_cang_xiang_tong_dian": {"message": "隱藏相同點"}, "you_xiao_liang": {"message": "有銷量"}, "yu_ji_dao_da_shi_jian": {"message": "預計到達時間"}, "yuan_gong_ren_shu": {"message": "員工人數"}, "yue_cheng_jiao": {"message": "月成交"}, "yue_dai_xiao": {"message": "月代銷"}, "yue_dai_xiao__desc": {"message": "最近30天的一件代發的銷量"}, "yue_dai_xiao_pai_xu__desc": {"message": "最近30天的一件代發的銷量, 從高到低排序"}, "yue_xiao_liang__desc": {"message": "最近30天的銷售"}, "zhan_kai": {"message": "更多"}, "zhe_kou": {"message": "商品的折扣百分比"}, "zhi_chi_yi_jian_dai_fa": {"message": "支持一件代發"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "支援包郵(一件代發包郵)"}, "zhi_fu_ding_dan_shu": {"message": "支付訂單數"}, "zhi_fu_ding_dan_shu__desc": {"message": "該商品近30天的訂單數"}, "zhu_ce_xing_zhi": {"message": "註冊性質"}, "zi_ding_yi_tiao_jian": {"message": "自訂條件"}, "zi_duan": {"message": "欄位"}, "zi_ti_xiao_liang": {"message": "子體銷量"}, "zong_he_fu_wu_fen": {"message": "綜合服務分"}, "zong_he_fu_wu_fen__desc": {"message": "對賣家服務的綜合評分"}, "zong_he_fu_wu_fen__short": {"message": "綜合服務分"}, "zong_he_ti_yan_fen": {"message": "綜合體驗分"}, "zong_he_ti_yan_fen_3": {"message": "4星以下"}, "zong_he_ti_yan_fen_4": {"message": "4星-4.5星"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5星-5.0星"}, "zong_he_ti_yan_fen_5": {"message": "5星"}, "zong_ku_cun": {"message": "總庫存"}, "zong_xiao_liang": {"message": "總銷量"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "最近30天3分鐘響應率"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "最近30天48H攬收率"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "最近30天48H履約率"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "最近30天交易紀錄"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "最近30天交易紀錄"}, "zui_jin_30D_jiu_fen_lv": {"message": "最近30天糾紛率"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "最近30天品質退款率"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "最近30天支付訂單數"}}