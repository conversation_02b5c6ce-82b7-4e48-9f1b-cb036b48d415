{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Tarptautinė karštoji pardavimo vieta"}, "1688_shi_li_ren_zheng": {"message": "1688 stiprumo sertifikatas"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Daugiau nei 1 metai"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Pristatymas per 24 valandas"}, "24H_lan_shou_lv": {"message": "24 valandų pakavimo greitis"}, "30D_shang_xin": {"message": "Mėnesio <PERSON>"}, "30d_sales": {"message": "$amount$ parduota per 30 dienų", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Atsakymas per 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "Veiksmingų Wangwang atsakymų į pirkėjo užklausos pranešimus per 3 minutes dalis per pastarąsias 30 dienų"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Pristatymas per 48 valandas"}, "48H_lan_shou_lv": {"message": "48 valandų pakavimo greitis"}, "48H_lan_shou_lv__desc": {"message": "Per 48 valandas paimto užsakymo numerio ir bendro užsakymų skaičiaus santykis"}, "48H_lv_yue_lv": {"message": "48 valandų našumo rodiklis"}, "48H_lv_yue_lv__desc": {"message": "Paimto ar pristatyto užsakymo numerio per 48 valandas santykis su bendru užsakymų skaičiumi"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "7D_wu_li_you": {"message": "7 dienos be priežiūros"}, "ABS_title_text": {"message": "Šiame sąraše yra prekės ženklo istorija"}, "AC_title_text": {"message": "<PERSON>is įrašas turi „Amazon's Choice“ ženklelį"}, "A_title_text": {"message": "Šiame įraše yra A+ turinio puslapis"}, "BS_title_text": {"message": "Šis įrašas yra įvertintas kaip $num$ perkamiausias $type$ kategorijoje", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (riboto laiko <PERSON>) re<PERSON><PERSON><PERSON><PERSON>, kad šis įrašas yra „7 dienų reklamos“ re<PERSON><PERSON>o dalis"}, "NR_title_text": {"message": "Šis įrašas yra įvertintas kaip $num$ naujas leidimas kategorijoje $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Šiame sąraše yra vaizdo įrašo skelbimas, tam tikro tipo PPC skelbimas, kuri<PERSON> pap<PERSON> rodomas paieškos rezultatų viduryje"}, "SB_title_text": {"message": "Šiame sąraše yra prek<PERSON>, tam tikro tipo PPC skelbimas, kuri<PERSON> pap<PERSON> rodomas pai<PERSON>škos rezultatų viršuje arba apačioje"}, "SP_title_text": {"message": "Šiame sąraše yra remiamo produkto skelbimas"}, "V_title_text": {"message": "Šiame sąraše yra vaizdo įrašo pristatymas"}, "advanced_research": {"message": "Išplėst<PERSON><PERSON> t<PERSON>"}, "agent_ds1688___my_order": {"message": "<PERSON><PERSON> įsakymai"}, "agent_ds1688__add_to_cart": {"message": "Pirkimas užsienyje"}, "agent_ds1688__cart": {"message": "Pirkinių krepšelis"}, "agent_ds1688__desc": {"message": "Pateikė 1688. Tai palaiko tiesioginį pirkimą i<PERSON>, mokėjimą USD ir pristatymą į jūsų tranzito sandėlį Kinijoje."}, "agent_ds1688__freight": {"message": "Siuntimo <PERSON> skaičiuoklė"}, "agent_ds1688__help": {"message": "Pagalba"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Asmeninis centras"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Argumentai \"už\""}, "ai_comment_analysis_ai": {"message": "AI apžvalgos analizė"}, "ai_comment_analysis_available": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_balance": {"message": "Nepakanka monetų, papildykite"}, "ai_comment_analysis_behavior": {"message": "Elgesys"}, "ai_comment_analysis_characteristic": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_comment": {"message": "Produktas neturi pakankamai atsiliepimų, kad būtų galima padaryti tikslias išvadas, pasirinkite produktą su daugiau atsiliepimų."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_default": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_desire": {"message": "Klientų lūkesčiai"}, "ai_comment_analysis_disadvantage": {"message": "Minusa<PERSON>"}, "ai_comment_analysis_free": {"message": "Nemokami bandymai"}, "ai_comment_analysis_freeNum": {"message": "Bus panaudotas 1 nemokamas kreditas"}, "ai_comment_analysis_go_recharge": {"message": "Eikite į papildymą"}, "ai_comment_analysis_intelligence": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_location": {"message": "Vieta"}, "ai_comment_analysis_motive": {"message": "Pirk<PERSON>"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON>, bandykite dar kartą"}, "ai_comment_analysis_normal": {"message": "Nuotraukų apžvalgos"}, "ai_comment_analysis_number_reviews": {"message": "Atsiliepimų skaičius: $num$, numatomas suvartojimas: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_percentage": {"message": "Procentas"}, "ai_comment_analysis_problem": {"message": "Problemos su mokėjimu"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON> anal<PERSON>"}, "ai_comment_analysis_reason": {"message": "Priežastis"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_retry": {"message": "Bandykite dar kartą"}, "ai_comment_analysis_scene": {"message": "<PERSON><PERSON><PERSON><PERSON> scenar<PERSON>"}, "ai_comment_analysis_start": {"message": "Pradėkite analizuoti"}, "ai_comment_analysis_subject": {"message": "Temos"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_tool": {"message": "AI įrankis"}, "ai_comment_analysis_user_portrait": {"message": "Vartotojo profilis"}, "ai_comment_analysis_welcome": {"message": "Sveiki atvykę į AI apžvalgos analizę"}, "ai_comment_analysis_year": {"message": "Komentarai iš praėjusių metų"}, "ai_listing_Exclude_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "ai_listing_Login_the_feature": {"message": "<PERSON><PERSON>, b<PERSON><PERSON>"}, "ai_listing_aI_generation": {"message": "AI karta"}, "ai_listing_add_automatic": {"message": "Automatinis"}, "ai_listing_add_dictionary_new": {"message": "Sukurkite naują biblioteką"}, "ai_listing_add_enter_keywords": {"message": "Įveskite r<PERSON><PERSON><PERSON>"}, "ai_listing_add_inputkey_selling": {"message": "Įveskite pardavimo tašką ir paspauskite $key$, kad užbaigtumėte pridėjimą", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Virš<PERSON>as limitas, iki $amount$ pardavimo taškų", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Pridėti raktinių žodžių"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON><PERSON><PERSON> būdu"}, "ai_listing_add_selling": {"message": "Pridėkite pardavimo taškų"}, "ai_listing_added_keywords": {"message": "Pridėta raktinių žodžių"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "Įveskite neįtrauktus r<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON><PERSON> pridėjimą."}, "ai_listing_adding_selling": {"message": "Pridėta pardavimo taškų"}, "ai_listing_addkeyword_enter": {"message": "Įveskite pagrindinių atributų žodžius ir paspauskite Enter, kad užbaigtumėte pridėjimą"}, "ai_listing_ai_description": {"message": "AI aprašymo žodžių biblioteka"}, "ai_listing_ai_dictionary": {"message": "AI pavadinimo žodžių biblioteka"}, "ai_listing_ai_title": {"message": "AI pavadinimas"}, "ai_listing_aidescription_repeated": {"message": "AI aprašo žodžio bibliotekos pavadinimas negali būti kart<PERSON>mas"}, "ai_listing_aititle_repeated": {"message": "AI pavadinimo žodžių bibliotekos pavadinimas negali būti kartojamas"}, "ai_listing_data_comes_from": {"message": "<PERSON>ie duomenys gaunami iš:"}, "ai_listing_deleted_successfully": {"message": "Sėkmingai ištrinta"}, "ai_listing_dictionary_name": {"message": "Bibliotekos pavadinimas"}, "ai_listing_edit_dictionary": {"message": "Keisti biblioteką..."}, "ai_listing_edit_word_library": {"message": "Redaguokite žodžių biblioteką"}, "ai_listing_enter_keywords": {"message": "Įveskite rak<PERSON>ius <PERSON> ir paspauskite $key$, kad užbaigtumėte pridėjimą", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, maksimalus raktinių žodžių $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Išskirta žodžių biblioteka"}, "ai_listing_generate_characters": {"message": "Generuokite simbolius"}, "ai_listing_generation_platform": {"message": "Kartos platforma"}, "ai_listing_help_optimize": {"message": "Padėkite man optimizuoti produkto pavadinimą, originalus pavadinimas yra"}, "ai_listing_include_selling": {"message": "Kiti pardavimo taškai apima:"}, "ai_listing_included_keyword": {"message": "Įtraukti rak<PERSON><PERSON><PERSON>žiai"}, "ai_listing_included_keywords": {"message": "Įtraukti rak<PERSON><PERSON><PERSON>žiai"}, "ai_listing_input_selling": {"message": "Įveskite pardavimo tašką"}, "ai_listing_input_selling_fit": {"message": "Įveskite pardavimo ta<PERSON>, kad atitikt<PERSON> pavadinimą"}, "ai_listing_input_selling_please": {"message": "Įveskite pardavimo taškus"}, "ai_listing_intelligently_title": {"message": "Įveskite reikalingą turinį auk<PERSON><PERSON><PERSON><PERSON>, kad sugeneruotum<PERSON>te pavadinim<PERSON> sum<PERSON>i"}, "ai_listing_keyword_product_title": {"message": "Raktinio žodžio produkto pavadinimas"}, "ai_listing_keywords_repeated": {"message": "Raktažodžiai negali būti kart<PERSON>mi"}, "ai_listing_listed_selling_points": {"message": "Įtraukti pardavimo taškai"}, "ai_listing_long_title_1": {"message": "Pateikiama pagrindinė informacija, pvz., pre<PERSON><PERSON><PERSON> p<PERSON>, produkto tip<PERSON>, produkto savy<PERSON> ir kt."}, "ai_listing_long_title_2": {"message": "<PERSON><PERSON><PERSON> standartiniu produkto pava<PERSON>, pridedami SEO skatinantys raktiniai žodžiai."}, "ai_listing_long_title_3": {"message": "Be prekės ž<PERSON><PERSON>, produkto tipo, produkto savybių ir raktinių žodžių, ilgauodegiai raktiniai žodžiai taip pat yra įtraukti siekiant aukštesnio reitingo konkrečiose, segmentuotose paieškos užklausose."}, "ai_listing_longtail_keyword_product_title": {"message": "Ilgos uodegos raktinio žodžio produkto pavadinimas"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON><PERSON> būdu įveskite..."}, "ai_listing_network_not_working": {"message": "Internetas nepasiekiamas, <PERSON><PERSON> pasi<PERSON>ti „ChatGPT“, reikalingas VPN"}, "ai_listing_new_dictionary": {"message": "Sukurti naują žodžių biblioteką..."}, "ai_listing_new_generate": {"message": "Generu<PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Pasirenkami žodžiai"}, "ai_listing_original_title": {"message": "Originalus pavadinimas"}, "ai_listing_other_keywords_included": {"message": "Kiti raktiniai žodžiai:"}, "ai_listing_please_again": {"message": "<PERSON><PERSON><PERSON><PERSON>, pabandykite dar kartą"}, "ai_listing_please_select": {"message": "Jums buvo sukurti šie pavadinimai, pasirinkite:"}, "ai_listing_product_category": {"message": "Produkto kategorija"}, "ai_listing_product_category_is": {"message": "Produkto kategorija yra"}, "ai_listing_product_category_to": {"message": "<PERSON><PERSON><PERSON> kategorijai produktas priklauso?"}, "ai_listing_random_keywords": {"message": "Atsitiktiniai $amount$ raktiniai žodžiai", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Atsitiktiniai $amount$ pardavimo taškai", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Atsitiktinai pasirinkti iš žodžių biblioteka"}, "ai_listing_search_selling": {"message": "Ieškokite pagal pardavimo vietą"}, "ai_listing_select_product_categories": {"message": "Automatiškai pasirinkti prekių kategorijas."}, "ai_listing_select_product_selling_points": {"message": "Automatiškai pasirinkite prekių pardavimo taškus"}, "ai_listing_select_word_library": {"message": "Pasirinkite žodžių biblioteką"}, "ai_listing_selling": {"message": "Pardavimo ta<PERSON>"}, "ai_listing_selling_ask": {"message": "Kokie kiti pardavimo kriterijai keliami pavadin<PERSON>?"}, "ai_listing_selling_optional": {"message": "Neprivalomi par<PERSON>"}, "ai_listing_selling_repeat": {"message": "Taškai negali b<PERSON><PERSON>"}, "ai_listing_set_excluded": {"message": "Nustatyti kaip neįtrauktą žodžių biblioteką"}, "ai_listing_set_include_selling_points": {"message": "Įtraukite pardavimo taškus"}, "ai_listing_set_included": {"message": "Nustatyti kaip įtrauktą žodžių biblioteką"}, "ai_listing_set_selling_dictionary": {"message": "Nustatyti kaip pardavimo vietų biblioteką"}, "ai_listing_standard_product_title": {"message": "Standartinis gaminio pavadinimas"}, "ai_listing_translated_title": {"message": "Išverstas pavadinimas"}, "ai_listing_visit_chatGPT": {"message": "Apsilankykite „ChatGPT“."}, "ai_listing_what_other_keywords": {"message": "Kokie kiti raktiniai žodžiai reikalingi pavadinimui?"}, "aliprice_coupons_apply_again": {"message": "<PERSON><PERSON><PERSON> dar kartą"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON>"}, "aliprice_coupons_apply_success": {"message": "Ra<PERSON><PERSON> k<PERSON>: sutaupykite $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Išbandomi geriausių pasiūlymų kodai..."}, "aliprice_coupons_applying_desc": {"message": "Išsiregistruojama: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Toliau eikite į „Checkout“."}, "aliprice_coupons_found_coupons": {"message": "Radome $amount$ kuponų", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>? Įsitikinkite, kad gausite geriausią kainą!"}, "aliprice_coupons_no_coupon_aviable": {"message": "<PERSON>ie kodai neveik<PERSON>. <PERSON><PERSON><PERSON> b<PERSON> – jau gaunate geriausią kain<PERSON>."}, "aliprice_coupons_toolbar_btn": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "aliww_translate": {"message": "Aliwangwang pokalbių vertėjas"}, "aliww_translate_supports": {"message": "Palaikymas: 1688 ir <PERSON>"}, "amazon_extended_keywords_Keywords": {"message": "Raktažodžiai"}, "amazon_extended_keywords_copy_all": {"message": "Nukopijuoti viską"}, "amazon_extended_keywords_more": {"message": "Daugiau"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pagal sukauptus pardavimus"}, "an_lei_xing_cha_kan": {"message": "Tipas"}, "an_yue_dai_xiao_pai_xu": {"message": "<PERSON><PERSON><PERSON> pagal dropshipping pardavimą"}, "apra_btn__cat_name": {"message": "Atsiliepimų analizė"}, "apra_chart__name": {"message": "Produktų pardavimo procentas pagal šalį"}, "apra_chart__update_at": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laikas $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Šalių pardavimo statistika"}, "auto_opening": {"message": "Automatiškai atidaroma po $num$ sek", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Kiti „$autoPaging$“ puslapiai", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Vidutinis <PERSON> dienomis"}, "average_hui_fu_lv": {"message": "Vidutinis atsakymų rodiklis"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Vidutinis tiekėjo lygis"}, "average_price": {"message": "Vidutinė kaina"}, "average_qi_ding_liang": {"message": "<PERSON>id<PERSON>nis <PERSON>"}, "average_rating": {"message": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON><PERSON><PERSON><PERSON> sertif<PERSON>i"}, "average_revenue": {"message": "Vidutin<PERSON><PERSON> paja<PERSON>"}, "average_revenue_per_product": {"message": "Bendros pajamos ÷ Produktų skaičius"}, "average_sales": {"message": "<PERSON><PERSON><PERSON><PERSON> parda<PERSON>"}, "average_sales_per_product": {"message": "Bendras pardavimas ÷ Produktų skaičius"}, "bao_han": {"message": "Sudėtyje yra"}, "bao_zheng_jin": {"message": "Marža"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "Pavadinimas"}, "blacklist_add_blacklist": {"message": "Blocca questo negozio"}, "blacklist_address_incorrect": {"message": "Adresas neteisingas. P<PERSON>au pat<PERSON>."}, "blacklist_blacked_out": {"message": "Il negozio è stato bloccato"}, "blacklist_blacklist": {"message": "<PERSON><PERSON><PERSON>"}, "blacklist_no_records_yet": {"message": "Dar nėra įrašo!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Prekės ženkla<PERSON>"}, "btn_aliprice_agent__daigou": {"message": "<PERSON><PERSON><PERSON>"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshippingas"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON>"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "Išbandykite dabar"}, "btn_txt_view_on_aliprice": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> „AliPrice“"}, "bu_bao_han": {"message": "Sudėtyje nėra"}, "bulk_copy_links": {"message": "Masinis nuorodų kopijavimas"}, "bulk_copy_products": {"message": "<PERSON><PERSON><PERSON> kop<PERSON>mo produktai"}, "cai_gou_zi_xun": {"message": "Klientų aptarnavimas"}, "cai_gou_zi_xun__desc": {"message": "Pardavėjo trijų minučių atsakymo greitis"}, "can_ping_lei_xing": {"message": "Tipas"}, "cao_zuo": {"message": "Operacija"}, "chan_pin_ID": {"message": "Produkto ID"}, "chan_pin_e_wai_xin_xi": {"message": "Produkto papildoma informacija"}, "chan_pin_lian_jie": {"message": "Produkto nuoroda"}, "cheng_li_shi_jian": {"message": "Įkūrimo laikas"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Spustelėkite Kopijuoti produkto informaciją"}, "commmon_txt_expired": {"message": "Baigėsi galio<PERSON> la<PERSON>s"}, "common__date_range_12m": {"message": "1 metai"}, "common__date_range_1m": {"message": "1 mėnuo"}, "common__date_range_1w": {"message": "1 savaitė"}, "common__date_range_2w": {"message": "2 savaitės"}, "common__date_range_3m": {"message": "3 mėnesiai"}, "common__date_range_3w": {"message": "3 savaitės"}, "common__date_range_6m": {"message": "6 mėnesiai"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "Uždaryti"}, "common_btn_save": {"message": "Su<PERSON>up<PERSON><PERSON>"}, "common_btn_setting": {"message": "Sąranka"}, "common_email": {"message": "El"}, "common_error_msg_no_data": {"message": "Nėra duomenų"}, "common_error_msg_no_result": {"message": "Deja, nerasta rezultatų."}, "common_favorites": {"message": "Mėgstamiausi"}, "common_feedback": {"message": "Atsiliepi<PERSON>"}, "common_help": {"message": "Pagalba"}, "common_loading": {"message": "Įkeliama"}, "common_login": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_logout": {"message": "<PERSON>si<PERSON><PERSON><PERSON>"}, "common_no": {"message": "Ne"}, "common_powered_by_aliprice": {"message": "Parengta pagal AliPrice.com"}, "common_setting": {"message": "<PERSON>ustat<PERSON><PERSON>"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_system_upgrading_title": {"message": "Sistemos <PERSON>"}, "common_system_upgrading_txt": {"message": "Pabandykite vėliau"}, "common_txt__currency": {"message": "Valiuta"}, "common_txt__video_tutorial": {"message": "Video pamoka"}, "common_txt_ago_time": {"message": "$time$ giorni fa", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "<PERSON><PERSON>"}, "common_txt_analysis": {"message": "Analiz<PERSON>"}, "common_txt_basically_used": {"message": "Quasi mai usato"}, "common_txt_biaoti_link": {"message": "Pavadinimas + nuoroda"}, "common_txt_biaoti_link_dian_pu": {"message": "Pavadinimas + nuoroda + parduotu<PERSON><PERSON><PERSON> pavadinimas"}, "common_txt_blacklist": {"message": "Užblokuotų sąrašas"}, "common_txt_cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_category": {"message": "Kategorija"}, "common_txt_chakan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_colors": {"message": "Spalvos"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Nukopijuota"}, "common_txt_copy": {"message": "Ko<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Nukopijuoti nuorodą"}, "common_txt_copy_title": {"message": "Kopiju<PERSON><PERSON> pavadin<PERSON>"}, "common_txt_copy_title__link": {"message": "Nukopijuokite pavadinimą ir nuorodą"}, "common_txt_day": {"message": "dangus"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "Nukopijuokite parduotuvės pavadinim<PERSON> + nuorodą"}, "common_txt_download": {"message": "parsisiųsti"}, "common_txt_downloaded": {"message": "Parsisiųsti"}, "common_txt_export_as_csv": {"message": "Eksportuoti Excel"}, "common_txt_export_as_txt": {"message": "Eksportuoti tekstą"}, "common_txt_fail": {"message": "Nepavyko"}, "common_txt_format": {"message": "Formatas"}, "common_txt_get": {"message": "gauti"}, "common_txt_incert_selection": {"message": "Apverskite pasirinkimą"}, "common_txt_install": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_load_failed": {"message": "Nepavyko už<PERSON>"}, "common_txt_month": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_more": {"message": "Daugiau"}, "common_txt_new_unused": {"message": "Nuovo di zecca, inutilizzato"}, "common_txt_next": {"message": "Kitas"}, "common_txt_no_limit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_noticeable": {"message": "<PERSON>essun graffio o sporco visibile"}, "common_txt_on_sale": {"message": "Disponibile"}, "common_txt_opt_in_out": {"message": "Įjungti išjungti"}, "common_txt_order": {"message": "Įsakymas"}, "common_txt_others": {"message": "Kit<PERSON>"}, "common_txt_overall_poor_condition": {"message": "Condizioni generali pessime"}, "common_txt_patterns": {"message": "model<PERSON>i"}, "common_txt_platform": {"message": "Platformos"}, "common_txt_please_select": {"message": "Prašome pasirinkti"}, "common_txt_prev": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Privatumo politika"}, "common_txt_product_condition": {"message": "Stato del prodotto"}, "common_txt_rating": {"message": "Įvertinimas"}, "common_txt_ratings": {"message": "Įvertinimai"}, "common_txt_reload": {"message": "Įkelti iš naujo"}, "common_txt_reset": {"message": "Nustatyti iš naujo"}, "common_txt_review": {"message": "Apžvalga"}, "common_txt_sale": {"message": "Disponibile"}, "common_txt_same": {"message": "Tas pats"}, "common_txt_scratches_and_dirt": {"message": "Con graffi e sporcizia"}, "common_txt_search_title": {"message": "Paieškos pavadinimas"}, "common_txt_select_all": {"message": "Pasirinkti viską"}, "common_txt_selected": {"message": "pasirinkta"}, "common_txt_share": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sold": {"message": "par<PERSON><PERSON>"}, "common_txt_sold_out": {"message": "esaurito"}, "common_txt_some_scratches": {"message": "Alcuni graffi e sporcizia"}, "common_txt_sort_by": {"message": "Rūšiuoti pagal"}, "common_txt_state": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_success": {"message": "Sėkmė"}, "common_txt_sys_err": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "common_txt_today": {"message": "Šiandien"}, "common_txt_total": {"message": "visi"}, "common_txt_unselect_all": {"message": "Apverskite pasirinkimą"}, "common_txt_upload_image": {"message": "Įkelti paveikslėlį"}, "common_txt_visit": {"message": "Apsilankykite"}, "common_txt_whitelist": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Metai"}, "common_yes": {"message": "<PERSON><PERSON>"}, "compare_tool_btn_clear_all": {"message": "Išvalyti viską"}, "compare_tool_btn_compare": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "compare_tool_btn_contact": {"message": "kontaktas"}, "compare_tool_tips_max_compared": {"message": "Pridėkite iki $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Konfigūruoti <PERSON>"}, "contact_us": {"message": "Susisiekite su mumis"}, "context_menu_screenshot_search": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON> ieškoti pagal vaizdą"}, "context_menus_aliprice_search_by_image": {"message": "Ieškokite vaizdo „AliPrice“"}, "context_menus_goote_trans": {"message": "<PERSON>ers<PERSON> puslapį / <PERSON><PERSON>i originalą"}, "context_menus_search_by_image": {"message": "Ieškokite pagal vaizdą „$storeName$“", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Užfiksuokite „$storeName$“", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON>tumė<PERSON>"}, "converter_modal_amount_placeholder": {"message": "Čia įveskite sumą"}, "converter_modal_btn_convert": {"message": "<PERSON><PERSON><PERSON>"}, "converter_modal_exchange_rate_source": {"message": "Duomenys gaunami iš $boc$ užsienio valiutos kurso Atnaujinimo laika<PERSON>: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Valiutos keit<PERSON>"}, "converter_modal_search_placeholder": {"message": "p<PERSON><PERSON>š<PERSON> valiuta"}, "copy_all_contact_us_notice": {"message": "<PERSON><PERSON> s<PERSON> metu <PERSON>, susisiekite su mumis"}, "copy_product_info": {"message": "Nukopijuokite produkto informaciją"}, "copy_suggest_search_kw": {"message": "Kopijuoti iš<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "country__han_gou": {"message": "Pietų <PERSON>"}, "country__ri_ben": {"message": "Japonija"}, "country__yue_nan": {"message": "Vietnamas"}, "currency_convert__custom": {"message": "Pasirinktas valiutos k<PERSON>as"}, "currency_convert__sync_server": {"message": "Sinchronizuoti serverį"}, "dang_ri_fa_huo": {"message": "Siuntimas tą pačią dieną"}, "dao_chu_quan_dian_shang_pin": {"message": "Eksportuokite visus parduotuvės produktus"}, "dao_chu_wei_CSV": {"message": "Eksportuoti"}, "dao_chu_zi_duan": {"message": "Eksportuoti laukai"}, "delivery_address": {"message": "Pristat<PERSON><PERSON>"}, "delivery_company": {"message": "Pristatymo įmonė"}, "di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "Spustelėkite norėdami užklausti"}, "dian_pu_ID": {"message": "Parduotuvės ID"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dian_pu_lian_jie": {"message": "Parduotuv<PERSON><PERSON> nuo<PERSON>a"}, "dian_pu_ming": {"message": "Parduotu<PERSON><PERSON><PERSON> p<PERSON>"}, "dian_pu_ming_cheng": {"message": "Parduotu<PERSON><PERSON><PERSON> p<PERSON>"}, "dian_pu_shang_pin_zong_hsu": {"message": "Bendras produktų skaičius parduotuvėje: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Saugokite informaciją"}, "ding_zai_zuo_ce": {"message": "prikaltas į kairę"}, "disable_old_version_tips_disable_btn_title": {"message": "<PERSON>šjun<PERSON><PERSON> seną versiją"}, "download_image__SKU_variant_images": {"message": "SKU varianto vaizdai"}, "download_image__assume": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, turime 2 vaizdus: produktas1.jpg ir produktas2.gif.\nimg_{$no$} bus pervardytas į img_01.jpg, img_02.gif;\n{$group$}_{$no$} bus pervardytas į main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Partijos <PERSON>"}, "download_image__combined_image": {"message": "Immagine combinata dei dettagli del prodotto"}, "download_image__continue_downloading": {"message": "Tęsti atsisiuntimą"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>"}, "download_image__download_combined_image": {"message": "Scarica l'immagine combinata dei dettagli del prodotto"}, "download_image__download_zip": {"message": "Parsisiųsti zip"}, "download_image__enlarge_check": {"message": "<PERSON><PERSON><PERSON>, JPG, GIF ir PNG vaizdus, ​​maks<PERSON><PERSON> vieno vaizdo dydis: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Padidinti vaizdą"}, "download_image__export": {"message": "Eksportuoti"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "Vaizdo įrašas buvo i<PERSON>, nes jo negalima eksportuoti"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON> vertimas"}, "download_image__main_image": {"message": "pagrin<PERSON>is vaizdas"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__name": {"message": "parsisiųsti paveikslėlį"}, "download_image__notice_content": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atsisiuntimo nustatymuose nežymėkite \"<PERSON><PERSON>, kur išsaugoti kiekvieną failą prieš atsisiunčiant\"!!! Priešingu atveju bus daug dialogo langų."}, "download_image__notice_ignore": {"message": "Daugiau nebeprašyti š<PERSON>"}, "download_image__order_number": {"message": "{$no$} serijos numeris; {$group$} grup<PERSON><PERSON> pava<PERSON>; {$date$} la<PERSON>", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Apžvalga"}, "download_image__prompt_download_zip": {"message": "Per daug vaizd<PERSON>, geriau atsisiųskite juos kaip <PERSON> a<PERSON>lanką."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__rule": {"message": "Vardų suteikimo taisyklės"}, "download_image__single_folder": {"message": "Vienas aplankas"}, "download_image__sku_image": {"message": "SKU vaizdai"}, "download_image__video": {"message": "vaizdo įrašą"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Atsisiųskite apžvalgos vaizdą"}, "download_reviews__dropdown_title": {"message": "Atsisiųskite apžvalgos vaizdą"}, "download_reviews__export_csv": {"message": "eksportuoti CSV"}, "download_reviews__no_images": {"message": "0 nuotraukų galima atsisiųsti"}, "download_reviews__no_reviews": {"message": "Nėra atsisiuntimo apžvalgos!"}, "download_reviews__notice": {"message": "Pat<PERSON><PERSON>:"}, "download_reviews__notice__chrome_settings": {"message": "Nustatykite „Chrome“ narš<PERSON>ę, kad ji paklaustų, kur išsaugoti kiekvieną failą prieš atsisiunčiant, nustatykite į „Išjungta“"}, "download_reviews__notice__wait": {"message": "Priklausomai nuo perž<PERSON><PERSON><PERSON><PERSON>, lauki<PERSON> laikas gali būti il<PERSON>"}, "download_reviews__pages_list__all": {"message": "Visi"}, "download_reviews__pages_list__page": {"message": "Ankstesni $page$ puslapiai", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Pasirink<PERSON>"}, "export_shopping_cart__csv_filed__details_url": {"message": "Produkto nuoroda"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU nuoroda"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON><PERSON><PERSON> nuo<PERSON>a"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "Parduotu<PERSON><PERSON><PERSON> p<PERSON>"}, "export_shopping_cart__csv_filed__store_url": {"message": "Parduotuv<PERSON><PERSON> nuo<PERSON>a"}, "export_shopping_cart__csv_filed__title": {"message": "Produkto pavadinimas"}, "export_shopping_cart__export_btn": {"message": "Eksportuoti"}, "export_shopping_cart__export_empty": {"message": "Prašome pasirinkti prekę!"}, "fa_huo_shi_jian": {"message": "Siuntimas"}, "favorite_add_email": {"message": "Pridėti el"}, "favorite_add_favorites": {"message": "Pridėti prie mėgstamiausių"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Kainos kritimo įspėjimas."}, "favorite_btn_notify": {"message": "<PERSON><PERSON> kaina"}, "favorite_cate_name_all": {"message": "Visi produktai"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON> kaina"}, "favorite_due_date": {"message": "Terminas"}, "favorite_enable_notification": {"message": "Įjunkite el. pa<PERSON><PERSON>"}, "favorite_expired": {"message": "Baigėsi galio<PERSON> la<PERSON>s"}, "favorite_go_to_enable": {"message": "Eikite įjungti"}, "favorite_msg_add_success": {"message": "Pridėta prie mėgstamiausių"}, "favorite_msg_del_success": {"message": "Ištrinta iš m<PERSON>ių"}, "favorite_msg_failure": {"message": "Nepavyko! Atnaujinkite puslapį ir bandykite dar kartą."}, "favorite_please_add_email": {"message": "Prašome pridėti el"}, "favorite_price_drop": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_price_rise": {"message": "Aukštyn"}, "favorite_price_untracked": {"message": "<PERSON><PERSON>"}, "favorite_saved_price": {"message": "Išsaugota kaina"}, "favorite_stop_tracking": {"message": "Nustokite sekti"}, "favorite_sub_email_address": {"message": "Prenumeratos el. pašto adresas"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_tracking_prices": {"message": "<PERSON>nų s<PERSON>"}, "favorite_verify_email": {"message": "Patvirtinkite el. pašto ad<PERSON>"}, "favorites_list_remove_prompt_msg": {"message": "Ar tikrai jį ištrinsite?"}, "favorites_update_button": {"message": "Atnaujinkite kainas dabar"}, "fen_lei": {"message": "Kategorija"}, "fen_xia_yan_xuan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "find_similar": {"message": "Raskite panašių"}, "first_ali_price_date": {"message": "Data, kai pirmą kartą užfiksavo „AliPrice“ tikrinimo programa"}, "fooview_coupons_modal_no_data": {"message": "Jokių kuponų"}, "fooview_coupons_modal_title": {"message": "Kuponai"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Kaina < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "arba kaina > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Terminas"}, "fooview_favorites_error_msg_no_favorites": {"message": "Pridėkite mėgstamų produktų čia, kad gautumėte įspėjimą apie kainų kritimą."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "ka<PERSON><PERSON>"}, "fooview_favorites_filter_price_up": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Eikite į „AliPrice Favorite“"}, "fooview_favorites_track_price": {"message": "Norėdami sekti kainą"}, "fooview_price_history_app_price": {"message": "APP kaina:"}, "fooview_price_history_title": {"message": "Kainų istorija"}, "fooview_product_list_feedback": {"message": "Atsiliepi<PERSON>"}, "fooview_product_list_orders": {"message": "Užsakymai"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "Neradome atsiliepimų apie šį produktą."}, "fooview_reviews_filter_buyer_reviews": {"message": "Pirkėjų nuotraukos"}, "fooview_reviews_modal_title": {"message": "Atsiliepimai"}, "fooview_same_product_choose_category": {"message": "Pasirinkite kategoriją"}, "fooview_same_product_filter_feedback": {"message": "Atsiliepi<PERSON>"}, "fooview_same_product_filter_orders": {"message": "Užsakymai"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Įvertinimas"}, "fooview_same_product_modal_title": {"message": "Raskite tą patį produktą"}, "fooview_same_product_search_by_image": {"message": "Paieška pagal vaizdą"}, "fooview_seller_analysis_modal_title": {"message": "Pardavėjo analizė"}, "for_12_months": {"message": "1 metams"}, "for_12_months_list_pro": {"message": "12 mėnesių"}, "for_12_months_nei": {"message": "Per 12 mėnesių"}, "for_1_months": {"message": "1 mėnuo"}, "for_1_months_nei": {"message": "Per 1 mėnesį"}, "for_3_months": {"message": "3 mėnesius"}, "for_3_months_nei": {"message": "Per 3 mėnesius"}, "for_6_months": {"message": "6 mėnesius"}, "for_6_months_nei": {"message": "Per 6 mėnesius"}, "for_9_months": {"message": "9 mėn"}, "for_9_months_nei": {"message": "Per 9 mėnesius"}, "fu_gou_lv": {"message": "Atpirk<PERSON> k<PERSON>"}, "gao_liang_bu_tong_dian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "gao_liang_guang_gao_chan_pin": {"message": "Pažymėkite reklaminius produktus"}, "geng_duo_xin_xi": {"message": "Daugiau informacijos"}, "geng_xin_shi_jian": {"message": "Tempo di aggiornamento"}, "get_store_products_fail_tip": {"message": "Spustelėkite Gerai, kad pereit<PERSON>te prie patvirtinimo ir užtikrintumėte įprastą prieigą"}, "gong_x_kuan_shang_pin": {"message": "<PERSON>š viso $amount$ produktų", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "Tiekėjo ID"}, "gong_ying_shang_deng_ji": {"message": "Tiekėjo įvertinimas"}, "gong_ying_shang_nian_zhan": {"message": "Tiekėjas yra senesnis"}, "gong_ying_shang_xin_xi": {"message": "tiekėjo informacija"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Tiekėjo pagrindinio puslapio nuoroda"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON><PERSON> pardavimo kaina"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "guang_gao_chan_pin": {"message": "Skelbimas produktų"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "Tarp<PERSON><PERSON><PERSON> siuntimo m<PERSON>is"}, "guo_lv_tiao_jian": {"message": "Filtrai"}, "hao_ping_lv": {"message": "Teigiamas įvertinimas"}, "highest_price": {"message": "Aukštas"}, "historical_trend": {"message": "Istorinė tendencija"}, "how_to_screenshot": {"message": "Laikykite nuspaudę kairįjį pelės mygtuką, kad pasirinktumėte sritį, bakstelėkite dešinįjį pelės mygtuką arba Esc klavišą, kad išeitumėte iš ekrano kopijos"}, "howt_it_works": {"message": "<PERSON><PERSON> tai veikia"}, "hui_fu_lv": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "hui_tou_lv": {"message": "gr<PERSON><PERSON><PERSON><PERSON> norma"}, "inquire_freightFee": {"message": "Krovinių užklausa"}, "inquire_freightFee_Yuan": {"message": "Krovinių / juanių"}, "inquire_freightFee_province": {"message": "Provincija"}, "inquire_freightFee_the": {"message": "Krovinys yra $num$, o tai reiškia, kad regione pristatymas nemokamas.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Reklama."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Vienetas"}, "jia_ge_qu_shi": {"message": "tendencija"}, "jia_zai_n_ge_shang_pin": {"message": "Įkelti $num$ produktų", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Pardavimo apimties procentas per pastarąsias 30 dienų"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Pajamų procentas per pastarąsias 30 dienų"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Bendras pardavimas per pastarąsias 30 dienų"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "Bendra apyvarta per pastarąsias 30 dienų"}, "jin_90_tian_mai_jia_shu": {"message": "Pirkėjai per pastarąsias 90 dienų"}, "jin_90_tian_xiao_shou_liang": {"message": "Pardavimas per pastarąsias 90 dienų"}, "jing_xuan_huo_yuan": {"message": "Pasirink<PERSON>"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON><PERSON> modelis"}, "jing_ying_mo_shi__gong_chang": {"message": "Gamintojas"}, "jiu_fen_jie_jue": {"message": "Ginčų sprendimas"}, "jiu_fen_jie_jue__desc": {"message": "Pardavėjų ginčų dėl parduotuvių teisių apskaita"}, "jiu_fen_lv": {"message": "Ginčų rodiklis"}, "jiu_fen_lv__desc": {"message": "Užsakymų su skundais, įvykdytų per pastarąsias 30 dienų ir kurie buvo laikomi pardavėjo arba abiejų šalių atsakomybe, dalis"}, "kai_dian_ri_qi": {"message": "Atidarymo data"}, "keywords": {"message": "Raktažodžiai"}, "kua_jin_Select_pan_huo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last15_days": {"message": "Paskutines 15 dienų"}, "last180_days": {"message": "Paskutinės 180 dienų"}, "last30_days": {"message": "Per pastarąsias 30 dienų"}, "last360_days": {"message": "Paskutinės 360 dienų"}, "last45_days": {"message": "Paskutines 45 dienas"}, "last60_days": {"message": "Paskutines 60 dienų"}, "last7_days": {"message": "Paskutines 7 dienas"}, "last90_days": {"message": "Paskutines 90 dienų"}, "last_30d_sales": {"message": "Paskutinės 30 dienų pardavimai"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "<PERSON><PERSON> viso"}, "lei_ji_xiao_liang__desc": {"message": "Visi pardavimai po prekę lent<PERSON>oje"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Sukauptas pardavimo kiekis per pastarąsias 30 dienų, sur<PERSON>šiuotas nuo didžiausios iki mažiausios"}, "lian_xi_fang_shi": {"message": "Kontaktinė informacija"}, "list_time": {"message": "<PERSON><PERSON> <PERSON> datos"}, "load_more": {"message": "Įkelti daugiau"}, "login_to_aliprice": {"message": "Prisijunkite prie „AliPrice“."}, "long_link": {"message": "Ilga nuoroda"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "mobile_view__dkxbqy": {"message": "Atidarykite naują <PERSON>ą"}, "mobile_view__sjdxq": {"message": "Išsami informacija programėlėje"}, "mobile_view__sjdxqy": {"message": "Išsamios informacijos puslapis programoje"}, "mobile_view__smck": {"message": "Nuskaityti į peržiūrą"}, "mobile_view__smckms": {"message": "Norėdami nuskaityti ir <PERSON><PERSON><PERSON><PERSON><PERSON>, naudokite fotoaparatą arba programą"}, "modified_failed": {"message": "Modifikacija nepavyko"}, "modified_successfully": {"message": "Sėkmingai modifikuotas"}, "nav_btn_favorites": {"message": "<PERSON><PERSON>"}, "nav_btn_package": {"message": "Pakuotė"}, "nav_btn_product_info": {"message": "Apie produktą"}, "nav_btn_viewed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Nėra"}, "normal_link": {"message": "Įprasta nuoroda"}, "notice": {"message": "užuomina"}, "number_reviews": {"message": "Atsiliepimai"}, "only_show_num": {"message": "<PERSON>š viso produktų: $allnum$, paslėptų: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Pašalinti Nepažymėtą"}, "open": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "open_links": {"message": "Atidary<PERSON> nuorod<PERSON>"}, "options_page_tab_check_links": {"message": "Patikrinkite nuorodas"}, "options_page_tab_gernal": {"message": "Generolas"}, "options_page_tab_notifications": {"message": "Pranešimai"}, "options_page_tab_others": {"message": "Kit<PERSON>"}, "options_page_tab_sbi": {"message": "Paieška pagal vaizdą"}, "options_page_tab_shortcuts": {"message": "Spartieji k<PERSON>šai"}, "options_page_tab_shortcuts_title": {"message": "Spartiųjų klavišų šrifto dydis"}, "options_page_tab_similar_products": {"message": "<PERSON>ie patys produktai"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Netrukus bus atidarytos kelios produktų nuorodos"}, "order_list_sku_show_title": {"message": "<PERSON><PERSON><PERSON> pasirinktus variantus bendrinamose nuorodose"}, "orders_last30_days": {"message": "Užsakymų skaičius per pastarąsias 30 dienų"}, "pTutorial_favorites_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, išvard<PERSON>i čia"}, "pTutorial_favorites_block1_title": {"message": "Mėgstamiausi"}, "pTutorial_popup_block1_desc1": {"message": "<PERSON><PERSON><PERSON>, kad yra produktų, kurių kaina sumažėjo"}, "pTutorial_popup_block1_title": {"message": "Spartieji klavišai ir parank<PERSON>i"}, "pTutorial_price_history_block1_desc1": {"message": "Spustelėkite „Stebėti kainą“, pridėkite produktų prie Parankinių. Kai jų kainos <PERSON>, gaus<PERSON>"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON> kaina"}, "pTutorial_reviews_block1_desc1": {"message": "Pirkėjų atsiliepimai iš „Itao“ ir tikros nuotrauk<PERSON> iš „AliExpress“ atsiliepimų"}, "pTutorial_reviews_block1_title": {"message": "Atsiliepimai"}, "pTutorial_reviews_block2_desc1": {"message": "Visada naudinga patikrinti kitų pirkėjų atsiliepimus"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON><PERSON><PERSON> juos p<PERSON>, kad pasirinktumėte geriausią"}, "pTutorial_same_products_block1_desc2": {"message": "Spustelėkite „Daugiau“ norėdami „Ieškoti pagal vaizdą“"}, "pTutorial_same_products_block1_title": {"message": "<PERSON>ie patys produktai"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Nuveskite produkto vaizdą ten ir pasirinkite kategoriją"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Paieška pagal vaizdą"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Pardavėjo teigiama<PERSON> grįžtamojo ry<PERSON><PERSON> lygis, atsiliepimų balai ir kiek laiko pardavėjas buvo rinkoje"}, "pTutorial_seller_analysis_block1_title": {"message": "Pardavėjo įvertinimas"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Pardavėjo įvertinimas yra pagrįstas 3 rodiklia<PERSON>: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> gre<PERSON>"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Mes naudojame 3 spalvas ir pik<PERSON>, kad nurodytume pardavėjų pasitikėjimo lygį"}, "page_count": {"message": "Puslapių s<PERSON>"}, "pai_chu": {"message": "Išskirta"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Neįtraukti Honkongo apribojimų"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Išskirti Japonijoje apribotą"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Išskirti Korėjoje apribotą"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Išskirti <PERSON>tano apribo<PERSON>"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Išskirti Makao apribojimus"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Neįtraukti Rytų Europos ribojimų"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Išskirti Saudo Arabijoje apribotą"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Neįtraukti <PERSON><PERSON><PERSON>oto naudojimo"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Išskirti JAV ribotą"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Išskirti Vietnamo ribotą"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Išskirkite apribotus elementus"}, "payable_price_formula": {"message": "Kaina + Siuntimas + Nuolaida"}, "pdd_check_retail_btn_txt": {"message": "Patikrinkite mažmeninė"}, "pdd_pifa_to_retail_btn_txt": {"message": "Pirkite mažmeninėje pre<PERSON>boje"}, "pdp_copy_fail": {"message": "Kopijuoti nepavyko!"}, "pdp_copy_success": {"message": "Kopijuoti pavyko!"}, "pdp_share_modal_subtitle": {"message": "Bendrinkite ekrano kop<PERSON>, jis pamatys jū<PERSON> p<PERSON>."}, "pdp_share_modal_title": {"message": "Pasidalinkite savo pasirinkimu"}, "pdp_share_screenshot": {"message": "Bendrinkite ekrano kopiją"}, "pei_song": {"message": "<PERSON><PERSON><PERSON>"}, "pin_lei": {"message": "Kategorija"}, "pin_zhi_ti_yan": {"message": "Produkto kokybė"}, "pin_zhi_ti_yan__desc": {"message": "Pardavėjo parduotuvės kokybės gr<PERSON><PERSON><PERSON><PERSON> norma"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> norma"}, "pin_zhi_tui_kuan_lv__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kurie buvo tik grąžinti ir grąžinti per pastarąsias 30 dienų, dalis"}, "ping_fen": {"message": "Įvertinimas"}, "ping_jun_fa_huo_su_du": {"message": "<PERSON><PERSON><PERSON><PERSON> pristat<PERSON>o greitis"}, "pkgInfo_hide": {"message": "Logistikos informacija: įjungta/išjungta"}, "pkgInfo_no_trace": {"message": "Nėra logistikos informacijos"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DH vartai"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Eikite į „$name$“", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "A<PERSON> perku ..."}, "popup_track_package_btn_track": {"message": "KELIAS"}, "popup_track_package_desc": {"message": "PAKUOTĖS Viskas viename"}, "popup_track_package_search_placeholder": {"message": "Sekimo numeris"}, "popup_translate_search_placeholder": {"message": "Išverskite ir ieškokite $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Kainų istorija"}, "price_history_chart_tip_ae": {"message": "Patarimas: užsakymų skaičius yra bendras užsakymų skaičius nuo paleidimo"}, "price_history_chart_tip_coupang": {"message": "Patarimas: Coupang ištrins nesąžiningų užsakymų skaičių"}, "price_history_inm_1688_l1": {"message": "Įdiekite"}, "price_history_inm_1688_l2": {"message": "AliPrice<PERSON><PERSON><PERSON><PERSON><PERSON>, skirtas 1688"}, "price_history_panel_lowest_price": {"message": "Žemiausia kaina:"}, "price_history_panel_tab_price_tracking": {"message": "Kainų istorija"}, "price_history_panel_tab_seller_analysis": {"message": "Pardavėjo analizė"}, "price_history_pro_modal_title": {"message": "Kainų istorija ir užsakymų istorija"}, "privacy_consent__btn_agree": {"message": "Dar kartą peržiūrėti duomenų rinkimo sutikimą"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "Įgalinti viską"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "Atminkite, kad be duomenų ar slapukų kai kurios funkcijos bus išjungtos, nes toms funkcijoms reikia paaiškinti duomenis ar slapukus, tačiau vis tiek galite naudoti kitas funkcijas."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON>, be duomenų ar slapukų tai neveiks, nes mums reikia duomenų ar slapukų paai<PERSON>."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON>i neleidž<PERSON> mums rinkti šios informacijos, pašalinkite ją."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> valiutos duomenis slapukuose gauname tik pirkdami internetu, kad <PERSON>tume kain<PERSON> istorij<PERSON>."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad an<PERSON> atpažintumėte savo kompiuterį ar įrenginį."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> funkcinius duomenis į priedą, kad gal<PERSON>te dirbti su funkcija."}, "privacy_consent__item_functional_title": {"message": "Funkciniai ir Analytics slapukai"}, "privacy_consent__more_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad mes nesidalijame jūsų asmens duomenimis su kitomis įmonėmis ir jokia skelbimų kompanija nerenka duomenų naudodama mūsų paslaugą."}, "privacy_consent__options__btn__desc": {"message": "Norėdami naudoti visas funkci<PERSON>, turite ją įjungti."}, "privacy_consent__options__btn__label": {"message": "Įjunkite jį"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON><PERSON><PERSON>, kurie jus identifikuoja:"}, "privacy_consent__options__desc_L2": {"message": "- <PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> valiutos duomenis slapukuose gauname tik tada, kai perkate internetu, kad <PERSON><PERSON>te kain<PERSON> istorij<PERSON>."}, "privacy_consent__options__desc_L3": {"message": "- ir prid<PERSON><PERSON>te slap<PERSON>, kad anoni<PERSON> atpažintumėte savo kompiuterį ar įrenginį."}, "privacy_consent__options__desc_L4": {"message": "- kiti anoniminiai duomenys daro šį plėtinį patogesnį."}, "privacy_consent__options__desc_L5": {"message": "Atkreipkite dėmesį, kad mes nesidalijame jūsų asmens duomenimis su kitomis įmonėmis ir jokia skelbimų kompanija nerenka duomenų per mūsų paslaugą."}, "privacy_consent__privacy_preferences": {"message": "Privatumo nuostatos"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON><PERSON><PERSON> >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_info": {"message": "Produkto informacija"}, "product_recommend__name": {"message": "<PERSON>ie patys produktai"}, "product_research": {"message": "Produkto tyrimas"}, "product_sub__email_desc": {"message": "Kainos įspėjimo el"}, "product_sub__email_edit": {"message": "Red<PERSON><PERSON><PERSON>"}, "product_sub__email_not_verified": {"message": "Patvirtinkite el. paštą"}, "product_sub__email_required": {"message": "Prašome pateikti el"}, "product_sub__form_countdown": {"message": "Automatinis uždarymas po $seconds$ sek", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Nepavyko pridėti priminimo!"}, "product_sub__form_input_price": {"message": "įvesties kaina"}, "product_sub__form_item_country": {"message": "tauta"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON> kaina"}, "product_sub__form_item_duration": {"message": "takelis"}, "product_sub__form_item_higher_price": {"message": "Arba kaina>"}, "product_sub__form_item_invalid_higher_price": {"message": "<PERSON>na turi būti did<PERSON> nei $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Kaina turi būti ma<PERSON> nei $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON> kaina <"}, "product_sub__form_submit": {"message": "Pat<PERSON><PERSON><PERSON>"}, "product_sub__form_success": {"message": "Pavyko pridėti priminimą!"}, "product_sub__high_price_notify": {"message": "Praneškite man apie kainų pad<PERSON>"}, "product_sub__low_price_notify": {"message": "Praneškite man apie kainų sumažinimą"}, "product_sub__modal_title": {"message": "Prenumeratos kainos pasikeitimo priminimas"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ ir M<PERSON>"}, "qi_ye_mian_ji": {"message": "Įmonės sritis"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Pasirinkite bent vieną produktą"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Pasirinkite bent vieną lauką"}, "qu_deng_lu": {"message": "Prisijunkite"}, "quan_guo_yan_xuan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendation_popup_banner_btn_install": {"message": "Įdiekite jį"}, "recommendation_popup_banner_desc": {"message": "Rodyti kainų istoriją per 3/6 mėnesius ir p<PERSON> apie kainų kritimą"}, "region__all": {"message": "Visi regionai"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Įgalinti dešinįjį pel<PERSON>s k<PERSON>"}, "ren_zheng_gong_chang": {"message": "Sertifikuota gamykla"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Metai kaip sertifikuotas tiekėjas"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON>a prisijungti prie AliPrice"}, "revenue_last30_days": {"message": "Pardavimo suma per pastarąsias 30 dienų"}, "review_counts": {"message": "Kolekcinink<PERSON> skaič<PERSON>"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Įėjimo laikotarpis"}, "sales_amount_last30_days": {"message": "Bendras pardavimas per pastarąsias 30 dienų"}, "sales_last30_days": {"message": "Pardavimai per pastarąsias 30 dienų"}, "sbi_alibaba_cate__accessories": {"message": "Pried<PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bags_cases": {"message": "Krepšiai ir lagaminai"}, "sbi_alibaba_cate__beauty": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bz": {"message": "Pak<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__clothes": {"message": "Drab<PERSON>ž<PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Žiniasklaidos transliavimas"}, "sbi_alibaba_cate__coat_jacket": {"message": "<PERSON><PERSON><PERSON> ir s<PERSON>"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Vartotojų elektronika"}, "sbi_alibaba_cate__cryp": {"message": "Produktai suaugusiems"}, "sbi_alibaba_cate__csyp": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__cwyy": {"message": "Naminių gyvūnų sodininkystė"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dgdq": {"message": "Elektrikas"}, "sbi_alibaba_cate__dl": {"message": "Vaidyba"}, "sbi_alibaba_cate__dress_suits": {"message": "Suk<PERSON><PERSON><PERSON> ir k<PERSON>"}, "sbi_alibaba_cate__dszm": {"message": "A<PERSON>š<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dzqj": {"message": "Elektroninis prietai<PERSON>s"}, "sbi_alibaba_cate__essb": {"message": "Naudo<PERSON> įranga"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Drabužiai ir aksesuarai"}, "sbi_alibaba_cate__furniture": {"message": "Baldai"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> oda"}, "sbi_alibaba_cate__ghjq": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__hfcz": {"message": "Odos priežiū<PERSON> ma<PERSON>"}, "sbi_alibaba_cate__hg": {"message": "<PERSON><PERSON><PERSON>jos <PERSON>"}, "sbi_alibaba_cate__jg": {"message": "Apdor<PERSON>jima<PERSON>"}, "sbi_alibaba_cate__jianccai": {"message": "Statybinės <PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "Staklė<PERSON>"}, "sbi_alibaba_cate__jjry": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "sbi_alibaba_cate__jtys": {"message": "Transportas"}, "sbi_alibaba_cate__jxsb": {"message": "Įranga"}, "sbi_alibaba_cate__jxwj": {"message": "Mechaninė įranga"}, "sbi_alibaba_cate__jydq": {"message": "Buitinė technika"}, "sbi_alibaba_cate__jzjc": {"message": "Namų tobulinimo statybinės medž<PERSON>"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON> te<PERSON>til<PERSON>"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Kūdikių prekės"}, "sbi_alibaba_cate__nanz": {"message": "Vyriški"}, "sbi_alibaba_cate__nvz": {"message": "Moteriški drabužiai"}, "sbi_alibaba_cate__ny": {"message": "Energija"}, "sbi_alibaba_cate__others": {"message": "Kit<PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Auto Priedai"}, "sbi_alibaba_cate__qmpj": {"message": "Automobilio dalys"}, "sbi_alibaba_cate__shoes": {"message": "Avalynė"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON>kait<PERSON><PERSON><PERSON> kom<PERSON>"}, "sbi_alibaba_cate__snqj": {"message": "Sand<PERSON>lia<PERSON><PERSON> ir valymas"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>"}, "sbi_alibaba_cate__txcp": {"message": "Komunikacijos produktai"}, "sbi_alibaba_cate__tz": {"message": "Vaikiški drabužiai"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Aparat<PERSON><PERSON> įrankiai"}, "sbi_alibaba_cate__xgpi": {"message": "Odiniai krepšiai"}, "sbi_alibaba_cate__xmhz": {"message": "projekt<PERSON><PERSON>"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "<PERSON>in<PERSON>"}, "sbi_alibaba_cate__ydhw": {"message": "Sportas lauke"}, "sbi_alibaba_cate__yjkc": {"message": "Metalurgijos mineralai"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentuotė"}, "sbi_alibaba_cate__ys": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Medicininė priežiūra"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Pirkėj<PERSON> skaičius per pastarąsias 90 dienų"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Pardavimų apimtis per pastarąsias 90 dienų"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON><PERSON> kaina"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Tarp<PERSON><PERSON><PERSON> siuntimo m<PERSON>is"}, "sbi_alibaba_cn_kj_gjyf": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON><PERSON> kaina"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Pelnas = apskaičiuota kaina x pelno marža"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "<PERSON><PERSON><PERSON><PERSON> pristat<PERSON>o greitis"}, "sbi_alibaba_cn_kj_qtfy": {"message": "kitas mokestis"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Kitos išlaidos = apskaičiuota kaina x kitų sąnaudų santykis"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "Vieta"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Pirkėjai"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Rinkiniai"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "kaina"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "<PERSON><PERSON><PERSON> par<PERSON>i"}, "sbi_aliprice_alibaba_cn__moren": {"message": "numaty<PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Apatiniai d<PERSON>užiai"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Pried<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Kit<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Striukė"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronika"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagažas"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "batų"}, "sbi_aliprice_cate__apparel": {"message": "Drab<PERSON>ž<PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automobiliai ir motociklai"}, "sbi_aliprice_cate__beauty_health": {"message": "<PERSON><PERSON><PERSON><PERSON> ir s<PERSON>ika<PERSON>"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobilieji telefonai ir telekomunikacijos"}, "sbi_aliprice_cate__computer_office": {"message": "Kompiuteriai ir biuras"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Vartotojų elektronika"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Švietimas ir ra<PERSON><PERSON>"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektroniniai komponentai ir reikmenys"}, "sbi_aliprice_cate__furniture": {"message": "Baldai"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Plaukų priauginimas ir perukai"}, "sbi_aliprice_cate__home_garden": {"message": "Namai ir sodas"}, "sbi_aliprice_cate__home_improvement": {"message": "Namų gerinimas"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir a<PERSON>"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagažas ir krepšiai"}, "sbi_aliprice_cate__mother_kids": {"message": "<PERSON><PERSON> ir vaikai"}, "sbi_aliprice_cate__novelty_special_use": {"message": "<PERSON><PERSON><PERSON><PERSON> ir specialus naudo<PERSON>s"}, "sbi_aliprice_cate__security_protection": {"message": "<PERSON><PERSON><PERSON><PERSON> ir a<PERSON><PERSON>ga"}, "sbi_aliprice_cate__shoes": {"message": "Avalynė"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sportas ir pramogos"}, "sbi_aliprice_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON> ir pom<PERSON>"}, "sbi_aliprice_cate__watches": {"message": "Laikrodžiai"}, "sbi_aliprice_cate__weddings_events": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_button__chat_with_me": {"message": "Kalbėk su manimi"}, "sbi_button__contact_supplier": {"message": "kontaktas"}, "sbi_button__hide_on_this_site": {"message": "Nerodyti š<PERSON> s<PERSON>"}, "sbi_button__open_settings": {"message": "Konfigūruokite paiešką pagal vaizdą"}, "sbi_capture_shortcut_tip": {"message": "arba paspauskite klaviatūros klavišą „Enter“"}, "sbi_capturing_tip": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_composed_rating_45": {"message": "4,5–5,0 žvaigždutės"}, "sbi_crop_and_search": {"message": "Pa<PERSON>š<PERSON>"}, "sbi_crop_start": {"message": "Naudoti ekrano kopiją"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON>, įkelkite n<PERSON><PERSON><PERSON>, kad pat<PERSON><PERSON>. (Peržiūrėkite $video_tutorial$ arba pabandykite išvalyti slapukus)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Neįprastas srautas, patik<PERSON><PERSON>te"}, "sbi_err_captcha_for_taobao": {"message": "Taobao prašo jūsų patvirtinti. Prašome neautomatiškai įkelti paveikslėlį ir ieškoti, kad jį patvirtintum<PERSON>te. <PERSON><PERSON> klaida atsirado dėl naujos „TaoBao“ paieškos pagal vaizdą “politikos, todėl rekomenduoja<PERSON> skundą„ Taobao $feedback$ “patikrinti per dažnai.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "Atsiliepi<PERSON>"}, "sbi_err_captcha_msg": {"message": "$platform$ re<PERSON><PERSON><PERSON>, kad įkeltumėte vaizd<PERSON>, kad gal<PERSON> i<PERSON>škoti, arba atlikite saugos patikrinimą, kad pa<PERSON>lint<PERSON>te paieškos apribojimus", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ar tai naujausia versija"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON>š<PERSON><PERSON><PERSON>"}, "sbi_err_cookie_for_alibaba_cn": {"message": "<PERSON>švalyti 1688 slapukus? (Reikia prisijungti dar kartą)"}, "sbi_err_desperate_feature_pdd": {"message": "Vaizdų paieškos funkcija buvo perkelta į „Pinduoduo Search by Image“ plėtinį."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "<PERSON><PERSON> vaizdų paieškos sėkmės rodiklį?"}, "sbi_err_img_undersize": {"message": "Vaizdas > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Prisijunkite $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_low_version": {"message": "Įdiekite naujausią versiją ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "parsisiųsti"}, "sbi_err_need_help": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON>, įsitikinkite, kad galite aps<PERSON> svet<PERSON>"}, "sbi_err_not_low_version": {"message": "Įdiegta naujausia versija ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Bandyk iš naujo"}, "sbi_err_try_again_action": {"message": "Bandyk iš naujo"}, "sbi_err_visit_and_try": {"message": "Bandykite dar kartą arba apsilankykite $website$ ir bandykite dar kartą", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Apsilankykite $siteName$ pagrindiniame puslapyje", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Įkelti ne<PERSON><PERSON><PERSON>, atnaujinkite puslapį ir bandykite dar kartą."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australija"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "Vokietija"}, "sbi_kuajing_filter_destination_country": {"message": "Paskirties <PERSON>"}, "sbi_kuajing_filter_es": {"message": "Ispanija"}, "sbi_kuajing_filter_estimate": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON> kaina"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Numatomos kainos formul<PERSON> = (preki<PERSON> kaina + tarptautinis logistikos krovinys)/(1 - pelno marža - kitų sąnaudų santykis)"}, "sbi_kuajing_filter_fr": {"message": "Prancūzija"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Įveskite r<PERSON><PERSON><PERSON>, kurie atitiktų pavadinimą"}, "sbi_kuajing_filter_logistics": {"message": "Logistikos šablonas"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Kinijos pašto oro paštas"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistikos nuolaida"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Tarptautiniai logistikos kroviniai = (svoris x siuntimo kaina + registracijos mokestis) x (1 - nuolaida)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON> m<PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "Pelno marža"}, "sbi_kuajing_filter_prop": {"message": "Atributai"}, "sbi_kuajing_filter_ru": {"message": "R<PERSON><PERSON>"}, "sbi_kuajing_filter_total": {"message": "Suderinkite $count$ panašius elementus", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "JK"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "Re<PERSON><PERSON><PERSON>,prisijunkite prie $loginSite$ arba išbandykite kitą paveikslėlį", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Laikinai negalima naudoti „Safari“, naudokite $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "„Chrome“ narš<PERSON>lė ir jos plė<PERSON>i"}, "sbi_msg_no_result_reinstall_l1": {"message": "Rezultatų nerasta, prisijunkite prie $loginSite$ arba išbandykite kitą paveikslėlį arba iš naujo įdiekite naujausią $latestExtUrl$ versiją", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Naujausia versija", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "<PERSON><PERSON><PERSON><PERSON> sritis"}, "sbi_shipping_": {"message": "Siuntimas tą pačią dieną"}, "sbi_specify_category": {"message": "Nurodykite kategorij<PERSON>:"}, "sbi_start_crop": {"message": "Pasirinkite sritį"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 užjūrio"}, "sbi_tutorial_btn_more": {"message": "Daug<PERSON><PERSON> būd<PERSON>"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Raskite Taobao kuponus"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON>, rezulta<PERSON><PERSON> nerasta, bandykite dar kartą."}, "sbi_txt__min_order": {"message": "Min. įsakymas"}, "sbi_visiting": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON><PERSON><PERSON> ka<PERSON> k<PERSON> į pardavėją"}, "sbi_yiwugo__qigou": {"message": "$num$ Pieces (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Ž<PERSON>igž<PERSON><PERSON><PERSON>"}, "searchByImage_screenshot": {"message": "Ekrano kopija vienu s<PERSON>telėjimu"}, "searchByImage_search": {"message": "Vienu paspaudimu ieškokite tų pačių elementų"}, "searchByImage_size_type": {"message": "Failo dydis negali būti didesnis nei $num$ MB, tik $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaizda<PERSON>"}, "search_by_image_progress_searching": {"message": "Ieškokite produktų"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai<PERSON>"}, "search_by_image_response_rate": {"message": "Atsakymo lygis: $responseRate$ pirkėjų, kurie susisiekė su šiuo tiekėju, atsakymą gavo per $responseInHour$ valandas.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Ieškoti pagal raktinį žodį:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Kalba"}, "select_country_region_modal_title": {"message": "Pasirinkite šalį / regioną"}, "select_language_modal_title": {"message": "Pasirinkite kalbą:"}, "select_shop": {"message": "Pasirinkite parduotuvę"}, "sellers_count": {"message": "Pardavė<PERSON><PERSON> skaičius dabartiniame puslapyje"}, "sellers_count_per_page": {"message": "Pardavė<PERSON><PERSON> skaičius dabartiniame puslapyje"}, "service_score": {"message": "<PERSON><PERSON><PERSON><PERSON> įvertinimas"}, "set_shortcut_keys": {"message": "Nustatykite spar<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_position_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>"}, "setting_modal_options_theme_title": {"message": "Temos spalva"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Šviesa"}, "setting_modal_title": {"message": "Nustatymai"}, "setting_options_country_title": {"message": "Šalis / regionas"}, "setting_options_hover_zoom_desc": {"message": "Nor<PERSON><PERSON><PERSON>, užveskite pelės žymeklį"}, "setting_options_hover_zoom_title": {"message": "Užveskite pelės žymeklį"}, "setting_options_jd_coupon_desc": {"message": "Rasti k<PERSON>ą JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com kuponas"}, "setting_options_language_title": {"message": "Kalba"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON> <PERSON>“ produktų kaina nukris, gausite tiesioginį pranešim<PERSON>."}, "setting_options_price_drop_alert_title": {"message": "Kainos kritimo įspėjimas"}, "setting_options_price_history_on_list_page_desc": {"message": "Rodyti kainų istoriją produktų paieškos puslapyje"}, "setting_options_price_history_on_list_page_title": {"message": "Kainų istorija (sąraš<PERSON>)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Rodyti produkto istoriją produkto išsamios informacijos puslapyje"}, "setting_options_price_history_on_produt_page_title": {"message": "Kainų istorija (išsamios informacijos puslapis)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ produktų sąrašo puslapyje palaikykite kainų, pardavimo apimties, pardavė<PERSON><PERSON> skaičiaus ir parduotuvės pardavimo santykio statistiką", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Pardavimų analizė"}, "setting_options_save_success_msg": {"message": "Sėkmė"}, "setting_options_tacking_price_title": {"message": "Kainos pasikeitimo įspėjimas"}, "setting_options_value_off": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_options_value_on": {"message": "Įjungta"}, "setting_pkg_quick_view_desc": {"message": "Palaikymas: 1688 ir <PERSON>"}, "setting_saved_message": {"message": "Pakeitimai sėkmingai išsaugoti"}, "setting_section_enable_platform_title": {"message": "Įjungti išjungti"}, "setting_section_setting_title": {"message": "Nustatymai"}, "setting_section_shortcuts_title": {"message": "Spartieji k<PERSON>šai"}, "settings_aliprice_agent__desc": {"message": "Rodoma $platforms$ išsamios produkto informacijos puslapyje", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Pirk<PERSON> u<PERSON>e"}, "settings_copy_link__desc": {"message": "Rodyti išsamios produkto informacijos puslapyje"}, "settings_copy_link__title": {"message": "<PERSON><PERSON><PERSON><PERSON> ir Paieškos pavadinimas"}, "settings_currency_desc__for_detail": {"message": "Palaikykite išsamią 1688 produkto puslapį"}, "settings_currency_desc__for_list": {"message": "<PERSON>eškoti pagal paveikslėlį (įskaitant 1688/1688 užj<PERSON>rio <PERSON> / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Pasirinkite kainą"}, "settings_currency_desc_display_for_list": {"message": "Rodoma vaizdų paie<PERSON>je (įskaitant 1688/1688 užsienyje / Taobao)"}, "settings_currency_rate_desc": {"message": "Valiutos kursas atnaujinamas nuo \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Kinijos bankas"}, "settings_download_images__desc": {"message": "Vaizdų atsisiuntimo iš $platforms$ palaikymas", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "mygt<PERSON>ą atsisiųsti paveikslėlį"}, "settings_download_reviews__desc": {"message": "Rodoma $platforms$ išsamios produkto informacijos puslapyje", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Atsisiųskite apžvalgos paveikslėlius"}, "settings_google_translate_desc": {"message": "Dešiniuo<PERSON> pelės my<PERSON> s<PERSON>, jei norite gauti „Google“ vertimo juostą"}, "settings_google_translate_title": {"message": "tinklalapio vertimas"}, "settings_historical_trend_desc": {"message": "Rodyti gaminių sąrašo puslapio apatiniame dešiniajame vaizdo kampe"}, "settings_modal_btn_more": {"message": "Daugiau nustatymų"}, "settings_productInfo_desc": {"message": "Rodyti išsamesnę informaciją apie gaminį produktų sąrašo puslapyje. Įjungus tai gali padidėti kompiuterio apkrova ir atsirasti puslapio delsa. Jei tai turi įtakos našumui, rekomenduojama jį išjungti."}, "settings_product_recommend__desc": {"message": "Rodoma po pagrindiniu vaizdu $platforms$ produkto informacijos puslapyje", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Rekomenduojami produktai"}, "settings_research_desc": {"message": "Išsamesnės informacijos teiraukitės produktų sąrašo pusla<PERSON>je"}, "settings_sbi_add_to_list": {"message": "Pridėkite prie $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Vaizdo paieškos rezultatų miniatiūra"}, "settings_sbi_remove_from_list": {"message": "Pašalinti iš $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Pridėti prie blokų sąrašo"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Sureguliuokite įėjimo padėtį"}, "settings_search_by_image_blacklist_desc": {"message": "Nerodyti piktogramos juodajame sąraše esančiose svetainėse."}, "settings_search_by_image_blacklist_title": {"message": "Užblokuotų sąrašas"}, "settings_search_by_image_bottom_left": {"message": "Apačioje ka<PERSON>"}, "settings_search_by_image_bottom_right": {"message": "Apačioje <PERSON>"}, "settings_search_by_image_clear_blacklist": {"message": "Išvalyti blokų sąrašą"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatiūra"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Jei norite ieškoti pagal vaizdą, spustelėkite vieną piktogramą"}, "settings_search_by_image_display_button_title": {"message": "Paveikslėlių piktograma"}, "settings_search_by_image_sourece_websites_desc": {"message": "Šiuose tinklalapiuose raskite pirminį produktą"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON>š<PERSON> pagal vaizdo rezultatus"}, "settings_search_by_image_top_left": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_keyword_on_x__desc": {"message": "Ieškokite žodžių $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Rodyti $platform$ piktogramą, kai pasirenkami žodžiai", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Pabandykite tose svetainėse rasti tą patį produktą (daugiausia iki 5)"}, "settings_similar_products_title": {"message": "Raskite tą patį produktą"}, "settings_toolbar_expand_title": {"message": "Papildinių sumažinimas"}, "settings_top_toolbar_desc": {"message": "Paieškos juosta puslapio viršuje"}, "settings_top_toolbar_title": {"message": "<PERSON><PERSON><PERSON><PERSON> juo<PERSON>"}, "settings_translate_search_desc": {"message": "Išverskite į kinų kalbą ir ieškokite"}, "settings_translate_search_title": {"message": "Daugiakalbė paieška"}, "settings_translator_contextmenu_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON>tumė<PERSON>"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Filtruoti į eksportavimą"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>"}, "shang_jia_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>"}, "shang_pin_biao_ti": {"message": "produkto pavadinimas"}, "shang_pin_dui_bi": {"message": "Produktų palyginimas"}, "shang_pin_lian_jie": {"message": "produkto nuoroda"}, "shang_pin_xin_xi": {"message": "Prekės informacija"}, "share_modal__content": {"message": "Pasidalinkite su draugais"}, "share_modal__disable_for_while": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "share_modal__title": {"message": "Ar jums patinka $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Likęs"}, "shi_fou_ke_ding_zhi": {"message": "Ar galima pritaik<PERSON>i?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Sertifikuota<PERSON>"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Sertifikuoti tiekėjai"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Prekybos užtikrinimas"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Prekybos garantijos"}, "shipping_fee": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shou_qi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Daugiausiai iki 5"}, "sku_calc_price": {"message": "Prezzo calcolato"}, "sku_calc_price_settings": {"message": "Impostazioni prezzo calcolato"}, "sku_formula": {"message": "Formula"}, "sku_formula_desc": {"message": "Descrizione formula"}, "sku_formula_desc_text": {"message": "<PERSON><PERSON><PERSON> sud<PERSON>as matematines formules, kur pradinė kaina žymima A, o krovinio kaina – B.\n\n<br/>\n\n<PERSON><PERSON><PERSON> (), pliuso +, minuso -, daugybą * ir dalybą /\n\n<br/>\n\nPavyzdys:\n\n<br/>\n\n1. <PERSON><PERSON> gauti 1,2 karto didesnę už pradinę kainą ir pridėti krovinio kainą, formulė yra: A*1,2+B\n\n<br/>\n\n2. <PERSON>int gauti pradinę kainą plius 1 juanį, tada padauginti iš 1,2 karto, formulė yra: (A+1)*1,2\n\n<br/>\n\n3. <PERSON><PERSON> gauti pradinę kainą plius 10 juanių, tada padauginti iš 1,2 karto ir tada atimti 3 juanius, formulė yra: (A+10)*1,2-3"}, "sku_in_stock": {"message": "Disponibile"}, "sku_invalid_formula_format": {"message": "Formato formula non valido"}, "sku_inventory": {"message": "Inventario"}, "sku_link_copy_fail": {"message": "Nukopijuota sė<PERSON>mingai, SKU specifikacijos ir atributai nepasirinkti"}, "sku_link_copy_success": {"message": "Nukopijuota sėkmingai, pasirinktos SKU specifikacijos ir atributai"}, "sku_list": {"message": "Elenco SKU"}, "sku_min_qrder_qty": {"message": "Quantità minima ordinabile"}, "sku_name": {"message": "Nome SKU"}, "sku_no": {"message": "N."}, "sku_original_price": {"message": "Prezzo originale"}, "sku_price": {"message": "Prezzo SKU"}, "stop_track_time_label": {"message": "Stebėjimo terminas:"}, "suo_zai_di_qu": {"message": "vieta"}, "tab_pkg_quick_view": {"message": "<PERSON>gis<PERSON><PERSON>"}, "tab_product_details_price_history": {"message": "Kainų istorija"}, "tab_product_details_reviews": {"message": "Nuotraukų apžvalgos"}, "tab_product_details_seller_analysis": {"message": "Pardavėjo analizė"}, "tab_product_details_similar_products": {"message": "<PERSON>ie patys produktai"}, "total_days_listed_per_product": {"message": "Sandėliavimo dienų suma ÷ Produktų skaičius"}, "total_items": {"message": "Bendras gaminių skaičius"}, "total_price_per_product": {"message": "Kainų suma ÷ Prekių skaičius"}, "total_rating_per_product": {"message": "Įvertinimų suma ÷ Prekių skaičius"}, "total_revenue": {"message": "<PERSON><PERSON> p<PERSON>"}, "total_revenue40_items": {"message": "Bendros pajamos iš $amount$ produktų dabartiniame puslapyje", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON> pardavimas"}, "total_sales40_items": {"message": "<PERSON>š viso parduota $amount$ produktų dabartiniame puslapyje", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Trasa: 1 metams"}, "track_for_3_months": {"message": "Trasa: 3 mėnesiams"}, "track_for_6_months": {"message": "Trasa: 6 mėn"}, "tracking_price_email_add_btn": {"message": "Pridėti el"}, "tracking_price_email_edit_btn": {"message": "Redaguoti el. <PERSON>ą"}, "tracking_price_email_intro": {"message": "Mes jums apie tai p<PERSON>šime el. <PERSON>."}, "tracking_price_email_invalid": {"message": "Pateikite galiojantį el. <PERSON>š<PERSON> ad<PERSON>"}, "tracking_price_email_verified_desc": {"message": "Dabar galite gauti įspėjimą apie kainų kritimą."}, "tracking_price_email_verified_title": {"message": "Sėkmingai pat<PERSON>"}, "tracking_price_email_verify_desc_line1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> patvir<PERSON> nuo<PERSON> jūsų el. <PERSON><PERSON><PERSON>,"}, "tracking_price_email_verify_desc_line2": {"message": "patikrinkite savo el. pašto d<PERSON>."}, "tracking_price_email_verify_title": {"message": "Patvirtinkite elektroninį paštą"}, "tracking_price_web_push_notification_intro": {"message": "Darbalaukyje: „AliPrice“ gali stebėti bet kurį produktą už jus ir išsiųsti žiniatinklio praneš<PERSON> p<PERSON>, kai kaina pasi<PERSON>."}, "tracking_price_web_push_notification_title": {"message": "Pranešimai apie žiniatinklį"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON><PERSON>, prisijunkite prie $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Išversta ir nukopijuota į mainų sritį, tačia<PERSON> dėl Aliwangwang apribojimų turite jį įklijuoti rankiniu būdu!"}, "translate_im__send": {"message": "<PERSON><PERSON><PERSON> ir si<PERSON>i"}, "translate_search": {"message": "Išversti ir ieškoti"}, "translation_originals_translated": {"message": "Originalus ir kin<PERSON>"}, "translation_translated": {"message": "kinų"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Automatinis a<PERSON>"}, "translator_language_detected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_search_placeholder": {"message": "Paieškos kalba"}, "try_again": {"message": "Bandyk iš naujo"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON><PERSON>:"}, "tu_pian_lian_jie": {"message": "<PERSON><PERSON><PERSON><PERSON> nuo<PERSON>a"}, "tui_huan_ti_yan": {"message": "Grįžti patirtis"}, "tui_huan_ti_yan__desc": {"message": "Įvertinkite pardavėjų po pardavimo rod<PERSON>"}, "tutorial__show_all": {"message": "Visos <PERSON>"}, "tutorial_ae_popup_title": {"message": "Prisekite plėtinį, atidarykite „Aliexpress“."}, "tutorial_aliexpress_reviews_analysis": {"message": "„AliExpress“ apžvalgos analizė"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Palaikykite USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Siuntimas į Korėją / Japoniją / ž<PERSON><PERSON><PERSON><PERSON>"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 palaiko pirkimą užsienyje"}, "tutorial_auto_apply_coupon_title": {"message": "Automatiškai pritaikyti kuponą"}, "tutorial_btn_end": {"message": "Galas"}, "tutorial_btn_example": {"message": "Pa<PERSON><PERSON><PERSON><PERSON>"}, "tutorial_btn_see_more": {"message": "Daugiau"}, "tutorial_compare_products": {"message": "<PERSON><PERSON><PERSON><PERSON> produktus"}, "tutorial_currency_convert_title": {"message": "Valiutos keit<PERSON>"}, "tutorial_export_shopping_cart": {"message": "Eksportuokite kaip CSV, palaikykite Taobao ir 1688"}, "tutorial_export_shopping_cart_title": {"message": "Eksporto krepšelis"}, "tutorial_price_history_pro": {"message": "Rod<PERSON>s produkto informacijos puslapyje.\nPalaikykite „Shopee“, „Lazada“, „Amazon“, „Ebay“."}, "tutorial_price_history_pro_title": {"message": "Visi metai Kainų istorija ir užsakymų istorija"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON> ieškoti pagal vaizdą"}, "tutorial_translate_search": {"message": "Išversti į paiešką"}, "tutorial_translate_search_and_package_tracking": {"message": "Vertimų paieška ir siuntų sekimas"}, "unit_bao": {"message": "vnt"}, "unit_ben": {"message": "vnt"}, "unit_bi": {"message": "įsakymus"}, "unit_chuang": {"message": "vnt"}, "unit_dai": {"message": "vnt"}, "unit_dui": {"message": "pora"}, "unit_fen": {"message": "vnt"}, "unit_ge": {"message": "vnt"}, "unit_he": {"message": "vnt"}, "unit_jian": {"message": "vnt"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "vnt"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "pora"}, "unit_tai": {"message": "vnt"}, "unit_ti": {"message": "vnt"}, "unit_tiao": {"message": "vnt"}, "unit_xiang": {"message": "vnt"}, "unit_zhang": {"message": "vnt"}, "unit_zhi": {"message": "vnt"}, "verify_contact_support": {"message": "Susisiekite su palaikymo tarnyba"}, "verify_human_verification": {"message": "Žmo<PERSON><PERSON>"}, "verify_unusual_access": {"message": "Aptikta neįprasta prieiga"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> visk<PERSON>"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus peržiūr<PERSON>tus įrašus?"}, "view_history_clean_all_warring_title": {"message": "Įspėjimas"}, "view_history_viewd": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "website": {"message": "svet<PERSON><PERSON>"}, "weight": {"message": "<PERSON><PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Nepavyko gauti duomenų"}, "wu_liu_shi_xiao": {"message": "Siuntimas laiku"}, "wu_liu_shi_xiao__desc": {"message": "Pardavėjo parduotuvės 48 valandų surinkimo ir įvykdymo rodiklis"}, "xia_dan_jia": {"message": "Galutin<PERSON> kaina"}, "xian_xuan_ze_product_attributes": {"message": "Pasirinkite produkto atributus"}, "xiao_liang": {"message": "Pardavimų apimtis"}, "xiao_liang_zhan_bi": {"message": "Pardavimo apimties procentas"}, "xiao_shi": {"message": "$num$ valandos", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Pajamos"}, "xiao_shou_e_zhan_bi": {"message": "Pajamų procentas"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Pasirinkite $amount$ įrašus", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Pasirinkimas"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Visi produktai įkelti"}, "yi_nian_xiao_liang": {"message": "Metiniai parda<PERSON>i"}, "yi_nian_xiao_liang_zhan_bi": {"message": "<PERSON><PERSON><PERSON> par<PERSON>"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yi_shua_xin": {"message": "Atsinaujino"}, "yin_cang_xiang_tong_dian": {"message": "slė<PERSON>i pan<PERSON>š<PERSON>us"}, "you_xiao_liang": {"message": "Su pardavimų apimtimi"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> laikas"}, "yuan_gong_ren_shu": {"message": "Darbuotoj<PERSON> s<PERSON>"}, "yue_cheng_jiao": {"message": "M<PERSON><PERSON><PERSON>"}, "yue_dai_xiao": {"message": "Dropshippingas"}, "yue_dai_xiao__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> per paskutines 30 dienų"}, "yue_dai_xiao_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> per pastarąsias 30 dienų, sur<PERSON><PERSON><PERSON><PERSON><PERSON> nuo didžiausios iki mažia<PERSON>ios"}, "yue_xiao_liang__desc": {"message": "Pardavimo apimtis per pastarąsias 30 dienų"}, "zhan_kai": {"message": "Daugiau"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshippingas"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON>emoka<PERSON> pristat<PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "Apmokėti užsakymai"}, "zhi_fu_ding_dan_shu__desc": {"message": "Šios prekės <PERSON> s<PERSON> (30 dienų)"}, "zhu_ce_xing_zhi": {"message": "Regis<PERSON>ci<PERSON>"}, "zi_ding_yi_tiao_jian": {"message": "Individualizuo<PERSON>"}, "zi_duan": {"message": "Laukai"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON> parduotas"}, "zong_he_fu_wu_fen": {"message": "Bendras įvertinimas"}, "zong_he_fu_wu_fen__desc": {"message": "Bendras pardavėjo paslaugų įvertinimas"}, "zong_he_fu_wu_fen__short": {"message": "Įvertinimas"}, "zong_he_ti_yan_fen": {"message": "Įvertinimas"}, "zong_he_ti_yan_fen_3": {"message": "Žemiau 4 žvaigždutės"}, "zong_he_ti_yan_fen_4": {"message": "4–4,5 žvaigždutės"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5–5,0 žvaigždutės"}, "zong_he_ti_yan_fen_5": {"message": "5 žvaigždutės"}, "zong_ku_cun": {"message": "Visas inventorius"}, "zong_xiao_liang": {"message": "<PERSON><PERSON> pardavimas"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3 minučių atsakymų dažnis per pastarąsias 30 dienų"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48 valandų atkūrimo greitis per pastarąsias 30 dienų"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48 valandų našumo rodiklis per pastarąsias 30 dienų"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Prekybos rekordas (30 dienų)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Prekybos rekordas (30 dienų)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Ginčų rodiklis per pastarąsias 30 dienų"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Kokybės grąžinimo norma per pastarąsias 30 dienų"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Mokėjimo nurodymų skaičius per pastarąsias 30 dienų"}}