{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 points de vente transfrontaliers à succès"}, "1688_shi_li_ren_zheng": {"message": "Certification de résistance 1688"}, "1_jian_qi_pi": {"message": "MOQ : 1"}, "1year_yi_shang": {"message": "Plus d'1 an"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Livraison sous 24 heures"}, "24H_lan_shou_lv": {"message": "Ta<PERSON>f emballage 24h"}, "30D_shang_xin": {"message": "Nouveautés mensuelles"}, "30d_sales": {"message": "$amount$ vendu en 30 jours", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Réponse dans les 3 minutes."}, "3Min_xiang_ying_lv__desc": {"message": "La proportion de réponses efficaces de Wangwang aux messages de demande d'acheteur dans les 3 minutes au cours des 30 derniers jours"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Livraison sous 48 heures"}, "48H_lan_shou_lv": {"message": "<PERSON><PERSON><PERSON> emballage 48 heures"}, "48H_lan_shou_lv__desc": {"message": "Rapport entre le numéro de commande récupérée dans les 48 heures et le nombre total de commandes"}, "48H_lv_yue_lv": {"message": "Taux de performance sur 48 heures"}, "48H_lv_yue_lv__desc": {"message": "Ratio du nombre de commandes récupérées ou livrées sous 48 heures sur le nombre total de commandes"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Nouveautés hebdomadaires"}, "7D_wu_li_you": {"message": "7 jours sans soins"}, "ABS_title_text": {"message": "<PERSON>tte annonce comprend une histoire de marque"}, "AC_title_text": {"message": "Cette annonce comporte le badge Amazon's Choice"}, "A_title_text": {"message": "Cette annonce comporte une page de contenu A+"}, "BS_title_text": {"message": "Cette annonce est classée comme le $num$ Best Seller dans la catégorie $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) signifie que cette annonce fait partie d'un événement de « promotion de 7 jours »"}, "NR_title_text": {"message": "Cette annonce est classée comme la $num$ New Release dans la catégorie $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Cette annonce comporte une publicité vidéo, un type d'annonce PPC qui apparaît généralement au milieu des résultats de recherche"}, "SB_title_text": {"message": "Cette annonce comporte une publicité de marque, un type d'annonce PPC qui apparaît généralement en haut ou en bas des résultats de recherche"}, "SP_title_text": {"message": "Cette annonce comporte une publicité de produit sponsorisé"}, "V_title_text": {"message": "Cette annonce comporte une introduction vidéo"}, "advanced_research": {"message": "Recherche avancée"}, "agent_ds1688___my_order": {"message": "<PERSON><PERSON> commandes"}, "agent_ds1688__add_to_cart": {"message": "Achat à l'étranger"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON>"}, "agent_ds1688__desc": {"message": "Fourni par 1688. Il prend en charge l'achat direct depuis l'étranger, le paiement en USD et la livraison à votre entrepôt de transit en Chine."}, "agent_ds1688__freight": {"message": "Calculateur de frais d'expédition"}, "agent_ds1688__help": {"message": "Aide"}, "agent_ds1688__packages": {"message": "Lettre de transport"}, "agent_ds1688__profile": {"message": "Centre personnel"}, "agent_ds1688__warehouse": {"message": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Avantages"}, "ai_comment_analysis_ai": {"message": "Analyse des avis par l'IA"}, "ai_comment_analysis_available": {"message": "Disponible"}, "ai_comment_analysis_balance": {"message": "Pièces insuffisantes, veuillez recharger"}, "ai_comment_analysis_behavior": {"message": "Comportement"}, "ai_comment_analysis_characteristic": {"message": "Caractéristiques de la foule"}, "ai_comment_analysis_comment": {"message": "Le produit n'a pas assez d'avis pour tirer des conclusions précises, veuillez sélectionner un produit avec plus d'avis."}, "ai_comment_analysis_consume": {"message": "Consommation estimée"}, "ai_comment_analysis_default": {"message": "Avis par défaut"}, "ai_comment_analysis_desire": {"message": "Attentes des clients"}, "ai_comment_analysis_disadvantage": {"message": "Inconvénients"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON> gratuits"}, "ai_comment_analysis_freeNum": {"message": "1 crédit gratuit sera utilisé"}, "ai_comment_analysis_go_recharge": {"message": "Accéder à la recharge"}, "ai_comment_analysis_intelligence": {"message": "Analyse intelligente des avis"}, "ai_comment_analysis_location": {"message": "Localisation"}, "ai_comment_analysis_motive": {"message": "Motivation d'achat"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON> rées<PERSON>er"}, "ai_comment_analysis_normal": {"message": "<PERSON><PERSON> photo"}, "ai_comment_analysis_number_reviews": {"message": "Nombre d'avis : $num$, Consommation estimée : $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Commentaires généraux"}, "ai_comment_analysis_percentage": {"message": "Pourcentage"}, "ai_comment_analysis_problem": {"message": "Problèmes de paiement"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "<PERSON>son"}, "ai_comment_analysis_recharge": {"message": "Recharger"}, "ai_comment_analysis_recharged": {"message": "J'ai rechargé"}, "ai_comment_analysis_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_scene": {"message": "Scénario d'utilisation"}, "ai_comment_analysis_start": {"message": "Commencer l'analyse"}, "ai_comment_analysis_subject": {"message": "Sujets"}, "ai_comment_analysis_time": {"message": "Heure d'utilisation"}, "ai_comment_analysis_tool": {"message": "<PERSON>il d'<PERSON>"}, "ai_comment_analysis_user_portrait": {"message": "Profil utilisateur"}, "ai_comment_analysis_welcome": {"message": "Bienvenue dans l'analyse des avis par l'IA"}, "ai_comment_analysis_year": {"message": "Commentaires de l'année écoulée"}, "ai_listing_Exclude_keywords": {"message": "Exclure des mots clés"}, "ai_listing_Login_the_feature": {"message": "La connexion est requise pour la fonctionnalité"}, "ai_listing_aI_generation": {"message": "Génération d'IA"}, "ai_listing_add_automatic": {"message": "Automatique"}, "ai_listing_add_dictionary_new": {"message": "Créer une nouvelle bibliothèque"}, "ai_listing_add_enter_keywords": {"message": "Entrez des mots-clés"}, "ai_listing_add_inputkey_selling": {"message": "Entrez un argument de vente et appuyez sur $key$ pour terminer l'ajout", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limite d<PERSON>, jusqu'à $amount$ points de vente", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Ajouter des mots-clés"}, "ai_listing_add_manually": {"message": "Ajouter manuellement"}, "ai_listing_add_selling": {"message": "Ajouter des arguments de vente"}, "ai_listing_added_keywords": {"message": "Mots-clés a<PERSON>"}, "ai_listing_added_successfully": {"message": "Ajouté avec succès"}, "ai_listing_addexcluded_keywords": {"message": "Entrez les mots-clés exclus, appuyez sur Entrée pour terminer l'ajout."}, "ai_listing_adding_selling": {"message": "Points de vente ajoutés"}, "ai_listing_addkeyword_enter": {"message": "Ta<PERSON>z les mots d'attribut clé et appuyez sur Entrée pour terminer l'ajout"}, "ai_listing_ai_description": {"message": "Bibliothèque de mots de description de l'IA"}, "ai_listing_ai_dictionary": {"message": "Bibliothèque de mots de titre AI"}, "ai_listing_ai_title": {"message": "Titre IA"}, "ai_listing_aidescription_repeated": {"message": "Le nom de la bibliothèque de mots de description AI ne peut pas être répété"}, "ai_listing_aititle_repeated": {"message": "Le nom de la bibliothèque de mots de titre AI ne peut pas être répété"}, "ai_listing_data_comes_from": {"message": "Ces données proviennent de :"}, "ai_listing_deleted_successfully": {"message": "Supprimé avec succès"}, "ai_listing_dictionary_name": {"message": "Nom de la bibliothèque"}, "ai_listing_edit_dictionary": {"message": "Modifier la bibliothèque..."}, "ai_listing_edit_word_library": {"message": "Modifier la bibliothèque de mots"}, "ai_listing_enter_keywords": {"message": "Saisissez des mots-clés et appuyez sur $key$ pour terminer l'ajout.", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "La limite a été dépassée. Nombre maximum de $amount$ mots clés", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Bibliothèque de mots exclus"}, "ai_listing_generate_characters": {"message": "Générer des personnages"}, "ai_listing_generation_platform": {"message": "Plateforme de génération"}, "ai_listing_help_optimize": {"message": "Aidez-moi à optimiser le titre du produit, le titre original est"}, "ai_listing_include_selling": {"message": "Autres arguments de vente inclus :"}, "ai_listing_included_keyword": {"message": "Mots-clés inclus"}, "ai_listing_included_keywords": {"message": "Mots-clés inclus"}, "ai_listing_input_selling": {"message": "Entrez un argument de vente"}, "ai_listing_input_selling_fit": {"message": "Entrez les arguments de vente correspondant au titre"}, "ai_listing_input_selling_please": {"message": "Veuillez saisir les arguments de vente"}, "ai_listing_intelligently_title": {"message": "Saisissez le contenu requis ci-dessus pour générer le titre intelligemment"}, "ai_listing_keyword_product_title": {"message": "Titre du produit par mot clé"}, "ai_listing_keywords_repeated": {"message": "Les mots clés ne peuvent pas être répétés"}, "ai_listing_listed_selling_points": {"message": "Arguments de vente inclus"}, "ai_listing_long_title_1": {"message": "Contient des informations de base telles que le nom de la marque, le type de produit, les caractéristiques du produit, etc."}, "ai_listing_long_title_2": {"message": "Sur la base du titre standard du produit, des mots-clés propices au référencement sont ajoutés."}, "ai_listing_long_title_3": {"message": "En plus de contenir le nom de la marque, le type de produit, les caractéristiques du produit et les mots-clés, des mots-clés à longue traîne sont également inclus pour obtenir un classement plus élevé dans les requêtes de recherche spécifiques et segmentées."}, "ai_listing_longtail_keyword_product_title": {"message": "Titre du produit par mot clé à longue traîne"}, "ai_listing_manually_enter": {"message": "Entrez manuellement..."}, "ai_listing_network_not_working": {"message": "Internet n'est pas disponible, un VPN est requis pour accéder à ChatGPT"}, "ai_listing_new_dictionary": {"message": "Créez une nouvelle bibliothèque de mots..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Mots facultatifs"}, "ai_listing_original_title": {"message": "Titre original"}, "ai_listing_other_keywords_included": {"message": "Autres mots-clés inclus :"}, "ai_listing_please_again": {"message": "<PERSON><PERSON><PERSON>z réessayer"}, "ai_listing_please_select": {"message": "Les titres suivants ont été générés pour vous, ve<PERSON><PERSON><PERSON> sélectionner :"}, "ai_listing_product_category": {"message": "Catégorie de produit"}, "ai_listing_product_category_is": {"message": "La catégorie de produit est"}, "ai_listing_product_category_to": {"message": "À quelle catégorie appartient le produit ?"}, "ai_listing_random_keywords": {"message": "Mots clés aléatoires de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Points de vente aléatoires de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomiser à partir de la bibliothèque de mots"}, "ai_listing_search_selling": {"message": "Recherche par argument de vente"}, "ai_listing_select_product_categories": {"message": "Sélectionnez automatiquement les catégories de produits."}, "ai_listing_select_product_selling_points": {"message": "Sélectionnez automatiquement les arguments de vente des produits"}, "ai_listing_select_word_library": {"message": "Sélectionnez une bibliothèque de mots"}, "ai_listing_selling": {"message": "Points de vente"}, "ai_listing_selling_ask": {"message": "Quelles sont les autres exigences en matière d'arguments de vente pour le titre ?"}, "ai_listing_selling_optional": {"message": "Arguments de vente optionnels"}, "ai_listing_selling_repeat": {"message": "Les points ne peuvent pas être dupliqués"}, "ai_listing_set_excluded": {"message": "Définir comme bibliothèque de mots exclus"}, "ai_listing_set_include_selling_points": {"message": "Inclure des arguments de vente"}, "ai_listing_set_included": {"message": "Définir comme bibliothèque de mots incluse"}, "ai_listing_set_selling_dictionary": {"message": "Définir comme bibliothèque d'arguments de vente"}, "ai_listing_standard_product_title": {"message": "Titre du produit standard"}, "ai_listing_translated_title": {"message": "Titre traduit"}, "ai_listing_visit_chatGPT": {"message": "Visitez ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Quels autres mots-clés sont requis pour le titre ?"}, "aliprice_coupons_apply_again": {"message": "Appliquer à nouveau"}, "aliprice_coupons_apply_coupons": {"message": "Appliquer des coupons"}, "aliprice_coupons_apply_success": {"message": "Bon de réduction trouvé : économisez $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Tester les codes pour les meilleures offres..."}, "aliprice_coupons_applying_desc": {"message": "Départ : $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continuer vers la caisse"}, "aliprice_coupons_found_coupons": {"message": "Nous avons trouvé $amount$ bons de réduction", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Prêt à passer à la caisse ? Assurons-nous d'obtenir le meilleur prix!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Ces codes ne fonctionnaient pas. Pas de problème, vous bénéficiez déjà du meilleur prix."}, "aliprice_coupons_toolbar_btn": {"message": "Obtenez des coupons"}, "aliww_translate": {"message": "Aliwangwang Chat Traducteur"}, "aliww_translate_supports": {"message": "Prise en charge : 1688 et Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Mots-clés"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON> tout"}, "amazon_extended_keywords_more": {"message": "Plus"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Trier par ventes cumulées"}, "an_lei_xing_cha_kan": {"message": "Taper"}, "an_yue_dai_xiao_pai_xu": {"message": "Classement par ventes dropshipping"}, "apra_btn__cat_name": {"message": "Analyse des avis"}, "apra_chart__name": {"message": "Pourcentage des ventes de produits par pays"}, "apra_chart__update_at": {"message": "Heure de mise à jour $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statistiques de ventes des pays"}, "auto_opening": {"message": "Ouverture automatique dans $num$ secondes", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "$autoPaging$ pages suivantes", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Moyenne des jours de conservation"}, "average_hui_fu_lv": {"message": "Taux de réponse moyen"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Niveau moyen des fournisseurs"}, "average_price": {"message": "Prix ​​moyen"}, "average_qi_ding_liang": {"message": "<PERSON><PERSON><PERSON> moyen"}, "average_rating": {"message": "<PERSON> moyenne"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Années moyennes de certification"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON> moyen"}, "average_revenue_per_product": {"message": "Revenu total ÷ Nombre de produits"}, "average_sales": {"message": "<PERSON><PERSON><PERSON> moyennes"}, "average_sales_per_product": {"message": "Ventes totales ÷ Nombre de produits"}, "bao_han": {"message": "Contient"}, "bao_zheng_jin": {"message": "Marge"}, "bian_ti_shu": {"message": "Variations"}, "biao_ti": {"message": "Titre"}, "blacklist_add_blacklist": {"message": "Bloquer cette boutique"}, "blacklist_address_incorrect": {"message": "L'adresse est incorrecte. S'il te plaît vérifie le."}, "blacklist_blacked_out": {"message": "La boutique a été bloquée"}, "blacklist_blacklist": {"message": "Liste noire"}, "blacklist_no_records_yet": {"message": "Pas encore de record!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Marque"}, "btn_aliprice_agent__daigou": {"message": "Intermédiaire d'achat"}, "btn_aliprice_agent__dropshipping": {"message": "Livraison directe"}, "btn_have_a_try": {"message": "Essayez-le maintenant"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "Essayez-le maintenant"}, "btn_txt_view_on_aliprice": {"message": "Voir sur AliPrice"}, "bu_bao_han": {"message": "Ne contient pas"}, "bulk_copy_links": {"message": "Liens en copie groupée"}, "bulk_copy_products": {"message": "Produits en copie groupée"}, "cai_gou_zi_xun": {"message": "Service client"}, "cai_gou_zi_xun__desc": {"message": "Taux de réponse du vendeur en trois minutes"}, "can_ping_lei_xing": {"message": "Type"}, "cao_zuo": {"message": "Opération"}, "chan_pin_ID": {"message": "Identifiant du produit"}, "chan_pin_e_wai_xin_xi": {"message": "Informations supplémentaires sur le produit"}, "chan_pin_lian_jie": {"message": "Lien produit"}, "cheng_li_shi_jian": {"message": "Temps d'établissement"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Cliquez sur Copier les informations sur le produit"}, "commmon_txt_expired": {"message": "Expiré"}, "common__date_range_12m": {"message": "1 an"}, "common__date_range_1m": {"message": "1 mois"}, "common__date_range_1w": {"message": "1 semaine"}, "common__date_range_2w": {"message": "2 semaines"}, "common__date_range_3m": {"message": "3 mois"}, "common__date_range_3w": {"message": "3 semaines"}, "common__date_range_6m": {"message": "6 mois"}, "common_btn_cancel": {"message": "Annuler"}, "common_btn_close": {"message": "fermer"}, "common_btn_save": {"message": "Enregistrer"}, "common_btn_setting": {"message": "Configuration"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "Pas de don<PERSON>"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON><PERSON>, aucun résultat trouvé."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "Retour d'information"}, "common_help": {"message": "<PERSON><PERSON><PERSON>moi"}, "common_loading": {"message": "Chargement"}, "common_login": {"message": "Connexion"}, "common_logout": {"message": "Déconnexion"}, "common_no": {"message": "Non"}, "common_powered_by_aliprice": {"message": "Propulsé par AliPrice.com"}, "common_setting": {"message": "Réglage"}, "common_sign_up": {"message": "Inscrivez-vous"}, "common_system_upgrading_title": {"message": "Mise à niveau du système"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON><PERSON> essayer plus tard"}, "common_txt__currency": {"message": "<PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Didacticiel vidéo"}, "common_txt_ago_time": {"message": "Il y a $time$ jours", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "tous"}, "common_txt_analysis": {"message": "Une analyse"}, "common_txt_basically_used": {"message": "Presque jamais utilisé"}, "common_txt_biaoti_link": {"message": "Titre + Lien"}, "common_txt_biaoti_link_dian_pu": {"message": "Titre + Lien + Nom de la boutique"}, "common_txt_blacklist": {"message": "Liste de blocage"}, "common_txt_cancel": {"message": "Annuler"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "Vérifier"}, "common_txt_colors": {"message": "couleurs"}, "common_txt_confirm": {"message": "Confirmer"}, "common_txt_copied": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy": {"message": "<PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Copier le lien"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON> le titre"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON><PERSON> le titre et le lien"}, "common_txt_day": {"message": "ciel"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "Copier le nom du magasin + le lien"}, "common_txt_download": {"message": "Télécharger"}, "common_txt_downloaded": {"message": "Télécharger"}, "common_txt_export_as_csv": {"message": "Exporter vers Excel"}, "common_txt_export_as_txt": {"message": "Exporter le texte"}, "common_txt_fail": {"message": "￉chouer"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "obtenir"}, "common_txt_incert_selection": {"message": "Inverser la sélection"}, "common_txt_install": {"message": "Installer"}, "common_txt_load_failed": {"message": "Échec du chargement"}, "common_txt_month": {"message": "mois"}, "common_txt_more": {"message": "Plus"}, "common_txt_new_unused": {"message": "<PERSON><PERSON> neuf, jamais utilis<PERSON>"}, "common_txt_next": {"message": "Prochain"}, "common_txt_no_limit": {"message": "Illimité"}, "common_txt_no_noticeable": {"message": "Aucune rayure ni saleté visible"}, "common_txt_on_sale": {"message": "Disponible"}, "common_txt_opt_in_out": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_order": {"message": "Ordre"}, "common_txt_others": {"message": "Autres"}, "common_txt_overall_poor_condition": {"message": "État général médiocre"}, "common_txt_patterns": {"message": "motifs"}, "common_txt_platform": {"message": "Plateformes"}, "common_txt_please_select": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner"}, "common_txt_prev": {"message": "Précédent"}, "common_txt_price": {"message": "Prix"}, "common_txt_privacy_policy": {"message": "Politique de confidentialité"}, "common_txt_product_condition": {"message": "Statut du produit"}, "common_txt_rating": {"message": "Évaluation"}, "common_txt_ratings": {"message": "Notes"}, "common_txt_reload": {"message": "Recharger"}, "common_txt_reset": {"message": "Réinitialiser"}, "common_txt_retail": {"message": "<PERSON><PERSON><PERSON> au d<PERSON>"}, "common_txt_review": {"message": "La revue"}, "common_txt_sale": {"message": "Disponible"}, "common_txt_same": {"message": "Même"}, "common_txt_scratches_and_dirt": {"message": "Avec des rayures et de la saleté"}, "common_txt_search_title": {"message": "Titre de recherche"}, "common_txt_select_all": {"message": "<PERSON><PERSON>"}, "common_txt_selected": {"message": "<PERSON><PERSON>"}, "common_txt_share": {"message": "Partager"}, "common_txt_sold": {"message": "vendu"}, "common_txt_sold_out": {"message": "é<PERSON><PERSON>é"}, "common_txt_some_scratches": {"message": "Quelques rayures et saletés"}, "common_txt_sort_by": {"message": "Trier par"}, "common_txt_state": {"message": "Statut"}, "common_txt_success": {"message": "Su<PERSON>ès"}, "common_txt_sys_err": {"message": "erreur système"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON>'hui"}, "common_txt_total": {"message": "tous"}, "common_txt_unselect_all": {"message": "Inverser la sélection"}, "common_txt_upload_image": {"message": "Télécharger une image"}, "common_txt_visit": {"message": "Visite"}, "common_txt_whitelist": {"message": "Liste blanche"}, "common_txt_wholesale": {"message": "De gros"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "O<PERSON>"}, "compare_tool_btn_clear_all": {"message": "Tout effacer"}, "compare_tool_btn_compare": {"message": "Comparer"}, "compare_tool_btn_contact": {"message": "Contact"}, "compare_tool_tips_max_compared": {"message": "Ajoutez jusqu'à $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configurer les notifications"}, "contact_us": {"message": "Contactez-nous"}, "context_menu_screenshot_search": {"message": "Capturer pour rechercher par image"}, "context_menus_aliprice_search_by_image": {"message": "Rechercher une image avec AliPrice"}, "context_menus_goote_trans": {"message": "Traduire la page/Afficher l'original"}, "context_menus_search_by_image": {"message": "Recherche par image sur $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Capture vers $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Capturer pour traduire"}, "converter_modal_amount_placeholder": {"message": "Entrez le montant ici"}, "converter_modal_btn_convert": {"message": "convertir"}, "converter_modal_exchange_rate_source": {"message": "Les données proviennent du taux de change $boc$ Heure de mise à jour : $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "conversion de devises"}, "converter_modal_search_placeholder": {"message": "devise de recherche"}, "copy_all_contact_us_notice": {"message": "Ce site n'est pas pris en charge pour le moment, veuillez nous contacter"}, "copy_product_info": {"message": "Co<PERSON>r les informations sur le produit"}, "copy_suggest_search_kw": {"message": "Co<PERSON>r la liste déroulante"}, "country__han_gou": {"message": "Corée du Sud"}, "country__ri_ben": {"message": "Japon"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "<PERSON>x de change personnalis<PERSON>"}, "currency_convert__sync_server": {"message": "Synchroniser le serveur"}, "dang_ri_fa_huo": {"message": "Expédition le jour même"}, "dao_chu_quan_dian_shang_pin": {"message": "Exporter tous les produits du magasin"}, "dao_chu_wei_CSV": {"message": "Exporter"}, "dao_chu_zi_duan": {"message": "Champs d'exportation"}, "delivery_address": {"message": "<PERSON><PERSON><PERSON>"}, "delivery_company": {"message": "Compagnie de livraison"}, "di_zhi": {"message": "adresse"}, "dian_ji_cha_xun": {"message": "Cliquez pour interroger"}, "dian_pu_ID": {"message": "Identifiant du magasin"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON> <PERSON> ma<PERSON>in"}, "dian_pu_lian_jie": {"message": "<PERSON>n du magasin"}, "dian_pu_ming": {"message": "Nom du magasin"}, "dian_pu_ming_cheng": {"message": "Nom du magasin"}, "dian_pu_shang_pin_zong_hsu": {"message": "Nombre total de produits dans le magasin : $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Informations sur le magasin"}, "ding_zai_zuo_ce": {"message": "cloué à gauche"}, "download_image__SKU_variant_images": {"message": "Images de variantes SKU"}, "download_image__assume": {"message": "Par exemple, nous avons 2 images, product1.jpg et product2.gif.\nimg_{$no$} sera renommé en img_01.jpg, img_02.gif ;\n{$group$}_{$no$} sera renommé en main_image_01.jpg, main_image_02.gif ;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Téléchargement par lots"}, "download_image__combined_image": {"message": "Image détaillée du produit combiné"}, "download_image__continue_downloading": {"message": "Continuer le téléchargement"}, "download_image__description_images": {"message": "Images de description"}, "download_image__download_combined_image": {"message": "Télécharger l'image détaillée du produit combiné"}, "download_image__download_zip": {"message": "Télécharger le zip"}, "download_image__enlarge_check": {"message": "Prend en charge uniquement les images JPEG, JPG, GIF et PNG, taille maximale d'une seule image : 1 600 * 1 600"}, "download_image__enlarge_image": {"message": "Agrandir l'image"}, "download_image__export": {"message": "Exporter"}, "download_image__height": {"message": "<PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "La vidéo a été ignorée, car elle ne peut pas être exportée"}, "download_image__img_translate": {"message": "Traduction d'images"}, "download_image__main_image": {"message": "image principale"}, "download_image__multi_folder": {"message": "Multi-dossier"}, "download_image__name": {"message": "télécharger l'image"}, "download_image__notice_content": {"message": "Veu<PERSON><PERSON> ne pas cocher « Demander où enregistrer chaque fichier avant de télécharger » dans les paramètres de téléchargement de votre navigateur !!! <PERSON><PERSON>, il y aura beaucoup de boîtes de dialogue."}, "download_image__notice_ignore": {"message": "Ne plus demander ce message"}, "download_image__order_number": {"message": "{$no$} numéro de série ; {$group$} nom de groupe ; {$date$} horodatage", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__prompt_download_zip": {"message": "Il y a trop d'images, vous feriez mieux de les télécharger sous forme de dossier zip."}, "download_image__rename": {"message": "<PERSON>mmer"}, "download_image__rule": {"message": "Règles de dénomination"}, "download_image__single_folder": {"message": "Dossier unique"}, "download_image__sku_image": {"message": "Images d'UGS"}, "download_image__video": {"message": "vidéo"}, "download_image__width": {"message": "<PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Télécharger l'image de l'avis"}, "download_reviews__dropdown_title": {"message": "Télécharger l'image de l'avis"}, "download_reviews__export_csv": {"message": "exporter CSV"}, "download_reviews__no_images": {"message": "Exemple de copie : 0 images à télécharger"}, "download_reviews__no_reviews": {"message": "Aucune critique à télécharger !"}, "download_reviews__notice": {"message": "Conseil:"}, "download_reviews__notice__chrome_settings": {"message": "Configurez le navigateur Chrome pour qu'il demande où enregistrer chaque fichier avant le téléchargement, réglez sur \"Désactivé\""}, "download_reviews__notice__wait": {"message": "Selon le nombre d'avis, le temps d'attente peut être plus long"}, "download_reviews__pages_list__all": {"message": "Tous"}, "download_reviews__pages_list__page": {"message": "Pages $page$ précédentes", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Plage de sélection"}, "export_shopping_cart__csv_filed__details_url": {"message": "Lien produit"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Lien SKU Echo"}, "export_shopping_cart__csv_filed__images": {"message": "Lien vers l'image"}, "export_shopping_cart__csv_filed__quantity": {"message": "Quantité"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Prix"}, "export_shopping_cart__csv_filed__specs": {"message": "Caractéristiques"}, "export_shopping_cart__csv_filed__store_name": {"message": "Nom du magasin"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON>n magasin"}, "export_shopping_cart__csv_filed__title": {"message": "Nom du produit"}, "export_shopping_cart__export_btn": {"message": "Exporter"}, "export_shopping_cart__export_empty": {"message": "Veuillez sélectionner un produit !"}, "fa_huo_shi_jian": {"message": "Expédition"}, "favorite_add_email": {"message": "Ajouter une adresse e-mail"}, "favorite_add_favorites": {"message": "Ajouter aux favoris"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Alerte pour la Baisse de Prix."}, "favorite_btn_notify": {"message": "Suivre le prix"}, "favorite_cate_name_all": {"message": "Tous les produits"}, "favorite_current_price": {"message": "Prix ​​actuel"}, "favorite_due_date": {"message": "Date d'échéance"}, "favorite_enable_notification": {"message": "Veuillez activer la notification par e-mail"}, "favorite_expired": {"message": "Expiré"}, "favorite_go_to_enable": {"message": "Activer"}, "favorite_msg_add_success": {"message": "Ajouter aux Favoris"}, "favorite_msg_del_success": {"message": "Supprimer des favoris"}, "favorite_msg_failure": {"message": "Échouer! Actualisez la page et réessayez."}, "favorite_please_add_email": {"message": "Veuillez ajouter une adresse e-mail"}, "favorite_price_drop": {"message": "Bas"}, "favorite_price_rise": {"message": "<PERSON><PERSON>"}, "favorite_price_untracked": {"message": "Prix non suivi"}, "favorite_saved_price": {"message": "Prix enregistré"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON><PERSON> le suivi"}, "favorite_sub_email_address": {"message": "Adresse e-mail d'abonnement"}, "favorite_tracking_period": {"message": "Période de suivi"}, "favorite_tracking_prices": {"message": "Suivi des prix"}, "favorite_verify_email": {"message": "Vérifier l'adresse e-mail"}, "favorites_list_remove_prompt_msg": {"message": "Êtes-vous sûr de vouloir le supprimer?"}, "favorites_update_button": {"message": "Mettre à jour les prix maintenant"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "<PERSON><PERSON>"}, "find_similar": {"message": "<PERSON>rou<PERSON> similaire"}, "first_ali_price_date": {"message": "La date de première capture par le robot d'exploration AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Aucun coupon"}, "fooview_coupons_modal_title": {"message": "Bons de réduction"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Prix < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "ou prix > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Date limite"}, "fooview_favorites_error_msg_no_favorites": {"message": "Ajoutez des produits favoris ici pour recevoir l'alerte de baisse de prix."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "VERS LE BAS"}, "fooview_favorites_filter_price_up": {"message": "EN HAUT"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON> favoris"}, "fooview_favorites_modal_title_title": {"message": "Allez à AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "Pour suivre le prix"}, "fooview_price_history_app_price": {"message": "APP Prix :"}, "fooview_price_history_title": {"message": "Suivi des prix"}, "fooview_product_list_feedback": {"message": "Score"}, "fooview_product_list_orders": {"message": "Commande"}, "fooview_product_list_price": {"message": "prix"}, "fooview_reviews_error_msg_no_review": {"message": "Nous n'avons trouvé aucun avis sur ce produit."}, "fooview_reviews_filter_buyer_reviews": {"message": "Photos des acheteurs"}, "fooview_reviews_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_same_product_choose_category": {"message": "Choisir une catégorie"}, "fooview_same_product_filter_feedback": {"message": "Score"}, "fooview_same_product_filter_orders": {"message": "Commande"}, "fooview_same_product_filter_price": {"message": "prix"}, "fooview_same_product_filter_rating": {"message": "Évaluation"}, "fooview_same_product_modal_title": {"message": "Le même produit"}, "fooview_same_product_search_by_image": {"message": "Recherchez par l'image"}, "fooview_seller_analysis_modal_title": {"message": "Analy<PERSON> du vendeur"}, "for_12_months": {"message": "Pendant 1 an"}, "for_12_months_list_pro": {"message": "12 mois"}, "for_12_months_nei": {"message": "Dans un délai de douze mois"}, "for_1_months": {"message": "1 mois"}, "for_1_months_nei": {"message": "Dans un délai d'un mois"}, "for_3_months": {"message": "Pour 3 mois"}, "for_3_months_nei": {"message": "Dans un délai de trois mois"}, "for_6_months": {"message": "Pour 6 mois"}, "for_6_months_nei": {"message": "Dans un délai de six mois"}, "for_9_months": {"message": "9 mois"}, "for_9_months_nei": {"message": "Dans un délai de neuf mois"}, "fu_gou_lv": {"message": "<PERSON><PERSON> de rachat"}, "gao_liang_bu_tong_dian": {"message": "souligner les différences"}, "gao_liang_guang_gao_chan_pin": {"message": "Mettre en avant les produits publicitaires"}, "geng_duo_xin_xi": {"message": "Plus d'informations"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON> de mise à jour"}, "get_store_products_fail_tip": {"message": "Cliquez sur OK pour accéder à la vérification afin de garantir un accès normal"}, "gong_x_kuan_shang_pin": {"message": "Un total de $amount$ produits", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Fournisseur"}, "gong_ying_shang_ID": {"message": "ID du fournisseur"}, "gong_ying_shang_deng_ji": {"message": "Évaluation du fournisseur"}, "gong_ying_shang_nian_zhan": {"message": "Le fournisseur est plus âgé"}, "gong_ying_shang_xin_xi": {"message": "informations fournis<PERSON>ur"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Lien vers la page d'accueil du fournisseur"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Prix de vente estimé"}, "guan_jian_zi": {"message": "Mot-clé"}, "guang_gao_chan_pin": {"message": "Produits publicitaires"}, "guang_gao_zhan_bi": {"message": "Ratio publicitaire"}, "guo_ji_wu_liu_yun_fei": {"message": "Frais d'expédition internationaux"}, "guo_lv_tiao_jian": {"message": "Filtres"}, "hao_ping_lv": {"message": "Note positive"}, "highest_price": {"message": "<PERSON><PERSON>"}, "historical_trend": {"message": "Tendance historique"}, "how_to_screenshot": {"message": "Maintenez le bouton gauche de la souris enfoncé pour sélectionner la zone, appuyez sur le bouton droit de la souris ou sur la touche Échap pour quitter la capture d'écran."}, "howt_it_works": {"message": "Comment ça fonctionne"}, "hui_fu_lv": {"message": "Taux de réponse"}, "hui_tou_lv": {"message": "taux de retour"}, "inquire_freightFee": {"message": "<PERSON><PERSON><PERSON> de fret"}, "inquire_freightFee_Yuan": {"message": "Fret/Yuan"}, "inquire_freightFee_province": {"message": "Province"}, "inquire_freightFee_the": {"message": "Le fret est de $num$, ce qui signifie que la région offre la livraison gratuite.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON>."}, "jia_ge": {"message": "Prix"}, "jia_ge_dan_wei": {"message": "Unité"}, "jia_ge_qu_shi": {"message": "S'orienter"}, "jia_zai_n_ge_shang_pin": {"message": "Charger $num$ produits", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Pourcentage du volume des ventes au cours des 30 derniers jours"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Pourcentage du chiffre d'affaires au cours des 30 derniers jours"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Ventes totales au cours des 30 derniers jours"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON> d'affaires"}, "jin_30d_xiao_shou_e__desc": {"message": "Chiffre d'affaires total des 30 derniers jours"}, "jin_90_tian_mai_jia_shu": {"message": "Acheteurs au cours des 90 derniers jours"}, "jin_90_tian_xiao_shou_liang": {"message": "Ventes au cours des 90 derniers jours"}, "jing_xuan_huo_yuan": {"message": "Sources sélectionnées"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON><PERSON><PERSON> d'affaires"}, "jing_ying_mo_shi__gong_chang": {"message": "Fabricant"}, "jiu_fen_jie_jue": {"message": "Résolution des litiges"}, "jiu_fen_jie_jue__desc": {"message": "Comptabilisation des litiges relatifs aux droits de magasin des vendeurs"}, "jiu_fen_lv": {"message": "Taux de litige"}, "jiu_fen_lv__desc": {"message": "Proportion de commandes avec réclamations finalisées au cours des 30 derniers jours et jugées comme relevant de la responsabilité du vendeur ou des deux parties"}, "kai_dian_ri_qi": {"message": "Date d'ouverture"}, "keywords": {"message": "<PERSON>ts clés"}, "kua_jin_Select_pan_huo": {"message": "Sélection transfrontalière"}, "last15_days": {"message": "15 derniers jours"}, "last180_days": {"message": "180 derniers jours"}, "last30_days": {"message": "Au cours des 30 derniers jours"}, "last360_days": {"message": "360 derniers jours"}, "last45_days": {"message": "45 derniers jours"}, "last60_days": {"message": "60 derniers jours"}, "last7_days": {"message": "7 derniers jours"}, "last90_days": {"message": "90 derniers jours"}, "last_30d_sales": {"message": "Ventes des 30 derniers jours"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "Toutes les ventes après produit en rayon"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volume cumulé des ventes au cours des 30 derniers jours, trié du haut au bas"}, "lian_xi_fang_shi": {"message": "Coordonnées"}, "list_time": {"message": "Date de péremption"}, "load_more": {"message": "Charger plus"}, "login_to_aliprice": {"message": "Connectez-vous <PERSON>"}, "long_link": {"message": "<PERSON>n long"}, "lowest_price": {"message": "Faible"}, "mai_jia_shu": {"message": "Vendeuses"}, "mao_li_lv": {"message": "Marge brute"}, "mobile_view__dkxbqy": {"message": "<PERSON><PERSON><PERSON><PERSON>r un nouvel onglet"}, "mobile_view__sjdxq": {"message": "Détails dans l'application"}, "mobile_view__sjdxqy": {"message": "Page de détails dans l'application"}, "mobile_view__smck": {"message": "Scanner pour afficher"}, "mobile_view__smckms": {"message": "Veuillez utiliser l'appareil photo ou l'application pour numériser et afficher"}, "modified_failed": {"message": "Échec de la modification"}, "modified_successfully": {"message": "Modifié avec succès"}, "nav_btn_favorites": {"message": "Mes collections"}, "nav_btn_package": {"message": "paquet"}, "nav_btn_product_info": {"message": "À propos du produit"}, "nav_btn_viewed": {"message": "Vu"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Aucun"}, "normal_link": {"message": "Lien normal"}, "notice": {"message": "indice"}, "number_reviews": {"message": "Commentaires"}, "only_show_num": {"message": "Total des produits : $allnum$, Masqué : $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Supprimer les cases non cochées"}, "open": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "open_links": {"message": "Ou<PERSON><PERSON>r les liens"}, "options_page_tab_check_links": {"message": "Vérifier les liens"}, "options_page_tab_gernal": {"message": "Général"}, "options_page_tab_notifications": {"message": "Notifications"}, "options_page_tab_others": {"message": "Autres"}, "options_page_tab_sbi": {"message": "Recherchez par l'image"}, "options_page_tab_shortcuts": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_shortcuts_title": {"message": "Taille de police pour les raccourcis"}, "options_page_tab_similar_products": {"message": "Même"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Plusieurs liens de produits sont sur le point de s'ouvrir"}, "order_list_sku_show_title": {"message": "Afficher les variantes sélectionnées dans les liens partagés"}, "orders_last30_days": {"message": "Nombre de commandes au cours des 30 derniers jours"}, "pTutorial_favorites_block1_desc1": {"message": "Les produits que vous avez suivis sont répertoriés ici"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Une étiquette verte signifie qu'il y a des prix en baisse"}, "pTutorial_popup_block1_title": {"message": "Ra<PERSON><PERSON>cis et favoris"}, "pTutorial_price_history_block1_desc1": {"message": "C<PERSON>z sur \"Suivre le prix\", ajoutez des produits aux favoris. Une fois leurs prix baissés, vous recevrez des notifications"}, "pTutorial_price_history_block1_title": {"message": "Suivre le prix"}, "pTutorial_reviews_block1_desc1": {"message": "Avis des acheteurs d'Itao et vraies photos des commentaires sur AliExpress"}, "pTutorial_reviews_block1_title": {"message": "<PERSON><PERSON>"}, "pTutorial_reviews_block2_desc1": {"message": "Il est toujours utile de vérifier les avis d'autres acheteurs"}, "pTutorial_same_products_block1_desc1": {"message": "Vous pouvez les comparer pour faire le meilleur choix"}, "pTutorial_same_products_block1_desc2": {"message": "Cliquez sur «Plus» pour «Rechercher par image»"}, "pTutorial_same_products_block1_title": {"message": "Mêmes produits"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Déposez l'image du produit là-bas et choisissez une catégorie"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Recherche par image"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Taux de rétroaction positif du vendeur, scores de rétroaction et depuis combien de temps le vendeur est sur le marché"}, "pTutorial_seller_analysis_block1_title": {"message": "Evaluation du vendeur"}, "pTutorial_seller_analysis_block2_desc2": {"message": "L'évaluation du vendeur est basée sur 3 index: article tel que décrit, vitesse d'expédition de communication"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Nous utilisons 3 couleurs et icônes pour indiquer les niveaux de confiance des vendeurs"}, "page_count": {"message": "Nombre de pages"}, "pai_chu": {"message": "Exclu"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints à Hong Kong"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints au Japon"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints en Corée"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints au Kazakhstan"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints à Macao"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints en Europe de l'Est"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints en Arabie saoudite"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints à Taïwan"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints aux États-Unis"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints au Vietnam"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Exclure les articles restreints"}, "payable_price_formula": {"message": "Prix ​​+ Livraison + Remise"}, "pdd_check_retail_btn_txt": {"message": "Vérifier la vente au détail"}, "pdd_pifa_to_retail_btn_txt": {"message": "Acheter au détail"}, "pdp_copy_fail": {"message": "Échec de la copie !"}, "pdp_copy_success": {"message": "Copie réussie !"}, "pdp_share_modal_subtitle": {"message": "Partagez la capture d'é<PERSON>ran, il/elle verra votre choix."}, "pdp_share_modal_title": {"message": "Partagez votre choix"}, "pdp_share_screenshot": {"message": "Partager la capture d'écran"}, "pei_song": {"message": "Mode d'expédition"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "La qualité des produits"}, "pin_zhi_ti_yan__desc": {"message": "Taux de remboursement de qualité du magasin du vendeur"}, "pin_zhi_tui_kuan_lv": {"message": "Taux de remboursement"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proportion de commandes qui n'ont été remboursées et retournées qu'au cours des 30 derniers jours"}, "ping_fen": {"message": "Notation"}, "ping_jun_fa_huo_su_du": {"message": "Vitesse d'expédition moyenne"}, "pkgInfo_hide": {"message": "Info logistique : marche/arrêt"}, "pkgInfo_no_trace": {"message": "Aucune information logistique"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "eBay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "vente en gros"}, "platform_name__shopee": {"message": "Boutique"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "page d'accueil"}, "popup_go_to_platform": {"message": "visiter $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Je veux acheter..."}, "popup_track_package_btn_track": {"message": "<PERSON><PERSON>"}, "popup_track_package_desc": {"message": "SUIVI DE COLIS TOUT EN UN"}, "popup_track_package_search_placeholder": {"message": "Numéro de suivi"}, "popup_translate_search_placeholder": {"message": "Traduire et rechercher sur $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Suivi des prix"}, "price_history_chart_tip_ae": {"message": "Astuce : Le nombre de commandes est le nombre cumulé de commandes depuis le lancement"}, "price_history_chart_tip_coupang": {"message": "Astuce : Coupang supprimera le nombre de commandes frauduleuses"}, "price_history_inm_1688_l1": {"message": "Se il vous plaît installer"}, "price_history_inm_1688_l2": {"message": "Assistant d<PERSON><PERSON><PERSON><PERSON> pour 1688"}, "price_history_panel_lowest_price": {"message": "Prix le plus bas: "}, "price_history_panel_tab_price_tracking": {"message": "Suivi des prix"}, "price_history_panel_tab_seller_analysis": {"message": "Analy<PERSON> du vendeur"}, "price_history_pro_modal_title": {"message": "Historique des prix & Historique des commandes"}, "privacy_consent__btn_agree": {"message": "Revoir le consentement à la collecte de données"}, "privacy_consent__btn_disable_all": {"message": "Ne pas accepter"}, "privacy_consent__btn_enable_all": {"message": "Tout <PERSON>r"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "Notez que, sans données ni cookies, certaines fonctions seront désactivées car ces fonctions nécessitent l'explication des données ou des cookies, mais vous pouvez toujours utiliser les autres fonctions."}, "privacy_consent__desc_privacy_L1": {"message": "Malheureusement, sans données ni cookies, cela ne fonctionnera pas car nous avons besoin de l'explication des données ou des cookies."}, "privacy_consent__desc_privacy_L2": {"message": "Si vous ne nous autorisez pas à collecter ces informations, veuillez les supprimer."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, nous obtenons uniquement vos données de devise dans les cookies lors de vos achats en ligne pour afficher l'historique des prix."}, "privacy_consent__item_cookies_title": {"message": "Cookies requis"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Ajou<PERSON>z des cookies dans le navigateur pour identifier anonymement votre ordinateur ou appareil."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON>z des données fonctionnelles dans l'add-on pour travailler avec la fonction."}, "privacy_consent__item_functional_title": {"message": "Cookies fonctionnels et analytiques"}, "privacy_consent__more_desc": {"message": "Sachez que nous ne partageons pas vos données personnelles avec d'autres sociétés et qu'aucune société de publicité ne collecte de données via notre service."}, "privacy_consent__options__btn__desc": {"message": "Pour utiliser toutes les fonctionnalités, vous devez l'activer."}, "privacy_consent__options__btn__label": {"message": "Allume ça"}, "privacy_consent__options__desc_L1": {"message": "Nous collecterons les données suivantes qui vous identifient personnellement:"}, "privacy_consent__options__desc_L2": {"message": "- cookies, nous n'obtenons vos données de devise dans les cookies que lorsque vous effectuez des achats en ligne pour afficher l'historique des prix."}, "privacy_consent__options__desc_L3": {"message": "- et ajoutez des Cookies dans le navigateur pour identifier anonymement votre ordinateur ou appareil."}, "privacy_consent__options__desc_L4": {"message": "- d'autres données anonymes rendent cette extension plus pratique."}, "privacy_consent__options__desc_L5": {"message": "Veuillez noter que nous ne partageons pas vos données personnelles avec d'autres sociétés et qu'aucune société de publicité ne collecte de données via notre service."}, "privacy_consent__privacy_preferences": {"message": "Préférences de confidentialité"}, "privacy_consent__read_more": {"message": "En savoir plus >>"}, "privacy_consent__title_privacy": {"message": "Intimité"}, "product_info": {"message": "Informations sur le produit"}, "product_recommend__name": {"message": "Même"}, "product_research": {"message": "Recherche de produits"}, "product_sub__email_desc": {"message": "<PERSON><PERSON><PERSON> d'alerte de prix"}, "product_sub__email_edit": {"message": "É<PERSON>er"}, "product_sub__email_not_verified": {"message": "Veuillez vérifier l'e-mail"}, "product_sub__email_required": {"message": "Veuillez fournir un e-mail"}, "product_sub__form_countdown": {"message": "Fermeture automatique après $seconds$ secondes", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Échec de l'ajout du rappel !"}, "product_sub__form_input_price": {"message": "prix des intrants"}, "product_sub__form_item_country": {"message": "nation"}, "product_sub__form_item_current_price": {"message": "Prix ​​actuel"}, "product_sub__form_item_duration": {"message": "<PERSON><PERSON>"}, "product_sub__form_item_higher_price": {"message": "Ou prix>"}, "product_sub__form_item_invalid_higher_price": {"message": "Le prix doit être supérieur à $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Le prix doit être inférieur à $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON> le prix <"}, "product_sub__form_submit": {"message": "So<PERSON><PERSON><PERSON>"}, "product_sub__form_success": {"message": "J'ai réussi à ajouter un rappel !"}, "product_sub__high_price_notify": {"message": "M'informer des augmentations de prix"}, "product_sub__low_price_notify": {"message": "M'informer des réductions de prix"}, "product_sub__modal_title": {"message": "Rappel de changement de prix d'abonnement"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Espace entreprise"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Veuillez sélectionner au moins un produit"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Veuillez sélectionner au moins un champ"}, "qu_deng_lu": {"message": "Se connecter"}, "quan_guo_yan_xuan": {"message": "Choix mondial"}, "recommendation_popup_banner_btn_install": {"message": "Installez-le"}, "recommendation_popup_banner_desc": {"message": "Afficher l'historique des prix dans les 3/6 mois et la notification de baisse de prix"}, "region__all": {"message": "Toutes les régions"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "<PERSON><PERSON> le clic droit"}, "ren_zheng_gong_chang": {"message": "Usine certifiée"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Années en tant que fournisseur certifié"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON>in de vous connecter à AliPrice"}, "revenue_last30_days": {"message": "Montant des ventes des 30 derniers jours"}, "review_counts": {"message": "Nombre de collectionneurs"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Période d'entrée"}, "sales_amount_last30_days": {"message": "Ventes totales au cours des 30 derniers jours"}, "sales_last30_days": {"message": "Ventes au cours des 30 derniers jours"}, "sbi_alibaba_cate__accessories": {"message": "Accessoires"}, "sbi_alibaba_cate__aqfk": {"message": "Sécurité"}, "sbi_alibaba_cate__bags_cases": {"message": "Sacs et étuis"}, "sbi_alibaba_cate__beauty": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Culture de bureau"}, "sbi_alibaba_cate__bz": {"message": "Forfait"}, "sbi_alibaba_cate__ccyj": {"message": "Ustensiles de cuisine"}, "sbi_alibaba_cate__clothes": {"message": "Vêtements"}, "sbi_alibaba_cate__cmgd": {"message": "Diffusion des médias"}, "sbi_alibaba_cate__coat_jacket": {"message": "Veste Manteau"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Électronique grand public"}, "sbi_alibaba_cate__cryp": {"message": "Produits pour adultes"}, "sbi_alibaba_cate__csyp": {"message": "Doublures de lit"}, "sbi_alibaba_cate__cwyy": {"message": "Jardinage pour animaux de compagnie"}, "sbi_alibaba_cate__cysx": {"message": "Restauration frais"}, "sbi_alibaba_cate__dgdq": {"message": "Électricien"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Robe et costumes"}, "sbi_alibaba_cate__dszm": {"message": "Éclairage"}, "sbi_alibaba_cate__dzqj": {"message": "Appareil électronique"}, "sbi_alibaba_cate__essb": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__food": {"message": "Aliments"}, "sbi_alibaba_cate__fspj": {"message": "Vêtements & Accessoires"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "Cuir textile"}, "sbi_alibaba_cate__ghjq": {"message": "Soins personnels"}, "sbi_alibaba_cate__gt": {"message": "Acier"}, "sbi_alibaba_cate__gyp": {"message": "Artisanat"}, "sbi_alibaba_cate__hb": {"message": "Respectueux de l'environnement"}, "sbi_alibaba_cate__hfcz": {"message": "Maquillage de soins de la peau"}, "sbi_alibaba_cate__hg": {"message": "Industrie chimique"}, "sbi_alibaba_cate__jg": {"message": "Traitement"}, "sbi_alibaba_cate__jianccai": {"message": "Matériaux de construction"}, "sbi_alibaba_cate__jichuang": {"message": "Machine-outils"}, "sbi_alibaba_cate__jjry": {"message": "Usage domestique quotidien"}, "sbi_alibaba_cate__jtys": {"message": "Le transport"}, "sbi_alibaba_cate__jxsb": {"message": "Équipement"}, "sbi_alibaba_cate__jxwj": {"message": "Quincaillerie mécanique"}, "sbi_alibaba_cate__jydq": {"message": "Appareils ménagers"}, "sbi_alibaba_cate__jzjc": {"message": "Matériaux de construction pour l'amélioration de l'habitat"}, "sbi_alibaba_cate__jzjf": {"message": "Linge de maison"}, "sbi_alibaba_cate__mj": {"message": "Serviette"}, "sbi_alibaba_cate__myyp": {"message": "Produits pour bébés"}, "sbi_alibaba_cate__nanz": {"message": "Pour des hommes"}, "sbi_alibaba_cate__nvz": {"message": "Vêtements pour femmes"}, "sbi_alibaba_cate__ny": {"message": "Énergie"}, "sbi_alibaba_cate__others": {"message": "Autres"}, "sbi_alibaba_cate__qcyp": {"message": "Accessoires auto"}, "sbi_alibaba_cate__qmpj": {"message": "Pièces automobiles"}, "sbi_alibaba_cate__shoes": {"message": "Chaussures"}, "sbi_alibaba_cate__smdn": {"message": "Ordinateur numérique"}, "sbi_alibaba_cate__snqj": {"message": "Rangement et nettoyage"}, "sbi_alibaba_cate__spjs": {"message": "Nourriture boisson"}, "sbi_alibaba_cate__swfw": {"message": "Les services aux entreprises"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Pantalon et jupe"}, "sbi_alibaba_cate__txcp": {"message": "Produits de communication"}, "sbi_alibaba_cate__tz": {"message": "Vêtements pour enfants"}, "sbi_alibaba_cate__underwear": {"message": "Sous-vêtement"}, "sbi_alibaba_cate__wjgj": {"message": "<PERSON><PERSON> matériels"}, "sbi_alibaba_cate__xgpi": {"message": "Sacs en cuir"}, "sbi_alibaba_cate__xmhz": {"message": "coopération de projet"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Tenue de sport"}, "sbi_alibaba_cate__ydhw": {"message": "Sports de plein air"}, "sbi_alibaba_cate__yjkc": {"message": "Minéraux métallurgiques"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentation"}, "sbi_alibaba_cate__ys": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Soins médicaux"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Nombre d'acheteurs au cours des 90 derniers jours"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volume des ventes au cours des 90 derniers jours"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Prix estimé"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Frais d'expédition internationale"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Frais d'expédition"}, "sbi_alibaba_cn_kj_gssj": {"message": "Prix estimé"}, "sbi_alibaba_cn_kj_lr": {"message": "Profit"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Bénéfice = prix estimé x marge bénéficiaire"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Vitesse de livraison moyenne"}, "sbi_alibaba_cn_kj_qtfy": {"message": "autre frais"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Autre coût = prix estimé x autre ratio de coût"}, "sbi_alibaba_cn_kj_spjg": {"message": "Prix"}, "sbi_alibaba_cn_kj_spzl": {"message": "Poids"}, "sbi_alibaba_cn_kj_szd": {"message": "Emplacement"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Pièces"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Pièces"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram<PERSON>"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Acheteurs"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Pièces"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Ensembles"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Journées"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Pas de prix"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "le prix"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Disponible à la vente"}, "sbi_aliprice_alibaba_cn__moren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Sûr"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "un meuble"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "casse-croûte"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "maquillages"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Sous-vêtement"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accessoires"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON> en bouteille"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Autres"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Ju<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Veste"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Électronique"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagage"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bas"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "chaussure"}, "sbi_aliprice_cate__apparel": {"message": "Vêtements"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automobiles et motos"}, "sbi_aliprice_cate__beauty_health": {"message": "Beau<PERSON> et santé"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Téléphones portables et télécommunications"}, "sbi_aliprice_cate__computer_office": {"message": "Ordinateur et bureau"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Electronique grand public"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Fournitures scolaires et de bureau"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Composants électroniques et fournitures"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Extensions de cheveux et perruques"}, "sbi_aliprice_cate__home_garden": {"message": "Maison & Jardin"}, "sbi_aliprice_cate__home_improvement": {"message": "Amélioration de l'habitat"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Bijoux et accessoires"}, "sbi_aliprice_cate__luggage_bags": {"message": "Valises et sacs"}, "sbi_aliprice_cate__mother_kids": {"message": "Mère et enfants"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Nouveauté et usage spécial"}, "sbi_aliprice_cate__security_protection": {"message": "Protection de la sécurité"}, "sbi_aliprice_cate__shoes": {"message": "Chaussures"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sports et divertissement"}, "sbi_aliprice_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON> et loisirs"}, "sbi_aliprice_cate__watches": {"message": "Montres"}, "sbi_aliprice_cate__weddings_events": {"message": "Mariages et événements"}, "sbi_btn_capture_txt": {"message": "Capturer"}, "sbi_btn_source_now_txt": {"message": "Source maintenant"}, "sbi_button__chat_with_me": {"message": "Discutez avec moi"}, "sbi_button__contact_supplier": {"message": "Contact"}, "sbi_button__hide_on_this_site": {"message": "Ne pas afficher sur ce site"}, "sbi_button__open_settings": {"message": "Configurer la recherche par image"}, "sbi_capture_shortcut_tip": {"message": "ou appuyez sur la touche \"Entrée\" du clavier"}, "sbi_capturing_tip": {"message": "Capture"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 <PERSON><PERSON><PERSON>"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_crop_start": {"message": "Utiliser une capture d'écran"}, "sbi_err_captcha_action": {"message": "Vérifier"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Besoin d'une vérification, veuillez télécharger une photo pour vérifier. (Voir $video_tutorial$ ou essayer de supprimer les cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "<PERSON>ra<PERSON> inhabit<PERSON>, veuillez vérifier"}, "sbi_err_captcha_for_taobao": {"message": "Taobao vous demande de vérifier, veuillez télécharger manuellement une image et rechercher pour la vérifier. Cette erreur est due à la nouvelle politique de vérification \"Recherche TaoBao par image\", nous vous suggérons de vérifier que les réclamations sont trop fréquentes sur Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "Rétroaction"}, "sbi_err_captcha_msg": {"message": "$platform$ vous oblige à télécharger une image à rechercher ou à effectuer une vérification de sécurité pour supprimer les restrictions de recherche", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Vérifiez s'il s'agit de la dernière version"}, "sbi_err_cookie_btn_clear": {"message": "Effacer les cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Essayez d'effacer les cookies de 1688 ? (<PERSON><PERSON> devez vous reconnecter)"}, "sbi_err_desperate_feature_pdd": {"message": "La fonction de recherche d'images a été déplacée vers l'extension Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Comment améliorer le taux de réussite de la recherche d'images ?"}, "sbi_err_img_undersize": {"message": "Image > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Connectez-vous $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Connexion"}, "sbi_err_low_version": {"message": "Installez la dernière version ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Télécharger"}, "sbi_err_need_help": {"message": "Be<PERSON>in d'aide"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON>, assurez-vous de pouvoir visiter le site Web"}, "sbi_err_not_low_version": {"message": "La dernière version a été installée ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Essayer à nouveau"}, "sbi_err_try_again_action": {"message": "Essayer à nouveau"}, "sbi_err_visit_and_try": {"message": "R<PERSON><PERSON>ez ou visitez le $website$ pour réessayer.", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Consulter la page d'accueil de $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Le chargement a échoué, veuillez actualiser la page et réessayer."}, "sbi_kuajing_filter_area": {"message": "Zone"}, "sbi_kuajing_filter_au": {"message": "Australie"}, "sbi_kuajing_filter_btn_confirm": {"message": "Confirmer"}, "sbi_kuajing_filter_de": {"message": "Allemagne"}, "sbi_kuajing_filter_destination_country": {"message": "Pays de destination"}, "sbi_kuajing_filter_es": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate": {"message": "Estimation"}, "sbi_kuajing_filter_estimate_price": {"message": "Prix estimé"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Formule de prix estimé = (prix de la marchandise + fret logistique international)/(1 - marge bénéficiaire - autre ratio de coûts)"}, "sbi_kuajing_filter_fr": {"message": "La France"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Saisissez des mots-clés correspondant au titre"}, "sbi_kuajing_filter_logistics": {"message": "Modèle de logistique"}, "sbi_kuajing_filter_logistics_china_post": {"message": "La poste chinoise"}, "sbi_kuajing_filter_logistics_discount": {"message": "Remise logistique"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Fret logistique international = (poids x prix d'expédition + frais d'inscription) x (1 - remise)"}, "sbi_kuajing_filter_others_fee": {"message": "Autre frais"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_prop": {"message": "Les attributs"}, "sbi_kuajing_filter_ru": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_total": {"message": "Associer $count$ articles similaires", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "ROYAUME-UNI"}, "sbi_kuajing_filter_usa": {"message": "Améri<PERSON>"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo en gros"}, "sbi_msg_no_result": {"message": "Aucun résultat trouvé,veuillez vous connecter à $loginSite$ ou essayer une autre image", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Temporairement indisponible pour Safari, veuillez utiliser $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Navigateur Chrome et ses extensions"}, "sbi_msg_no_result_reinstall_l1": {"message": "Aucun résultat trouvé, connectez-vous sur $loginSite$ ou essayez une autre image, ou réinstallez $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Dernière version", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Zone sélectionnée"}, "sbi_shipping_": {"message": "Expédition le jour même"}, "sbi_specify_category": {"message": "Précisez la catégorie:"}, "sbi_start_crop": {"message": "Sélectionnez la zone"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 outre-mer"}, "sbi_tutorial_btn_more": {"message": "Plus d'utilisation"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Trouver des coupons Taobao"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>, aucun rés<PERSON>at trouvé, ve<PERSON><PERSON><PERSON> réessayer."}, "sbi_txt__min_order": {"message": "Min. ordre"}, "sbi_visiting": {"message": "Navigation"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON>er le vendeur pour le prix"}, "sbi_yiwugo__qigou": {"message": "$num$ Pièces (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "<PERSON><PERSON><PERSON>"}, "searchByImage_screenshot": {"message": "Capture d'écran en un clic"}, "searchByImage_search": {"message": "Recherche en un clic des mêmes éléments"}, "searchByImage_size_type": {"message": "La taille du fichier ne peut pas dépasser $num$ Mo, $type$ uniquement.", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Analyse d'image"}, "search_by_image_progress_searching": {"message": "Rechercher des produits"}, "search_by_image_progress_sending": {"message": "Envoi de l'image"}, "search_by_image_response_rate": {"message": "Taux de réponse: $responseRate$ des acheteurs qui ont contacté ce fournisseur ont reçu une réponse dans les $responseInHour$ heures.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Recherche par mots-clés:"}, "select_country_language_modal_title_country": {"message": "Pays"}, "select_country_language_modal_title_language": {"message": "La langue"}, "select_country_region_modal_title": {"message": "Sélectionnez un pays / une région"}, "select_language_modal_title": {"message": "Sélectionnez une langue:"}, "select_shop": {"message": "Sélectionnez un magasin"}, "sellers_count": {"message": "Nombre de vendeurs sur la page actuelle"}, "sellers_count_per_page": {"message": "Nombre de vendeurs sur la page actuelle"}, "service_score": {"message": "Évaluation du service complet"}, "set_shortcut_keys": {"message": "Définir les touches de raccourci"}, "setting_logo_title": {"message": "Assistant commercial"}, "setting_modal_options_position_title": {"message": "Emplacement"}, "setting_modal_options_position_value_left": {"message": "À gauche"}, "setting_modal_options_position_value_right": {"message": "À droite"}, "setting_modal_options_theme_title": {"message": "<PERSON>hème couleur"}, "setting_modal_options_theme_value_dark": {"message": "Dark"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "Personnaliser"}, "setting_options_country_title": {"message": "Pays/région"}, "setting_options_hover_zoom_desc": {"message": "Passer la souris dessus pour zoomer"}, "setting_options_hover_zoom_title": {"message": "Survol et zoom"}, "setting_options_jd_coupon_desc": {"message": "Coupon trouvé sur JD.com"}, "setting_options_jd_coupon_title": {"message": "Coupon JD.com"}, "setting_options_language_title": {"message": "<PERSON><PERSON>"}, "setting_options_price_drop_alert_desc": {"message": "Lorsque le prix des produits dans Mes Favoris diminue, vous recevrez une notification push."}, "setting_options_price_drop_alert_title": {"message": "Alerte de baisse de prix"}, "setting_options_price_history_on_list_page_desc": {"message": "Afficher l'historique des prix sur la page de recherche de produits"}, "setting_options_price_history_on_list_page_title": {"message": "Historique des prix (page de liste)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Afficher l'historique du produit sur la page de recherche de produit"}, "setting_options_price_history_on_produt_page_title": {"message": "Historique des prix (page de détails)"}, "setting_options_sales_analysis_desc": {"message": "Prend en charge les statistiques sur les prix, le volume des ventes, le nombre de vendeurs et le ratio des ventes en magasin sur la page de liste de produits $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Analy<PERSON> des ventes"}, "setting_options_save_success_msg": {"message": "Su<PERSON>ès"}, "setting_options_tacking_price_title": {"message": "Alerte de changement de prix"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Prise en charge : 1688 et Taobao"}, "setting_saved_message": {"message": "Changements sauvegardés avec succès"}, "setting_section_enable_platform_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_section_setting_title": {"message": "Personnaliser"}, "setting_section_shortcuts_title": {"message": "<PERSON><PERSON><PERSON><PERSON> :"}, "settings_aliprice_agent__desc": {"message": "Affiché sur la page de détails du produit $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Achète pour moi"}, "settings_copy_link__desc": {"message": "Affichage sur la page de détail du produit"}, "settings_copy_link__title": {"message": "Bouton Copier et titre de recherche"}, "settings_currency_desc__for_detail": {"message": "Soutenir la page de détails du produit 1688"}, "settings_currency_desc__for_list": {"message": "Recherche par image (inclure 1688/1688 outre-mer/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Sélectionnez le prix"}, "settings_currency_desc_display_for_list": {"message": "Affiché dans la recherche d'images (y compris 1688/1688 à l'étranger/Taobao)"}, "settings_currency_rate_desc": {"message": "Mise à jour du taux de change à partir de \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "banque de Chine"}, "settings_download_images__desc": {"message": "Prise en charge du téléchargement d'images depuis $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "bouton télécharger l'image"}, "settings_download_reviews__desc": {"message": "Affiché sur la page de détails du produit $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Télécharger les images d'examen"}, "settings_google_translate_desc": {"message": "Faites un clic droit pour obtenir la barre de traduction google"}, "settings_google_translate_title": {"message": "traduction de pages Web"}, "settings_historical_trend_desc": {"message": "S'affiche en bas à droite de l'image sur la page de la liste des produits."}, "settings_modal_btn_more": {"message": "Plus de réglages"}, "settings_productInfo_desc": {"message": "Afficher des informations plus détaillées sur le produit sur la page de liste des produits. L'activation de cette option peut augmenter la charge de l'ordinateur et provoquer un décalage de page. Si cela affecte les performances, il est recommandé de la désactiver."}, "settings_product_recommend__desc": {"message": "Affiché sous l'image principale sur la page de détails du produit $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produits recommandés"}, "settings_research_desc": {"message": "Afficher des informations plus détaillées sur la page de liste des produits"}, "settings_sbi_add_to_list": {"message": "Ajouter dans le $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniature du résultat de la recherche d'images"}, "settings_sbi_remove_from_list": {"message": "Supprimer du $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Ajouter à la liste noire"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Ajuster la position d'entrée"}, "settings_search_by_image_blacklist_desc": {"message": "Ne pas afficher l'icône sur les sites Web de la liste noire."}, "settings_search_by_image_blacklist_title": {"message": "Liste noire"}, "settings_search_by_image_bottom_left": {"message": "En bas à gauche"}, "settings_search_by_image_bottom_right": {"message": "En bas à droite"}, "settings_search_by_image_clear_blacklist": {"message": "Effacer la liste noire"}, "settings_search_by_image_detail_page_icon_title": {"message": "La vignette"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "Plus grand"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Plus petit"}, "settings_search_by_image_display_button_desc": {"message": "Un clic sur l'icône pour rechercher par image"}, "settings_search_by_image_display_button_title": {"message": "Icône sur les images"}, "settings_search_by_image_sourece_websites_desc": {"message": "Trouvez le produit source sur ces sites Web"}, "settings_search_by_image_sourece_websites_title": {"message": "Recherche par résultat d'image"}, "settings_search_by_image_top_left": {"message": "En haut à gauche"}, "settings_search_by_image_top_right": {"message": "En haut à droite"}, "settings_search_keyword_on_x__desc": {"message": "Rechercher des mots sur $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Afficher l'icône $platform$ lorsque les mots sélectionnés", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Essayez de trouver le même produit sur ces sites Web (maximum 5)"}, "settings_similar_products_title": {"message": "Trouver le même produit"}, "settings_toolbar_expand_title": {"message": "Minimiser le plug-in"}, "settings_top_toolbar_desc": {"message": "Barre de recherche en haut de la page"}, "settings_top_toolbar_title": {"message": "Barre de recherche"}, "settings_translate_search_desc": {"message": "Traduire en chinois et rechercher"}, "settings_translate_search_title": {"message": "Recherche multilingue"}, "settings_translator_contextmenu_title": {"message": "Capturer pour traduire"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Filtrer pour exporter"}, "shai_xuan_zi_duan": {"message": "Champs de filtrage"}, "shang_jia_shi_jian": {"message": "Temps de conservation"}, "shang_pin_biao_ti": {"message": "titre du produit"}, "shang_pin_dui_bi": {"message": "Comparaison de produits"}, "shang_pin_lian_jie": {"message": "lien produit"}, "shang_pin_xin_xi": {"message": "Information sur le produit"}, "share_modal__content": {"message": "Partage avec tes amis"}, "share_modal__disable_for_while": {"message": "Je ne veux rien partager"}, "share_modal__title": {"message": "Aimez-vous $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Restant"}, "shi_fou_ke_ding_zhi": {"message": "Est-ce personnalisable ?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Fournisseur certifié"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Fournisseurs certifiés"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Assurance commerciale"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Garanties commerciales"}, "shipping_fee": {"message": "Frais d'expédition"}, "shop_followers": {"message": "Abonnés de la boutique"}, "shou_qi": {"message": "<PERSON>ins"}, "similar_products_warn_max_platforms": {"message": "Max à 5"}, "sku_calc_price": {"message": "Prix calculé"}, "sku_calc_price_settings": {"message": "Paramètres du prix calculé"}, "sku_formula": {"message": "Formule"}, "sku_formula_desc": {"message": "Description de la formule"}, "sku_formula_desc_text": {"message": "Prend en charge les formules mathématiques complexes, avec le prix initial représenté par A et les frais de transport représentés par B.\n\n<br/>\n\nPrend en charge les parenthèses (), plus +, moins -, la multiplication * et la division /.\n\n<br/>\n\nExemple :\n\n<br/>\n\n1. Pour obtenir 1,2 fois le prix initial et ajouter les frais de transport, la formule est : A * 1,2 + B.\n\n<br/>\n\n2. Pour obtenir le prix initial plus 1 yuan, multiplier par 1,2, la formule est : (A + 1) * 1,2.\n\n<br/>\n\n3. Pour obtenir le prix initial plus 10 yuans, multiplier par 1,2, puis soustraire 3 yuans, la formule est : (A + 10) * 1,2 - 3."}, "sku_in_stock": {"message": "En stock"}, "sku_invalid_formula_format": {"message": "Format de formule non valide"}, "sku_inventory": {"message": "Stock"}, "sku_link_copy_fail": {"message": "Copié avec succès, les spécifications et les attributs du SKU ne sont pas sélectionnés"}, "sku_link_copy_success": {"message": "Copié avec succès, spécifications et attributs du SKU sélectionnés"}, "sku_list": {"message": "Liste SKU"}, "sku_min_qrder_qty": {"message": "Quantité minimum de commande"}, "sku_name": {"message": "Nom SKU"}, "sku_no": {"message": "N°"}, "sku_original_price": {"message": "Prix d'origine"}, "sku_price": {"message": "Prix ​​SKU"}, "stop_track_time_label": {"message": "<PERSON><PERSON><PERSON> suiv<PERSON>:"}, "suo_zai_di_qu": {"message": "emplacement"}, "tab_pkg_quick_view": {"message": "Moniteur Logistique"}, "tab_product_details_price_history": {"message": "L'histoire"}, "tab_product_details_reviews": {"message": "Commentaires"}, "tab_product_details_seller_analysis": {"message": "Une analyse"}, "tab_product_details_similar_products": {"message": "Même"}, "total_days_listed_per_product": {"message": "Somme des jours de conservation ÷ Nombre de produits"}, "total_items": {"message": "Nombre total de produits"}, "total_price_per_product": {"message": "Somme des prix ÷ Nombre de produits"}, "total_rating_per_product": {"message": "Somme des notes ÷ Nombre de produits"}, "total_revenue": {"message": "Revenu total"}, "total_revenue40_items": {"message": "Revenu total des $amount$ produits de la page actuelle", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Ventes totales"}, "total_sales40_items": {"message": "Ventes totales des $amount$ produits de la page actuelle", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Piste pendant: 1 an"}, "track_for_3_months": {"message": "Suivre pendant: 3 mois"}, "track_for_6_months": {"message": "Suivre pendant: 6 mois"}, "tracking_price_email_add_btn": {"message": "Ajouter un e-mail"}, "tracking_price_email_edit_btn": {"message": "Modifier l'e-mail"}, "tracking_price_email_intro": {"message": "Nous vous informerons par e-mail."}, "tracking_price_email_invalid": {"message": "Veuillez fournir un e-mail valide"}, "tracking_price_email_verified_desc": {"message": "Vous pouvez maintenant recevoir notre alerte de baisse de prix."}, "tracking_price_email_verified_title": {"message": "Vérifié avec succès"}, "tracking_price_email_verify_desc_line1": {"message": "Nous avons envoyé un lien de vérification vers votre adresse e-mail,"}, "tracking_price_email_verify_desc_line2": {"message": "veuil<PERSON>z vérifier votre boî<PERSON> de réception."}, "tracking_price_email_verify_title": {"message": "Vérifier les courriels"}, "tracking_price_web_push_notification_intro": {"message": "Sur le bureau: <PERSON><PERSON><PERSON> surveiller n'importe quel produit pour vous et vous envoyer une notification Web Push une fois le prix modifié."}, "tracking_price_web_push_notification_title": {"message": "Notifications Web Push"}, "translate_im__login_required": {"message": "Traduit par <PERSON><PERSON><PERSON>, veuillez vous connecter à $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Traduit et copié dans le presse-papiers, mais en raison des limitations d'Aliwangwang, vous devez le coller manuellement !"}, "translate_im__send": {"message": "Traduire et envoyer"}, "translate_search": {"message": "Traduire et rechercher"}, "translation_originals_translated": {"message": "Original et chinois"}, "translation_translated": {"message": "<PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Détection automatique"}, "translator_language_detected": {"message": "Détecté"}, "translator_language_search_placeholder": {"message": "Langue de recherche"}, "try_again": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON> de l'image :"}, "tu_pian_lian_jie": {"message": "Lien vers l'image"}, "tui_huan_ti_yan": {"message": "Expérience de retour"}, "tui_huan_ti_yan__desc": {"message": "Évaluer les indicateurs après-vente des vendeurs"}, "tutorial__show_all": {"message": "Toutes les fonctionnalités"}, "tutorial_ae_popup_title": {"message": "Épinglez l'extension, ouvrez Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Analyse de l'examen d'AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Support USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Expédition vers la Corée/le Japon/la Chine continentale"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 prend en charge les achats à l'étranger"}, "tutorial_auto_apply_coupon_title": {"message": "Coupon d'application automatique"}, "tutorial_btn_end": {"message": "Fin"}, "tutorial_btn_example": {"message": "Exemple"}, "tutorial_btn_have_a_try": {"message": "<PERSON>, essay<PERSON>"}, "tutorial_btn_next": {"message": "Prochaine"}, "tutorial_btn_see_more": {"message": "Plus"}, "tutorial_compare_products": {"message": "comparer les produits"}, "tutorial_currency_convert_title": {"message": "Conversion de devises"}, "tutorial_export_shopping_cart": {"message": "Exporter au format CSV, prendre en charge Taobao et 1688"}, "tutorial_export_shopping_cart_title": {"message": "Exporter le panier"}, "tutorial_price_history_pro": {"message": "Affiché sur la page de détail du produit.\nSoutenez Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Toute l'année Historique des prix et Historique des commandes"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Capturer pour rechercher par image"}, "tutorial_translate_search": {"message": "Traduire pour rechercher"}, "tutorial_translate_search_and_package_tracking": {"message": "Recherche de traduction et suivi de colis"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "ordres"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pièces"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "<PERSON>er le support"}, "verify_human_verification": {"message": "Vérification humaine"}, "verify_unusual_access": {"message": "Accès inhabituel d<PERSON>"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> tout"}, "view_history_clean_all_warring": {"message": "Nettoyer tous les enregistrements consultés?"}, "view_history_clean_all_warring_title": {"message": "avertissement"}, "view_history_viewd": {"message": "Vu"}, "website": {"message": "site web"}, "weight": {"message": "Poids"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Impossible d'obtenir les données"}, "wu_liu_shi_xiao": {"message": "Expédition à temps"}, "wu_liu_shi_xiao__desc": {"message": "Le taux d’encaissement en 48 heures et le taux d’exécution du magasin du vendeur"}, "xia_dan_jia": {"message": "Prix final"}, "xian_xuan_ze_product_attributes": {"message": "Sélectionnez les attributs du produit"}, "xiao_liang": {"message": "Volume des ventes"}, "xiao_liang_zhan_bi": {"message": "Pourcentage du volume des ventes"}, "xiao_shi": {"message": "$num$ heures", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON><PERSON> d'affaires"}, "xiao_shou_e_zhan_bi": {"message": "Pourcentage du chiffre d'affaires"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Sélectionner $amount$ enregistrements", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Tous les produits chargés"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON>s annuelles"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Part des ventes annuelles"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON> d'affaires annuel"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Part du chiffre d'affaires annuel"}, "yi_shua_xin": {"message": "Actualisé"}, "yin_cang_xiang_tong_dian": {"message": "masquer les similitudes"}, "you_xiao_liang": {"message": "Avec volume de ventes"}, "yu_ji_dao_da_shi_jian": {"message": "Heure d'arrivée estimée"}, "yuan_gong_ren_shu": {"message": "Nombre d'employés"}, "yue_cheng_jiao": {"message": "Volume mensuel"}, "yue_dai_xiao": {"message": "Livraison directe"}, "yue_dai_xiao__desc": {"message": "<PERSON><PERSON><PERSON> dropshipping au cours des 30 derniers jours"}, "yue_dai_xiao_pai_xu__desc": {"message": "Ventes dropshipping au cours des 30 derniers jours, classées du plus haut au plus bas"}, "yue_xiao_liang__desc": {"message": "Volume des ventes au cours des 30 derniers jours"}, "zhan_kai": {"message": "Plus"}, "zhe_kou": {"message": "Remise"}, "zhi_chi_yi_jian_dai_fa": {"message": "Livraison directe"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON> gratuite"}, "zhi_fu_ding_dan_shu": {"message": "Commandes payantes"}, "zhi_fu_ding_dan_shu__desc": {"message": "Nombre de commandes pour ce produit (30 jours)"}, "zhu_ce_xing_zhi": {"message": "Nature de l'inscription"}, "zi_ding_yi_tiao_jian": {"message": "Conditions personnalis<PERSON>"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variante vendue"}, "zong_he_fu_wu_fen": {"message": "Note globale"}, "zong_he_fu_wu_fen__desc": {"message": "Évaluation globale du service du vendeur"}, "zong_he_fu_wu_fen__short": {"message": "Évaluation"}, "zong_he_ti_yan_fen": {"message": "Notation"}, "zong_he_ti_yan_fen_3": {"message": "En dessous de 4 étoiles"}, "zong_he_ti_yan_fen_4": {"message": "4 à 4,5 <PERSON><PERSON>iles"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 <PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen_5": {"message": "5 étoiles"}, "zong_ku_cun": {"message": "Inventaire total"}, "zong_xiao_liang": {"message": "Ventes totales"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Taux de réponse en 3 minutes au cours des 30 derniers jours"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Taux de récupération 48H au cours des 30 derniers jours"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Taux de performance 48H dans les 30 derniers jours"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Dossier commercial (30 jours)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Dossier commercial (30 jours)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Taux de contestation au cours des 30 derniers jours"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Taux de remboursement de qualité au cours des 30 derniers jours"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Le nombre d'ordres de paiement au cours des 30 derniers jours"}}