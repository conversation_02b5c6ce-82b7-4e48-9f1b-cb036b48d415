{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Punto di vendita caldo transfrontaliero"}, "1688_shi_li_ren_zheng": {"message": "Certificazione di forza 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Più di 1 anno"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Consegna entro 24 ore"}, "24H_lan_shou_lv": {"message": "Tariffa di imballaggio 24 ore su 24"}, "30D_shang_xin": {"message": "Nuovi arrivi mensili"}, "30d_sales": {"message": "$amount$ venduto in 30 giorni", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Risposta entro 3 minuti."}, "3Min_xiang_ying_lv__desc": {"message": "La percentuale di risposte efficaci di Wangwang ai messaggi di richiesta dell'acquirente entro 3 minuti negli ultimi 30 giorni"}, "48H": {"message": "48 ore"}, "48H_fa_huo": {"message": "Consegna entro 48 ore"}, "48H_lan_shou_lv": {"message": "Tasso di imballaggio di 48 ore"}, "48H_lan_shou_lv__desc": {"message": "Rapporto tra il numero dell'ordine ritirato entro 48 ore e il numero totale degli ordini"}, "48H_lv_yue_lv": {"message": "Tasso di prestazione di 48 ore"}, "48H_lv_yue_lv__desc": {"message": "Rapporto tra il numero di ordini ritirati o consegnati entro 48 ore e il numero totale di ordini"}, "72H": {"message": "72 ore"}, "7D_shang_xin": {"message": "<PERSON>uovi arrivi <PERSON>"}, "7D_wu_li_you": {"message": "7 giorni di assistenza gratuita"}, "ABS_title_text": {"message": "Questa inserzione include una storia del marchio"}, "AC_title_text": {"message": "Questa inserzione ha il badge Amazon's Choice"}, "A_title_text": {"message": "Questa inserzione ha una pagina di contenuto A+"}, "BS_title_text": {"message": "Questa inserzione è classificata come $num$ Best Seller nella categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) significa che questa inserzione fa parte di un evento \"promozione di 7 giorni\""}, "NR_title_text": {"message": "Questa inserzione è classificata come $num$ New Release nella categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Questa inserzione ha un annuncio video, un tipo di annuncio PPC che di solito appare al centro dei risultati di ricerca"}, "SB_title_text": {"message": "Questa inserzione ha un annuncio del marchio, un tipo di annuncio PPC che di solito appare in cima o in fondo ai risultati di ricerca"}, "SP_title_text": {"message": "Questa inserzione ha un annuncio Sponsored Product"}, "V_title_text": {"message": "Questa inserzione ha un'introduzione video"}, "advanced_research": {"message": "Ricerca avanzata"}, "agent_ds1688___my_order": {"message": "i miei ordini"}, "agent_ds1688__add_to_cart": {"message": "Acquisto all'estero"}, "agent_ds1688__cart": {"message": "Carrello della spesa"}, "agent_ds1688__desc": {"message": "Fornito da 1688. Supporta l'acquisto diretto dall'estero, il pagamento in USD e la consegna al magazzino di transito in Cina."}, "agent_ds1688__freight": {"message": "Calcolatore dei costi di spedizione"}, "agent_ds1688__help": {"message": "<PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Lettera di vettura"}, "agent_ds1688__profile": {"message": "Centro personale"}, "agent_ds1688__warehouse": {"message": "Il mio magazzino"}, "ai_comment_analysis_advantage": {"message": "Pro"}, "ai_comment_analysis_ai": {"message": "Analisi delle recensioni AI"}, "ai_comment_analysis_available": {"message": "Disponibile"}, "ai_comment_analysis_balance": {"message": "Monete insufficienti, ricarica"}, "ai_comment_analysis_behavior": {"message": "Comportamento"}, "ai_comment_analysis_characteristic": {"message": "Caratteristiche della folla"}, "ai_comment_analysis_comment": {"message": "Il prodotto non ha abbastanza recensioni per trarre conclusioni accurate, seleziona un prodotto con più recensioni."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON> stimato"}, "ai_comment_analysis_default": {"message": "Recensioni predefinite"}, "ai_comment_analysis_desire": {"message": "Aspettative del cliente"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "Tentativi gratuiti"}, "ai_comment_analysis_freeNum": {"message": "1 credito gratuito verrà utilizzato"}, "ai_comment_analysis_go_recharge": {"message": "Vai alla ricarica"}, "ai_comment_analysis_intelligence": {"message": "Analisi delle recensioni intelligenti"}, "ai_comment_analysis_location": {"message": "Posizione"}, "ai_comment_analysis_motive": {"message": "Motivazione all'acquisto"}, "ai_comment_analysis_network_error": {"message": "Errore di rete, riprova"}, "ai_comment_analysis_normal": {"message": "Recensioni fotografiche"}, "ai_comment_analysis_number_reviews": {"message": "Numero di recensioni: $num$, Consumo stimato: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Commenti generali"}, "ai_comment_analysis_percentage": {"message": "Percent<PERSON><PERSON>"}, "ai_comment_analysis_problem": {"message": "Problemi con il pagamento"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "Motivo"}, "ai_comment_analysis_recharge": {"message": "Ricarica"}, "ai_comment_analysis_recharged": {"message": "<PERSON> ricaricato"}, "ai_comment_analysis_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_scene": {"message": "Scenario di utilizzo"}, "ai_comment_analysis_start": {"message": "Inizia l'analisi"}, "ai_comment_analysis_subject": {"message": "Argomenti"}, "ai_comment_analysis_time": {"message": "Tempo di utilizzo"}, "ai_comment_analysis_tool": {"message": "Strumento IA"}, "ai_comment_analysis_user_portrait": {"message": "Profilo utente"}, "ai_comment_analysis_welcome": {"message": "Benvenuto all'analisi delle recensioni AI"}, "ai_comment_analysis_year": {"message": "Commenti dell'anno passato"}, "ai_listing_Exclude_keywords": {"message": "Escludi parole chiave"}, "ai_listing_Login_the_feature": {"message": "Per utilizzare la funzionalità è necessario effettuare l'accesso"}, "ai_listing_aI_generation": {"message": "Generazione dell'intelligenza artificiale"}, "ai_listing_add_automatic": {"message": "Automatico"}, "ai_listing_add_dictionary_new": {"message": "Crea una nuova libreria"}, "ai_listing_add_enter_keywords": {"message": "Inserisci le parole chiave"}, "ai_listing_add_inputkey_selling": {"message": "Inserisci un punto vendita e premi $key$ per completare l'aggiunta", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limite superato, fino a $amount$ punti vendita", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Aggiungi parole chiave"}, "ai_listing_add_manually": {"message": "Aggiungi manualmente"}, "ai_listing_add_selling": {"message": "Aggiungi punti vendita"}, "ai_listing_added_keywords": {"message": "Aggiunte parole chiave"}, "ai_listing_added_successfully": {"message": "Aggiunto con successo"}, "ai_listing_addexcluded_keywords": {"message": "Inserisci le parole chiave escluse, premi Invio per completare l'aggiunta."}, "ai_listing_adding_selling": {"message": "Aggiunti punti vendita"}, "ai_listing_addkeyword_enter": {"message": "Digita le parole chiave degli attributi e premi Invio per completare l'aggiunta"}, "ai_listing_ai_description": {"message": "Libreria di parole descrittive dell'intelligenza artificiale"}, "ai_listing_ai_dictionary": {"message": "Libreria di parole del titolo AI"}, "ai_listing_ai_title": {"message": "Titolo dell'AI"}, "ai_listing_aidescription_repeated": {"message": "Il nome della libreria delle parole di descrizione AI non può essere ripetuto"}, "ai_listing_aititle_repeated": {"message": "Il nome della libreria delle parole del titolo AI non può essere ripetuto"}, "ai_listing_data_comes_from": {"message": "<PERSON>i dati provengono da:"}, "ai_listing_deleted_successfully": {"message": "Eliminato con successo"}, "ai_listing_dictionary_name": {"message": "Nome della libreria"}, "ai_listing_edit_dictionary": {"message": "Modifica libreria..."}, "ai_listing_edit_word_library": {"message": "Modifica la libreria di parole"}, "ai_listing_enter_keywords": {"message": "Inserisci le parole chiave e premi $key$ per completare l'aggiunta", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "È stato superato il limite massimo di parole chiave $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Libreria di parole esclusa"}, "ai_listing_generate_characters": {"message": "<PERSON>ra personaggi"}, "ai_listing_generation_platform": {"message": "Piattaforma di generazione"}, "ai_listing_help_optimize": {"message": "Aiutami a ottimizzare il titolo del prodotto, il titolo originale è"}, "ai_listing_include_selling": {"message": "Altri punti di forza includevano:"}, "ai_listing_included_keyword": {"message": "<PERSON><PERSON><PERSON> chiave incluse"}, "ai_listing_included_keywords": {"message": "<PERSON><PERSON><PERSON> chiave incluse"}, "ai_listing_input_selling": {"message": "Inserisci un punto vendita"}, "ai_listing_input_selling_fit": {"message": "Inserisci i punti vendita corrispondenti al titolo"}, "ai_listing_input_selling_please": {"message": "Inserisci i punti vendita"}, "ai_listing_intelligently_title": {"message": "Inserisci il contenuto richiesto qui sopra per generare il titolo in modo intelligente"}, "ai_listing_keyword_product_title": {"message": "<PERSON>lo del prodotto con parola chiave"}, "ai_listing_keywords_repeated": {"message": "Le parole chiave non possono essere ripetute"}, "ai_listing_listed_selling_points": {"message": "Punti vendita inclusi"}, "ai_listing_long_title_1": {"message": "Contiene informazioni di base come nome del marchio, tipo di prodotto, caratteristiche del prodotto, ecc."}, "ai_listing_long_title_2": {"message": "Sulla base del titolo standard del prodotto vengono aggiunte parole chiave utili alla SEO."}, "ai_listing_long_title_3": {"message": "Oltre a contenere nome del marchio, tipo di prodotto, caratteristiche del prodotto e parole chiave, vengono incluse anche parole chiave a coda lunga per ottenere posizionamenti più elevati in query di ricerca specifiche e segmentate."}, "ai_listing_longtail_keyword_product_title": {"message": "<PERSON><PERSON> del prodotto con parole chiave a coda lunga"}, "ai_listing_manually_enter": {"message": "Inserisci manualmente..."}, "ai_listing_network_not_working": {"message": "Internet non è disponibile, per accedere a ChatGPT è necessaria una VPN"}, "ai_listing_new_dictionary": {"message": "Crea una nuova libreria di parole..."}, "ai_listing_new_generate": {"message": "creare"}, "ai_listing_optional_words": {"message": "Parole facoltative"}, "ai_listing_original_title": {"message": "<PERSON><PERSON> originale"}, "ai_listing_other_keywords_included": {"message": "Altre parole chiave includevano:"}, "ai_listing_please_again": {"message": "Per <PERSON>e rip<PERSON>a"}, "ai_listing_please_select": {"message": "Sono stati generati i seguenti titoli per te, seleziona:"}, "ai_listing_product_category": {"message": "Categoria di prodotto"}, "ai_listing_product_category_is": {"message": "La categoria del prodotto è"}, "ai_listing_product_category_to": {"message": "A quale categoria appartiene il prodotto?"}, "ai_listing_random_keywords": {"message": "Parole chiave casuali $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Punti vendita casuali di $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomizza dalla libreria di parole"}, "ai_listing_search_selling": {"message": "Cerca per punto vendita"}, "ai_listing_select_product_categories": {"message": "Seleziona automaticamente le categorie di prodotti."}, "ai_listing_select_product_selling_points": {"message": "Seleziona automaticamente i punti vendita dei prodotti"}, "ai_listing_select_word_library": {"message": "Seleziona la libreria di parole"}, "ai_listing_selling": {"message": "Punti vendita"}, "ai_listing_selling_ask": {"message": "Quali altri requisiti di vendita sono previsti per il titolo?"}, "ai_listing_selling_optional": {"message": "Punti vendita opzionali"}, "ai_listing_selling_repeat": {"message": "I punti non possono essere duplicati"}, "ai_listing_set_excluded": {"message": "Imposta come libreria di parole escluse"}, "ai_listing_set_include_selling_points": {"message": "<PERSON>ludi punti vendita"}, "ai_listing_set_included": {"message": "Imposta come libreria di parole inclusa"}, "ai_listing_set_selling_dictionary": {"message": "Imposta come libreria di punti vendita"}, "ai_listing_standard_product_title": {"message": "Titolo del prodotto standard"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON> trado<PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Visita ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Quali altre parole chiave sono richieste per il titolo?"}, "aliprice_coupons_apply_again": {"message": "Applica di nuovo"}, "aliprice_coupons_apply_coupons": {"message": "Applicare coupon"}, "aliprice_coupons_apply_success": {"message": "Coupon trovato: risparmia $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Test dei codici per le migliori offerte..."}, "aliprice_coupons_applying_desc": {"message": "Pagamento in uscita: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continua alla cassa"}, "aliprice_coupons_found_coupons": {"message": "Abbiamo trovato coupon da $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Pronto per il checkout? Assicuriamoci di ottenere il miglior prezzo!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Quei codici non funzionavano. Nessun problema: stai già ottenendo il miglior prezzo."}, "aliprice_coupons_toolbar_btn": {"message": "Ottieni coupon"}, "aliww_translate": {"message": "Traduttore di chat di <PERSON>"}, "aliww_translate_supports": {"message": "Supporto: 1688 e Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON><PERSON> chiave"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON> tutto"}, "amazon_extended_keywords_more": {"message": "Altro"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Ordina per vendite cumulative"}, "an_lei_xing_cha_kan": {"message": "Tipo"}, "an_yue_dai_xiao_pai_xu": {"message": "Classifica per vendite dropshipping"}, "apra_btn__cat_name": {"message": "Analisi delle recensioni"}, "apra_chart__name": {"message": "Percentuale di vendite di prodotti per paese"}, "apra_chart__update_at": {"message": "Tempo di aggiornamento $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statistiche di vendita dei paesi"}, "auto_opening": {"message": "Apertura automatica in $num$ secondi", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Avanti $autoPaging$ pagine", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Media nei giorni di scaffale"}, "average_hui_fu_lv": {"message": "Tasso medio di risposta"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Livello medio dei fornitori"}, "average_price": {"message": "Prezzo medio"}, "average_qi_ding_liang": {"message": "MOQ medio"}, "average_rating": {"message": "Voto medio"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Anni medi di certificazione"}, "average_revenue": {"message": "Entrate medie"}, "average_revenue_per_product": {"message": "Entrate totali ÷ Numero di prodotti"}, "average_sales": {"message": "Vendite medie"}, "average_sales_per_product": {"message": "Vendite totali ÷ Numero di prodotti"}, "bao_han": {"message": "<PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "<PERSON><PERSON><PERSON>"}, "bian_ti_shu": {"message": "Variazioni"}, "biao_ti": {"message": "<PERSON><PERSON>"}, "blacklist_add_blacklist": {"message": "Blocca questo negozio"}, "blacklist_address_incorrect": {"message": "L'indirizzo non è corretto. Per favore controllalo."}, "blacklist_blacked_out": {"message": "Il negozio è stato bloccato"}, "blacklist_blacklist": {"message": "Blacklist"}, "blacklist_no_records_yet": {"message": "Nessun record ancora!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Intermediario di acquisto"}, "btn_aliprice_agent__dropshipping": {"message": "Scaricare la consegna"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON>"}, "btn_refresh": {"message": "rica<PERSON><PERSON>"}, "btn_try_it_now": {"message": "<PERSON><PERSON><PERSON> ora"}, "btn_txt_view_on_aliprice": {"message": "Visualizza su AliPrice"}, "bu_bao_han": {"message": "Non contiene"}, "bulk_copy_links": {"message": "Link di copia in blocco"}, "bulk_copy_products": {"message": "Prodotti di copia in blocco"}, "cai_gou_zi_xun": {"message": "Assistenza clienti"}, "cai_gou_zi_xun__desc": {"message": "Tasso di risposta in tre minuti del venditore"}, "can_ping_lei_xing": {"message": "Tipo"}, "cao_zuo": {"message": "Operazione"}, "chan_pin_ID": {"message": "Co<PERSON> prodotto"}, "chan_pin_e_wai_xin_xi": {"message": "Prodotto Informazioni extra"}, "chan_pin_lian_jie": {"message": "Collegamento del prodotto"}, "cheng_li_shi_jian": {"message": "Tempo di costituzione"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Fai clic su Copia informazioni sul prodotto"}, "commmon_txt_expired": {"message": "Scaduto"}, "common__date_range_12m": {"message": "1 anno"}, "common__date_range_1m": {"message": "1 mese"}, "common__date_range_1w": {"message": "1 settimana"}, "common__date_range_2w": {"message": "2 settimane"}, "common__date_range_3m": {"message": "3 mesi"}, "common__date_range_3w": {"message": "3 settimane"}, "common__date_range_6m": {"message": "6 mesi"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "<PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON>"}, "common_btn_setting": {"message": "Impostare"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON><PERSON> dato"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato."}, "common_favorites": {"message": "Preferiti"}, "common_feedback": {"message": "Risposta"}, "common_help": {"message": "<PERSON><PERSON>"}, "common_loading": {"message": "Caricamento in corso"}, "common_login": {"message": "Accesso"}, "common_logout": {"message": "<PERSON>nne<PERSON><PERSON>"}, "common_no": {"message": "No"}, "common_powered_by_aliprice": {"message": "Alimentato da AliPrice.com"}, "common_setting": {"message": "Ambientazione"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_system_upgrading_title": {"message": "Aggiornamento del sistema"}, "common_system_upgrading_txt": {"message": "Per favore prova più tardi"}, "common_txt__currency": {"message": "Moneta"}, "common_txt__video_tutorial": {"message": "Esercitazione video"}, "common_txt_ago_time": {"message": "$time$ giorni fa", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "<PERSON><PERSON>"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Quasi mai usato"}, "common_txt_biaoti_link": {"message": "Titolo+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Titolo+Link+Nome del negozio"}, "common_txt_blacklist": {"message": "Lista di Bloccati"}, "common_txt_cancel": {"message": "Cancellare"}, "common_txt_category": {"message": "Categoria"}, "common_txt_chakan": {"message": "Controllo"}, "common_txt_colors": {"message": "colori"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Copiato"}, "common_txt_copy": {"message": "copia"}, "common_txt_copy_link": {"message": "Copia link"}, "common_txt_copy_title": {"message": "Copia titolo"}, "common_txt_copy_title__link": {"message": "Copia titolo e collegamento"}, "common_txt_day": {"message": "cielo"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Elimina"}, "common_txt_dian_pu_link": {"message": "Copia il nome del negozio + link"}, "common_txt_download": {"message": "Scarica"}, "common_txt_downloaded": {"message": "Scaricamento"}, "common_txt_export_as_csv": {"message": "Esporta Excel"}, "common_txt_export_as_txt": {"message": "Esportazione testo"}, "common_txt_fail": {"message": "Fallire"}, "common_txt_format": {"message": "Formato"}, "common_txt_get": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_incert_selection": {"message": "Inverti selezione"}, "common_txt_install": {"message": "Installare"}, "common_txt_load_failed": {"message": "Caricamento fallito"}, "common_txt_month": {"message": "mese"}, "common_txt_more": {"message": "Di <PERSON>"}, "common_txt_new_unused": {"message": "Nuovo di zecca, inutilizzato"}, "common_txt_next": {"message": "Il prossimo"}, "common_txt_no_limit": {"message": "Illimitato"}, "common_txt_no_noticeable": {"message": "<PERSON>essun graffio o sporco visibile"}, "common_txt_on_sale": {"message": "Disponibile"}, "common_txt_opt_in_out": {"message": "Acceso spento"}, "common_txt_order": {"message": "Ordine"}, "common_txt_others": {"message": "<PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "Condizioni generali pessime"}, "common_txt_patterns": {"message": "<PERSON><PERSON>"}, "common_txt_platform": {"message": "Piattaforme"}, "common_txt_please_select": {"message": "Si prega di selezionare"}, "common_txt_prev": {"message": "Prec"}, "common_txt_price": {"message": "Prezzo"}, "common_txt_privacy_policy": {"message": "politica sulla riservatezza"}, "common_txt_product_condition": {"message": "Stato del prodotto"}, "common_txt_rating": {"message": "Valutazione"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "Rica<PERSON>re"}, "common_txt_reset": {"message": "R<PERSON><PERSON><PERSON>"}, "common_txt_retail": {"message": "Al dettaglio"}, "common_txt_review": {"message": "Revisione"}, "common_txt_sale": {"message": "Disponibile"}, "common_txt_same": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Con graffi e sporcizia"}, "common_txt_search_title": {"message": "Titolo di ricerca"}, "common_txt_select_all": {"message": "Se<PERSON><PERSON>na tutto"}, "common_txt_selected": {"message": "selezionato"}, "common_txt_share": {"message": "Condividere"}, "common_txt_sold": {"message": "venduto"}, "common_txt_sold_out": {"message": "esaurito"}, "common_txt_some_scratches": {"message": "Alcuni graffi e sporcizia"}, "common_txt_sort_by": {"message": "Ordina per"}, "common_txt_state": {"message": "Stato"}, "common_txt_success": {"message": "Successo"}, "common_txt_sys_err": {"message": "errore di sistema"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_total": {"message": "tutti"}, "common_txt_unselect_all": {"message": "Inverti selezione"}, "common_txt_upload_image": {"message": "Carica immagine"}, "common_txt_visit": {"message": "Visita"}, "common_txt_whitelist": {"message": "Lista bianca"}, "common_txt_wholesale": {"message": "<PERSON><PERSON>ita all'ingrosso"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "sì"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON>a tutto"}, "compare_tool_btn_compare": {"message": "Confrontare"}, "compare_tool_btn_contact": {"message": "Contat<PERSON>"}, "compare_tool_tips_max_compared": {"message": "Aggiungi fino a $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configura notifiche"}, "contact_us": {"message": "Con<PERSON><PERSON><PERSON>"}, "context_menu_screenshot_search": {"message": "Cattura per cercare per immagine"}, "context_menus_aliprice_search_by_image": {"message": "Cerca immagine su AliPrice"}, "context_menus_goote_trans": {"message": "Traduci pagina/Mostra originale"}, "context_menus_search_by_image": {"message": "Cerca per immagine su $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Acquisisci in $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Cattura per tradurre"}, "converter_modal_amount_placeholder": {"message": "Inserisci qui l'importo"}, "converter_modal_btn_convert": {"message": "convertire"}, "converter_modal_exchange_rate_source": {"message": "I dati provengono dal tasso di cambio $boc$ Tempo di aggiornamento: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Conversione di valuta"}, "converter_modal_search_placeholder": {"message": "ricerca valuta"}, "copy_all_contact_us_notice": {"message": "Questo sito non è supportato al momento, contattaci"}, "copy_product_info": {"message": "Copia le informazioni sul prodotto"}, "copy_suggest_search_kw": {"message": "Copia elenchi a discesa"}, "country__han_gou": {"message": "Corea del Sud"}, "country__ri_ben": {"message": "Giappone"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Tasso di cambio personalizzato"}, "currency_convert__sync_server": {"message": "Sincronizza il server"}, "dang_ri_fa_huo": {"message": "Spedizione lo stesso giorno"}, "dao_chu_quan_dian_shang_pin": {"message": "Esporta tutti i prodotti del negozio"}, "dao_chu_wei_CSV": {"message": "Esportare"}, "dao_chu_zi_duan": {"message": "Campi di esportazione"}, "delivery_address": {"message": "Indirizzo di spedizione"}, "delivery_company": {"message": "Corriere"}, "di_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "Fare clic per interrogare"}, "dian_pu_ID": {"message": "ID del negozio"}, "dian_pu_di_zhi": {"message": "Indirizzo del negozio"}, "dian_pu_lian_jie": {"message": "Collegamento al negozio"}, "dian_pu_ming": {"message": "Nome del negozio"}, "dian_pu_ming_cheng": {"message": "Nome del negozio"}, "dian_pu_shang_pin_zong_hsu": {"message": "Numero totale di prodotti nel negozio: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Informazione di magazzino"}, "ding_zai_zuo_ce": {"message": "inchiodato a sinistra"}, "disable_old_version_tips_disable_btn_title": {"message": "Disabilita la vecchia versione"}, "download_image__SKU_variant_images": {"message": "Immagini varianti SKU"}, "download_image__assume": {"message": "Ad esempio, abbiamo 2 immagini, product1.jpg e product2.gif.\nimg_{$no$} verrà rinominato in img_01.jpg, img_02.gif;\n{$group$}_{$no$} verrà rinominato in main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Download batch"}, "download_image__combined_image": {"message": "Immagine combinata dei dettagli del prodotto"}, "download_image__continue_downloading": {"message": "Continua a scaricare"}, "download_image__description_images": {"message": "Immagini descrizione"}, "download_image__download_combined_image": {"message": "Scarica l'immagine combinata dei dettagli del prodotto"}, "download_image__download_zip": {"message": "Scarica zip"}, "download_image__enlarge_check": {"message": "Supporta solo immagini JPEG, JPG, GIF e PNG, dimensione massima di una singola immagine: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Ingrandisci immagine"}, "download_image__export": {"message": "Esporta link"}, "download_image__height": {"message": "Altezza"}, "download_image__ignore_videos": {"message": "Il video è stato ignorato, in quanto non può essere esportato"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "immagine principale"}, "download_image__multi_folder": {"message": "Multi-cartella"}, "download_image__name": {"message": "scarica immagine"}, "download_image__notice_content": {"message": "Per favore non selezionare \"Chiedi dove salvare ogni file prima di scaricarlo\" nelle impostazioni di download del tuo browser!!! Altrimenti ci saranno molte finestre di dialogo."}, "download_image__notice_ignore": {"message": "Non richiedere più questo messaggio"}, "download_image__order_number": {"message": "{$no$} numero di serie; {$group$} nome gruppo; {$date$} timestamp", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Panoramica"}, "download_image__prompt_download_zip": {"message": "Ci sono troppe immagini, è meglio scaricarle come cartella zip."}, "download_image__rename": {"message": "Rinomina"}, "download_image__rule": {"message": "Regole di denominazione"}, "download_image__single_folder": {"message": "Singola cartella"}, "download_image__sku_image": {"message": "Immagini SKU"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Scarica l'immagine della recensione"}, "download_reviews__dropdown_title": {"message": "Scarica l'immagine della recensione"}, "download_reviews__export_csv": {"message": "esporta CSV"}, "download_reviews__no_images": {"message": "0 immagini disponibili per il download"}, "download_reviews__no_reviews": {"message": "Nessuna recensione da scaricare!"}, "download_reviews__notice": {"message": "Mancia:"}, "download_reviews__notice__chrome_settings": {"message": "Imposta il browser Chrome per chiedere dove salvare ogni file prima del download, imposta su \"Off\""}, "download_reviews__notice__wait": {"message": "A seconda del numero di recensioni, il tempo di attesa potrebbe essere più lungo"}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON>"}, "download_reviews__pages_list__page": {"message": "Pagine $page$ precedenti", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Gamma di selezione"}, "export_shopping_cart__csv_filed__details_url": {"message": "<PERSON> al prodotto"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Collegamento SKU eco"}, "export_shopping_cart__csv_filed__images": {"message": "Link all'immagine"}, "export_shopping_cart__csv_filed__quantity": {"message": "Quantità"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Prezzo"}, "export_shopping_cart__csv_filed__specs": {"message": "Specifiche tecniche"}, "export_shopping_cart__csv_filed__store_name": {"message": "Nome del negozio"}, "export_shopping_cart__csv_filed__store_url": {"message": "Link al negozio"}, "export_shopping_cart__csv_filed__title": {"message": "Nome del prodotto"}, "export_shopping_cart__export_btn": {"message": "Esportare"}, "export_shopping_cart__export_empty": {"message": "Se<PERSON>ziona un prodotto!"}, "fa_huo_shi_jian": {"message": "Spedizione"}, "favorite_add_email": {"message": "Aggiungi e-mail"}, "favorite_add_favorites": {"message": "Aggiungi ai preferiti"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Avviso di calo dei prezzi."}, "favorite_btn_notify": {"message": "Tieni traccia del prezzo"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON> i prodotti"}, "favorite_current_price": {"message": "Prezzo attuale"}, "favorite_due_date": {"message": "Data di scadenza"}, "favorite_enable_notification": {"message": "Abilita la notifica email"}, "favorite_expired": {"message": "Scaduto"}, "favorite_go_to_enable": {"message": "Vai ad abilitare"}, "favorite_msg_add_success": {"message": "Aggiunto ai preferiti"}, "favorite_msg_del_success": {"message": "Eliminato dai preferiti"}, "favorite_msg_failure": {"message": "Fallire! Aggiorna la pagina e riprova."}, "favorite_please_add_email": {"message": "Aggiungi e-mail"}, "favorite_price_drop": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_price_rise": {"message": "Su"}, "favorite_price_untracked": {"message": "Prezzo non monitorato"}, "favorite_saved_price": {"message": "Prezzo salvato"}, "favorite_stop_tracking": {"message": "Interrompi <PERSON>aggio"}, "favorite_sub_email_address": {"message": "Indirizzo email di abbonamento"}, "favorite_tracking_period": {"message": "Periodo di tracciamento"}, "favorite_tracking_prices": {"message": "Tracciamento dei prezzi"}, "favorite_verify_email": {"message": "Verifica l'indirizzo email"}, "favorites_list_remove_prompt_msg": {"message": "Sei sicuro di cancellarlo?"}, "favorites_update_button": {"message": "Aggiorna i prezzi adesso"}, "fen_lei": {"message": "Categoria"}, "fen_xia_yan_xuan": {"message": "Scelta del distributore"}, "find_similar": {"message": "Trova simile"}, "first_ali_price_date": {"message": "La data della prima acquisizione da parte del crawler AliPrice"}, "fooview_coupons_modal_no_data": {"message": "N<PERSON>un coupon"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Prezzo < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "o prezzo > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Scadenza"}, "fooview_favorites_error_msg_no_favorites": {"message": "Aggiungi qui i prodotti preferiti per ricevere un avviso di riduzione dei prezzi."}, "fooview_favorites_filter_latest": {"message": "Ultimo"}, "fooview_favorites_filter_price_drop": {"message": "FUORI USO"}, "fooview_favorites_filter_price_up": {"message": "SU"}, "fooview_favorites_modal_title": {"message": "I miei preferiti"}, "fooview_favorites_modal_title_title": {"message": "Vai ad AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "Per monitorare il prezzo"}, "fooview_price_history_app_price": {"message": "Prezzo APP:"}, "fooview_price_history_title": {"message": "Cronologia dei prezzi"}, "fooview_product_list_feedback": {"message": "Risposta"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "Prezzo"}, "fooview_reviews_error_msg_no_review": {"message": "Non abbiamo trovato recensioni per questo prodotto."}, "fooview_reviews_filter_buyer_reviews": {"message": "Foto degli acquirenti"}, "fooview_reviews_modal_title": {"message": "Recensioni"}, "fooview_same_product_choose_category": {"message": "Scegli la categoria"}, "fooview_same_product_filter_feedback": {"message": "Risposta"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "Prezzo"}, "fooview_same_product_filter_rating": {"message": "Valutazione"}, "fooview_same_product_modal_title": {"message": "<PERSON><PERSON><PERSON> lo stesso prodotto"}, "fooview_same_product_search_by_image": {"message": "Cerca per immagine"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON> del venditore"}, "for_12_months": {"message": "Per 1 anno"}, "for_12_months_list_pro": {"message": "12 mesi"}, "for_12_months_nei": {"message": "Entro 12 mesi"}, "for_1_months": {"message": "1 mese"}, "for_1_months_nei": {"message": "Entro 1 mese"}, "for_3_months": {"message": "Per 3 mesi"}, "for_3_months_nei": {"message": "Entro 3 mesi"}, "for_6_months": {"message": "Per 6 mesi"}, "for_6_months_nei": {"message": "Entro 6 mesi"}, "for_9_months": {"message": "9 mesi"}, "for_9_months_nei": {"message": "Entro 9 mesi"}, "fu_gou_lv": {"message": "Tasso di riacquisto"}, "gao_liang_bu_tong_dian": {"message": "evidenziare differenze"}, "gao_liang_guang_gao_chan_pin": {"message": "Evidenzia i prodotti pubblicitari"}, "geng_duo_xin_xi": {"message": "Ulteriori informazioni"}, "geng_xin_shi_jian": {"message": "Tempo di aggiornamento"}, "get_store_products_fail_tip": {"message": "Fare clic su OK per andare alla verifica per garantire l'accesso normale"}, "gong_x_kuan_shang_pin": {"message": "Un totale di $amount$ prodotti", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Fornitore"}, "gong_ying_shang_ID": {"message": "Identificativo del fornitore"}, "gong_ying_shang_deng_ji": {"message": "Valutazione del fornitore"}, "gong_ying_shang_nian_zhan": {"message": "Il fornitore è più vecchio"}, "gong_ying_shang_xin_xi": {"message": "informazioni sui fornitori"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Collegamento alla home page del fornitore"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Prezzo di vendita stimato"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON> chia<PERSON>"}, "guang_gao_chan_pin": {"message": "Prodotti pubblicitari"}, "guang_gao_zhan_bi": {"message": "Rapporto pubblicitario"}, "guo_ji_wu_liu_yun_fei": {"message": "Tassa di spedizione internazionale"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "Valutazione positiva"}, "highest_price": {"message": "Alto"}, "historical_trend": {"message": "Andamento storico"}, "how_to_screenshot": {"message": "Tieni premuto il pulsante sinistro del mouse per selezionare l'area, tocca il pulsante destro del mouse o il tasto Esc per uscire dallo screenshot"}, "howt_it_works": {"message": "Come funziona"}, "hui_fu_lv": {"message": "Tasso di risposta"}, "hui_tou_lv": {"message": "tasso di ritorno"}, "inquire_freightFee": {"message": "Richiesta di spedizione"}, "inquire_freightFee_Yuan": {"message": "Spedizione/Yuan"}, "inquire_freightFee_province": {"message": "Provincia"}, "inquire_freightFee_the": {"message": "La spedizione è di $num$, il che significa che la regione ha la spedizione gratuita.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON>."}, "jia_ge": {"message": "Prezzo"}, "jia_ge_dan_wei": {"message": "Unità"}, "jia_ge_qu_shi": {"message": "Tendenza"}, "jia_zai_n_ge_shang_pin": {"message": "Carica $num$ prodotti", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Percentuale del volume delle vendite negli ultimi 30 giorni"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Percentuale del fatturato negli ultimi 30 giorni"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Vendite totali negli ultimi 30 giorni"}, "jin_30d_xiao_shou_e": {"message": "Turnover"}, "jin_30d_xiao_shou_e__desc": {"message": "Fatturato totale negli ultimi 30 giorni"}, "jin_90_tian_mai_jia_shu": {"message": "Acquirenti negli ultimi 90 giorni"}, "jin_90_tian_xiao_shou_liang": {"message": "Vendite negli ultimi 90 giorni"}, "jing_xuan_huo_yuan": {"message": "Fonti selezionate"}, "jing_ying_mo_shi": {"message": "Modello di business"}, "jing_ying_mo_shi__gong_chang": {"message": "Produttore"}, "jiu_fen_jie_jue": {"message": "Soluzione della disputa"}, "jiu_fen_jie_jue__desc": {"message": "Contabilità delle controversie sui diritti dei negozi dei venditori"}, "jiu_fen_lv": {"message": "Tasso di controversia"}, "jiu_fen_lv__desc": {"message": "Proporzione di ordini con reclami completati negli ultimi 30 giorni e ritenuti di responsabilità del venditore o di entrambe le parti"}, "kai_dian_ri_qi": {"message": "Data di apertura"}, "keywords": {"message": "<PERSON><PERSON><PERSON> chiave"}, "kua_jin_Select_pan_huo": {"message": "Selezione transfrontaliera"}, "last15_days": {"message": "Ultimi 15 giorni"}, "last180_days": {"message": "<PERSON>lt<PERSON>i 180 giorni"}, "last30_days": {"message": "Negli ultimi 30 giorni"}, "last360_days": {"message": "Ultimi 360 giorni"}, "last45_days": {"message": "Ultimi 45 giorni"}, "last60_days": {"message": "Ultimi 60 giorni"}, "last7_days": {"message": "Ultimi 7 giorni"}, "last90_days": {"message": "Ultimi 90 giorni"}, "last_30d_sales": {"message": "Vendite degli ultimi 30 giorni"}, "lei_ji": {"message": "Cumulativo"}, "lei_ji_xiao_liang": {"message": "Totale"}, "lei_ji_xiao_liang__desc": {"message": "<PERSON>tte le vendite dopo il prodotto sullo scaffale"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volume di vendite cumulativo negli ultimi 30 giorni, ordinato dal più alto al più basso"}, "lian_xi_fang_shi": {"message": "Informazioni sui contatti"}, "list_time": {"message": "Alla data di scadenza"}, "load_more": {"message": "Carica altro"}, "login_to_aliprice": {"message": "Accedi ad AliPrice"}, "long_link": {"message": "lungo"}, "lowest_price": {"message": "<PERSON><PERSON>"}, "mai_jia_shu": {"message": "Numero venditori"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON> lordo"}, "mobile_view__dkxbqy": {"message": "Apri una nuova scheda"}, "mobile_view__sjdxq": {"message": "Dettagli nell'app"}, "mobile_view__sjdxqy": {"message": "Pagina dei dettagli nell'app"}, "mobile_view__smck": {"message": "Scansiona per visualizzare"}, "mobile_view__smckms": {"message": "Utilizza la fotocamera o l'app per scansionare e visualizzare"}, "modified_failed": {"message": "Modifica non riuscita"}, "modified_successfully": {"message": "Modificato con successo"}, "nav_btn_favorites": {"message": "Le mie collezioni"}, "nav_btn_package": {"message": "<PERSON><PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Sul prodotto"}, "nav_btn_viewed": {"message": "Visto"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON><PERSON>"}, "normal_link": {"message": "normale"}, "notice": {"message": "suggerimento"}, "number_reviews": {"message": "Recensioni"}, "only_show_num": {"message": "Totale prodotti: $allnum$, Nascosto: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Rimuovi non selezionato"}, "open": {"message": "<PERSON>i"}, "open_links": {"message": "Apri link"}, "options_page_tab_check_links": {"message": "Controlla i link"}, "options_page_tab_gernal": {"message": "Generale"}, "options_page_tab_notifications": {"message": "Notifiche"}, "options_page_tab_others": {"message": "<PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "Cerca per immagine"}, "options_page_tab_shortcuts": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_shortcuts_title": {"message": "Dimensione del carattere per i collegamenti"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON><PERSON>"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Più link di prodotti stanno per essere aperti"}, "order_list_sku_show_title": {"message": "Mostra le varianti selezionate nei link condivisi"}, "orders_last30_days": {"message": "Numero di ordini negli ultimi 30 giorni"}, "pTutorial_favorites_block1_desc1": {"message": "I prodotti che hai tracciato sono elencati qui"}, "pTutorial_favorites_block1_title": {"message": "Preferiti"}, "pTutorial_popup_block1_desc1": {"message": "Un'etichetta verde significa che ci sono prodotti in calo di prezzo"}, "pTutorial_popup_block1_title": {"message": "Scorciatoie e Preferiti"}, "pTutorial_price_history_block1_desc1": {"message": "Fai clic su \"Segui prezzo\", aggiungi prodotti ai preferiti. Una volta che i loro prezzi scendono, riceverai notifiche"}, "pTutorial_price_history_block1_title": {"message": "Tieni traccia del prezzo"}, "pTutorial_reviews_block1_desc1": {"message": "Recensioni degli acquirenti da Itao e foto reali dal feedback di AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Recensioni"}, "pTutorial_reviews_block2_desc1": {"message": "È sempre utile controllare le recensioni di altri acquirenti"}, "pTutorial_same_products_block1_desc1": {"message": "Puoi confrontarli per fare la scelta migliore"}, "pTutorial_same_products_block1_desc2": {"message": "Fai clic su \"Altro\" per \"Ricerca per immagine\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON><PERSON><PERSON> prodo<PERSON>"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Trascina l'immagine del prodotto lì e scegli una categoria"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Cerca per immagine"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Tasso di feedback positivo del venditore, punteggi di feedback e da quanto tempo il venditore è sul mercato"}, "pTutorial_seller_analysis_block1_title": {"message": "Valutazioni del venditore"}, "pTutorial_seller_analysis_block2_desc2": {"message": "La valutazione del venditore si basa su 3 indici: articolo come descritto, velocità di spedizione della comunicazione"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Usiamo 3 colori e icone per indicare i livelli di fiducia dei venditori"}, "page_count": {"message": "Numero di pagine"}, "pai_chu": {"message": "Escluso"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Hong Kong"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Giappone"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Corea"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Kazakistan"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Macao"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Europa orientale"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Arabia Saudita"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Taiwan"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni negli Stati Uniti"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni in Vietnam"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Escludi articoli con restrizioni"}, "payable_price_formula": {"message": "Prezzo + Spedizione + Sconto"}, "pdd_check_retail_btn_txt": {"message": "Verificare la vendita al dettaglio"}, "pdd_pifa_to_retail_btn_txt": {"message": "Acquista al dettaglio"}, "pdp_copy_fail": {"message": "Copia non riuscita!"}, "pdp_copy_success": {"message": "Copia riuscita!"}, "pdp_share_modal_subtitle": {"message": "Condividi screenshot, vedr<PERSON> la tua scelta."}, "pdp_share_modal_title": {"message": "Condividi la tua scelta"}, "pdp_share_screenshot": {"message": "Condividi screenshot"}, "pei_song": {"message": "<PERSON><PERSON><PERSON> spediz<PERSON>"}, "pin_lei": {"message": "Categoria"}, "pin_zhi_ti_yan": {"message": "Qualità del prodotto"}, "pin_zhi_ti_yan__desc": {"message": "Tasso di rimborso della qualità del negozio del venditore"}, "pin_zhi_tui_kuan_lv": {"message": "Tasso di rimborso"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proporzione di ordini che sono stati rimborsati e restituiti solo negli ultimi 30 giorni"}, "ping_fen": {"message": "Valutazione"}, "ping_jun_fa_huo_su_du": {"message": "Velocità media di spedizione"}, "pkgInfo_hide": {"message": "Informazioni logistiche: on/off"}, "pkgInfo_no_trace": {"message": "Nessuna informazione logistica"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "eBay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo all'ingrosso"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "Casa"}, "popup_go_to_platform": {"message": "Vai ad $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Sto acquistando per ..."}, "popup_track_package_btn_track": {"message": "TRACCIA"}, "popup_track_package_desc": {"message": "MONITORAGGIO DEL PACCHETTO ALL-IN-ONE"}, "popup_track_package_search_placeholder": {"message": "Numero di identificazione"}, "popup_translate_search_placeholder": {"message": "Traduci e cerca su $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Cronologia dei prezzi"}, "price_history_chart_tip_ae": {"message": "Suggerimento: il numero di ordini è il numero cumulativo di ordini dal lancio"}, "price_history_chart_tip_coupang": {"message": "Tip: <PERSON><PERSON><PERSON> akan menghapus jumlah pesanan penipuan"}, "price_history_inm_1688_l1": {"message": "Si prega di installare"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant per 1688"}, "price_history_panel_lowest_price": {"message": "Prezzo Pi<PERSON>: "}, "price_history_panel_tab_price_tracking": {"message": "Cronologia prezzi"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON> vendi<PERSON>"}, "price_history_pro_modal_title": {"message": "Cronologia prezzi e Cronologia ordini"}, "privacy_consent__btn_agree": {"message": "Rivedere il consenso alla raccolta dei dati"}, "privacy_consent__btn_disable_all": {"message": "Non accettare"}, "privacy_consent__btn_enable_all": {"message": "Attiva tutto"}, "privacy_consent__btn_uninstall": {"message": "Rimuovere"}, "privacy_consent__desc_privacy": {"message": "Tieni presente che, senza dati o cookie alcune funzioni saranno disattivate perché quelle funzioni necessitano della spiegazione dei dati o dei cookie, ma puoi comunque utilizzare le altre funzioni."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, senza dati o cookie non funzionerà perché abbiamo bisogno della spiegazione di dati o cookie."}, "privacy_consent__desc_privacy_L2": {"message": "Se non ci autorizzi a raccogliere queste informazioni, rimuovile."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, riceviamo i tuoi dati di valuta nei cookie solo quando fai acquisti online per mostrare la cronologia dei prezzi."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Aggiungi i cookie nel browser per identificare in modo anonimo il tuo computer o dispositivo."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Aggiungere i dati funzionali in add-on per lavorare con la funzione."}, "privacy_consent__item_functional_title": {"message": "Cookie funzionali e analitici"}, "privacy_consent__more_desc": {"message": "Tieni presente che non condividiamo i tuoi dati personali con altre società e nessuna società pubblicitaria raccoglie dati tramite il nostro servizio."}, "privacy_consent__options__btn__desc": {"message": "Per utilizzare tutte le funzionalità, è necessario attivarlo."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__options__desc_L1": {"message": "Raccoglieremo i seguenti dati che ti identificano personalmente:"}, "privacy_consent__options__desc_L2": {"message": "- cookie, riceviamo i tuoi dati di valuta nei cookie solo quando acquisti online per mostrare la cronologia dei prezzi."}, "privacy_consent__options__desc_L3": {"message": "- e aggiungi cookie nel Browser per identificare in modo anonimo il tuo computer o dispositivo."}, "privacy_consent__options__desc_L4": {"message": "- altri dati anonimi rendono questa estensione più comoda."}, "privacy_consent__options__desc_L5": {"message": "Tieni presente che non condividiamo i tuoi dati personali con altre società e nessuna società pubblicitaria raccoglie dati tramite il nostro servizio."}, "privacy_consent__privacy_preferences": {"message": "Preferenze sulla privacy"}, "privacy_consent__read_more": {"message": "Leggi di più >>"}, "privacy_consent__title_privacy": {"message": "vita privata"}, "product_info": {"message": "Informazioni sul prodotto"}, "product_recommend__name": {"message": "<PERSON><PERSON><PERSON>"}, "product_research": {"message": "Ricerca sul prodotto"}, "product_sub__email_desc": {"message": "E-mail di avviso sui prezzi"}, "product_sub__email_edit": {"message": "modificare"}, "product_sub__email_not_verified": {"message": "Si prega di verificare l'e-mail"}, "product_sub__email_required": {"message": "Si prega di fornire e-mail"}, "product_sub__form_countdown": {"message": "Chiusura automatica dopo $seconds$ secondi", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Impossibile aggiungere il promemoria!"}, "product_sub__form_input_price": {"message": "Prezzo di input"}, "product_sub__form_item_country": {"message": "nazione"}, "product_sub__form_item_current_price": {"message": "Prezzo attuale"}, "product_sub__form_item_duration": {"message": "traccia"}, "product_sub__form_item_higher_price": {"message": "Oppure prezzo>"}, "product_sub__form_item_invalid_higher_price": {"message": "Il prezzo deve essere maggiore di $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Il prezzo deve essere inferiore a $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Quando il prezzo <"}, "product_sub__form_submit": {"message": "Invia"}, "product_sub__form_success": {"message": "Riuscito ad aggiungere promemoria!"}, "product_sub__high_price_notify": {"message": "Avvisami sugli aumenti di prezzo"}, "product_sub__low_price_notify": {"message": "Avvisami delle riduzioni di prezzo"}, "product_sub__modal_title": {"message": "Promemoria per la modifica del prezzo dell'abbonamento"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ e MOP"}, "qi_ye_mian_ji": {"message": "Zona impresa"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Si prega di selezionare almeno un prodotto"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Seleziona almeno un campo"}, "qu_deng_lu": {"message": "Accedi"}, "quan_guo_yan_xuan": {"message": "Scelta globale"}, "recommendation_popup_banner_btn_install": {"message": "Installalo"}, "recommendation_popup_banner_desc": {"message": "Visualizza la cronologia dei prezzi entro 3/6 mesi e la notifica di riduzione del prezzo"}, "region__all": {"message": "Tutte le regioni"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Abilita clic destro"}, "ren_zheng_gong_chang": {"message": "Fabbrica certificata"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON> come Fornitore Certificato"}, "required_to_aliprice_login": {"message": "Devi accedere ad AliPrice"}, "revenue_last30_days": {"message": "Importo delle vendite negli ultimi 30 giorni"}, "review_counts": {"message": "Numero di collezionisti"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Periodo di ingresso"}, "sales_amount_last30_days": {"message": "Vendite totali negli ultimi 30 giorni"}, "sales_last30_days": {"message": "Vendite negli ultimi 30 giorni"}, "sbi_alibaba_cate__accessories": {"message": "Accessori"}, "sbi_alibaba_cate__aqfk": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bags_cases": {"message": "Borse e custodie"}, "sbi_alibaba_cate__beauty": {"message": "bellezza"}, "sbi_alibaba_cate__beverage": {"message": "Bevanda"}, "sbi_alibaba_cate__bgwh": {"message": "Cultura dell'ufficio"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Stoviglie"}, "sbi_alibaba_cate__clothes": {"message": "Abbigliamento"}, "sbi_alibaba_cate__cmgd": {"message": "Trasmissione multimediale"}, "sbi_alibaba_cate__coat_jacket": {"message": "Cappotto e giacca"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Elettronica di consumo"}, "sbi_alibaba_cate__cryp": {"message": "Prodotti per adulti"}, "sbi_alibaba_cate__csyp": {"message": "Fodere per letti"}, "sbi_alibaba_cate__cwyy": {"message": "Giardinaggio per animali domestici"}, "sbi_alibaba_cate__cysx": {"message": "Ristorazione fresca"}, "sbi_alibaba_cate__dgdq": {"message": "Elettricista"}, "sbi_alibaba_cate__dl": {"message": "Recitazione"}, "sbi_alibaba_cate__dress_suits": {"message": "Abito e abiti"}, "sbi_alibaba_cate__dszm": {"message": "Illuminazione"}, "sbi_alibaba_cate__dzqj": {"message": "Dispositivo elettronico"}, "sbi_alibaba_cate__essb": {"message": "Attrezza<PERSON> usate"}, "sbi_alibaba_cate__food": {"message": "Cibo"}, "sbi_alibaba_cate__fspj": {"message": "vestiti e accessori"}, "sbi_alibaba_cate__furniture": {"message": "Mobilia"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON> tessile"}, "sbi_alibaba_cate__ghjq": {"message": "Cura personale"}, "sbi_alibaba_cate__gt": {"message": "Acciaio"}, "sbi_alibaba_cate__gyp": {"message": "Artigianato"}, "sbi_alibaba_cate__hb": {"message": "Rispettoso dell'ambiente"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON><PERSON> per la cura della pelle"}, "sbi_alibaba_cate__hg": {"message": "Industria chimica"}, "sbi_alibaba_cate__jg": {"message": "in lavorazione"}, "sbi_alibaba_cate__jianccai": {"message": "Materiali da costruzione"}, "sbi_alibaba_cate__jichuang": {"message": "Macchina utensile"}, "sbi_alibaba_cate__jjry": {"message": "Uso domestico quotidiano"}, "sbi_alibaba_cate__jtys": {"message": "Trasporto"}, "sbi_alibaba_cate__jxsb": {"message": "Attrezzatura"}, "sbi_alibaba_cate__jxwj": {"message": "Hardware meccanico"}, "sbi_alibaba_cate__jydq": {"message": "Elettrodomestici"}, "sbi_alibaba_cate__jzjc": {"message": "Materiali da costruzione per la casa"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON> per la casa"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Prodotti per bambini"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "Abbigliamento Donna"}, "sbi_alibaba_cate__ny": {"message": "Energia"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Accessori per auto"}, "sbi_alibaba_cate__qmpj": {"message": "Ricambi auto"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Computer digitale"}, "sbi_alibaba_cate__snqj": {"message": "Stoccaggio e pulizia"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON> be<PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "Servizi per gli affari"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Pantaloni e gonna"}, "sbi_alibaba_cate__txcp": {"message": "Prodotti di comunicazione"}, "sbi_alibaba_cate__tz": {"message": "Vestiti per bambini"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON><PERSON> intima"}, "sbi_alibaba_cate__wjgj": {"message": "Strumenti hardware"}, "sbi_alibaba_cate__xgpi": {"message": "Borse di pelle"}, "sbi_alibaba_cate__xmhz": {"message": "cooperazione progettuale"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON> da cancellare"}, "sbi_alibaba_cate__ydfs": {"message": "Abbigliamento sportivo"}, "sbi_alibaba_cate__ydhw": {"message": "Sport all'aperto"}, "sbi_alibaba_cate__yjkc": {"message": "Minerali metallurgici"}, "sbi_alibaba_cate__yqyb": {"message": "Strumentazione"}, "sbi_alibaba_cate__ys": {"message": "Stampa"}, "sbi_alibaba_cate__yyby": {"message": "Cure mediche"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Numero di acquirenti negli ultimi 90 giorni"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volume delle vendite negli ultimi 90 giorni"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Prezzo stimato"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Spese di spedizione internazionali"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Tassa di spedizione"}, "sbi_alibaba_cn_kj_gssj": {"message": "Prezzo stimato"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Profitto = prezzo stimato x margine di profitto"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Velocità media di consegna"}, "sbi_alibaba_cn_kj_qtfy": {"message": "altra tassa"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Altro costo = prezzo stimato x altro rapporto di costo"}, "sbi_alibaba_cn_kj_spjg": {"message": "Prezzo"}, "sbi_alibaba_cn_kj_spzl": {"message": "Peso"}, "sbi_alibaba_cn_kj_szd": {"message": "Posizione"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Grammo"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Acquirenti"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Imposta"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON>ior<PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON>zzo"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "prezzo"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Disponibile alla vendita"}, "sbi_aliprice_alibaba_cn__moren": {"message": "predefinito"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Sicuro"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "Arredamento"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "merenda"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "trucchi"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON><PERSON><PERSON> intima"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accessori"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Bevanda in bottiglia"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Gonna"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Giacca"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elettronica"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagaglio"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bottoms"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "scarpa"}, "sbi_aliprice_cate__apparel": {"message": "Abbigliamento"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automobili e motocicli"}, "sbi_aliprice_cate__beauty_health": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Cellulari e telecomunicazioni"}, "sbi_aliprice_cate__computer_office": {"message": "Computer e ufficio"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Elettronica di consumo"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Forniture per ufficio e istruzione"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Componenti e forniture elettroniche"}, "sbi_aliprice_cate__furniture": {"message": "Mobilia"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Extension e parrucche"}, "sbi_aliprice_cate__home_garden": {"message": "Casa & Giardino"}, "sbi_aliprice_cate__home_improvement": {"message": "Migliorie di casa"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Gioielli e accessori"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagagli e borse"}, "sbi_aliprice_cate__mother_kids": {"message": "Madre e figli"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Novità e uso speciale"}, "sbi_aliprice_cate__security_protection": {"message": "Sicurezza e protezione"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport e intrattenimento"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Giocattoli e hobby"}, "sbi_aliprice_cate__watches": {"message": "Orologi"}, "sbi_aliprice_cate__weddings_events": {"message": "Matrimoni ed eventi"}, "sbi_btn_capture_txt": {"message": "Catturare"}, "sbi_btn_source_now_txt": {"message": "Fonte ora"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON> con me"}, "sbi_button__contact_supplier": {"message": "Contat<PERSON>"}, "sbi_button__hide_on_this_site": {"message": "Non mostrare su questo sito"}, "sbi_button__open_settings": {"message": "Configura la ricerca per immagine"}, "sbi_capture_shortcut_tip": {"message": "oppure premere il tasto \"Invio\" sulla tastiera"}, "sbi_capturing_tip": {"message": "cattura"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 stelle"}, "sbi_crop_and_search": {"message": "Ricerca"}, "sbi_crop_start": {"message": "<PERSON>a Screenshot"}, "sbi_err_captcha_action": {"message": "Verificare"}, "sbi_err_captcha_for_alibaba_cn": {"message": "È necessaria la verifica, carica un'immagine per verificare. (Visualizza $video_tutorial$ o prova a cancellare i cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Traffico insolito, per favore verifica"}, "sbi_err_captcha_for_taobao": {"message": "Taobao ti chiede di verificare, carica manualmente un'immagine e cerca per verificarla. <PERSON>o errore è dovuto alla nuova politica di verifica \"TaoBao search by image\", ti suggeriamo che la verifica dei reclami è troppo frequente su Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "risposta"}, "sbi_err_captcha_msg": {"message": "$platform$ richiede il caricamento di un'immagine per la ricerca o il completamento della verifica di sicurezza per rimuovere le restrizioni di ricerca", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Controlla se è l'ultima versione"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON><PERSON> puliti"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Can<PERSON>are i cookie di 1688? (È necessario effettuare nuovamente il login)"}, "sbi_err_desperate_feature_pdd": {"message": "La funzione di ricerca delle immagini è stata spostata in Pinduoduo Ricerca per estensione immagine."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Come migliorare il tasso di successo della ricerca di immagini?"}, "sbi_err_img_undersize": {"message": "Immagine > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Accedi $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Accesso"}, "sbi_err_low_version": {"message": "Installa l'ultima versione ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Scarica"}, "sbi_err_need_help": {"message": "Hai bisogno di aiuto"}, "sbi_err_network": {"message": "Errore di rete, assicurati di poter visitare il sito web"}, "sbi_err_not_low_version": {"message": "L'ultima versione è stata installata ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_visit_and_try": {"message": "Riprova o visita $website$ per riprovare", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Visita la home page di $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Caricamento non riuscito, aggiorna la pagina e riprova."}, "sbi_kuajing_filter_area": {"message": "La zona"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "Germania"}, "sbi_kuajing_filter_destination_country": {"message": "Paese di destinazione"}, "sbi_kuajing_filter_es": {"message": "Spagna"}, "sbi_kuajing_filter_estimate": {"message": "Stima"}, "sbi_kuajing_filter_estimate_price": {"message": "Prezzo stimato"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Formula del prezzo stimato = (prezzo della merce + trasporto logistico internazionale)/(1 - margine di profitto - altro rapporto di costo)"}, "sbi_kuajing_filter_fr": {"message": "Francia"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Inserisci le parole chiave per abbinare il titolo"}, "sbi_kuajing_filter_logistics": {"message": "Modello di logistica"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Posta di aria della cina"}, "sbi_kuajing_filter_logistics_discount": {"message": "Sconto logistico"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Trasporto logistico internazionale = (peso x prezzo di spedizione + quota di registrazione) x (1 - sconto)"}, "sbi_kuajing_filter_others_fee": {"message": "Altra commissione"}, "sbi_kuajing_filter_profit_percent": {"message": "Margine di profitto"}, "sbi_kuajing_filter_prop": {"message": "attributi"}, "sbi_kuajing_filter_ru": {"message": "Russia"}, "sbi_kuajing_filter_total": {"message": "Abbina $count$ articoli simili", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "UK"}, "sbi_kuajing_filter_usa": {"message": "America"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo all'ingrosso"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato,accedi a $loginSite$ o prova un'altra immagine", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Temporaneamente non disponibile per Safari, utilizza $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Browser Chrome e sue estensioni"}, "sbi_msg_no_result_reinstall_l1": {"message": "<PERSON><PERSON><PERSON> risultato trovato, accedi a $loginSite$ o prova un'altra immagine o reinstalla l'ultima versione $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Ultima versione", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Area selezionata"}, "sbi_shipping_": {"message": "Spedizione lo stesso giorno"}, "sbi_specify_category": {"message": "Specifica la categoria:"}, "sbi_start_crop": {"message": "Seleziona l'area"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 oltremare"}, "sbi_tutorial_btn_more": {"message": "Utilizzo 2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>, nessun risultato trovato, rip<PERSON>a."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON> ordine"}, "sbi_visiting": {"message": "Navigazione"}, "sbi_yiwugo__jiagexiangtan": {"message": "Contatta il venditore per il prezzo"}, "sbi_yiwugo__qigou": {"message": "$num$ Pezzi (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Stelle"}, "searchByImage_screenshot": {"message": "Scher<PERSON> con un clic"}, "searchByImage_search": {"message": "Ricerca con un clic per gli stessi elementi"}, "searchByImage_size_type": {"message": "La dimensione del file non può essere maggiore di $num$ MB, solo $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON> l'immagine"}, "search_by_image_progress_searching": {"message": "Cerca prodotti"}, "search_by_image_progress_sending": {"message": "Invio immagine"}, "search_by_image_response_rate": {"message": "Tasso di risposta: $responseRate$ di acquirenti che hanno contattato questo fornitore hanno ricevuto una risposta entro $responseInHour$ ore.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Cerca per parola chiave:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "linguaggio"}, "select_country_region_modal_title": {"message": "Seleziona un paese/regione"}, "select_language_modal_title": {"message": "Seleziona una lingua:"}, "select_shop": {"message": "Seleziona il negozio"}, "sellers_count": {"message": "Numero di venditori nella pagina corrente"}, "sellers_count_per_page": {"message": "Numero di venditori nella pagina corrente"}, "service_score": {"message": "Valutazione del servizio completo"}, "set_shortcut_keys": {"message": "Imposta i tasti di scelta rapida"}, "setting_logo_title": {"message": "Assistente commerciale"}, "setting_modal_options_position_title": {"message": "Posizione"}, "setting_modal_options_position_value_left": {"message": "A sinistra"}, "setting_modal_options_position_value_right": {"message": "A destra"}, "setting_modal_options_theme_title": {"message": "Colore Tema"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Chiaro"}, "setting_modal_title": {"message": "Impostazioni"}, "setting_options_country_title": {"message": "Paese/Regione"}, "setting_options_hover_zoom_desc": {"message": "Passa con il mouse per ingrandire"}, "setting_options_hover_zoom_title": {"message": "Passa il mouse e ingrandisci"}, "setting_options_jd_coupon_desc": {"message": "Buono trovato su JD.com"}, "setting_options_jd_coupon_title": {"message": "Buono JD.com"}, "setting_options_language_title": {"message": "<PERSON><PERSON>"}, "setting_options_price_drop_alert_desc": {"message": "Quando il prezzo di un prodotto della mia I Miei Preferiti si abbassa, riceverai una notifica push."}, "setting_options_price_drop_alert_title": {"message": "Notifica calo prezzo"}, "setting_options_price_history_on_list_page_desc": {"message": "Visualizza storico prezzo sulla pagina ricerca prodotto"}, "setting_options_price_history_on_list_page_title": {"message": "Cronologia prezzi (pagina elenco)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Visualizza cronologia prodotto sulla pagina dettagli prodotto"}, "setting_options_price_history_on_produt_page_title": {"message": "Cronologia dei prezzi (pagina dei dettagli)"}, "setting_options_sales_analysis_desc": {"message": "Supporta statistiche su prezzo, volume delle vendite, numero di venditori e rapporto vendite in negozio nella pagina dell'elenco dei prodotti $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Analisi delle vendite"}, "setting_options_save_success_msg": {"message": "Successo"}, "setting_options_tacking_price_title": {"message": "Avviso variazione prezzo"}, "setting_options_value_off": {"message": "Spent<PERSON>"}, "setting_options_value_on": {"message": "Attivo"}, "setting_pkg_quick_view_desc": {"message": "Supporto: 1688 e Taobao"}, "setting_saved_message": {"message": "Modifiche salvate correttamente"}, "setting_section_enable_platform_title": {"message": "Acceso spento"}, "setting_section_setting_title": {"message": "Impostazioni"}, "setting_section_shortcuts_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_aliprice_agent__desc": {"message": "Visualizzato nella pagina dei dettagli del prodotto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Compra per me"}, "settings_copy_link__desc": {"message": "Visualizza sulla pagina dei dettagli del prodotto"}, "settings_copy_link__title": {"message": "Pulsante Copia e Cerca titolo"}, "settings_currency_desc__for_detail": {"message": "Supporta la pagina dei dettagli del prodotto 1688"}, "settings_currency_desc__for_list": {"message": "Ricerca per immagine (includi 1688/1688 all'estero/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Seleziona il prezzo"}, "settings_currency_desc_display_for_list": {"message": "Mostrato nella ricerca di immagini (incluso 1688/1688 all'estero/Taobao)"}, "settings_currency_rate_desc": {"message": "Aggiornamento tasso di cambio da \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "banca di Cina"}, "settings_download_images__desc": {"message": "Supporto per il download di immagini da $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "pulsante scarica immagine"}, "settings_download_reviews__desc": {"message": "Visualizzato nella pagina dei dettagli del prodotto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Scarica le immagini della recensione"}, "settings_google_translate_desc": {"message": "Fare clic con il tasto destro per ottenere la barra di traduzione di Google"}, "settings_google_translate_title": {"message": "traduzione di pagine web"}, "settings_historical_trend_desc": {"message": "Visualizza nell'angolo in basso a destra dell'immagine nella pagina dell'elenco dei prodotti"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON> configurazioni"}, "settings_productInfo_desc": {"message": "Visualizza informazioni più dettagliate sul prodotto sulla pagina dell'elenco prodotti. Abilitare questa opzione potrebbe aumentare il carico del computer e causare un ritardo della pagina. Se influisce sulle prestazioni, si consiglia di disattivarla."}, "settings_product_recommend__desc": {"message": "Visualizzato sotto l'immagine principale nella pagina dei dettagli del prodotto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "<PERSON>dotti consigliati"}, "settings_research_desc": {"message": "Richiedi informazioni più dettagliate sulla pagina dell'elenco prodotti"}, "settings_sbi_add_to_list": {"message": "Aggiungi in $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatura dei risultati di ricerca delle immagini"}, "settings_sbi_remove_from_list": {"message": "Rimuovi da $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Aggiungi alla lista nera"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Regola la posizione di ingresso"}, "settings_search_by_image_blacklist_desc": {"message": "Non mostrare l'icona sui siti Web nella lista nera."}, "settings_search_by_image_blacklist_title": {"message": "Lista nera"}, "settings_search_by_image_bottom_left": {"message": "In basso a sinistra"}, "settings_search_by_image_bottom_right": {"message": "In basso a destra"}, "settings_search_by_image_clear_blacklist": {"message": "Cancella lista nera"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatura"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON> grandi"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON> p<PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Un clic sull'icona per cercare per immagine"}, "settings_search_by_image_display_button_title": {"message": "Icona sulle immagini"}, "settings_search_by_image_sourece_websites_desc": {"message": "Trova il prodotto di origine su questi siti web"}, "settings_search_by_image_sourece_websites_title": {"message": "Cerca per risultato dell'immagine"}, "settings_search_by_image_top_left": {"message": "In alto a sinistra"}, "settings_search_by_image_top_right": {"message": "In alto a destra"}, "settings_search_keyword_on_x__desc": {"message": "Cerca parole su $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Mostra l'icona $platform$ quando le parole selezionate", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Prova a trovare lo stesso prodotto in quei siti (max a 5)"}, "settings_similar_products_title": {"message": "<PERSON><PERSON><PERSON> lo stesso prodotto"}, "settings_toolbar_expand_title": {"message": "Riduci a icona il plug-in"}, "settings_top_toolbar_desc": {"message": "Barra di ricerca nella parte superiore della pagina"}, "settings_top_toolbar_title": {"message": "Barra di ricerca"}, "settings_translate_search_desc": {"message": "Traduci in cinese e cerca"}, "settings_translate_search_title": {"message": "Ricerca multilingue"}, "settings_translator_contextmenu_title": {"message": "Cattura per tradurre"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Filtra per esportare"}, "shai_xuan_zi_duan": {"message": "Campi filtro"}, "shang_jia_shi_jian": {"message": "In tempo sullo scaffale"}, "shang_pin_biao_ti": {"message": "titolo del prodotto"}, "shang_pin_dui_bi": {"message": "<PERSON><PERSON><PERSON>"}, "shang_pin_lian_jie": {"message": "collegamento del prodotto"}, "shang_pin_xin_xi": {"message": "Informazioni sul prodotto"}, "share_modal__content": {"message": "condividi con i tuoi amici"}, "share_modal__disable_for_while": {"message": "Non voglio condividere niente"}, "share_modal__title": {"message": "Ti piace $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON><PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "È personalizzabile?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Fornitore certificato"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Fornitori certificati"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Garanzia commerciale"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Garanzie commerciali"}, "shipping_fee": {"message": "Tassa di spedizione"}, "shop_followers": {"message": "Acquista follower"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Massimo a 5"}, "sku_calc_price": {"message": "Prezzo calcolato"}, "sku_calc_price_settings": {"message": "Impostazioni prezzo calcolato"}, "sku_formula": {"message": "Formula"}, "sku_formula_desc": {"message": "Descrizione formula"}, "sku_formula_desc_text": {"message": "Supporta formule matematiche complesse, con il prezzo originale rappresentato da A e il trasporto rappresentato da B\n\n<br/>\n\nSupporta parentesi (), più +, meno -, moltiplicazione * e divisione /\n\n<br/>\n\nEsempio:\n\n<br/>\n\n1. Per ottenere 1,2 volte il prezzo originale e poi aggiungere il trasporto, la formula è: A*1,2+B\n\n<br/>\n\n2. Per ottenere il prezzo originale più 1 yuan, moltiplicare per 1,2, la formula è: (A+1)*1,2\n\n<br/>\n\n3. Per ottenere il prezzo originale più 10 yuan, moltiplicare per 1,2 e poi sottrarre 3 yuan, la formula è: (A+10)*1,2-3"}, "sku_in_stock": {"message": "Disponibile"}, "sku_invalid_formula_format": {"message": "Formato formula non valido"}, "sku_inventory": {"message": "Inventario"}, "sku_link_copy_fail": {"message": "Copiato correttamente, le specifiche e gli attributi dello sku non sono selezionati"}, "sku_link_copy_success": {"message": "Copiato correttamente, specifiche e attributi dello sku selezionati"}, "sku_list": {"message": "Elenco SKU"}, "sku_min_qrder_qty": {"message": "Quantità minima ordinabile"}, "sku_name": {"message": "Nome SKU"}, "sku_no": {"message": "N."}, "sku_original_price": {"message": "Prezzo originale"}, "sku_price": {"message": "Prezzo SKU"}, "stop_track_time_label": {"message": "Scadenza per il monitoraggio:"}, "suo_zai_di_qu": {"message": "posizione"}, "tab_pkg_quick_view": {"message": "Monitoraggio della logistica"}, "tab_product_details_price_history": {"message": "Storia"}, "tab_product_details_reviews": {"message": "Recensioni"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON><PERSON>"}, "total_days_listed_per_product": {"message": "Somma dei giorni sugli scaffali ÷ Numero di prodotti"}, "total_items": {"message": "Numero totale di prodotti"}, "total_price_per_product": {"message": "Somma dei prezzi ÷ Numero di prodotti"}, "total_rating_per_product": {"message": "Somma delle valutazioni ÷ Numero di prodotti"}, "total_revenue": {"message": "Entrate totali"}, "total_revenue40_items": {"message": "Entrate totali dei $amount$ prodotti nella pagina corrente", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Vendite totali"}, "total_sales40_items": {"message": "Vendite totali dei $amount$ prodotti nella pagina corrente", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Traccia per: 1 anno"}, "track_for_3_months": {"message": "Traccia per: 3 mesi"}, "track_for_6_months": {"message": "Traccia per: 6 mesi"}, "tracking_price_email_add_btn": {"message": "Aggiungi e-mail"}, "tracking_price_email_edit_btn": {"message": "Modifica email"}, "tracking_price_email_intro": {"message": "Ti informeremo via e-mail."}, "tracking_price_email_invalid": {"message": "Per favore inserisci un'email valida"}, "tracking_price_email_verified_desc": {"message": "Ora puoi ricevere il nostro avviso di riduzione dei prezzi."}, "tracking_price_email_verified_title": {"message": "Verificato con successo"}, "tracking_price_email_verify_desc_line1": {"message": "Abbiamo inviato un link di verifica al tuo indirizzo email,"}, "tracking_price_email_verify_desc_line2": {"message": "controlla la tua casella di posta elettronica."}, "tracking_price_email_verify_title": {"message": "Verifica Email"}, "tracking_price_web_push_notification_intro": {"message": "Sul desktop: <PERSON><PERSON><PERSON> monitorare qualsiasi prodotto per te e inviarti una notifica push Web una volta che il prezzo cambia."}, "tracking_price_web_push_notification_title": {"message": "Notifiche push web"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON> AliPrice, accedi a $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Tradotto e copiato negli appunti, ma a causa della limitazione di Aliwangwang, è necessario incollarlo manualmente!"}, "translate_im__send": {"message": "Traduci e invia"}, "translate_search": {"message": "Tradurre e cercare"}, "translation_originals_translated": {"message": "Originale e cinese"}, "translation_translated": {"message": "Cinese"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Rilevamento automatico"}, "translator_language_detected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_search_placeholder": {"message": "Cerca lingua"}, "try_again": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tu_pian_chi_cun": {"message": "Dimensioni immagine:"}, "tu_pian_lian_jie": {"message": "Collegamento immagine"}, "tui_huan_ti_yan": {"message": "Esperienza di ritorno"}, "tui_huan_ti_yan__desc": {"message": "Valutare gli indicatori post-vendita dei venditori"}, "tutorial__show_all": {"message": "Tutte le funzionalità"}, "tutorial_ae_popup_title": {"message": "Appunta l'estensione, apri Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Analisi delle recensioni di AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Supporto USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Spedizione in Corea/Giappone/Cina continentale"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Supporta l'acquisto all'estero"}, "tutorial_auto_apply_coupon_title": {"message": "Applica automaticamente il coupon"}, "tutorial_btn_end": {"message": "FINE"}, "tutorial_btn_example": {"message": "Esempio"}, "tutorial_btn_have_a_try": {"message": "Ok, provalo"}, "tutorial_btn_next": {"message": "Prossimo"}, "tutorial_btn_see_more": {"message": "Di più"}, "tutorial_compare_products": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_currency_convert_title": {"message": "Conversione di valuta"}, "tutorial_export_shopping_cart": {"message": "Esporta come CSV, supporta Taobao e 1688"}, "tutorial_export_shopping_cart_title": {"message": "Esporta carrello"}, "tutorial_price_history_pro": {"message": "Visualizzato nella pagina dei dettagli del prodotto.\nSupporto <PERSON>, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "<PERSON>tto l'anno Cronologia dei prezzi e cronologia degli ordini"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Cattura per cercare per immagine"}, "tutorial_translate_search": {"message": "Traduci per cercare"}, "tutorial_translate_search_and_package_tracking": {"message": "Ricerca delle traduzioni e tracciabilità dei pacchi"}, "unit_bao": {"message": "pz"}, "unit_ben": {"message": "pz"}, "unit_bi": {"message": "ordini"}, "unit_chuang": {"message": "pz"}, "unit_dai": {"message": "pz"}, "unit_dui": {"message": "paio"}, "unit_fen": {"message": "pz"}, "unit_ge": {"message": "pz"}, "unit_he": {"message": "pz"}, "unit_jian": {"message": "pz"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pz"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "paio"}, "unit_tai": {"message": "pz"}, "unit_ti": {"message": "pz"}, "unit_tiao": {"message": "pz"}, "unit_xiang": {"message": "pz"}, "unit_zhang": {"message": "pz"}, "unit_zhi": {"message": "pz"}, "verify_contact_support": {"message": "Contatta l'assistenza"}, "verify_human_verification": {"message": "Verifica umana"}, "verify_unusual_access": {"message": "Accesso insolito rilevato"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON><PERSON> tutto"}, "view_history_clean_all_warring": {"message": "Pulisci tutti i record visualizzati?"}, "view_history_clean_all_warring_title": {"message": "avvertimento"}, "view_history_viewd": {"message": "Visto"}, "website": {"message": "sito web"}, "weight": {"message": "Peso"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Impossibile ottenere i dati"}, "wu_liu_shi_xiao": {"message": "Spedizione puntuale"}, "wu_liu_shi_xiao__desc": {"message": "Il tasso di ritiro in 48 ore e il tasso di evasione del negozio del venditore"}, "xia_dan_jia": {"message": "Prezzo finale"}, "xian_xuan_ze_product_attributes": {"message": "Seleziona gli attributi del prodotto"}, "xiao_liang": {"message": "Volume delle vendite"}, "xiao_liang_zhan_bi": {"message": "Percentuale del volume delle vendite"}, "xiao_shi": {"message": "$num$ ore", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Entrate"}, "xiao_shou_e_zhan_bi": {"message": "Percentuale del fatturato"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Seleziona $amount$ record", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "<PERSON>tti i prodotti caricati"}, "yi_nian_xiao_liang": {"message": "Vendite annuali"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Quota vendite annuali"}, "yi_nian_xiao_shou_e": {"message": "Fatturato annuale"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Quota fatturato annuale"}, "yi_shua_xin": {"message": "Rinfrescato"}, "yin_cang_xiang_tong_dian": {"message": "nascondere somiglianze"}, "you_xiao_liang": {"message": "Con volume delle vendite"}, "yu_ji_dao_da_shi_jian": {"message": "Orario di arrivo stimato"}, "yuan_gong_ren_shu": {"message": "Numero di dipendenti"}, "yue_cheng_jiao": {"message": "Volume mensile"}, "yue_dai_xiao": {"message": "Scaricare la consegna"}, "yue_dai_xiao__desc": {"message": "Vendite in dropshipping negli ultimi 30 giorni"}, "yue_dai_xiao_pai_xu__desc": {"message": "Vendite in dropshipping negli ultimi 30 giorni, ordinate dal più alto al più basso"}, "yue_xiao_liang__desc": {"message": "Volume delle vendite negli ultimi 30 giorni"}, "zhan_kai": {"message": "Di più"}, "zhe_kou": {"message": "Sconto"}, "zhi_chi_yi_jian_dai_fa": {"message": "Scaricare la consegna"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Spedizione gratuita"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON> pagati"}, "zhi_fu_ding_dan_shu__desc": {"message": "Numero di ordini per questo prodotto (30 giorni)"}, "zhu_ce_xing_zhi": {"message": "Natura della registrazione"}, "zi_ding_yi_tiao_jian": {"message": "Condizioni personalizzate"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variazione venduta"}, "zong_he_fu_wu_fen": {"message": "Valutazione complessiva"}, "zong_he_fu_wu_fen__desc": {"message": "Valutazione complessiva del servizio del venditore"}, "zong_he_fu_wu_fen__short": {"message": "Valutazione"}, "zong_he_ti_yan_fen": {"message": "Valutazione"}, "zong_he_ti_yan_fen_3": {"message": "Sotto le 4 stelle"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 stelle"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 stelle"}, "zong_he_ti_yan_fen_5": {"message": "5 stelle"}, "zong_ku_cun": {"message": "Inventario totale"}, "zong_xiao_liang": {"message": "Vendite totali"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Tasso di risposta in 3 minuti negli ultimi 30 giorni"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Tasso di recupero di 48 ore negli ultimi 30 giorni"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Tasso di rendimento di 48 ore negli ultimi 30 giorni"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Record commerciale (30 giorni)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Record commerciale (30 giorni)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Tasso di controversie negli ultimi 30 giorni"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Tasso di rimborso di qualità negli ultimi 30 giorni"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Il numero di ordini di pagamento negli ultimi 30 giorni"}}