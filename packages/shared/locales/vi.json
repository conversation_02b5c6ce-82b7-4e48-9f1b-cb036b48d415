{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 <PERSON><PERSON><PERSON><PERSON> bán chạy xuyên biên giới"}, "1688_shi_li_ren_zheng": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>n sức mạnh <PERSON>"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Hơn 1 năm"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Giao hàng trong vòng 24 giờ"}, "24H_lan_shou_lv": {"message": "Tỷ lệ đóng gói 24 giờ"}, "30D_shang_xin": {"message": "<PERSON><PERSON><PERSON> mới về hàng tháng"}, "30d_sales": {"message": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>m giá:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON>ản hồi trong vòng 3 phút."}, "3Min_xiang_ying_lv__desc": {"message": "Tỷ lệ phản hồi hiệu quả của Wangwang đối với tin nhắn yêu cầu của người mua trong vòng 3 phút trong 30 ngày qua"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Giao hàng trong vòng 48 giờ"}, "48H_lan_shou_lv": {"message": "Tỷ lệ đóng gói 48 giờ"}, "48H_lan_shou_lv__desc": {"message": "Tỷ lệ số đơn hàng được nhận trong vòng 48 giờ trên tổng số đơn hàng"}, "48H_lv_yue_lv": {"message": "Tỷ lệ hiệu suất 48 giờ"}, "48H_lv_yue_lv__desc": {"message": "Tỷ lệ số đơn hàng được nhận hoặc giao trong vòng 48 giờ trên tổng số đơn hàng"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "<PERSON><PERSON><PERSON> mới về hàng tuần"}, "7D_wu_li_you": {"message": "7 ng<PERSON>y ch<PERSON>m sóc mi<PERSON>n phí"}, "ABS_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này bao gồm câu chuyện về thương hiệu"}, "AC_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có huy hiệu Amazon's Choice"}, "A_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có trang nội dung A+"}, "BS_title_text": {"message": "<PERSON>h sách này đư<PERSON>c xếp hạng là $num$ Bán chạy nhất trong danh mục $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Ưu đãi giới hạn thời gian) có nghĩa là danh sách này là một phần của sự kiện \"khuyến mãi 7 ngày\""}, "NR_title_text": {"message": "<PERSON>h sách này đư<PERSON> xếp hạng là $num$ Bản phát hành mới trong danh mục $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có quảng cáo video, một loại quảng cáo PPC thường xuất hiện ở giữa kết quả tìm kiếm"}, "SB_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có quảng cáo thương hiệu, một loại quảng cáo PPC thường xuất hiện ở đầu hoặc cuối kết quả tìm kiếm"}, "SP_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có quảng cáo <PERSON>ản phẩm đư<PERSON><PERSON> tài trợ"}, "V_title_text": {"message": "<PERSON><PERSON> s<PERSON>ch này có phần giới thiệu bằng video"}, "advanced_research": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>u nâng cao"}, "agent_ds1688___my_order": {"message": "Đ<PERSON><PERSON> hàng của tôi"}, "agent_ds1688__add_to_cart": {"message": "<PERSON><PERSON> hàng ở nước ngoài"}, "agent_ds1688__cart": {"message": "Giỏ hàng"}, "agent_ds1688__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> cung cấp bởi 1688. <PERSON><PERSON> hỗ trợ mua hàng trực tiếp từ nước ngoài, thanh toán bằng USD và giao hàng đến kho trung chuyển của bạn ở Trung Quốc."}, "agent_ds1688__freight": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h chi phí vận chuyển"}, "agent_ds1688__help": {"message": "Giúp đỡ"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>n"}, "agent_ds1688__profile": {"message": "Trung tâm cá nhân"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON> c<PERSON>a tôi"}, "ai_comment_analysis_advantage": {"message": "Ưu điểm"}, "ai_comment_analysis_ai": {"message": "<PERSON><PERSON> tích đ<PERSON>h gi<PERSON>"}, "ai_comment_analysis_available": {"message": "<PERSON><PERSON> sẵn"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON><PERSON> đủ xu, vui lòng nạp tiền"}, "ai_comment_analysis_behavior": {"message": "Hành vi"}, "ai_comment_analysis_characteristic": {"message": "Đặc điểm đám đông"}, "ai_comment_analysis_comment": {"message": "<PERSON><PERSON>n phẩm không có đủ đánh giá để đưa ra kết luận ch<PERSON>h xác, vui lòng chọn sản phẩm có nhiều đánh giá hơn."}, "ai_comment_analysis_consume": {"message": "Ước t<PERSON>h mức tiêu thụ"}, "ai_comment_analysis_default": {"message": "<PERSON><PERSON><PERSON> giá mặc định"}, "ai_comment_analysis_desire": {"message": "<PERSON>ỳ vọng của kh<PERSON>ch hàng"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> điể<PERSON>"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON> miễn phí"}, "ai_comment_analysis_freeNum": {"message": "Sẽ sử dụng 1 tín dụng miễn phí"}, "ai_comment_analysis_go_recharge": {"message": "<PERSON><PERSON> đến nạp tiền"}, "ai_comment_analysis_intelligence": {"message": "<PERSON><PERSON> tích đánh giá thông minh"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON> trí"}, "ai_comment_analysis_motive": {"message": "<PERSON><PERSON><PERSON> lực mua hàng"}, "ai_comment_analysis_network_error": {"message": "Lỗi mạng, vui lòng thử lại"}, "ai_comment_analysis_normal": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_number_reviews": {"message": "Số lượng đ<PERSON>h giá: $num$, <PERSON><PERSON><PERSON> tiêu thụ ước tính: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t chung"}, "ai_comment_analysis_percentage": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>m"}, "ai_comment_analysis_problem": {"message": "Sự cố khi thanh toán"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON> tích lại"}, "ai_comment_analysis_reason": {"message": "Lý do"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON><PERSON> đã nạp tiền"}, "ai_comment_analysis_retry": {"message": "<PERSON><PERSON><PERSON> lại"}, "ai_comment_analysis_scene": {"message": "<PERSON><PERSON><PERSON> bản sử dụng"}, "ai_comment_analysis_start": {"message": "<PERSON><PERSON><PERSON> đầu phân tích"}, "ai_comment_analysis_subject": {"message": "Chủ đề"}, "ai_comment_analysis_time": {"message": "Thời gian sử dụng"}, "ai_comment_analysis_tool": {"message": "C<PERSON>ng cụ AI"}, "ai_comment_analysis_user_portrait": {"message": "<PERSON><PERSON> s<PERSON> ng<PERSON>ời dùng"}, "ai_comment_analysis_welcome": {"message": "<PERSON><PERSON>o mừng đến với phân tích đánh giá <PERSON>"}, "ai_comment_analysis_year": {"message": "<PERSON><PERSON><PERSON><PERSON> xét từ năm ngoái"}, "ai_listing_Exclude_keywords": {"message": "Loại trừ từ khóa"}, "ai_listing_Login_the_feature": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>i đăng nhập để sử dụng tính năng này"}, "ai_listing_aI_generation": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "ai_listing_add_automatic": {"message": "<PERSON><PERSON> động"}, "ai_listing_add_dictionary_new": {"message": "<PERSON><PERSON><PERSON> một thư viện mới"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khóa"}, "ai_listing_add_inputkey_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> điểm bán và nhấn $key$ để hoàn tất việc thêm", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "<PERSON><PERSON> vư<PERSON>t quá giới hạn, tối đa $amount$ điểm bán", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khóa"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON><PERSON><PERSON> thủ công"}, "ai_listing_add_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> điểm b<PERSON> hàng"}, "ai_listing_added_keywords": {"message": "<PERSON><PERSON> thêm từ khóa"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON> thành công"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> các từ khóa bị loại trừ, <PERSON><PERSON><PERSON><PERSON> enter để hoàn tất vi<PERSON><PERSON> thêm."}, "ai_listing_adding_selling": {"message": "<PERSON><PERSON> thêm điểm bán hàng"}, "ai_listing_addkeyword_enter": {"message": "<PERSON><PERSON><PERSON><PERSON> các từ thuộc tính ch<PERSON>h và nhấn enter để hoàn tất việc thêm"}, "ai_listing_ai_description": {"message": "<PERSON><PERSON><PERSON> viện từ mô tả AI"}, "ai_listing_ai_dictionary": {"message": "<PERSON><PERSON><PERSON> viện từ tiêu đề AI"}, "ai_listing_ai_title": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "ai_listing_aidescription_repeated": {"message": "<PERSON><PERSON><PERSON> thư viện từ mô tả AI không thể lặp lại"}, "ai_listing_aititle_repeated": {"message": "<PERSON><PERSON><PERSON> thư viện từ tiêu đề AI không thể lặp lại"}, "ai_listing_data_comes_from": {"message": "<PERSON><PERSON> liệu đến từ:"}, "ai_listing_deleted_successfully": {"message": "Đã xoá thành công"}, "ai_listing_dictionary_name": {"message": "<PERSON><PERSON><PERSON> thư viện"}, "ai_listing_edit_dictionary": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>i thư viện..."}, "ai_listing_edit_word_library": {"message": "Chỉnh sửa thư viện từ"}, "ai_listing_enter_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khóa và nhấn $key$ để hoàn tất việc thêm", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "<PERSON><PERSON> vư<PERSON>t quá giới hạn, tối đa $amount$ từ khóa", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "<PERSON><PERSON><PERSON> viện từ bị loại trừ"}, "ai_listing_generate_characters": {"message": "<PERSON><PERSON>o ký tự"}, "ai_listing_generation_platform": {"message": "<PERSON><PERSON><PERSON> tảng thế hệ"}, "ai_listing_help_optimize": {"message": "<PERSON><PERSON><PERSON><PERSON> mình tối ưu tiêu đề sản phẩm nhé, tiêu đề gốc là"}, "ai_listing_include_selling": {"message": "<PERSON><PERSON><PERSON> đi<PERSON> bán hàng khác bao gồm:"}, "ai_listing_included_keyword": {"message": "<PERSON><PERSON> khóa đư<PERSON><PERSON> bao gồm"}, "ai_listing_included_keywords": {"message": "<PERSON><PERSON> khóa đư<PERSON><PERSON> bao gồm"}, "ai_listing_input_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON> bán"}, "ai_listing_input_selling_fit": {"message": "<PERSON><PERSON><PERSON><PERSON> điểm bán phù hợp với tiêu đề"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON> lòng nh<PERSON>p điểm bán"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON><PERSON><PERSON> nội dung được yêu cầu ở trên để tạo tiêu đề một cách thông minh"}, "ai_listing_keyword_product_title": {"message": "Ti<PERSON><PERSON> đề sản phẩm từ khóa"}, "ai_listing_keywords_repeated": {"message": "Từ khóa không thể lặp lại"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON><PERSON><PERSON> bán hàng bao gồm"}, "ai_listing_long_title_1": {"message": "<PERSON><PERSON><PERSON> c<PERSON>c thông tin cơ bản như tên thư<PERSON><PERSON> hi<PERSON>, lo<PERSON><PERSON> sản phẩm, t<PERSON><PERSON> n<PERSON>ng sản phẩm, v.v."}, "ai_listing_long_title_2": {"message": "Trên cơ sở tiêu đề sản phẩm chuẩn, các từ khóa có lợi cho SE<PERSON> được thêm vào."}, "ai_listing_long_title_3": {"message": "<PERSON><PERSON><PERSON><PERSON> việc chứa tên thư<PERSON> hiệ<PERSON>, lo<PERSON><PERSON> sản phẩm, t<PERSON><PERSON> năng sản phẩm và từ khóa, các từ khóa đuôi dài cũng được đưa vào để đạt được thứ hạng cao hơn trong các truy vấn tìm kiếm đượ<PERSON> phân khúc, c<PERSON> thể."}, "ai_listing_longtail_keyword_product_title": {"message": "Tiêu đề sản phẩm từ khóa đuôi dài"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON><PERSON><PERSON> thủ công..."}, "ai_listing_network_not_working": {"message": "Internet không có sẵn, cần có VPN để truy cập <PERSON>"}, "ai_listing_new_dictionary": {"message": "<PERSON><PERSON><PERSON> thư viện từ mới..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON> ra"}, "ai_listing_optional_words": {"message": "<PERSON>ừ tùy chọn"}, "ai_listing_original_title": {"message": "Tiêu đề ban đầu"}, "ai_listing_other_keywords_included": {"message": "<PERSON><PERSON><PERSON> từ khóa khác bao gồm:"}, "ai_listing_please_again": {"message": "<PERSON><PERSON> lòng thử lại"}, "ai_listing_please_select": {"message": "<PERSON><PERSON><PERSON> tiêu đề sau đã đư<PERSON><PERSON> tạo cho bạn, vui lòng chọn:"}, "ai_listing_product_category": {"message": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m"}, "ai_listing_product_category_is": {"message": "<PERSON><PERSON> mục sản phẩm là"}, "ai_listing_product_category_to": {"message": "<PERSON><PERSON>n phẩm thuộc danh mục nào?"}, "ai_listing_random_keywords": {"message": "Từ khóa ngẫu nhiên $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> bán ngẫu nhiên $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "<PERSON><PERSON><PERSON> ngẫu nhiên từ thư viện từ"}, "ai_listing_search_selling": {"message": "<PERSON><PERSON><PERSON> kiếm theo điểm bán"}, "ai_listing_select_product_categories": {"message": "Tự động chọn danh mục sản phẩm."}, "ai_listing_select_product_selling_points": {"message": "Tự động chọn điểm bán sản phẩm"}, "ai_listing_select_word_library": {"message": "<PERSON><PERSON><PERSON> thư viện từ"}, "ai_listing_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ng"}, "ai_listing_selling_ask": {"message": "<PERSON><PERSON> những yêu cầu về điểm bán hàng nào khác đối với tiêu đề?"}, "ai_listing_selling_optional": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> hàng tùy ch<PERSON>n"}, "ai_listing_selling_repeat": {"message": "<PERSON><PERSON><PERSON><PERSON> không thể trùng lặp"}, "ai_listing_set_excluded": {"message": "Đặt làm thư viện từ bị loại trừ"}, "ai_listing_set_include_selling_points": {"message": "<PERSON><PERSON> g<PERSON><PERSON> các điểm bán hàng"}, "ai_listing_set_included": {"message": "Đặt làm thư viện từ được bao gồm"}, "ai_listing_set_selling_dictionary": {"message": "Đặt làm thư viện điểm bán hàng"}, "ai_listing_standard_product_title": {"message": "Tiê<PERSON> đề sản phẩm tiêu chuẩn"}, "ai_listing_translated_title": {"message": "Ti<PERSON>u đề đã dịch"}, "ai_listing_visit_chatGPT": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_what_other_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khóa nào khác được yêu cầu cho tiêu đề?"}, "aliprice_coupons_apply_again": {"message": "Đ<PERSON>ng ký lại"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON> dụng phiếu giảm giá"}, "aliprice_coupons_apply_success": {"message": "<PERSON><PERSON> tìm thấy phiếu giảm giá: Ti<PERSON><PERSON> kiệm $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "<PERSON><PERSON><PERSON> tra mã cho các giao dịch tốt nhất..."}, "aliprice_coupons_applying_desc": {"message": "Thanh toán: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h toán"}, "aliprice_coupons_found_coupons": {"message": "<PERSON><PERSON>g tôi đã tìm thấy phiếu giảm giá $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Sẵn sàng để kiểm tra? Hãy chắc chắn rằng bạn nhận được giá tốt nhất!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Những mã đó không hoạt động. Không thành vấn đề—bạn đã nhận được mức giá tốt nhất."}, "aliprice_coupons_toolbar_btn": {"message": "<PERSON><PERSON><PERSON><PERSON> phiếu giảm giá"}, "aliww_translate": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ch trò chuy<PERSON>n <PERSON>"}, "aliww_translate_supports": {"message": "Hỗ trợ: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON> khóa"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON> ch<PERSON>p tất cả"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "<PERSON><PERSON><PERSON> xếp theo doanh số tích lũy"}, "an_lei_xing_cha_kan": {"message": "<PERSON>em theo"}, "an_yue_dai_xiao_pai_xu": {"message": "<PERSON><PERSON><PERSON> hạng theo doanh số dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON> tích đ<PERSON>h giá"}, "apra_chart__name": {"message": "<PERSON>ần trăm doanh số bán sản phẩm theo quốc gia"}, "apra_chart__update_at": {"message": "<PERSON><PERSON><PERSON> nhật thời gian $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "<PERSON><PERSON><PERSON><PERSON> kê bán hàng của các quốc gia"}, "auto_opening": {"message": "Tự động mở sau $num$ giây", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Các trang $autoPaging$ tiếp theo", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Trung bình vào ngày kệ"}, "average_hui_fu_lv": {"message": "Tỷ lệ trả lời trung bình"}, "average_ping_gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON> độ nhà cung cấp trung bình"}, "average_price": {"message": "<PERSON><PERSON><PERSON> trung bình"}, "average_qi_ding_liang": {"message": "MO<PERSON> trung bình"}, "average_rating": {"message": "<PERSON><PERSON><PERSON> giá trung bình"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON><PERSON> năm chứng nhận trung bình"}, "average_revenue": {"message": "<PERSON><PERSON>h thu trung bình"}, "average_revenue_per_product": {"message": "T<PERSON>ng doanh thu ÷ <PERSON>ố lượng sản phẩm"}, "average_sales": {"message": "<PERSON><PERSON>h thu trung bình"}, "average_sales_per_product": {"message": "T<PERSON>ng doanh số ÷ Số lượng sản phẩm"}, "bao_han": {"message": "<PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "Lề"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> thể"}, "biao_ti": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "blacklist_add_blacklist": {"message": "Chặn c<PERSON>a hàng này"}, "blacklist_address_incorrect": {"message": "Địa chỉ không chính xác. Xin vui lòng kiểm tra xem nó."}, "blacklist_blacked_out": {"message": "<PERSON><PERSON><PERSON> hàng đã bị chặn"}, "blacklist_blacklist": {"message": "<PERSON><PERSON> s<PERSON>ch đen"}, "blacklist_no_records_yet": {"message": "<PERSON><PERSON><PERSON> có hồ sơ!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>"}, "btn_aliprice_agent__daigou": {"message": "<PERSON>rung gian mua hàng"}, "btn_aliprice_agent__dropshipping": {"message": "<PERSON><PERSON><PERSON> chuyển thả"}, "btn_have_a_try": {"message": "Thử"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "<PERSON><PERSON><PERSON> ngay bây giờ"}, "btn_txt_view_on_aliprice": {"message": "<PERSON><PERSON> tr<PERSON><PERSON>"}, "bu_bao_han": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON>"}, "bulk_copy_links": {"message": "<PERSON><PERSON><PERSON> kết sao chép số lượng lớn"}, "bulk_copy_products": {"message": "<PERSON><PERSON><PERSON> phẩm sao chép số lượng lớn"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON> v<PERSON> kh<PERSON>ch hàng"}, "cai_gou_zi_xun__desc": {"message": "Tỷ lệ phản hồi trong ba phút của người bán"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON><PERSON>"}, "cao_zuo": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "chan_pin_ID": {"message": "<PERSON> sản phẩm"}, "chan_pin_e_wai_xin_xi": {"message": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON> sung thông tin"}, "chan_pin_lian_jie": {"message": "<PERSON><PERSON><PERSON> kết sản ph<PERSON>m"}, "cheng_li_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> gian thành lập"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>p thông tin sản phẩm"}, "commmon_txt_expired": {"message": "<PERSON><PERSON> hết hạn"}, "common__date_range_12m": {"message": "1 năm"}, "common__date_range_1m": {"message": "1 tháng"}, "common__date_range_1w": {"message": "1 tuần"}, "common__date_range_2w": {"message": "2 tuần"}, "common__date_range_3m": {"message": "3 tháng"}, "common__date_range_3w": {"message": "3 tuần"}, "common__date_range_6m": {"message": "6 tháng"}, "common_btn_cancel": {"message": "Huỷ bỏ"}, "common_btn_close": {"message": "Đ<PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON> lỗi, kh<PERSON><PERSON> tìm thấy kết quả."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON>ch"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "common_help": {"message": "<PERSON><PERSON><PERSON> g<PERSON>"}, "common_loading": {"message": "<PERSON><PERSON> t<PERSON>"}, "common_login": {"message": "<PERSON><PERSON><PERSON>"}, "common_logout": {"message": "<PERSON><PERSON><PERSON> xu<PERSON>"}, "common_no": {"message": "K<PERSON>ô<PERSON>"}, "common_powered_by_aliprice": {"message": "<PERSON><PERSON><PERSON><PERSON> cung cấp bởi AliPrice.com"}, "common_setting": {"message": "Cài đặt"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON> ký"}, "common_system_upgrading_title": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON> thử nó sau"}, "common_txt__currency": {"message": "ti<PERSON><PERSON> tệ"}, "common_txt__video_tutorial": {"message": "Video hướng dẫn"}, "common_txt_ago_time": {"message": "$time$ ngày trước", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "tất cả"}, "common_txt_analysis": {"message": "<PERSON><PERSON> tích"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> không bao giờ sử dụng"}, "common_txt_biaoti_link": {"message": "<PERSON><PERSON><PERSON><PERSON> đề+<PERSON><PERSON><PERSON> kết"}, "common_txt_biaoti_link_dian_pu": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>+<PERSON><PERSON><PERSON> kết+<PERSON>ê<PERSON> c<PERSON><PERSON> hàng"}, "common_txt_blacklist": {"message": "<PERSON><PERSON> s<PERSON>ch chặn"}, "common_txt_cancel": {"message": "Hủy bỏ"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "<PERSON><PERSON><PERSON> tra"}, "common_txt_colors": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Đã sao chép"}, "common_txt_copy": {"message": "Sao chép"}, "common_txt_copy_link": {"message": "<PERSON>o chép đường dẫn"}, "common_txt_copy_title": {"message": "<PERSON><PERSON> chép tiêu đề"}, "common_txt_copy_title__link": {"message": "<PERSON>o chép tiêu đề và liên kết"}, "common_txt_day": {"message": "b<PERSON><PERSON> tr<PERSON>i"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Xóa bỏ"}, "common_txt_dian_pu_link": {"message": "<PERSON>o chép tên cửa hàng + liên kết"}, "common_txt_download": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_downloaded": {"message": "t<PERSON><PERSON> về"}, "common_txt_export_as_csv": {"message": "Xuất Excel"}, "common_txt_export_as_txt": {"message": "Xuất Txt"}, "common_txt_fail": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "common_txt_format": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ng"}, "common_txt_get": {"message": "l<PERSON>y"}, "common_txt_incert_selection": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n đ<PERSON> ng<PERSON>"}, "common_txt_install": {"message": "<PERSON><PERSON><PERSON> về"}, "common_txt_load_failed": {"message": "<PERSON><PERSON><PERSON><PERSON> tải <PERSON>"}, "common_txt_month": {"message": "th<PERSON>g"}, "common_txt_more": {"message": "H<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "<PERSON><PERSON><PERSON>, ch<PERSON>a sử dụng"}, "common_txt_next": {"message": "<PERSON><PERSON> tiếp"}, "common_txt_no_limit": {"message": "<PERSON><PERSON> h<PERSON>"}, "common_txt_no_noticeable": {"message": "<PERSON><PERSON><PERSON><PERSON> có vết xước hoặc bụi bẩn nào có thể nhìn thấy"}, "common_txt_on_sale": {"message": "<PERSON><PERSON> sẵn"}, "common_txt_opt_in_out": {"message": "Bật/Tắt"}, "common_txt_order": {"message": "Đặt hàng"}, "common_txt_others": {"message": "K<PERSON><PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "<PERSON>ình trạng chung kém"}, "common_txt_patterns": {"message": "mô hình"}, "common_txt_platform": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "common_txt_please_select": {"message": "<PERSON><PERSON> lòng ch<PERSON>n"}, "common_txt_prev": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó"}, "common_txt_price": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "common_txt_privacy_policy": {"message": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật"}, "common_txt_product_condition": {"message": "<PERSON><PERSON><PERSON> trạng sản phẩm"}, "common_txt_rating": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON> l<PERSON>i"}, "common_txt_reset": {"message": "<PERSON><PERSON>i lại"}, "common_txt_retail": {"message": "B<PERSON> lẻ"}, "common_txt_review": {"message": "Ôn tập"}, "common_txt_sale": {"message": "<PERSON><PERSON> sẵn"}, "common_txt_same": {"message": "<PERSON><PERSON><PERSON><PERSON> tự"}, "common_txt_scratches_and_dirt": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>ng v<PERSON><PERSON> x<PERSON><PERSON><PERSON> và bụi bẩn"}, "common_txt_search_title": {"message": "Ti<PERSON><PERSON> đề tìm kiếm"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON> tất cả"}, "common_txt_selected": {"message": "<PERSON><PERSON> ch<PERSON>n"}, "common_txt_share": {"message": "<PERSON><PERSON> sẻ"}, "common_txt_sold": {"message": "<PERSON><PERSON> bán"}, "common_txt_sold_out": {"message": "b<PERSON>"}, "common_txt_some_scratches": {"message": "Một số vết x<PERSON><PERSON><PERSON> và bụi bẩn"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON> xếp theo"}, "common_txt_state": {"message": "tình trạng"}, "common_txt_success": {"message": "<PERSON><PERSON><PERSON><PERSON> công"}, "common_txt_sys_err": {"message": "lỗi hệ thống"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON> nay"}, "common_txt_total": {"message": "tất cả"}, "common_txt_unselect_all": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n đ<PERSON> ng<PERSON>"}, "common_txt_upload_image": {"message": "<PERSON><PERSON><PERSON> lên h<PERSON>"}, "common_txt_visit": {"message": "Thăm nom"}, "common_txt_whitelist": {"message": "<PERSON><PERSON> s<PERSON>ch trắng"}, "common_txt_wholesale": {"message": "B<PERSON> sỉ"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Năm"}, "common_yes": {"message": "<PERSON><PERSON><PERSON>"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch tất cả"}, "compare_tool_btn_compare": {"message": "<PERSON><PERSON><PERSON>"}, "compare_tool_btn_contact": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "configure_notifiactions": {"message": "<PERSON><PERSON><PERSON> cấu hình thông báo"}, "contact_us": {"message": "<PERSON><PERSON><PERSON> h<PERSON> chúng tôi"}, "context_menu_screenshot_search": {"message": "Ảnh chụp màn hình tìm kiếm cùng kiểu"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON><PERSON><PERSON> kiếm hình <PERSON>nh trên AliPrice"}, "context_menus_goote_trans": {"message": "<PERSON><PERSON><PERSON> trang/<PERSON><PERSON><PERSON> thị bản gốc"}, "context_menus_search_by_image": {"message": "Tìm kiếm bằng hình ảnh trên $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Chụp vào $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> d<PERSON>ch"}, "converter_modal_amount_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> số tiền tại đây"}, "converter_modal_btn_convert": {"message": "đ<PERSON>i"}, "converter_modal_exchange_rate_source": {"message": "Dữ liệu đến từ tỷ giá hối đoái $boc$ Thời gian cập nhật: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "chuyển đổi tiền tệ"}, "converter_modal_search_placeholder": {"message": "tìm kiếm tiền tệ"}, "copy_all_contact_us_notice": {"message": "Trang web này hiện không được hỗ trợ, vui lòng liên hệ với chúng tôi"}, "copy_product_info": {"message": "<PERSON><PERSON> ch<PERSON>p thông tin sản phẩm"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON> ch<PERSON><PERSON> danh sách thả xuống"}, "country__han_gou": {"message": "<PERSON><PERSON><PERSON>"}, "country__ri_ben": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "country__yue_nan": {"message": "Việt Nam"}, "currency_convert__custom": {"message": "Tỷ giá hối đoái tùy chỉnh"}, "currency_convert__sync_server": {"message": "Đ<PERSON>ng bộ hóa máy chủ"}, "dang_ri_fa_huo": {"message": "<PERSON><PERSON>n chuyển trong ngày"}, "dao_chu_quan_dian_shang_pin": {"message": "<PERSON><PERSON><PERSON> tất cả sản phẩm trong cửa hàng"}, "dao_chu_wei_CSV": {"message": "<PERSON><PERSON><PERSON>"}, "dao_chu_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "delivery_address": {"message": "Đ<PERSON>a chỉ giao hàng"}, "delivery_company": {"message": "<PERSON><PERSON><PERSON> ty chuyển phát"}, "di_zhi": {"message": "Địa chỉ"}, "dian_ji_cha_xun": {"message": "<PERSON>ấm để truy vấn"}, "dian_pu_ID": {"message": "<PERSON><PERSON> c<PERSON>a hàng"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON> chỉ c<PERSON>a hàng"}, "dian_pu_lian_jie": {"message": "<PERSON><PERSON><PERSON> kết c<PERSON><PERSON> hàng"}, "dian_pu_ming": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng"}, "dian_pu_shang_pin_zong_hsu": {"message": "Tổng số sản phẩm trong cửa hàng: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "<PERSON><PERSON><PERSON> trữ thông tin"}, "ding_zai_zuo_ce": {"message": "<PERSON><PERSON>g đinh bên trái"}, "disable_old_version_tips_disable_btn_title": {"message": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n bản c<PERSON>"}, "download_image__SKU_variant_images": {"message": "<PERSON><PERSON><PERSON>nh biến thể SKU"}, "download_image__assume": {"message": "<PERSON><PERSON>, chúng ta có 2 hình ảnh, product1.jpg và product2.gif.\nimg_{$no$} sẽ được đổi tên thành img_01.jpg, img_02.gif;\n{$group$}_{$no$} sẽ được đổi tên thành main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "<PERSON><PERSON><PERSON> xu<PERSON> hàng lo<PERSON>"}, "download_image__combined_image": {"message": "<PERSON><PERSON><PERSON>nh chi tiết sản phẩm kết hợp"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON><PERSON><PERSON> tục tải xu<PERSON>ng"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON>nh mô tả"}, "download_image__download_combined_image": {"message": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh chi tiết sản phẩm kết hợp"}, "download_image__download_zip": {"message": "<PERSON><PERSON><PERSON> xu<PERSON>ng zip"}, "download_image__enlarge_check": {"message": "Chỉ hỗ trợ hình ảnh JPEG, JPG, GIF và PNG, kí<PERSON> thước tối đa của một hình ảnh: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Phóng to hình <PERSON>nh"}, "download_image__export": {"message": "<PERSON><PERSON><PERSON> liên kết"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON> cao"}, "download_image__ignore_videos": {"message": "Video đã bị bỏ qua vì không thể xuất được"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> mục"}, "download_image__name": {"message": "t<PERSON><PERSON> h<PERSON>nh <PERSON>"}, "download_image__notice_content": {"message": "<PERSON><PERSON> lòng không chọn \"Hỏi nơi lưu từng tệp trước khi tải xuống\" trong cài đặt tải xuống của trình duyệt của bạn!!! Nếu không sẽ có rất nhiều hộp thoại."}, "download_image__notice_ignore": {"message": "Đ<PERSON>ng nhắc lại tin nhắn này"}, "download_image__order_number": {"message": "{$no$} số sê-ri; {$group$} tên nhóm; {$date$} dấu thời gian", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON> quat"}, "download_image__prompt_download_zip": {"message": "<PERSON><PERSON> quá nhiều h<PERSON>nh <PERSON>, tốt hơn bạn nên tải xuống dưới dạng thư mục zip."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON> tên"}, "download_image__rule": {"message": "<PERSON><PERSON> tắc đặt tên"}, "download_image__single_folder": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__sku_image": {"message": "Hình ảnh SKU"}, "download_image__video": {"message": "b<PERSON><PERSON> h<PERSON>nh"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON> r<PERSON>"}, "download_reviews__download_images": {"message": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh đ<PERSON>h giá"}, "download_reviews__dropdown_title": {"message": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh đ<PERSON>h giá"}, "download_reviews__export_csv": {"message": "xuất CSV"}, "download_reviews__no_images": {"message": "0 hình ảnh có sẵn để tải về"}, "download_reviews__no_reviews": {"message": "<PERSON><PERSON><PERSON>ng có đánh giá để tải về!"}, "download_reviews__notice": {"message": "Mẹo:"}, "download_reviews__notice__chrome_settings": {"message": "Đặt trình duyệt Chrome hỏi vị trí lưu từng tệp trước khi tải xuống, đặt thành \"Tắt\""}, "download_reviews__notice__wait": {"message": "<PERSON><PERSON><PERSON><PERSON> vào số lượ<PERSON> đ<PERSON> gi<PERSON>, thời gian chờ đợi có thể lâu hơn"}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "download_reviews__pages_list__page": {"message": "Các trang $page$ trước đó", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Phạm vi lựa chọn"}, "export_shopping_cart__csv_filed__details_url": {"message": "li<PERSON><PERSON> kết sản ph<PERSON>m"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Liên kết SKU Echo"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON><PERSON><PERSON> kết nối tới hình <PERSON>nh"}, "export_shopping_cart__csv_filed__quantity": {"message": "Số lượng"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Giá"}, "export_shopping_cart__csv_filed__specs": {"message": "thông số kỹ thuật"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng"}, "export_shopping_cart__csv_filed__store_url": {"message": "l<PERSON><PERSON><PERSON> kết c<PERSON><PERSON> hàng"}, "export_shopping_cart__csv_filed__title": {"message": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m"}, "export_shopping_cart__export_btn": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__export_empty": {"message": "<PERSON>ui lòng chọn một sản phẩm!"}, "fa_huo_shi_jian": {"message": "<PERSON><PERSON> ch<PERSON> hàng"}, "favorite_add_email": {"message": "<PERSON>hê<PERSON> địa chỉ email"}, "favorite_add_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o mục yêu thích"}, "favorite_added": {"message": "<PERSON><PERSON> thêm"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o gi<PERSON> gi<PERSON>."}, "favorite_btn_notify": {"message": "<PERSON> gi<PERSON>"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON><PERSON> cả sản phẩm"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>n tại"}, "favorite_due_date": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>n hạn"}, "favorite_enable_notification": {"message": "<PERSON><PERSON> lòng bật thông báo qua email"}, "favorite_expired": {"message": "<PERSON><PERSON> hết hạn"}, "favorite_go_to_enable": {"message": "<PERSON><PERSON> đ<PERSON> bật"}, "favorite_msg_add_success": {"message": "<PERSON><PERSON> thêm vào mục yêu thích"}, "favorite_msg_del_success": {"message": "<PERSON>ã xóa khỏi mục yêu thích"}, "favorite_msg_failure": {"message": "Thất bại! Làm mới Trang và thử lại."}, "favorite_please_add_email": {"message": "<PERSON><PERSON> lòng thêm địa chỉ email"}, "favorite_price_drop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_price_rise": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_price_untracked": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>a theo dõi"}, "favorite_saved_price": {"message": "<PERSON><PERSON><PERSON> đã lưu"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON> theo dõi"}, "favorite_sub_email_address": {"message": "Địa chỉ email đăng ký"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON><PERSON> gian theo dõi"}, "favorite_tracking_prices": {"message": "<PERSON> gi<PERSON>"}, "favorite_verify_email": {"message": "<PERSON><PERSON><PERSON> <PERSON>h địa chỉ email"}, "favorites_list_remove_prompt_msg": {"message": "Bạn có chắc chắn muốn xoá nó?"}, "favorites_update_button": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t gi<PERSON> ngay"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Lựa chọn của nhà phân phối"}, "find_similar": {"message": "<PERSON><PERSON><PERSON> điểm giống nhau"}, "first_ali_price_date": {"message": "<PERSON><PERSON><PERSON> mà trình thu thập thông tin AliPrice thu thập lần đầu tiên"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON><PERSON><PERSON> có phiếu giảm giá"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> giá"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Giá <$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "hoặc giá> $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_error_msg_no_favorites": {"message": "<PERSON>hê<PERSON> sản phẩm yêu thích tại đây để nhận thông báo giảm giá."}, "fooview_favorites_filter_latest": {"message": "Mu<PERSON><PERSON>t"}, "fooview_favorites_filter_price_drop": {"message": "gi<PERSON>m giá"}, "fooview_favorites_filter_price_up": {"message": "tăng giá"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON><PERSON> y<PERSON>u thích của tôi"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON> t<PERSON>i Ali<PERSON>ch"}, "fooview_favorites_track_price": {"message": "<PERSON><PERSON> theo dõi giá"}, "fooview_price_history_app_price": {"message": "Giá APP:"}, "fooview_price_history_title": {"message": "<PERSON><PERSON><PERSON> sử giá cả"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON> hàng"}, "fooview_product_list_price": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON><PERSON><PERSON> tôi không tìm thấy bất kỳ nhận xét nào cho sản phẩm này."}, "fooview_reviews_filter_buyer_reviews": {"message": "Ảnh của người mua"}, "fooview_reviews_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON> danh mục"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON> hàng"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "fooview_same_product_filter_rating": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "fooview_same_product_modal_title": {"message": "<PERSON><PERSON><PERSON> sản phẩm tương tự"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON>m kiếm bằng hình ảnh"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON> tích ng<PERSON><PERSON> bán"}, "for_12_months": {"message": "Trong 1 năm"}, "for_12_months_list_pro": {"message": "12 tháng"}, "for_12_months_nei": {"message": "Trong vòng 12 tháng"}, "for_1_months": {"message": "1 tháng"}, "for_1_months_nei": {"message": "Trong vòng 1 tháng"}, "for_3_months": {"message": "3 tháng"}, "for_3_months_nei": {"message": "Trong vòng 3 tháng"}, "for_6_months": {"message": "Trong 6 tháng"}, "for_6_months_nei": {"message": "Trong vòng 6 tháng"}, "for_9_months": {"message": "9 tháng"}, "for_9_months_nei": {"message": "Trong vòng 9 tháng"}, "fu_gou_lv": {"message": "Tỷ lệ mua lại"}, "gao_liang_bu_tong_dian": {"message": "làm n<PERSON> bật s<PERSON> kh<PERSON>c bi<PERSON>t"}, "gao_liang_guang_gao_chan_pin": {"message": "<PERSON><PERSON><PERSON> phẩm quảng c<PERSON>o nổi bật"}, "geng_duo_xin_xi": {"message": "<PERSON><PERSON><PERSON><PERSON> thông tin"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t"}, "get_store_products_fail_tip": {"message": "<PERSON><PERSON><PERSON><PERSON> vào OK để đi đến xác minh để đảm bảo truy cập bình thường"}, "gong_x_kuan_shang_pin": {"message": "Tổng cộng có $amount$ sản phẩm", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "<PERSON><PERSON><PERSON> cung cấp"}, "gong_ying_shang_ID": {"message": "ID nhà cung cấp"}, "gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON> giá nhà cung cấp"}, "gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON> cung cấp cũ h<PERSON>n"}, "gong_ying_shang_xin_xi": {"message": "thông tin nhà cung cấp"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "<PERSON><PERSON><PERSON> kết trang chủ của nhà cung cấp"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "guan_jian_zi": {"message": "<PERSON><PERSON> khóa"}, "guang_gao_chan_pin": {"message": "<PERSON><PERSON><PERSON> phẩm quảng cáo"}, "guang_gao_zhan_bi": {"message": "Tỷ lệ quảng cáo"}, "guo_ji_wu_liu_yun_fei": {"message": "<PERSON><PERSON> vận chuyển quốc tế"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON> lọc"}, "hao_ping_lv": {"message": "<PERSON><PERSON><PERSON> giá tích c<PERSON>c"}, "highest_price": {"message": "<PERSON>"}, "historical_trend": {"message": "<PERSON> h<PERSON> lịch sử"}, "how_to_screenshot": {"message": "Nhấn giữ chuột trái để chọn vùng, nhấn chuột phải hoặc phím Esc để thoát khỏi ảnh chụp màn hình"}, "howt_it_works": {"message": "<PERSON><PERSON><PERSON> thế nào nó hoạt động"}, "hui_fu_lv": {"message": "Tỷ lệ phản hồi"}, "hui_tou_lv": {"message": "tỷ lệ hoàn vốn"}, "inquire_freightFee": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u vận chuyển"}, "inquire_freightFee_Yuan": {"message": "<PERSON><PERSON><PERSON> chuyển/<PERSON><PERSON><PERSON> dân tệ"}, "inquire_freightFee_province": {"message": "Các tỉnh"}, "inquire_freightFee_the": {"message": "Chi phí vận chuyển là $num$, nghĩa là chúng tôi miễn phí vận chuyển trong khu vực.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>o."}, "jia_ge": {"message": "Giá"}, "jia_ge_dan_wei": {"message": "Đơn vị"}, "jia_ge_qu_shi": {"message": "<PERSON>"}, "jia_zai_n_ge_shang_pin": {"message": "Tải $num$ Sản phẩm", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Phần tr<PERSON>m kh<PERSON>i l<PERSON> bán hàng trong 30 ngày qua"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "<PERSON>ần tr<PERSON>m doanh thu trong 30 ngày qua"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "T<PERSON>ng do<PERSON>h số bán hàng trong 30 ngày qua"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON> s<PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "T<PERSON>ng doanh thu trong 30 ngày qua"}, "jin_90_tian_mai_jia_shu": {"message": "Ngư<PERSON><PERSON> mua trong 90 ngày qua"}, "jin_90_tian_xiao_shou_liang": {"message": "<PERSON><PERSON><PERSON> số bán hàng trong 90 ngày qua"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON> h<PERSON>nh kinh doanh"}, "jing_ying_mo_shi__gong_chang": {"message": "nhà chế tạo"}, "jiu_fen_jie_jue": {"message": "<PERSON><PERSON><PERSON><PERSON> quyết tranh chấp"}, "jiu_fen_jie_jue__desc": {"message": "<PERSON><PERSON> toán tranh chấp quyền sở hữu cửa hàng của người bán"}, "jiu_fen_lv": {"message": "Tỷ lệ tranh chấp"}, "jiu_fen_lv__desc": {"message": "Tỷ lệ đơn hàng có khiếu nại được hoàn thành trong 30 ngày qua và được đánh giá là trách nhiệm của người bán hoặc cả hai bên"}, "kai_dian_ri_qi": {"message": "<PERSON><PERSON><PERSON> mở cửa"}, "keywords": {"message": "<PERSON><PERSON> khóa"}, "kua_jin_Select_pan_huo": {"message": "<PERSON><PERSON><PERSON> chọn xuyên biên giới"}, "last15_days": {"message": "15 ngày qua"}, "last180_days": {"message": "180 ngày qua"}, "last30_days": {"message": "Trong 30 ngày qua"}, "last360_days": {"message": "360 ngày qua"}, "last45_days": {"message": "45 ngày qua"}, "last60_days": {"message": "60 ngày qua"}, "last7_days": {"message": "7 ngày qua"}, "last90_days": {"message": "90 ngày qua"}, "last_30d_sales": {"message": "<PERSON><PERSON><PERSON> s<PERSON> 30 ngày qua"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "<PERSON><PERSON><PERSON> cộng"}, "lei_ji_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> c<PERSON> do<PERSON>h số bán hàng sau khi sản phẩm lên kệ"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON> số bán hàng tích lũy trong 30 ngày qua, <PERSON><PERSON><PERSON><PERSON> sắp xếp từ cao đến thấp"}, "lian_xi_fang_shi": {"message": "Thông tin liên lạc"}, "list_time": {"message": "<PERSON><PERSON><PERSON> trữ"}, "load_more": {"message": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "login_to_aliprice": {"message": "<PERSON><PERSON><PERSON>h<PERSON><PERSON> v<PERSON><PERSON> AliPrice"}, "long_link": {"message": "<PERSON><PERSON><PERSON> kết dài"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> bán"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "Mở một tab mới"}, "mobile_view__sjdxq": {"message": "<PERSON> tiết trong ứng dụng"}, "mobile_view__sjdxqy": {"message": "Trang chi tiết trong ứng dụng"}, "mobile_view__smck": {"message": "<PERSON><PERSON><PERSON> để xem"}, "mobile_view__smckms": {"message": "<PERSON><PERSON> lòng sử dụng máy ảnh hoặc ứng dụng để quét và xem"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON> đổi không thành công"}, "modified_successfully": {"message": "Đ<PERSON> sửa đổi thành công"}, "nav_btn_favorites": {"message": "<PERSON><PERSON> s<PERSON>u tập của tôi"}, "nav_btn_package": {"message": "<PERSON><PERSON><PERSON>"}, "nav_btn_product_info": {"message": "<PERSON><PERSON> sản phẩm"}, "nav_btn_viewed": {"message": "Đã xem"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON><PERSON><PERSON> có"}, "normal_link": {"message": "<PERSON><PERSON><PERSON> kết b<PERSON>nh th<PERSON>"}, "notice": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>u"}, "number_reviews": {"message": "Đánh giá"}, "only_show_num": {"message": "Tổng số sản phẩm: $allnum$, Ẩn: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "<PERSON>óa bỏ chọn"}, "open": {"message": "Mở"}, "open_links": {"message": "Mở liên kết"}, "options_page_tab_check_links": {"message": "<PERSON><PERSON><PERSON> tra các liên kết"}, "options_page_tab_gernal": {"message": "<PERSON>"}, "options_page_tab_notifications": {"message": "<PERSON><PERSON><PERSON><PERSON> báo"}, "options_page_tab_others": {"message": "K<PERSON><PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON>m kiếm bằng hình ảnh"}, "options_page_tab_shortcuts": {"message": "<PERSON><PERSON><PERSON> ph<PERSON> t<PERSON>"}, "options_page_tab_shortcuts_title": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> phông chữ cho các phím tắt"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>m tư<PERSON> tự"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "<PERSON><PERSON><PERSON><PERSON> liên kết sản phẩm sắp mở"}, "order_list_sku_show_title": {"message": "Hiển thị các biến thể đã chọn trong các liên kết được chia sẻ"}, "orders_last30_days": {"message": "<PERSON><PERSON> lượng đơn đặt hàng trong 30 ngày qua"}, "pTutorial_favorites_block1_desc1": {"message": "<PERSON><PERSON><PERSON> sản phẩm mà bạn đã theo dõi được liệt kê ở đây"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON>ch"}, "pTutorial_popup_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON> xanh có nghĩa là có những sản phẩm giảm giá"}, "pTutorial_popup_block1_title": {"message": "<PERSON><PERSON>m t<PERSON>t và <PERSON> y<PERSON>u thích"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON> và<PERSON> \"<PERSON>\", thêm sản phẩm vào Yêu thích. <PERSON>hi gi<PERSON> giảm, bạn sẽ nhận đượ<PERSON> thông báo"}, "pTutorial_price_history_block1_title": {"message": "<PERSON> gi<PERSON>"}, "pTutorial_reviews_block1_desc1": {"message": "Người mua đánh giá từ Itao và hình ảnh thực tế từ phản hồi của AliExpress"}, "pTutorial_reviews_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t"}, "pTutorial_reviews_block2_desc1": {"message": "<PERSON><PERSON><PERSON> tra đánh giá từ những người mua khác luôn hữu ích"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON>ạn có thể so sánh chúng để đưa ra lựa chọn tốt nhất"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON><PERSON><PERSON> và<PERSON> '<PERSON>h<PERSON><PERSON>' để \"Tìm kiếm bằng hình ảnh\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>m tư<PERSON> tự"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "<PERSON><PERSON><PERSON> hình ảnh sản phẩm vào đó và chọn một danh mục"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON><PERSON>m kiếm bằng hình ảnh"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Tỷ lệ phản hồi tích cực của người bán, đi<PERSON><PERSON> phản hồi và thời gian người bán đã có mặt trên thị trường"}, "pTutorial_seller_analysis_block1_title": {"message": "<PERSON><PERSON><PERSON> h<PERSON>ng <PERSON><PERSON> bán"}, "pTutorial_seller_analysis_block2_desc2": {"message": "<PERSON><PERSON><PERSON> hạng người bán dựa trên 3 chỉ số: mặt hàng đư<PERSON><PERSON> mô tả, <PERSON>ố<PERSON> độ vận chuyển thông tin liên lạc"}, "pTutorial_seller_analysis_block3_desc3": {"message": "<PERSON><PERSON>g tôi sử dụng 3 màu và biểu tượng để biểu thị mức độ tin cậy của người bán"}, "page_count": {"message": "Số trang"}, "pai_chu": {"message": "Đã loại trừ"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "<PERSON>ại trừ các mặt hàng bị hạn chế ở Hồng <PERSON>ông"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON> trừ các mặt hàng bị hạn chế ở <PERSON>h<PERSON>t Bản"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON> trừ các mặt hàng bị hạn chế ở Hàn Quốc"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON>i trừ các mặt hàng bị hạn chế ở Kazakhstan"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON>i trừ các mặt hàng bị hạn chế ở Ma <PERSON>"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "<PERSON>ại trừ các mặt hàng bị hạn chế ở Đông Âu"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON> trừ các mặt hàng bị hạn chế ở Ả <PERSON><PERSON><PERSON>"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON>i trừ các mặt hàng bị hạn chế ở <PERSON><PERSON><PERSON>an"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "<PERSON>ại trừ các mặt hàng bị hạn chế ở Hoa Kỳ"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Loại trừ các mặt hàng bị hạn chế ở Việt Nam"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "<PERSON>ại trừ các mặt hàng bị hạn chế"}, "payable_price_formula": {"message": "Giá + <PERSON><PERSON><PERSON> chuyển + <PERSON><PERSON><PERSON><PERSON> giá"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON><PERSON> tra bán lẻ"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON> lẻ"}, "pdp_copy_fail": {"message": "Sao chép không thành công!"}, "pdp_copy_success": {"message": "Đã sao chép thành công!"}, "pdp_share_modal_subtitle": {"message": "<PERSON>a sẻ ảnh chụp màn hình, anh ấy / cô ấy sẽ nhìn thấy tiếng kêu của bạn."}, "pdp_share_modal_title": {"message": "Chia sẻ sự lựa chọn của bạn"}, "pdp_share_screenshot": {"message": "<PERSON>a sẻ ảnh chụp màn hình"}, "pei_song": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON> lo<PERSON>"}, "pin_zhi_ti_yan": {"message": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> sản phẩm"}, "pin_zhi_ti_yan__desc": {"message": "Tỷ lệ hoàn tiền chất lượng của cửa hàng người bán"}, "pin_zhi_tui_kuan_lv": {"message": "Tỷ lệ hoàn tiền"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Tỷ lệ đơn hàng chỉ được hoàn tiền và trả lại trong 30 ngày qua"}, "ping_fen": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "ping_jun_fa_huo_su_du": {"message": "<PERSON><PERSON><PERSON> độ vận chuyển trung bình"}, "pkgInfo_hide": {"message": "Th<PERSON>ng tin hậu cần: bật/tắt"}, "pkgInfo_no_trace": {"message": "<PERSON><PERSON><PERSON><PERSON> có thông tin hậu cần"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON> b<PERSON>n"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "Trang Chủ"}, "popup_go_to_platform": {"message": "Đi tới $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "T<PERSON>i đang mua sắm cho ..."}, "popup_track_package_btn_track": {"message": "THEO DÕI"}, "popup_track_package_desc": {"message": "THEO DÕI TRỌN GÓI TẤT CẢ TRONG MỘT"}, "popup_track_package_search_placeholder": {"message": "Số theo dõi"}, "popup_translate_search_placeholder": {"message": "Dị<PERSON> và tìm kiếm trên $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "<PERSON><PERSON><PERSON> sử giá cả"}, "price_history_chart_tip_ae": {"message": "Mẹo: <PERSON><PERSON> lượng đơn hàng là số lượng đơn hàng tích lũy kể từ khi ra mắt"}, "price_history_chart_tip_coupang": {"message": "Mẹo: <PERSON><PERSON><PERSON> sẽ xóa số lượng đơn hàng gian lận"}, "price_history_inm_1688_l1": {"message": "Xin vui lòng cài đặt"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant for 1688"}, "price_history_panel_lowest_price": {"message": "<PERSON><PERSON><PERSON> thấp nhất:"}, "price_history_panel_tab_price_tracking": {"message": "<PERSON><PERSON><PERSON> sử giá cả"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON> tích ng<PERSON><PERSON> bán"}, "price_history_pro_modal_title": {"message": "<PERSON><PERSON><PERSON> sử giá & <PERSON><PERSON><PERSON> sử đặt hàng"}, "privacy_consent__btn_agree": {"message": "<PERSON>em lại sự đồng ý thu thập dữ liệu"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON><PERSON><PERSON> chấp nh<PERSON>n"}, "privacy_consent__btn_enable_all": {"message": "<PERSON> phép tất cả"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON>u ý rằng, nếu không có dữ liệu hoặc cookie, một số chức năng sẽ bị tắt vì các chức năng đó cần giải thích về dữ liệu hoặc cookie, nhưng bạn vẫn có thể sử dụng các chức năng khác."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON><PERSON> tiế<PERSON>, nế<PERSON> không có dữ liệu hoặc cookie, nó sẽ không hoạt động vì chúng tôi cần giải thích về dữ liệu hoặc cookie."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON><PERSON><PERSON> bạn không cho phép chúng tôi thu thập những thông tin này, vui lòng xóa nó."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, ch<PERSON>g tôi chỉ lấy dữ liệu tiền tệ của bạn trong cookie khi mua sắm trực tuyến để hiển thị lịch sử giá."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON> b<PERSON> b<PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Thêm cookie vào trình duyệt để nhận dạng ẩn danh máy tính hoặc thiết bị của bạn."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON><PERSON> dữ liệu chức năng trong phần bổ trợ để hoạt động với chức năng."}, "privacy_consent__item_functional_title": {"message": "<PERSON><PERSON> ch<PERSON> năng và phân tích"}, "privacy_consent__more_desc": {"message": "Xin lưu ý rằng chúng tôi không chia sẻ dữ liệu cá nhân của bạn với các công ty khác và không có công ty quảng cáo nào thu thập dữ liệu thông qua dịch vụ của chúng tôi."}, "privacy_consent__options__btn__desc": {"message": "<PERSON><PERSON> sử dụng tất cả các t<PERSON> n<PERSON>ng, bạn cần bật nó lên."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON> n<PERSON> lên"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON>g tôi sẽ thu thập các dữ liệu sau nhận dạng cá nhân bạn:"}, "privacy_consent__options__desc_L2": {"message": "- cookie, chúng tôi chỉ lấy dữ liệu tiền tệ của bạn trong cookie khi bạn mua sắm trực tuyến để hiển thị lịch sử giá."}, "privacy_consent__options__desc_L3": {"message": "- và thêm cookie trong trình duyệt để nhận dạng ẩn danh máy tính hoặc thiết bị của bạn."}, "privacy_consent__options__desc_L4": {"message": "- dữ liệu ẩn danh khác làm cho tiện ích mở rộng này thuận tiện hơn."}, "privacy_consent__options__desc_L5": {"message": "Xin lưu ý rằng chúng tôi không chia sẻ dữ liệu cá nhân của bạn với các công ty khác và không có công ty quảng cáo nào thu thập dữ liệu thông qua dịch vụ của chúng tôi."}, "privacy_consent__privacy_preferences": {"message": "<PERSON><PERSON><PERSON> chọn quyền riêng tư"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON><PERSON> thêm >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON> tư"}, "product_info": {"message": "Thông tin sản phẩm"}, "product_recommend__name": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>m tư<PERSON> tự"}, "product_research": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> sản ph<PERSON>m"}, "product_sub__email_desc": {"message": "<PERSON>ail thông báo giá"}, "product_sub__email_edit": {"message": "chỉnh sửa"}, "product_sub__email_not_verified": {"message": "<PERSON>ui lòng x<PERSON>c minh email"}, "product_sub__email_required": {"message": "<PERSON>ui lòng cung cấp email"}, "product_sub__form_countdown": {"message": "Tự động đóng sau $seconds$ seconds", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Thêm lời nhắc không thành công!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u vào"}, "product_sub__form_item_country": {"message": "<PERSON><PERSON> tộc"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>n tại"}, "product_sub__form_item_duration": {"message": "<PERSON> cho"}, "product_sub__form_item_higher_price": {"message": "hoặc giá>"}, "product_sub__form_item_invalid_higher_price": {"message": "Giá phải lớn hơn $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>i thấp hơn $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON>hi giá <"}, "product_sub__form_submit": {"message": "<PERSON>ộp"}, "product_sub__form_success": {"message": "Thêm lời nhắc thành công!"}, "product_sub__high_price_notify": {"message": "<PERSON><PERSON><PERSON><PERSON> báo cho tôi về việc tăng giá"}, "product_sub__low_price_notify": {"message": "<PERSON><PERSON><PERSON><PERSON> báo cho tôi về việc giảm giá"}, "product_sub__modal_title": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>c thay đổi giá đăng ký"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "<PERSON><PERSON> v<PERSON><PERSON> do<PERSON>h ng<PERSON>"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON> lòng chọn ít nhất một sản phẩm"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON> lòng chọn ít nhất một trường"}, "qu_deng_lu": {"message": "<PERSON><PERSON><PERSON>"}, "quan_guo_yan_xuan": {"message": "<PERSON><PERSON><PERSON> chọn toàn cầu"}, "recommendation_popup_banner_btn_install": {"message": "Cài đặt nó"}, "recommendation_popup_banner_desc": {"message": "<PERSON><PERSON><PERSON> thị lịch sử giá trong vòng 3/6 tháng và thông báo giảm giá"}, "region__all": {"message": "<PERSON><PERSON><PERSON> cả các vùng"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "<PERSON><PERSON><PERSON> ho<PERSON> n<PERSON><PERSON><PERSON> chu<PERSON> ph<PERSON>i"}, "ren_zheng_gong_chang": {"message": "<PERSON><PERSON><PERSON> m<PERSON><PERSON> đ<PERSON><PERSON><PERSON> chứ<PERSON> nh<PERSON>n"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON> năm là nhà cung cấp đ<PERSON><PERSON><PERSON> chứng nhận"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p AliPrice"}, "revenue_last30_days": {"message": "<PERSON><PERSON> tiền b<PERSON> hàng trong 30 ngày qua"}, "review_counts": {"message": "<PERSON><PERSON> l<PERSON> ng<PERSON><PERSON>i thu gom"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "th<PERSON><PERSON> gian n<PERSON><PERSON><PERSON> cảnh"}, "sales_amount_last30_days": {"message": "T<PERSON>ng doanh số trong 30 ngày qua"}, "sales_last30_days": {"message": "<PERSON><PERSON><PERSON> số bán hàng trong 30 ngày qua"}, "sbi_alibaba_cate__accessories": {"message": "<PERSON><PERSON> k<PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "Bả<PERSON> vệ"}, "sbi_alibaba_cate__bags_cases": {"message": "Túi & Hộp"}, "sbi_alibaba_cate__beauty": {"message": "sắc đẹp, vẻ đẹp"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "văn hóa công sở"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "<PERSON><PERSON> dùng nhà bếp"}, "sbi_alibaba_cate__clothes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "t<PERSON><PERSON><PERSON><PERSON> thông phát sóng"}, "sbi_alibaba_cate__coat_jacket": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Điện tử dân dụng"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON> phẩm dành cho người lớn"}, "sbi_alibaba_cate__csyp": {"message": "l<PERSON><PERSON> g<PERSON>"}, "sbi_alibaba_cate__cwyy": {"message": "làm v<PERSON><PERSON><PERSON> thú cưng"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON><PERSON> vụ đồ ăn tư<PERSON>i"}, "sbi_alibaba_cate__dgdq": {"message": "<PERSON><PERSON><PERSON> điện"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Dress & Suits"}, "sbi_alibaba_cate__dszm": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>g"}, "sbi_alibaba_cate__dzqj": {"message": "<PERSON><PERSON><PERSON><PERSON> bị điện tử"}, "sbi_alibaba_cate__essb": {"message": "Dụng cụ đã qua sử dụng"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Quần <PERSON> & <PERSON><PERSON> kiện"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON> nội thất"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "<PERSON><PERSON><PERSON> s<PERSON>c c<PERSON> nhân"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON>é<PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "<PERSON><PERSON> thủ công"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON> trư<PERSON><PERSON> thân thiện"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON> điểm chăm sóc da"}, "sbi_alibaba_cate__hg": {"message": "công ng<PERSON><PERSON><PERSON> h<PERSON> chất"}, "sbi_alibaba_cate__jg": {"message": "<PERSON><PERSON> lý"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON><PERSON> li<PERSON> x<PERSON> d<PERSON>ng"}, "sbi_alibaba_cate__jichuang": {"message": "m<PERSON>y công cụ"}, "sbi_alibaba_cate__jjry": {"message": "hộ gia đình sử dụng hàng ngày"}, "sbi_alibaba_cate__jtys": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>"}, "sbi_alibaba_cate__jxsb": {"message": "<PERSON><PERSON><PERSON><PERSON> bị, dụ<PERSON> cụ"}, "sbi_alibaba_cate__jxwj": {"message": "<PERSON><PERSON><PERSON> c<PERSON>ng c<PERSON> khí"}, "sbi_alibaba_cate__jydq": {"message": "<PERSON><PERSON><PERSON><PERSON> bị gia dụng"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u xây dựng cải tạo nhà"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>m dệt dân dụng"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "sản phẩm em bé"}, "sbi_alibaba_cate__nanz": {"message": "nam"}, "sbi_alibaba_cate__nvz": {"message": "Quần <PERSON>o phụ nữ"}, "sbi_alibaba_cate__ny": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__others": {"message": "K<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "<PERSON><PERSON> lắp ráp"}, "sbi_alibaba_cate__qmpj": {"message": "<PERSON><PERSON> tùng ô tô"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON> g<PERSON>y"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h kĩ thuật số"}, "sbi_alibaba_cate__snqj": {"message": "<PERSON><PERSON><PERSON> trữ và làm sạch"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON> <PERSON>n thức u<PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "d<PERSON><PERSON> v<PERSON> kinh doanh"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON> ch<PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Quần & Váy"}, "sbi_alibaba_cate__txcp": {"message": "sản ph<PERSON>m tru<PERSON><PERSON>n thông"}, "sbi_alibaba_cate__tz": {"message": "Quần áo trẻ em"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON> lót"}, "sbi_alibaba_cate__wjgj": {"message": "công cụ phần cứng"}, "sbi_alibaba_cate__xgpi": {"message": "túi da"}, "sbi_alibaba_cate__xmhz": {"message": "hợp tác d<PERSON>n"}, "sbi_alibaba_cate__xs": {"message": "<PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "quần áo thể thao"}, "sbi_alibaba_cate__ydhw": {"message": "<PERSON><PERSON><PERSON> thao ngoài trời"}, "sbi_alibaba_cate__yjkc": {"message": "<PERSON><PERSON><PERSON><PERSON> sản luy<PERSON>n kim"}, "sbi_alibaba_cate__yqyb": {"message": "<PERSON><PERSON><PERSON><PERSON> bị đo đạc"}, "sbi_alibaba_cate__ys": {"message": "In"}, "sbi_alibaba_cate__yyby": {"message": "<PERSON><PERSON><PERSON> s<PERSON>c y tế"}, "sbi_alibaba_cn_kj_90mjs": {"message": "S<PERSON> l<PERSON>ng ngư<PERSON>i mua trong 90 ngày qua"}, "sbi_alibaba_cn_kj_90xsl": {"message": "<PERSON><PERSON><PERSON> số bán hàng trong 90 ngày qua"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "<PERSON><PERSON> vận chuyển quốc tế"}, "sbi_alibaba_cn_kj_gjyf": {"message": "<PERSON><PERSON> vận chuy<PERSON>n"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Lợi nhuận = gi<PERSON> ước tính x tỷ suất lợi nhuận"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "<PERSON><PERSON><PERSON> độ giao hàng trung bình"}, "sbi_alibaba_cn_kj_qtfy": {"message": "phí kh<PERSON>c"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Chi phí khác = gi<PERSON> ước tính x tỷ lệ chi phí khác"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "Cân nặng"}, "sbi_alibaba_cn_kj_szd": {"message": "<PERSON><PERSON> trí"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON> mua"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Bộ"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Không có giá"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "g<PERSON><PERSON> b<PERSON>"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "<PERSON><PERSON> sẵn để bán"}, "sbi_aliprice_alibaba_cn__moren": {"message": "vỡ nợ"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON> nội thất"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "snack"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "trang điểm"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON> lót"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "<PERSON><PERSON> k<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Nước uống đ<PERSON>g chai"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "K<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "<PERSON><PERSON><PERSON><PERSON> bị điện tử"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON> ch<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "<PERSON><PERSON><PERSON> lý"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Đ<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "g<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__apparel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Ô tô & <PERSON>e máy"}, "sbi_aliprice_cate__beauty_health": {"message": "Sắc đẹp / Sức khỏe"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON><PERSON> di động & <PERSON><PERSON><PERSON><PERSON> thông"}, "sbi_aliprice_cate__computer_office": {"message": "Máy <PERSON> & Văn ph<PERSON>ng"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Điện tử dân dụng"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Gi<PERSON><PERSON> & <PERSON><PERSON> dùng <PERSON> phòng"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "<PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> cung cấp <PERSON> tử"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON> nội thất"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Phần mở rộng & Tóc giả"}, "sbi_aliprice_cate__home_garden": {"message": "nhà và vườn"}, "sbi_aliprice_cate__home_improvement": {"message": "<PERSON><PERSON><PERSON> tạo nhà"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Trang sức & Phụ kiện"}, "sbi_aliprice_cate__luggage_bags": {"message": "<PERSON><PERSON><PERSON> l<PERSON> & Túi x<PERSON>ch"}, "sbi_aliprice_cate__mother_kids": {"message": "Mẹ & Trẻ em"}, "sbi_aliprice_cate__novelty_special_use": {"message": "<PERSON><PERSON><PERSON> mới và sử dụng đặc biệt"}, "sbi_aliprice_cate__security_protection": {"message": "<PERSON><PERSON><PERSON> vệ an ninh"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "<PERSON><PERSON><PERSON>ao & Giải trí"}, "sbi_aliprice_cate__toys_hobbies": {"message": "<PERSON><PERSON> chơi và sở thích"}, "sbi_aliprice_cate__watches": {"message": "Xem"}, "sbi_aliprice_cate__weddings_events": {"message": "<PERSON><PERSON><PERSON> & <PERSON>ự kiện"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>y giờ"}, "sbi_button__chat_with_me": {"message": "Tro chuyện vơi tôi"}, "sbi_button__contact_supplier": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_button__hide_on_this_site": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị trên trang web này"}, "sbi_button__open_settings": {"message": "<PERSON><PERSON><PERSON> cấu hình tìm kiếm bằng hình ảnh"}, "sbi_capture_shortcut_tip": {"message": "hoặc nhấn phím \"Enter\" trên bàn phím"}, "sbi_capturing_tip": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 sao"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_crop_start": {"message": "Sử dụng Ảnh chụp màn hình"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON> x<PERSON>c <PERSON>h, vui lòng tải lên một hình ảnh để xác minh. (Xem $video_tutorial$ hoặc thử xóa cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "<PERSON><PERSON><PERSON> truy cập b<PERSON><PERSON> th<PERSON>, vui lòng x<PERSON><PERSON>h"}, "sbi_err_captcha_for_taobao": {"message": "Taobao yêu cầu bạn xác <PERSON>h, vui lòng tải ảnh lên và tìm kiếm để xác minh theo cách thủ công. Lỗi này là do chính sách xác minh mới \"Tìm kiếm trên Tao<PERSON>ao bằng hình ảnh\", chúng tôi khuyên bạn nên xác minh khiếu nại quá thường xuyên trên Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_captcha_msg": {"message": "$platform$ yêu cầu bạn tải hình ảnh lên để tìm kiếm hoặc hoàn tất xác minh bảo mật để xóa các hạn chế tìm kiếm", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON> tra xem đó có phải là phiên bản mới nhất không"}, "sbi_err_cookie_btn_clear": {"message": "Xoá cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Xóa cookie 1688? (<PERSON><PERSON><PERSON> đ<PERSON><PERSON> nh<PERSON>p lạ<PERSON>)"}, "sbi_err_desperate_feature_pdd": {"message": "<PERSON><PERSON><PERSON> năng tìm kiếm hình ảnh đã đư<PERSON><PERSON> chuyể<PERSON> sang phần mở rộng Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "<PERSON>àm cách nào để cải thiện tỷ lệ thành công của tìm kiếm hình ảnh?"}, "sbi_err_img_undersize": {"message": "Hình ảnh > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Đăng nhập $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_low_version": {"message": "Cài đặt phiên bản mới nhất ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "Cần <PERSON>"}, "sbi_err_network": {"message": "Lỗi mạng, đả<PERSON> bảo bạn có thể truy cập đư<PERSON> website"}, "sbi_err_not_low_version": {"message": "<PERSON><PERSON><PERSON> bản mới nhất đã được cài đặt ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON> lại"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON> lại"}, "sbi_err_visit_and_try": {"message": "Hãy thử lại hoặc truy cập $website$ để thử lại", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "T<PERSON><PERSON> cập trang chủ $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON><PERSON> không thành công, vui lòng làm mới trang và thử lại."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON> v<PERSON>"}, "sbi_kuajing_filter_au": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON><PERSON><PERSON> gia đến"}, "sbi_kuajing_filter_es": {"message": "Tây <PERSON>ha"}, "sbi_kuajing_filter_estimate": {"message": "Ước tính"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Công thức giá ước tính = (giá hàng hóa + vận chuyển logistics quốc tế) / (1 - tỷ suất lợi nhuận - tỷ lệ chi phí khác)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON>ướ<PERSON> p<PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khóa để khớp với tiêu đề"}, "sbi_kuajing_filter_logistics": {"message": "Mẫu hậu cần"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u hậu c<PERSON>n"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Cước logistics quốc tế = (trọng lượ<PERSON> x giá vận chuyển + phí đăng ký) x (1 - chiết khấu)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "Tỷ suất lợi n<PERSON>n"}, "sbi_kuajing_filter_prop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_ru": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_total": {"message": "Khớp với $count$ các mặt hàng tương tự", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>"}, "sbi_kuajing_filter_usa": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON> b<PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả nào,vui lòng đăng nhập vào $loginSite$ hoặc thử một bức ảnh khác", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "<PERSON><PERSON><PERSON> th<PERSON>i không kh<PERSON> dụng cho <PERSON>, vui lòng sử dụng $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t Chrome và các tiện ích mở rộng của nó"}, "sbi_msg_no_result_reinstall_l1": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả, vui lòng đăng nhập vào $loginSite$ hoặc thử hình khác, hoặc cài đặt lại phiên bản mới nhất $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "<PERSON><PERSON><PERSON> bản mới nh<PERSON>t", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "<PERSON><PERSON> vực đã chọn"}, "sbi_shipping_": {"message": "<PERSON><PERSON>n chuyển trong ngày"}, "sbi_specify_category": {"message": "Chỉ định danh mục:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON> khu vực"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 ở nước ngoài"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> h<PERSON>n"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON><PERSON><PERSON> phi<PERSON>u gi<PERSON>m gi<PERSON>"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON> lỗi, kh<PERSON><PERSON> tìm thấy kết qu<PERSON>, vui lòng thử lại."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON><PERSON> thiểu. đặt hàng"}, "sbi_visiting": {"message": "<PERSON><PERSON>"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON><PERSON><PERSON> với ng<PERSON><PERSON> bán"}, "sbi_yiwugo__qigou": {"message": "$num$ Pieces (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "<PERSON><PERSON><PERSON> ng<PERSON> sao"}, "searchByImage_screenshot": {"message": "Ảnh chụp màn hình bằng một cú nhấp chuột"}, "searchByImage_search": {"message": "<PERSON><PERSON><PERSON> kiếm bằng một cú nhấp chuột cho các mục tương tự"}, "searchByImage_size_type": {"message": "<PERSON><PERSON><PERSON> thước tệp không được lớn hơn $num$ MB, chỉ $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON> tích h<PERSON>nh <PERSON>nh"}, "search_by_image_progress_searching": {"message": "tìm ki<PERSON>m sản phẩm"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON>"}, "search_by_image_response_rate": {"message": "Tỷ lệ phản hồi: $responseRate$ của những người mua đã liên hệ với nhà cung cấp này đã nhận được phản hồi trong vòng $responseInHour$ giờ.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "T<PERSON><PERSON> kiếm bằng từ khóa:"}, "select_country_language_modal_title_country": {"message": "Quốc gia"}, "select_country_language_modal_title_language": {"message": "<PERSON><PERSON><PERSON>"}, "select_country_region_modal_title": {"message": "<PERSON><PERSON><PERSON> một quốc gia / khu vực"}, "select_language_modal_title": {"message": "<PERSON><PERSON><PERSON> một ngôn ngữ:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng"}, "sellers_count": {"message": "<PERSON><PERSON> l<PERSON> ng<PERSON><PERSON> bán trên trang hiện tại"}, "sellers_count_per_page": {"message": "<PERSON><PERSON> l<PERSON> ng<PERSON><PERSON> bán trên trang hiện tại"}, "service_score": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> d<PERSON>ch vụ toàn <PERSON>n"}, "set_shortcut_keys": {"message": "Đặt phím tắt"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON> lý mua sắm"}, "setting_modal_options_position_title": {"message": "<PERSON><PERSON> trí trình cắm"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON><PERSON> chủ đề"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON>"}, "setting_modal_title": {"message": "Cài đặt"}, "setting_options_country_title": {"message": "Quốc gia / <PERSON>hu vực"}, "setting_options_hover_zoom_desc": {"message": "<PERSON> chu<PERSON>t qua để phóng to"}, "setting_options_hover_zoom_title": {"message": "<PERSON>hu ph<PERSON>g di chu<PERSON>t"}, "setting_options_jd_coupon_desc": {"message": "<PERSON><PERSON><PERSON> thấy phiếu giảm giá trên JD.com"}, "setting_options_jd_coupon_title": {"message": "Phiếu gi<PERSON>m giá JD.com"}, "setting_options_language_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON><PERSON> gi<PERSON> sản phẩm trong M<PERSON>c yêu thích của tôi giảm xuống, bạn sẽ nhận được thông báo đẩy."}, "setting_options_price_drop_alert_title": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o gi<PERSON>m giá"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON><PERSON><PERSON> thị lịch sử giá trên trang tìm kiếm sản phẩm"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON><PERSON> sử giá (<PERSON><PERSON>nh sách)"}, "setting_options_price_history_on_produt_page_desc": {"message": "<PERSON><PERSON><PERSON> thị lịch sử sản phẩm trên trang chi tiết sản phẩm"}, "setting_options_price_history_on_produt_page_title": {"message": "<PERSON><PERSON><PERSON> sử giá (trang chi tiết)"}, "setting_options_sales_analysis_desc": {"message": "Hỗ trợ thố<PERSON> kê <PERSON>, s<PERSON> lư<PERSON><PERSON> bán, số lượng người bán và tỷ lệ doanh số tại cửa hàng trên trang danh sách sản phẩm $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON> tích b<PERSON> hàng"}, "setting_options_save_success_msg": {"message": "<PERSON><PERSON> thành công"}, "setting_options_tacking_price_title": {"message": "<PERSON><PERSON><PERSON><PERSON> báo thay đổi giá"}, "setting_options_value_off": {"message": "Tắt"}, "setting_options_value_on": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_pkg_quick_view_desc": {"message": "Hỗ trợ: 1688 & Taobao"}, "setting_saved_message": {"message": "<PERSON><PERSON><PERSON> thay đổi đã đư<PERSON><PERSON> lưu thành công"}, "setting_section_enable_platform_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_section_setting_title": {"message": "Cài đặt"}, "setting_section_shortcuts_title": {"message": "<PERSON><PERSON><PERSON> ph<PERSON> t<PERSON>"}, "settings_aliprice_agent__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị trên trang chi tiết sản phẩm $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON> cho tôi"}, "settings_copy_link__desc": {"message": "<PERSON><PERSON><PERSON> thị trên trang chi tiết sản phẩm"}, "settings_copy_link__title": {"message": "Nút sao chép và tiêu đề tìm kiếm"}, "settings_currency_desc__for_detail": {"message": "Hỗ trợ trang chi tiết sản phẩm 1688"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON> kiếm bằng hình ảnh (bao gồm 1688/1688 ở nước ngoài / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Chọn giá"}, "settings_currency_desc_display_for_list": {"message": "Hiển thị trong tìm kiếm hình <PERSON>nh (bao gồm 1688/1688 ở nước ngoài/Taobao)"}, "settings_currency_rate_desc": {"message": "<PERSON><PERSON><PERSON> nhật tỷ giá hối đoái từ \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "<PERSON><PERSON> hàng <PERSON>"}, "settings_download_images__desc": {"message": "Hỗ trợ tải hình ảnh xuống từ $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "nút tải xuống h<PERSON>nh <PERSON>nh"}, "settings_download_reviews__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị trên trang chi tiết sản phẩm $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh đ<PERSON>h giá"}, "settings_google_translate_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> chu<PERSON>t phải để nhận thanh google dịch"}, "settings_google_translate_title": {"message": "d<PERSON><PERSON> trang <PERSON>"}, "settings_historical_trend_desc": {"message": "<PERSON><PERSON>n thị ở góc dưới bên phải của hình ảnh trên trang danh sách sản phẩm"}, "settings_modal_btn_more": {"message": "Cài đặt khác"}, "settings_productInfo_desc": {"message": "Hiển thị thông tin sản phẩm chi tiết hơn trên trang danh sách sản phẩm. Bật tính năng này có thể làm tăng tải của máy tính và gây ra độ trễ trang. Nếu nó ảnh hưởng đến hiệu suất, bạn nên tắt tính năng này."}, "settings_product_recommend__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị bên dư<PERSON><PERSON> hình <PERSON>nh chính trên trang chi tiết sản phẩm $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "<PERSON><PERSON><PERSON> phẩm đư<PERSON><PERSON> đề xuất"}, "settings_research_desc": {"message": "<PERSON><PERSON><PERSON> vấn thông tin chi tiết hơn trên trang danh sách sản phẩm"}, "settings_sbi_add_to_list": {"message": "Thêm vào $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "<PERSON><PERSON>nh thu nhỏ của kết quả tìm kiếm theo ảnh"}, "settings_sbi_remove_from_list": {"message": "Xóa khỏi $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "<PERSON><PERSON>ê<PERSON> vào danh sách chặn"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Điều chỉnh vị trí lối vào"}, "settings_search_by_image_blacklist_desc": {"message": "<PERSON><PERSON>ông hiển thị biểu tượng trên các trang web trong danh sách đen."}, "settings_search_by_image_blacklist_title": {"message": "<PERSON><PERSON> s<PERSON>ch chặn"}, "settings_search_by_image_bottom_left": {"message": "<PERSON><PERSON><PERSON><PERSON> cùng bên trái"}, "settings_search_by_image_bottom_right": {"message": "<PERSON><PERSON><PERSON><PERSON> cùng bên ph<PERSON>i"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON><PERSON><PERSON> danh sách chặn"}, "settings_search_by_image_detail_page_icon_title": {"message": "<PERSON>hu nhỏ"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Nhỏ hơn"}, "settings_search_by_image_display_button_desc": {"message": "<PERSON>ột cú nhấp chuột vào biểu tượng để tìm kiếm bằng hình ảnh"}, "settings_search_by_image_display_button_title": {"message": "<PERSON><PERSON><PERSON><PERSON> tượ<PERSON> trên hình <PERSON>nh"}, "settings_search_by_image_sourece_websites_desc": {"message": "<PERSON><PERSON><PERSON> sản phẩm nguồn trên các trang web này"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON><PERSON> kiếm theo kết quả hình <PERSON>nh"}, "settings_search_by_image_top_left": {"message": "Trên cùng bên trái"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng bên p<PERSON>i"}, "settings_search_keyword_on_x__desc": {"message": "T<PERSON><PERSON> kiếm từ trên $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Hiển thị biểu tượng $platform$ khi các từ được chọn", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "<PERSON><PERSON> gắng tìm cùng một sản phẩm trong các trang web đó (tối đa 5)"}, "settings_similar_products_title": {"message": "<PERSON><PERSON><PERSON> sản phẩm tương tự"}, "settings_toolbar_expand_title": {"message": "<PERSON>hu nhỏ tiện ích"}, "settings_top_toolbar_desc": {"message": "<PERSON><PERSON> tìm kiếm trên đầu trang"}, "settings_top_toolbar_title": {"message": "<PERSON><PERSON> t<PERSON>m k<PERSON>m"}, "settings_translate_search_desc": {"message": "<PERSON><PERSON><PERSON> sang tiếng Trung và tìm kiếm"}, "settings_translate_search_title": {"message": "<PERSON><PERSON><PERSON> kiếm đa ngôn ngữ"}, "settings_translator_contextmenu_title": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> d<PERSON>ch"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "<PERSON><PERSON><PERSON> <PERSON>ể xuất"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>"}, "shang_jia_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> thời gian"}, "shang_pin_biao_ti": {"message": "tiêu đ<PERSON> sản phẩm"}, "shang_pin_dui_bi": {"message": "sự so s<PERSON>h sản phẩm"}, "shang_pin_lian_jie": {"message": "li<PERSON><PERSON> kết sản ph<PERSON>m"}, "shang_pin_xin_xi": {"message": "Thông tin sản phẩm"}, "share_modal__content": {"message": "<PERSON>a sẻ với bạn bè của bạn"}, "share_modal__disable_for_while": {"message": "<PERSON><PERSON><PERSON> không muốn chia sẻ bất cứ điều gì"}, "share_modal__title": {"message": "Bạn có thích $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON>òn lại"}, "shi_fou_ke_ding_zhi": {"message": "Nó có thể tùy chỉnh được không?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "<PERSON><PERSON><PERSON> cung cấ<PERSON> đ<PERSON><PERSON><PERSON> chứng nh<PERSON>n"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "<PERSON><PERSON><PERSON> cung cấ<PERSON> đ<PERSON><PERSON><PERSON> chứng nh<PERSON>n"}, "shi_fou_you_mao_yi_dan_bao": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> th<PERSON><PERSON> mại"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON> l<PERSON> thư<PERSON> mại"}, "shipping_fee": {"message": "<PERSON><PERSON> vận chuy<PERSON>n"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON> theo dõi cửa hàng"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Tối đa 5"}, "sku_calc_price": {"message": "<PERSON><PERSON><PERSON> đã t<PERSON>h"}, "sku_calc_price_settings": {"message": "Cài đặt giá đã tính"}, "sku_formula": {"message": "<PERSON><PERSON><PERSON> thức"}, "sku_formula_desc": {"message": "<PERSON><PERSON> tả công thức"}, "sku_formula_desc_text": {"message": "Hỗ trợ các công thức toán học phức tạp, với giá gốc được biểu diễn bởi A và cước phí được biểu diễn bởi B\n\n<br/>\n\nHỗ trợ dấu ngoặc (), cộng +, trừ -, nhân * và chia /\n\n<br/>\n\nV<PERSON> dụ:\n\n<br/>\n\n1. Đ<PERSON> đạt được 1,2 lần giá gốc và sau đó cộng cước phí, công thức là: A*1,2+B\n\n<br/>\n\n2. Đ<PERSON> đạt được giá gốc cộng với 1 nhân dân tệ, sau đ<PERSON> nhân với 1,2 lần, công thức là: (A+1)*1,2\n\n<br/>\n\n3. Đ<PERSON> đạt được giá gốc cộng với 10 nhân dân tệ, sau đ<PERSON> nhân với 1,2 lần, sau đ<PERSON> trừ đi 3 nhân dân tệ, công thức là: (A+10)*1,2-3"}, "sku_in_stock": {"message": "<PERSON><PERSON><PERSON> hàng"}, "sku_invalid_formula_format": {"message": "<PERSON><PERSON><PERSON> dạng công thức không hợp lệ"}, "sku_inventory": {"message": "<PERSON><PERSON><PERSON> tồn kho"}, "sku_link_copy_fail": {"message": "<PERSON>ã sao chép thành công, thông số kỹ thuật và thuộc tính của sku không đ<PERSON><PERSON><PERSON> chọn"}, "sku_link_copy_success": {"message": "Đã sao chép thành công, các thông số và thuộc tính của mã hàng đã đ<PERSON><PERSON><PERSON> chọn"}, "sku_list": {"message": "Danh sách SKU"}, "sku_min_qrder_qty": {"message": "<PERSON><PERSON> lượng đặt hàng tối thiểu"}, "sku_name": {"message": "Tên SKU"}, "sku_no": {"message": "Số"}, "sku_original_price": {"message": "<PERSON><PERSON><PERSON>"}, "sku_price": {"message": "Giá SKU"}, "stop_track_time_label": {"message": "<PERSON><PERSON><PERSON><PERSON> hạn theo dõi:"}, "suo_zai_di_qu": {"message": "vị trí"}, "tab_pkg_quick_view": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> h<PERSON>u c<PERSON>n"}, "tab_product_details_price_history": {"message": "<PERSON><PERSON><PERSON> sử giá cả"}, "tab_product_details_reviews": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON> tích ng<PERSON><PERSON> bán"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>m tư<PERSON> tự"}, "total_days_listed_per_product": {"message": "Tổng số ngày tồn kho -> Số lượng sản phẩm"}, "total_items": {"message": "Tổng số sản phẩm"}, "total_price_per_product": {"message": "Tổng giá `<PERSON><PERSON> lượng sản phẩm"}, "total_rating_per_product": {"message": "<PERSON><PERSON><PERSON> xếp hạng `<PERSON><PERSON> lượng sản phẩm"}, "total_revenue": {"message": "<PERSON><PERSON>ng doanh thu"}, "total_revenue40_items": {"message": "Tổng doanh thu của $amount$ sản phẩm trên trang hiện tại", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON>ng doanh thu"}, "total_sales40_items": {"message": "Tổng doanh số của $amount$ sản phẩm trên trang hiện tại", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "<PERSON> dõi trong: 1 năm"}, "track_for_3_months": {"message": "<PERSON> d<PERSON>i trong: 3 tháng"}, "track_for_6_months": {"message": "<PERSON> d<PERSON>i trong: 6 tháng"}, "tracking_price_email_add_btn": {"message": "Thêm email"}, "tracking_price_email_edit_btn": {"message": "Chỉnh sửa email"}, "tracking_price_email_intro": {"message": "<PERSON><PERSON>g tôi sẽ thông báo cho bạn qua email."}, "tracking_price_email_invalid": {"message": "<PERSON><PERSON> lòng cung cấp một email hợp lệ"}, "tracking_price_email_verified_desc": {"message": "<PERSON><PERSON>y giờ bạn có thể nhận đư<PERSON><PERSON> thông báo giảm giá của chúng tôi."}, "tracking_price_email_verified_title": {"message": "<PERSON><PERSON> xác minh thành công"}, "tracking_price_email_verify_desc_line1": {"message": "<PERSON><PERSON>g tôi đã gửi một liên kết xác minh đến địa chỉ email của bạn,"}, "tracking_price_email_verify_desc_line2": {"message": "vui lòng kiểm tra hộp thư email của bạn."}, "tracking_price_email_verify_title": {"message": "<PERSON><PERSON><PERSON>"}, "tracking_price_web_push_notification_intro": {"message": "Trên máy tính để bàn: <PERSON><PERSON><PERSON> có thể giám sát bất kỳ sản phẩm nào cho bạn và gửi cho bạn Thông báo đẩy trên web khi giá thay đổi."}, "tracking_price_web_push_notification_title": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o đ<PERSON>y trên web"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ch bởi <PERSON><PERSON><PERSON>, vui lòng đăng nhập vào $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "<PERSON>ã dịch và copy vào clipboard, nhưng do hạn chế của Aliwangwang nên các bạn phải dán thủ công nhé!"}, "translate_im__send": {"message": "<PERSON><PERSON><PERSON> v<PERSON> g<PERSON>i"}, "translate_search": {"message": "<PERSON><PERSON><PERSON> và tìm kiếm"}, "translation_originals_translated": {"message": "<PERSON>ản gốc và tiếng Trung"}, "translation_translated": {"message": "ngư<PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Tự động phát hiện"}, "translator_language_detected": {"message": "<PERSON><PERSON> ph<PERSON><PERSON>n"}, "translator_language_search_placeholder": {"message": "<PERSON><PERSON><PERSON> ki<PERSON>m ngôn ngữ"}, "try_again": {"message": "<PERSON><PERSON><PERSON> lại"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>:"}, "tu_pian_lian_jie": {"message": "<PERSON><PERSON><PERSON><PERSON> kết nối tới hình <PERSON>nh"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON><PERSON> lại kinh nghiệm"}, "tui_huan_ti_yan__desc": {"message": "<PERSON><PERSON><PERSON> giá các chỉ số sau bán hàng của người bán"}, "tutorial__show_all": {"message": "<PERSON><PERSON><PERSON> c<PERSON> các t<PERSON> n<PERSON>ng"}, "tutorial_ae_popup_title": {"message": "<PERSON><PERSON> tiện ích mở rộng, mở Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Phân tích <PERSON>h giá AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Hỗ trợ USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "<PERSON>ận chuyển đến <PERSON>/<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>uốc đại lục"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Hỗ trợ mua hàng ở nước ngoài"}, "tutorial_auto_apply_coupon_title": {"message": "Tự động áp dụng phiếu giảm giá"}, "tutorial_btn_end": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_btn_example": {"message": "<PERSON><PERSON>"}, "tutorial_btn_have_a_try": {"message": "Ok, h<PERSON>y thử"}, "tutorial_btn_next": {"message": "<PERSON><PERSON> tiếp"}, "tutorial_btn_see_more": {"message": "H<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "So s<PERSON>h với cùng một phong cách"}, "tutorial_currency_convert_title": {"message": "chuyển đổi tỷ giá hối đoái"}, "tutorial_export_shopping_cart": {"message": "<PERSON><PERSON><PERSON>, hỗ trợ Taobao và 1688"}, "tutorial_export_shopping_cart_title": {"message": "giỏ hàng xuất khẩu"}, "tutorial_price_history_pro": {"message": "<PERSON><PERSON><PERSON> thị trên trang chi tiết sản phẩm.\nHỗ tr<PERSON>, Lazada, Amazon và Ebay"}, "tutorial_price_history_pro_title": {"message": "<PERSON><PERSON>ch sử giá cả năm và lịch sử đặt hàng"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Ảnh chụp màn hình tìm kiếm cùng kiểu"}, "tutorial_translate_search": {"message": "<PERSON><PERSON><PERSON> để tìm kiếm"}, "tutorial_translate_search_and_package_tracking": {"message": "<PERSON><PERSON><PERSON> ki<PERSON>m bản dịch và theo dõi gói hàng"}, "unit_bao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_ben": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_bi": {"message": "m<PERSON><PERSON> l<PERSON>nh"}, "unit_chuang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_dai": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_dui": {"message": "<PERSON><PERSON><PERSON>"}, "unit_fen": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_ge": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_he": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "<PERSON><PERSON><PERSON>"}, "unit_tai": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_ti": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_tiao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_xiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_zhang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unit_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "verify_contact_support": {"message": "<PERSON><PERSON><PERSON> hệ với bộ phận hỗ trợ"}, "verify_human_verification": {"message": "<PERSON><PERSON><PERSON>h của con ng<PERSON>ời"}, "verify_unusual_access": {"message": "<PERSON><PERSON> phát hiện truy cập bất thường"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch tất cả"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON> tất cả các bản ghi đã xem?"}, "view_history_clean_all_warring_title": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o"}, "view_history_viewd": {"message": "Đã xem"}, "website": {"message": "trang web"}, "weight": {"message": "Cân nặng"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON><PERSON><PERSON><PERSON> thể lấy dữ liệu"}, "wu_liu_shi_xiao": {"message": "<PERSON><PERSON><PERSON> hàng đúng hẹn"}, "wu_liu_shi_xiao__desc": {"message": "Tỷ lệ lấy hàng trong 48 giờ và tỷ lệ hoàn thành đơn hàng của cửa hàng người bán"}, "xia_dan_jia": {"message": "<PERSON><PERSON><PERSON> cu<PERSON>i c<PERSON>ng"}, "xian_xuan_ze_product_attributes": {"message": "<PERSON><PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sản phẩm"}, "xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> hàng"}, "xiao_liang_zhan_bi": {"message": "Tỷ lệ khối lư<PERSON> bán hàng"}, "xiao_shi": {"message": "$num$ Giờ", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON>h thu"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>m doanh thu"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Chọn $amount$ bản ghi", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "Đã ghim"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "<PERSON><PERSON> tải tất cả sản phẩm"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON><PERSON> số hàng năm"}, "yi_nian_xiao_liang_zhan_bi": {"message": "<PERSON><PERSON> sẻ doanh số hàng năm"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON> thu hàng năm"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON> sẻ doanh thu hàng năm"}, "yi_shua_xin": {"message": "<PERSON><PERSON> làm mới"}, "yin_cang_xiang_tong_dian": {"message": "che gi<PERSON>u sự tương đồng"}, "you_xiao_liang": {"message": "<PERSON><PERSON><PERSON> do<PERSON>h số bán hàng"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> gian đến dự kiến"}, "yuan_gong_ren_shu": {"message": "Số lượng nhân viên"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> hàng tháng"}, "yue_dai_xiao": {"message": "<PERSON><PERSON><PERSON> chuyển thả"}, "yue_dai_xiao__desc": {"message": "<PERSON><PERSON><PERSON> số Dropshipping trong 30 ngày qua"}, "yue_dai_xiao_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON> số dropshipping trong 30 ngày qua, sắp xếp từ cao đến thấp"}, "yue_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> số bán hàng trong 30 ngày qua"}, "zhan_kai": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "zhe_kou": {"message": "G<PERSON>ảm giá"}, "zhi_chi_yi_jian_dai_fa": {"message": "<PERSON><PERSON><PERSON> chuyển thả"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON> phí vận chuyển"}, "zhi_fu_ding_dan_shu": {"message": "Đ<PERSON><PERSON> hàng đã <PERSON>h toán"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON><PERSON> lượng đặt hàng cho sản phẩm này (30 ngày)"}, "zhu_ce_xing_zhi": {"message": "t<PERSON>h chất đ<PERSON>ng ký"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> kiện tùy chỉnh"}, "zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON> thể đã bán"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON><PERSON> giá tổng thể"}, "zong_he_fu_wu_fen__desc": {"message": "<PERSON><PERSON><PERSON> giá chung về dịch vụ của ngư<PERSON>i bán"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "zong_he_ti_yan_fen": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "zong_he_ti_yan_fen_3": {"message": "Dưới 4 sao"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 sao"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 sao"}, "zong_he_ti_yan_fen_5": {"message": "5 sao"}, "zong_ku_cun": {"message": "<PERSON><PERSON><PERSON> hàng tồn kho"}, "zong_xiao_liang": {"message": "<PERSON><PERSON>ng doanh thu"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Tỷ lệ phản hồi 3 phút trong 30 ngày qua"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Tỷ l<PERSON> ph<PERSON><PERSON> hồ<PERSON> 48 giờ trong 30 ngày qua"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Tỷ lệ hiệu suất 48 gi<PERSON> trong 30 ngày qua"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Kỷ lục giao d<PERSON> (30 ngày)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Kỷ lục giao d<PERSON> (30 ngày)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Tỷ lệ tranh chấp trong 30 ngày qua"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Tỷ lệ hoàn tiền chất lượng trong 30 ngày qua"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "S<PERSON> lệnh thanh toán trong 30 ngày gần nh<PERSON>t"}}