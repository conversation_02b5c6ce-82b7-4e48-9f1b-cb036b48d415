{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688跨境热卖现货"}, "1688_shi_li_ren_zheng": {"message": "1688实力认证"}, "1_jian_qi_pi": {"message": "1件起批"}, "1year_yi_shang": {"message": "一年以上"}, "24H": {"message": "24小时"}, "24H_fa_huo": {"message": "24小时发货"}, "24H_lan_shou_lv": {"message": "24小时揽收率"}, "30D_shang_xin": {"message": "30天上新"}, "30d_sales": {"message": "30天内$amount$个成交", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3分钟响应率"}, "3Min_xiang_ying_lv__desc": {"message": "近30天旺旺3分钟内有效回复买家咨询消息的比例"}, "48H": {"message": "48小时"}, "48H_fa_huo": {"message": "48小时发货"}, "48H_lan_shou_lv": {"message": "48小时揽收率"}, "48H_lan_shou_lv__desc": {"message": "48小时内有揽收记录的订单数与总订单数的比率"}, "48H_lv_yue_lv": {"message": "48小时履约率"}, "48H_lv_yue_lv__desc": {"message": "48小时内有揽收记录或发货记录的订单数与总订单数的比率"}, "72H": {"message": "72小时"}, "7D_shang_xin": {"message": "7天上新"}, "7D_wu_li_you": {"message": "7天无理由"}, "ABS_title_text": {"message": "该商品有品牌故事"}, "AC_title_text": {"message": "该商品有Amazon's Choice标识"}, "A_title_text": {"message": "该商品有A+页面"}, "BS_title_text": {"message": "该商品是$type$类目下的$num$ 热销品", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "Limited time deal的缩写，表示该商品参加了“7天促销”活动"}, "NR_title_text": {"message": "该商品是$type$类目下的$num$ 热销新品", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "该商品投放了视频广告。PPC广告的一种，一般在搜索结果页的中间位置出现"}, "SB_title_text": {"message": "该商品投放了品牌广告。PPC广告的一种，一般在搜索结果页的顶部或底部出现"}, "SP_title_text": {"message": "该商品投放了Sponsored Product广告"}, "V_title_text": {"message": "该商品有视频介绍"}, "advanced_research": {"message": "高级查询"}, "agent_ds1688___my_order": {"message": "我的订单"}, "agent_ds1688__add_to_cart": {"message": "跨境直购"}, "agent_ds1688__cart": {"message": "跨境购物车"}, "agent_ds1688__desc": {"message": "1688跨境直购, 由AliPrice与1688官方联合提供"}, "agent_ds1688__freight": {"message": "国际运费试算"}, "agent_ds1688__help": {"message": "帮助"}, "agent_ds1688__packages": {"message": "配送单"}, "agent_ds1688__profile": {"message": "个人中心"}, "agent_ds1688__warehouse": {"message": "我的仓库"}, "ai_comment_analysis_advantage": {"message": "优点"}, "ai_comment_analysis_ai": {"message": "AI评论分析"}, "ai_comment_analysis_available": {"message": "可用"}, "ai_comment_analysis_balance": {"message": "金币不足，请充值"}, "ai_comment_analysis_behavior": {"message": "行为"}, "ai_comment_analysis_characteristic": {"message": "人群特征"}, "ai_comment_analysis_comment": {"message": "该商品的评论数不足，无法得出准确的结论，请选择评论数更多的商品进行分析"}, "ai_comment_analysis_consume": {"message": "预计消耗"}, "ai_comment_analysis_default": {"message": "默认评论"}, "ai_comment_analysis_desire": {"message": "客户期望"}, "ai_comment_analysis_disadvantage": {"message": "缺点"}, "ai_comment_analysis_free": {"message": "免费次数"}, "ai_comment_analysis_freeNum": {"message": "将消耗 1 次免费次数"}, "ai_comment_analysis_go_recharge": {"message": "去充值"}, "ai_comment_analysis_intelligence": {"message": "智能评论分析"}, "ai_comment_analysis_location": {"message": "使用地点"}, "ai_comment_analysis_motive": {"message": "购买动机"}, "ai_comment_analysis_network_error": {"message": "网络出错，请重试"}, "ai_comment_analysis_normal": {"message": "图文评论"}, "ai_comment_analysis_number_reviews": {"message": "评论数 $num$, 预计消耗 $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "普通评论"}, "ai_comment_analysis_percentage": {"message": "百分比"}, "ai_comment_analysis_problem": {"message": "支付遇到问题"}, "ai_comment_analysis_reanalysis": {"message": "重新分析"}, "ai_comment_analysis_reason": {"message": "原因"}, "ai_comment_analysis_recharge": {"message": "充值"}, "ai_comment_analysis_recharged": {"message": "我已充值"}, "ai_comment_analysis_retry": {"message": "重试"}, "ai_comment_analysis_scene": {"message": "使用场景"}, "ai_comment_analysis_start": {"message": "开始分析"}, "ai_comment_analysis_subject": {"message": "话题"}, "ai_comment_analysis_time": {"message": "使用时刻"}, "ai_comment_analysis_tool": {"message": "AI工具"}, "ai_comment_analysis_user_portrait": {"message": "用户画像"}, "ai_comment_analysis_welcome": {"message": "欢迎使用AI评论分析"}, "ai_comment_analysis_year": {"message": "近一年的评论"}, "ai_listing_Exclude_keywords": {"message": "排除关键字"}, "ai_listing_Login_the_feature": {"message": "此功能需要登录"}, "ai_listing_aI_generation": {"message": "AI生成"}, "ai_listing_add_automatic": {"message": "自动"}, "ai_listing_add_dictionary_new": {"message": "新建词库"}, "ai_listing_add_enter_keywords": {"message": "输入关键词"}, "ai_listing_add_inputkey_selling": {"message": "输入卖点，按$key$键完成添加", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "已超出限制，最多$amount$个卖点", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "添加关键字"}, "ai_listing_add_manually": {"message": "手动添加"}, "ai_listing_add_selling": {"message": "添加卖点"}, "ai_listing_added_keywords": {"message": "已添加的关键词"}, "ai_listing_added_successfully": {"message": "添加成功"}, "ai_listing_addexcluded_keywords": {"message": "输入排除关键词，按回车键完成添加"}, "ai_listing_adding_selling": {"message": "已添加的卖点"}, "ai_listing_addkeyword_enter": {"message": "输入关键属性词，按回车键完成添加"}, "ai_listing_ai_description": {"message": "AI描述词库"}, "ai_listing_ai_dictionary": {"message": "AI标题词库"}, "ai_listing_ai_title": {"message": "AI标题"}, "ai_listing_aidescription_repeated": {"message": "AI描述词库名称不能重复"}, "ai_listing_aititle_repeated": {"message": "AI标题词库名称不能重复"}, "ai_listing_data_comes_from": {"message": "该数据源自："}, "ai_listing_deleted_successfully": {"message": "删除成功"}, "ai_listing_dictionary_name": {"message": "词库名"}, "ai_listing_edit_dictionary": {"message": "修改词库 ..."}, "ai_listing_edit_word_library": {"message": "编辑词库"}, "ai_listing_enter_keywords": {"message": "输入关键词，按$key$键完成添加", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "已超出限制，最多$amount$个关键词", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "排除词库"}, "ai_listing_generate_characters": {"message": "生成字符"}, "ai_listing_generation_platform": {"message": "生成平台"}, "ai_listing_help_optimize": {"message": "帮我优化一下产品标题，原标题为"}, "ai_listing_include_selling": {"message": "其他卖点包括："}, "ai_listing_included_keyword": {"message": "包含关键词"}, "ai_listing_included_keywords": {"message": "已收录的关键字"}, "ai_listing_input_selling": {"message": "输入卖点"}, "ai_listing_input_selling_fit": {"message": "输入卖点对标题进行匹配"}, "ai_listing_input_selling_please": {"message": "请输入卖点"}, "ai_listing_intelligently_title": {"message": "上方输入所需内容 即可智能生成标题"}, "ai_listing_keyword_product_title": {"message": "关键词产品标题"}, "ai_listing_keywords_repeated": {"message": "关键字不能重复"}, "ai_listing_listed_selling_points": {"message": "已收录的卖点"}, "ai_listing_long_title_1": {"message": "包含品牌名称、产品类型、产品特征等基本信息。"}, "ai_listing_long_title_2": {"message": "在标准产品标题的基础上，添加了有利于搜索引擎优化的关键词。"}, "ai_listing_long_title_3": {"message": "除了包含品牌名称、产品类型、产品特征和关键词外，还包含了长尾关键词，以便在具体、细分的搜索查询中获得更高的排名。"}, "ai_listing_longtail_keyword_product_title": {"message": "长尾关键词产品标题"}, "ai_listing_manually_enter": {"message": "手动输入 ..."}, "ai_listing_network_not_working": {"message": "网络不通，访问ChatGPT需要科学上网"}, "ai_listing_new_dictionary": {"message": "新建词库 ..."}, "ai_listing_new_generate": {"message": "生成"}, "ai_listing_optional_words": {"message": "可选词"}, "ai_listing_original_title": {"message": "原标题"}, "ai_listing_other_keywords_included": {"message": "其他关键词包括："}, "ai_listing_please_again": {"message": "请重试"}, "ai_listing_please_select": {"message": "已帮您生成以下标题，请选择："}, "ai_listing_product_category": {"message": "产品分类"}, "ai_listing_product_category_is": {"message": "产品分类是"}, "ai_listing_product_category_to": {"message": "请问产品属于什么分类？"}, "ai_listing_random_keywords": {"message": "随机$amount$个关键词", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "随机$amount$个卖点", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "从词库中随机"}, "ai_listing_search_selling": {"message": "按卖点搜索"}, "ai_listing_select_product_categories": {"message": "自动选择产品分类，"}, "ai_listing_select_product_selling_points": {"message": "自动选择产品卖点"}, "ai_listing_select_word_library": {"message": "选择词库"}, "ai_listing_selling": {"message": "卖点"}, "ai_listing_selling_ask": {"message": "标题还有什么其他卖点要求？"}, "ai_listing_selling_optional": {"message": "可选卖点"}, "ai_listing_selling_repeat": {"message": "卖点不能重复"}, "ai_listing_set_excluded": {"message": "设为排除词库"}, "ai_listing_set_include_selling_points": {"message": "包含卖点"}, "ai_listing_set_included": {"message": "设为包含词库"}, "ai_listing_set_selling_dictionary": {"message": "设为卖点词库"}, "ai_listing_standard_product_title": {"message": "标准产品标题"}, "ai_listing_translated_title": {"message": "翻译标题"}, "ai_listing_visit_chatGPT": {"message": "访问ChatGPT"}, "ai_listing_what_other_keywords": {"message": "标题还有什么其他关键词要求？"}, "aliprice_coupons_apply_again": {"message": "再试一次"}, "aliprice_coupons_apply_coupons": {"message": "使用优惠券"}, "aliprice_coupons_apply_success": {"message": "找到优惠券：节省 $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "测试最优惠的代码..."}, "aliprice_coupons_applying_desc": {"message": "确认中：$code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "继续付款"}, "aliprice_coupons_found_coupons": {"message": "我们找到了 $amount$ 优惠券", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "准备好付款了吗？ 让我们帮你确认最优惠的价格！"}, "aliprice_coupons_no_coupon_aviable": {"message": "这些代码无法使用。 没关系——你已经得到了最优惠的价格。"}, "aliprice_coupons_toolbar_btn": {"message": "获取优惠券"}, "aliww_translate": {"message": "阿里旺旺聊天翻译"}, "aliww_translate_supports": {"message": "支持：1688 & 淘宝"}, "amazon_extended_keywords_Keywords": {"message": "搜索词"}, "amazon_extended_keywords_copy_all": {"message": "复制所有"}, "amazon_extended_keywords_more": {"message": "查看更多"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "按累计销量排序"}, "an_lei_xing_cha_kan": {"message": "类型"}, "an_yue_dai_xiao_pai_xu": {"message": "按月代销排序"}, "apra_btn__cat_name": {"message": "评论分析"}, "apra_chart__name": {"message": "产品销量国家地区百分比"}, "apra_chart__update_at": {"message": "更新时间 $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "国家地区销售统计"}, "auto_opening": {"message": "$num$秒后自动打开", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "下$autoPaging$ 页", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "平均上架天数"}, "average_hui_fu_lv": {"message": "平均回复率"}, "average_ping_gong_ying_shang_deng_ji": {"message": "平均供应商等级"}, "average_price": {"message": "平均价格"}, "average_qi_ding_liang": {"message": "平均起订量"}, "average_rating": {"message": "平均评分"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "平均认证供应年长"}, "average_revenue": {"message": "平均销售额"}, "average_revenue_per_product": {"message": "总销售额 ÷ 产品数"}, "average_sales": {"message": "平均销量"}, "average_sales_per_product": {"message": "总销量 ÷ 产品数"}, "bao_han": {"message": "包含"}, "bao_zheng_jin": {"message": "保证金"}, "bian_ti_shu": {"message": "变体数"}, "biao_ti": {"message": "标题"}, "blacklist_add_blacklist": {"message": "拉黑此店铺"}, "blacklist_address_incorrect": {"message": "地址不正确。 请检查。"}, "blacklist_blacked_out": {"message": "已拉黑此店铺"}, "blacklist_blacklist": {"message": "黑名单"}, "blacklist_no_records_yet": {"message": "暂无记录！"}, "blue_rocket": {"message": "蓝色火箭"}, "brand_name": {"message": "品牌名"}, "btn_aliprice_agent__daigou": {"message": "代购"}, "btn_aliprice_agent__dropshipping": {"message": "一件代发"}, "btn_have_a_try": {"message": "试一试"}, "btn_refresh": {"message": "刷新"}, "btn_try_it_now": {"message": "马上试试"}, "btn_txt_view_on_aliprice": {"message": "在AliPrice上查看"}, "bu_bao_han": {"message": "不包含"}, "bulk_copy_links": {"message": "批量复制链接"}, "bulk_copy_products": {"message": "批量复制产品"}, "cai_gou_zi_xun": {"message": "采购咨询"}, "cai_gou_zi_xun__desc": {"message": "商家的三分钟响应率"}, "can_ping_lei_xing": {"message": "商品类型"}, "cao_zuo": {"message": "操作"}, "chan_pin_ID": {"message": "产品ID"}, "chan_pin_e_wai_xin_xi": {"message": "产品额外信息"}, "chan_pin_lian_jie": {"message": "产品链接"}, "cheng_li_shi_jian": {"message": "成立时间"}, "city__aba": {"message": "阿坝藏族羌族自治州"}, "city__aksu": {"message": "阿克苏市"}, "city__alaer": {"message": "阿拉尔市"}, "city__altai": {"message": "阿勒泰"}, "city__alxaleague": {"message": "阿拉善盟"}, "city__ankang": {"message": "安康市"}, "city__anqing": {"message": "安庆市"}, "city__anshan": {"message": "鞍山市"}, "city__anshun": {"message": "安顺市"}, "city__anyang": {"message": "安阳市"}, "city__baicheng": {"message": "白城市"}, "city__baise": {"message": "百色市"}, "city__baisha": {"message": "白沙黎族自治县"}, "city__baishan": {"message": "白山市"}, "city__baiyin": {"message": "白银市"}, "city__baoding": {"message": "保定市"}, "city__baoji": {"message": "宝鸡市"}, "city__baoshan": {"message": "保山市"}, "city__baoting": {"message": "保亭黎族苗族自治县"}, "city__baotou": {"message": "包头市"}, "city__bayannur": {"message": "巴彦淖尔市"}, "city__bayinguoleng": {"message": "巴音郭楞蒙古自治州"}, "city__bazhong": {"message": "巴中市"}, "city__beihai": {"message": "北海市"}, "city__bengbu": {"message": "蚌埠市"}, "city__benxi": {"message": "本溪市"}, "city__bijie": {"message": "毕节市"}, "city__binzhou": {"message": "滨州市"}, "city__bortala": {"message": "博尔塔拉蒙古自治州"}, "city__bozhou": {"message": "亳州市"}, "city__cangzhou": {"message": "沧州市"}, "city__chamdo": {"message": "昌都市"}, "city__changchun": {"message": "长春市"}, "city__changde": {"message": "常德市"}, "city__changhua": {"message": "彰化县"}, "city__changji": {"message": "昌吉回族自治州"}, "city__changjiang": {"message": "昌江黎族自治县"}, "city__changsha": {"message": "长沙市"}, "city__changzhi": {"message": "长治市"}, "city__changzhou": {"message": "常州市"}, "city__chaohu": {"message": "巢湖市"}, "city__chaoyang": {"message": "朝阳市"}, "city__chaozhou": {"message": "潮州市"}, "city__chengde": {"message": "承德市"}, "city__chengdu": {"message": "成都市"}, "city__chengmai": {"message": "澄迈县"}, "city__chenzhou": {"message": "郴州市"}, "city__chiayi": {"message": "嘉义市"}, "city__chifeng": {"message": "赤峰市"}, "city__chizhou": {"message": "池州市"}, "city__chongzuo": {"message": "崇左市"}, "city__chuxiong": {"message": "楚雄彝族自治州"}, "city__chuzhou": {"message": "滁州市"}, "city__dali": {"message": "大理白族自治州"}, "city__dalian": {"message": "大连市"}, "city__dandong": {"message": "丹东市"}, "city__danzhou": {"message": "儋州市"}, "city__daqing": {"message": "大庆市"}, "city__datong": {"message": "大同市"}, "city__daxinganling": {"message": "大兴安岭"}, "city__dazhou": {"message": "达州市"}, "city__dehong": {"message": "德宏傣族景颇族自治州"}, "city__deyang": {"message": "德阳市"}, "city__dezhou": {"message": "德州市"}, "city__dingan": {"message": "定安县"}, "city__dingxi": {"message": "定西市"}, "city__diqing": {"message": "迪庆藏族自治州"}, "city__dongfang": {"message": "东方市"}, "city__dongguan": {"message": "东莞市"}, "city__dongying": {"message": "东营市"}, "city__enshi": {"message": "恩施土家族苗族自治州"}, "city__ezhou": {"message": "鄂州市"}, "city__fangchenggang": {"message": "防城港市"}, "city__foshan": {"message": "佛山市"}, "city__fushun": {"message": "抚顺市"}, "city__fuxin": {"message": "阜新市"}, "city__fuyang": {"message": "阜阳市"}, "city__fuzhou": {"message": "抚州市"}, "city__gannan": {"message": "甘南藏族自治州"}, "city__ganzhou": {"message": "赣州市"}, "city__ganzi": {"message": "甘孜藏族自治州"}, "city__guangan": {"message": "广安市"}, "city__guangyuan": {"message": "广元市"}, "city__guangzhou": {"message": "广州市"}, "city__guigang": {"message": "贵港市"}, "city__guilin": {"message": "桂林市"}, "city__guiyang": {"message": "贵阳市"}, "city__guolok": {"message": "果洛藏族自治州"}, "city__guyuan": {"message": "固原市"}, "city__haibei": {"message": "海北藏族自治州"}, "city__haidong": {"message": "海东市"}, "city__haikou": {"message": "海口市"}, "city__hainan": {"message": "海南藏族自治州"}, "city__haixi": {"message": "海西蒙古族藏族自治州"}, "city__hami": {"message": "哈密市"}, "city__handan": {"message": "邯郸市"}, "city__hangzhou": {"message": "杭州市"}, "city__hanzhong": {"message": "汉中市"}, "city__harbin": {"message": "哈尔滨市"}, "city__hebi": {"message": "鹤壁市"}, "city__hechi": {"message": "河池市"}, "city__hefei": {"message": "合肥市"}, "city__hegang": {"message": "鹤岗市"}, "city__heihe": {"message": "黑河市"}, "city__hengshui": {"message": "衡水市"}, "city__hengyang": {"message": "衡阳市"}, "city__heyuan": {"message": "河源市"}, "city__heze": {"message": "菏泽市"}, "city__hezhou": {"message": "贺州市"}, "city__hohhot": {"message": "呼和浩特市"}, "city__honghe": {"message": "红河哈尼族彝族自治州"}, "city__hongkongisland": {"message": "香港岛"}, "city__hotan": {"message": "和田市"}, "city__hsinchu": {"message": "新竹县"}, "city__huaian": {"message": "淮安市"}, "city__huaibei": {"message": "淮北市"}, "city__huaihua": {"message": "怀化市"}, "city__huaisouth": {"message": "淮南市"}, "city__hualien": {"message": "花莲县"}, "city__huanggang": {"message": "黄冈市"}, "city__huangnan": {"message": "黄南藏族自治州"}, "city__huangshan": {"message": "黄山市"}, "city__huangshi": {"message": "黄石市"}, "city__huizhou": {"message": "惠州市"}, "city__huludao": {"message": "葫芦岛市"}, "city__hulunbuir": {"message": "呼伦贝尔市"}, "city__huzhou": {"message": "湖州市"}, "city__jiamusi": {"message": "佳木斯市"}, "city__jian": {"message": "吉安市"}, "city__jiangmen": {"message": "江门市"}, "city__jiaozuo": {"message": "焦作市"}, "city__jiaxing": {"message": "嘉兴市"}, "city__jiayuguan": {"message": "嘉峪关市"}, "city__jieyang": {"message": "揭阳市"}, "city__jilin": {"message": "吉林市"}, "city__jinan": {"message": "济南市"}, "city__jinchang": {"message": "金昌市"}, "city__jincheng": {"message": "晋城市"}, "city__jingdezhen": {"message": "景德镇市"}, "city__jingmen": {"message": "荆门市"}, "city__jingzhou": {"message": "荆州市"}, "city__jinhua": {"message": "金华市"}, "city__jining": {"message": "济宁市"}, "city__jinzhong": {"message": "晋中市"}, "city__jinzhou": {"message": "锦州市"}, "city__jiujiang": {"message": "九江市"}, "city__jiuquan": {"message": "酒泉市"}, "city__jixi": {"message": "鸡西市"}, "city__kaifeng": {"message": "开封市"}, "city__kaohsiung": {"message": "高雄市"}, "city__karamay": {"message": "克拉玛依市"}, "city__kashgar": {"message": "喀什市"}, "city__keelung": {"message": "基隆市"}, "city__kinmen": {"message": "金门县"}, "city__kizilsu": {"message": "克孜勒苏柯尔克孜自治州"}, "city__kowloon": {"message": "九龙"}, "city__kunming": {"message": "昆明市"}, "city__laibin": {"message": "来宾市"}, "city__laiwu": {"message": "莱芜市"}, "city__langfang": {"message": "廊坊市"}, "city__lanzhou": {"message": "兰州市"}, "city__ledong": {"message": "乐东黎族自治县"}, "city__leshan": {"message": "乐山市"}, "city__lhasa": {"message": "拉萨市"}, "city__liangshan": {"message": "凉山彝族自治州"}, "city__lianyungang": {"message": "连云港市"}, "city__liaocheng": {"message": "聊城市"}, "city__liaoyang": {"message": "辽阳市"}, "city__liaoyuan": {"message": "辽源市"}, "city__lienchiang": {"message": "连江县"}, "city__lijiang": {"message": "丽江市"}, "city__lincang": {"message": "临沧市"}, "city__linfen": {"message": "临汾市"}, "city__lingao": {"message": "临高县"}, "city__lingshui": {"message": "陵水黎族自治县"}, "city__linxia": {"message": "临夏回族自治州"}, "city__linyi": {"message": "临沂市"}, "city__lishui": {"message": "丽水市"}, "city__liupanshui": {"message": "六盘水市"}, "city__liuzhou": {"message": "柳州市"}, "city__longnan": {"message": "陇南市"}, "city__longyan": {"message": "龙岩市"}, "city__loudi": {"message": "娄底市"}, "city__luan": {"message": "六安市"}, "city__luohe": {"message": "漯河市"}, "city__luoyang": {"message": "洛阳市"}, "city__luzhou": {"message": "泸州市"}, "city__lüliang": {"message": "吕梁市"}, "city__maanshan": {"message": "马鞍山市"}, "city__macauoutlyingislands": {"message": "澳门离岛"}, "city__macaupeninsula": {"message": "澳门半岛"}, "city__maoming": {"message": "茂名市"}, "city__meishan": {"message": "眉山市"}, "city__meizhou": {"message": "梅州市"}, "city__mianyang": {"message": "绵阳市"}, "city__miaoli": {"message": "苗栗县"}, "city__mudanjiang": {"message": "牡丹江市"}, "city__nagqu": {"message": "那曲"}, "city__nanchang": {"message": "南昌市"}, "city__nanchong": {"message": "南充市"}, "city__nanjing": {"message": "南京市"}, "city__nanning": {"message": "南宁市"}, "city__nanping": {"message": "南平市"}, "city__nantong": {"message": "南通市"}, "city__nantou": {"message": "南投县"}, "city__nanyang": {"message": "南阳市"}, "city__neijiang": {"message": "内江市"}, "city__newterritories": {"message": "新界"}, "city__ngari": {"message": "阿里"}, "city__ningbo": {"message": "宁波市"}, "city__ningde": {"message": "宁德市"}, "city__nujiang": {"message": "怒江傈傈族自治州"}, "city__nyingchi": {"message": "林芝市"}, "city__ordos": {"message": "鄂尔多斯市"}, "city__panjin": {"message": "盘锦市"}, "city__panzhihua": {"message": "攀枝花市"}, "city__penghu": {"message": "澎湖县"}, "city__pingdingshan": {"message": "平顶山市"}, "city__pingliang": {"message": "平凉市"}, "city__pingtung": {"message": "屏东县"}, "city__pingxiang": {"message": "萍乡市"}, "city__puer": {"message": "普洱市"}, "city__putian": {"message": "莆田市"}, "city__puyang": {"message": "濮阳市"}, "city__qiandongnan": {"message": "黔东南苗族侗族自治州"}, "city__qianjiang": {"message": "潜江市"}, "city__qiannan": {"message": "黔南布依族苗族自治州"}, "city__qianxinan": {"message": "黔西南布依族苗族自治州"}, "city__qingdao": {"message": "青岛市"}, "city__qingyang": {"message": "庆阳市"}, "city__qingyuan": {"message": "清远市"}, "city__qinhuangdao": {"message": "秦皇岛市"}, "city__qinzhou": {"message": "钦州市"}, "city__qionghai": {"message": "琼海市"}, "city__qiongzhong": {"message": "琼中黎族苗族自治县"}, "city__qiqihar": {"message": "齐齐哈尔市"}, "city__qitaihe": {"message": "七台河市"}, "city__quanzhou": {"message": "泉州市"}, "city__qujing": {"message": "曲靖市"}, "city__quzhou": {"message": "衢州市"}, "city__rizhao": {"message": "日照市"}, "city__sanmenxia": {"message": "三门峡市"}, "city__sanming": {"message": "三明市"}, "city__sanya": {"message": "三亚市"}, "city__shangluo": {"message": "商洛市"}, "city__shangqiu": {"message": "商丘市"}, "city__shangrao": {"message": "上饶市"}, "city__shannan": {"message": "山南市"}, "city__shantou": {"message": "汕头市"}, "city__shanwei": {"message": "汕尾市"}, "city__shaoguan": {"message": "韶关市"}, "city__shaoxing": {"message": "绍兴市"}, "city__shaoyang": {"message": "邵阳市"}, "city__shennongjiaforestarea": {"message": "神农架林区"}, "city__shenyang": {"message": "沈阳市"}, "city__shenzhen": {"message": "深圳市"}, "city__shigatse": {"message": "日喀则市"}, "city__shihezi": {"message": "石河子市"}, "city__shijiazhuang": {"message": "石家庄市"}, "city__shiyan": {"message": "十堰市"}, "city__shizuishan": {"message": "石嘴山市"}, "city__shuangyashan": {"message": "双鸭山市"}, "city__shuozhou": {"message": "朔州市"}, "city__siping": {"message": "四平市"}, "city__songyuan": {"message": "松原市"}, "city__suihua": {"message": "绥化市"}, "city__suining": {"message": "遂宁市"}, "city__suizhou": {"message": "随州市"}, "city__suqian": {"message": "宿迁市"}, "city__suzhou": {"message": "宿州市"}, "city__tacheng": {"message": "塔城"}, "city__taian": {"message": "泰安市"}, "city__taichung": {"message": "台中市"}, "city__tainan": {"message": "台南市"}, "city__taipei": {"message": "台北市"}, "city__taitung": {"message": "台东县"}, "city__taiyuan": {"message": "太原市"}, "city__taizhou": {"message": "泰州市"}, "city__tangshan": {"message": "唐山市"}, "city__taoyuan": {"message": "桃园市"}, "city__tianmen": {"message": "天门市"}, "city__tianshui": {"message": "天水市"}, "city__tieling": {"message": "铁岭市"}, "city__tongchuan": {"message": "铜川市"}, "city__tonghua": {"message": "通化市"}, "city__tongliao": {"message": "通辽市"}, "city__tongling": {"message": "铜陵市"}, "city__tongren": {"message": "铜仁市"}, "city__tumushuke": {"message": "图木舒克市"}, "city__tunchang": {"message": "屯昌县"}, "city__turpan": {"message": "吐鲁番市"}, "city__ulanqab": {"message": "乌兰察布盟"}, "city__urumqi": {"message": "乌鲁木齐市"}, "city__wanning": {"message": "万宁市"}, "city__weifang": {"message": "潍坊市"}, "city__weihai": {"message": "威海市"}, "city__weinan": {"message": "渭南市"}, "city__wenchang": {"message": "文昌市"}, "city__wenshan": {"message": "文山壮族苗族自治州"}, "city__wenzhou": {"message": "温州市"}, "city__wuhai": {"message": "乌海市"}, "city__wuhan": {"message": "武汉市"}, "city__wuhu": {"message": "芜湖市"}, "city__wujiaqu": {"message": "五家渠市"}, "city__wuwei": {"message": "武威市"}, "city__wuxi": {"message": "无锡市"}, "city__wuzhishan": {"message": "五指山市"}, "city__wuzhong": {"message": "吴忠市"}, "city__wuzhou": {"message": "梧州市"}, "city__xiamen": {"message": "厦门市"}, "city__xian": {"message": "西安市"}, "city__xiangfan": {"message": "襄樊市"}, "city__xiangtan": {"message": "湘潭市"}, "city__xiangxi": {"message": "湘西土家族苗族自治州"}, "city__xianning": {"message": "咸宁市"}, "city__xiantao": {"message": "仙桃市"}, "city__xianyang": {"message": "咸阳市"}, "city__xiaogan": {"message": "孝感市"}, "city__xilingol": {"message": "锡林郭勒盟"}, "city__xinganleague": {"message": "兴安盟"}, "city__xingtai": {"message": "邢台市"}, "city__xining": {"message": "西宁市"}, "city__xinxiang": {"message": "新乡市"}, "city__xinyang": {"message": "信阳市"}, "city__xinyu": {"message": "新余市"}, "city__xinzhou": {"message": "忻州市"}, "city__xishuangbanna": {"message": "西双版纳傣族自治州"}, "city__xuancheng": {"message": "宣城市"}, "city__xuchang": {"message": "许昌市"}, "city__xuzhou": {"message": "徐州市"}, "city__yaan": {"message": "雅安市"}, "city__yanan": {"message": "延安市"}, "city__yanbian": {"message": "延边朝鲜族自治州"}, "city__yancheng": {"message": "盐城市"}, "city__yangjiang": {"message": "阳江市"}, "city__yangquan": {"message": "阳泉市"}, "city__yangzhou": {"message": "扬州市"}, "city__yantai": {"message": "烟台市"}, "city__yibin": {"message": "宜宾市"}, "city__yichang": {"message": "宜昌市"}, "city__yichun": {"message": "宜春市"}, "city__yilan": {"message": "宜兰县"}, "city__yili": {"message": "伊犁哈萨克自治州"}, "city__yinchuan": {"message": "银川市"}, "city__yingkou": {"message": "营口市"}, "city__yingtan": {"message": "鹰潭市"}, "city__yiwu": {"message": "义乌市"}, "city__yiyang": {"message": "益阳市"}, "city__yongzhou": {"message": "永州市"}, "city__yueyang": {"message": "岳阳市"}, "city__yulin": {"message": "榆林市"}, "city__yuncheng": {"message": "运城市"}, "city__yunfu": {"message": "云浮市"}, "city__yunlin": {"message": "云林县"}, "city__yushu": {"message": "玉树藏族自治州"}, "city__yuxi": {"message": "玉溪市"}, "city__zaozhuang": {"message": "枣庄市"}, "city__zhangjiajie": {"message": "张家界市"}, "city__zhangjiakou": {"message": "张家口市"}, "city__zhangye": {"message": "张掖市"}, "city__zhangzhou": {"message": "漳州市"}, "city__zhanjiang": {"message": "湛江市"}, "city__zhaoqing": {"message": "肇庆市"}, "city__zhaotong": {"message": "昭通市"}, "city__zhengzhou": {"message": "郑州市"}, "city__zhenjiang": {"message": "镇江市"}, "city__zhongshan": {"message": "中山市"}, "city__zhongwei": {"message": "中卫市"}, "city__zhoukou": {"message": "周口市"}, "city__zhoushan": {"message": "舟山市"}, "city__zhuhai": {"message": "珠海市"}, "city__zhumadian": {"message": "驻马店市"}, "city__zhuzhou": {"message": "株洲市"}, "city__zibo": {"message": "淄博市"}, "city__zigong": {"message": "自贡市"}, "city__ziyang": {"message": "资阳市"}, "city__zunyi": {"message": "遵义市"}, "click_copy_product_info": {"message": "再点击复制产品信息"}, "commmon_txt_expired": {"message": "已过期"}, "common__date_range_12m": {"message": "一年"}, "common__date_range_1m": {"message": "一个月"}, "common__date_range_1w": {"message": "一个星期"}, "common__date_range_2w": {"message": "两个星期"}, "common__date_range_3m": {"message": "三个月"}, "common__date_range_3w": {"message": "三个星期"}, "common__date_range_6m": {"message": "半年"}, "common_btn_cancel": {"message": "取消"}, "common_btn_close": {"message": "关闭"}, "common_btn_save": {"message": "保存"}, "common_btn_setting": {"message": "设置"}, "common_email": {"message": "电邮"}, "common_error_msg_no_data": {"message": "无数据"}, "common_error_msg_no_result": {"message": "抱歉，未找到结果。"}, "common_favorites": {"message": "收藏夹"}, "common_feedback": {"message": "反馈"}, "common_help": {"message": "帮助"}, "common_loading": {"message": "载入中"}, "common_login": {"message": "登录"}, "common_logout": {"message": "登出"}, "common_no": {"message": "否"}, "common_powered_by_aliprice": {"message": "由AliPrice.com提供技术支持"}, "common_setting": {"message": "设置"}, "common_sign_up": {"message": "注册"}, "common_system_upgrading_title": {"message": "系统升级"}, "common_system_upgrading_txt": {"message": "请稍后再试"}, "common_txt__currency": {"message": "货币"}, "common_txt__video_tutorial": {"message": "视频教程"}, "common_txt_ago_time": {"message": "$time$日前", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "全部"}, "common_txt_analysis": {"message": "卖家"}, "common_txt_basically_used": {"message": "几乎没用过"}, "common_txt_biaoti_link": {"message": "标题+链接"}, "common_txt_biaoti_link_dian_pu": {"message": "标题+链接+店铺名"}, "common_txt_blacklist": {"message": "黑名单"}, "common_txt_cancel": {"message": "退出"}, "common_txt_category": {"message": "类别"}, "common_txt_chakan": {"message": "查看"}, "common_txt_colors": {"message": "颜色"}, "common_txt_confirm": {"message": "确定"}, "common_txt_copied": {"message": "已复制"}, "common_txt_copy": {"message": "复制"}, "common_txt_copy_link": {"message": "复制链接"}, "common_txt_copy_title": {"message": "复制标题"}, "common_txt_copy_title__link": {"message": "复制标题+链接"}, "common_txt_day": {"message": "天"}, "common_txt_day__short": {"message": "天"}, "common_txt_delete": {"message": "删除"}, "common_txt_dian_pu_link": {"message": "复制店铺名+链接"}, "common_txt_download": {"message": "下载"}, "common_txt_downloaded": {"message": "下载"}, "common_txt_export_as_csv": {"message": "导出Excel"}, "common_txt_export_as_txt": {"message": "导出文本Txt"}, "common_txt_fail": {"message": "失败"}, "common_txt_format": {"message": "格式"}, "common_txt_get": {"message": "获取"}, "common_txt_incert_selection": {"message": "反转选择"}, "common_txt_install": {"message": "安装"}, "common_txt_load_failed": {"message": "加载失败"}, "common_txt_month": {"message": "月"}, "common_txt_more": {"message": "更多"}, "common_txt_new_unused": {"message": "全新、未使用"}, "common_txt_next": {"message": "下一页"}, "common_txt_no_limit": {"message": "不限"}, "common_txt_no_noticeable": {"message": "没有明显的划痕或污垢"}, "common_txt_on_sale": {"message": "在售"}, "common_txt_opt_in_out": {"message": "开/关"}, "common_txt_order": {"message": "订单"}, "common_txt_others": {"message": "其他"}, "common_txt_overall_poor_condition": {"message": "总体状况不佳"}, "common_txt_patterns": {"message": "款式"}, "common_txt_platform": {"message": "平台"}, "common_txt_please_select": {"message": "请选择"}, "common_txt_prev": {"message": "上一页"}, "common_txt_price": {"message": "价格"}, "common_txt_privacy_policy": {"message": "隐私政策"}, "common_txt_product_condition": {"message": "产品状况"}, "common_txt_rating": {"message": "评分"}, "common_txt_ratings": {"message": "评分人数"}, "common_txt_reload": {"message": "重新加载"}, "common_txt_reset": {"message": "重置"}, "common_txt_retail": {"message": "零售"}, "common_txt_review": {"message": "晒图"}, "common_txt_sale": {"message": "在售"}, "common_txt_same": {"message": "同款"}, "common_txt_scratches_and_dirt": {"message": "有划痕和污垢"}, "common_txt_search_title": {"message": "搜标题"}, "common_txt_select_all": {"message": "全选"}, "common_txt_selected": {"message": "已选中"}, "common_txt_share": {"message": "分享"}, "common_txt_sold": {"message": "已售"}, "common_txt_sold_out": {"message": "已售"}, "common_txt_some_scratches": {"message": "有一些划痕和污垢"}, "common_txt_sort_by": {"message": "排序方式"}, "common_txt_state": {"message": "状态"}, "common_txt_success": {"message": "成功"}, "common_txt_sys_err": {"message": "系统错误"}, "common_txt_today": {"message": "今天"}, "common_txt_total": {"message": "全部"}, "common_txt_unselect_all": {"message": "反选"}, "common_txt_upload_image": {"message": "上传图片"}, "common_txt_visit": {"message": "访问"}, "common_txt_whitelist": {"message": "白名单"}, "common_txt_wholesale": {"message": "批发"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "年"}, "common_yes": {"message": "是"}, "compare_tool_btn_clear_all": {"message": "全部清除"}, "compare_tool_btn_compare": {"message": "对比"}, "compare_tool_btn_contact": {"message": "联系"}, "configure_notifiactions": {"message": "设置通知"}, "contact_us": {"message": "联系我们"}, "context_menu_screenshot_search": {"message": "截图搜索同款"}, "context_menus_aliprice_search_by_image": {"message": "在AliPrice上用图片搜同款"}, "context_menus_goote_trans": {"message": "翻译网页/显示原文"}, "context_menus_search_by_image": {"message": "在$storeName$上以图搜同款", "placeholders": {"storeName": {"content": "$1", "example": "AliPrice"}}}, "context_menus_search_by_image_capture": {"message": "截图去$storeName$搜同款", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "截图翻译"}, "converter_modal_amount_placeholder": {"message": "在此输入金额"}, "converter_modal_btn_convert": {"message": "换算"}, "converter_modal_exchange_rate_source": {"message": "数据来源于$boc$外汇牌价 更新时间: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "汇率换算"}, "converter_modal_search_placeholder": {"message": "搜索货币"}, "copy_all_contact_us_notice": {"message": "暂不支持此网站，请联系我们"}, "copy_product_info": {"message": "复制产品信息"}, "copy_suggest_search_kw": {"message": "复制下拉词"}, "country__han_gou": {"message": "韩国"}, "country__ri_ben": {"message": "日本"}, "country__yue_nan": {"message": "越南"}, "currency_convert__custom": {"message": "自定义汇率"}, "currency_convert__sync_server": {"message": "同步服务器"}, "dang_ri_fa_huo": {"message": "当日发货"}, "dao_chu_quan_dian_shang_pin": {"message": "导出全店商品"}, "dao_chu_wei_CSV": {"message": "导出为CSV"}, "dao_chu_zi_duan": {"message": "导出字段"}, "delivery_address": {"message": "发货地址"}, "delivery_company": {"message": "配送公司"}, "di_zhi": {"message": "地址"}, "dian_ji_cha_xun": {"message": "点击查询"}, "dian_pu_ID": {"message": "店铺ID"}, "dian_pu_di_zhi": {"message": "店铺地址"}, "dian_pu_lian_jie": {"message": "店铺链接"}, "dian_pu_ming": {"message": "店铺名"}, "dian_pu_ming_cheng": {"message": "店铺名称"}, "dian_pu_shang_pin_zong_hsu": {"message": "店铺商品总数$num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "店铺信息"}, "ding_zai_zuo_ce": {"message": "钉在左侧"}, "disable_old_version_tips_disable_btn_title": {"message": "禁用旧版本"}, "download_image__SKU_variant_images": {"message": "SKU 属性图"}, "download_image__assume": {"message": "例如有product1.jpg、product2.gif 2张图片\n\nimg_{$no$} 会重命名为 img_01.jpg、img_02.gif;\n\n{$group$}_{$no$} 会重命名为 主图_01.jpg、主图_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "批量下载"}, "download_image__combined_image": {"message": "详情拼接长图"}, "download_image__continue_downloading": {"message": "继续下载"}, "download_image__description_images": {"message": "描述图"}, "download_image__download_combined_image": {"message": "下载详情拼接长图"}, "download_image__download_zip": {"message": "下载为压缩包"}, "download_image__enlarge_check": {"message": "仅支持 JPEG、JPG、GIF 和 PNG 格式的图片，单张图片最大尺寸: 1600 * 1600"}, "download_image__enlarge_image": {"message": "高清放大"}, "download_image__export": {"message": "导出链接"}, "download_image__height": {"message": "高度"}, "download_image__ignore_videos": {"message": "视频不能导出，已为你忽略视频"}, "download_image__img_translate": {"message": "图片翻译"}, "download_image__main_image": {"message": "主图"}, "download_image__multi_folder": {"message": "多文件夹"}, "download_image__name": {"message": "下载图片"}, "download_image__notice_content": {"message": "请不要在浏览器下载设置中选中“下载前询问每个文件的保存位置\"!!!否则会弹出非常多的对话框。"}, "download_image__notice_ignore": {"message": "不再提示此信息"}, "download_image__order_number": {"message": "{$no$} 序号; {$group$} 分组名; {$date$} 时间戳", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "概述"}, "download_image__prompt_download_zip": {"message": "图片数量过多，建议下载为压缩包。"}, "download_image__rename": {"message": "重命名"}, "download_image__rule": {"message": "命名规则"}, "download_image__single_folder": {"message": "单文件夹"}, "download_image__sku_image": {"message": "SKU 图片"}, "download_image__video": {"message": "视频"}, "download_image__width": {"message": "宽度"}, "download_reviews__download_images": {"message": "下载评论图片"}, "download_reviews__dropdown_title": {"message": "下载评论图片"}, "download_reviews__export_csv": {"message": "导出评论CSV"}, "download_reviews__no_images": {"message": "0张图片可下载"}, "download_reviews__no_reviews": {"message": "没有评论可以下载！"}, "download_reviews__notice": {"message": "提示："}, "download_reviews__notice__chrome_settings": {"message": "设置Chrome浏览器下载前询问每个文件的保存位置，设为“关闭”"}, "download_reviews__notice__wait": {"message": "根据评论数量，等待时间可能较长"}, "download_reviews__pages_list__all": {"message": "全部"}, "download_reviews__pages_list__page": {"message": "前$page$页", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "选择范围"}, "export_shopping_cart__csv_filed__details_url": {"message": "产品链接"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "回显SKU链接"}, "export_shopping_cart__csv_filed__images": {"message": "图片链接"}, "export_shopping_cart__csv_filed__quantity": {"message": "数量"}, "export_shopping_cart__csv_filed__sale_price": {"message": "价格"}, "export_shopping_cart__csv_filed__specs": {"message": "规格"}, "export_shopping_cart__csv_filed__store_name": {"message": "店铺名称"}, "export_shopping_cart__csv_filed__store_url": {"message": "商店链接"}, "export_shopping_cart__csv_filed__title": {"message": "产品名称"}, "export_shopping_cart__export_btn": {"message": "导出"}, "export_shopping_cart__export_empty": {"message": "请选择产品!"}, "fa_huo_shi_jian": {"message": "发货时间"}, "favorite_add_email": {"message": "添加邮箱"}, "favorite_add_favorites": {"message": "添加收藏"}, "favorite_added": {"message": "已收藏"}, "favorite_btn_add": {"message": "降价提醒"}, "favorite_btn_notify": {"message": "追踪价格"}, "favorite_cate_name_all": {"message": "全部商品"}, "favorite_current_price": {"message": "当前价格"}, "favorite_due_date": {"message": "截止日期"}, "favorite_enable_notification": {"message": "请开启邮件通知"}, "favorite_expired": {"message": "过期"}, "favorite_go_to_enable": {"message": "去开启"}, "favorite_msg_add_success": {"message": "已加入收藏"}, "favorite_msg_del_success": {"message": "已从收藏中移除"}, "favorite_msg_failure": {"message": "失败！刷新页面并重试"}, "favorite_please_add_email": {"message": "请添加邮箱"}, "favorite_price_drop": {"message": "降价"}, "favorite_price_rise": {"message": "涨价"}, "favorite_price_untracked": {"message": "价格未追踪"}, "favorite_saved_price": {"message": "收藏价格"}, "favorite_stop_tracking": {"message": "取消追踪"}, "favorite_sub_email_address": {"message": "订阅邮箱"}, "favorite_tracking_period": {"message": "追踪时长"}, "favorite_tracking_prices": {"message": "追踪价格"}, "favorite_verify_email": {"message": "验证邮箱"}, "favorites_list_remove_prompt_msg": {"message": "确定要删除它吗？"}, "favorites_update_button": {"message": "立即更新价格"}, "fen_lei": {"message": "分类"}, "fen_xia_yan_xuan": {"message": "分销严选"}, "find_similar": {"message": "找同款"}, "first_ali_price_date": {"message": "首次被AliPrice爬虫抓取到的日期"}, "fooview_coupons_modal_no_data": {"message": "暂无优惠券"}, "fooview_coupons_modal_title": {"message": "优惠"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "价格 < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "或价格 > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "截止日"}, "fooview_favorites_error_msg_no_favorites": {"message": "点亮小心心将产品加入收藏夹，以收到降价提醒"}, "fooview_favorites_filter_latest": {"message": "最新的"}, "fooview_favorites_filter_price_drop": {"message": "降价"}, "fooview_favorites_filter_price_up": {"message": "涨价"}, "fooview_favorites_modal_title": {"message": "收藏夹"}, "fooview_favorites_modal_title_title": {"message": "去AliPrice收藏夹"}, "fooview_favorites_track_price": {"message": "以跟踪价格"}, "fooview_price_history_app_price": {"message": "APP端价格:"}, "fooview_price_history_title": {"message": "价格历史"}, "fooview_product_list_feedback": {"message": "评分"}, "fooview_product_list_orders": {"message": "订单"}, "fooview_product_list_price": {"message": "价格"}, "fooview_reviews_error_msg_no_review": {"message": "我们未找到对此产品的评论。"}, "fooview_reviews_filter_buyer_reviews": {"message": "买家秀"}, "fooview_reviews_modal_title": {"message": "评论"}, "fooview_same_product_choose_category": {"message": "选择类目"}, "fooview_same_product_filter_feedback": {"message": "评分"}, "fooview_same_product_filter_orders": {"message": "订单"}, "fooview_same_product_filter_price": {"message": "价格"}, "fooview_same_product_filter_rating": {"message": "评分"}, "fooview_same_product_modal_title": {"message": "搜索同款产品"}, "fooview_same_product_search_by_image": {"message": "以图搜同款"}, "fooview_seller_analysis_modal_title": {"message": "卖家分析"}, "for_12_months": {"message": "全年"}, "for_12_months_list_pro": {"message": "12个月"}, "for_12_months_nei": {"message": "12个月内"}, "for_1_months": {"message": "1个月"}, "for_1_months_nei": {"message": "1个月内"}, "for_3_months": {"message": "3个月"}, "for_3_months_nei": {"message": "3个月内"}, "for_6_months": {"message": "6个月"}, "for_6_months_nei": {"message": "6个月内"}, "for_9_months": {"message": "9个月"}, "for_9_months_nei": {"message": "9个月内"}, "fu_gou_lv": {"message": "复购率"}, "gao_liang_bu_tong_dian": {"message": "高亮不同点"}, "gao_liang_guang_gao_chan_pin": {"message": "高亮广告产品"}, "geng_duo_xin_xi": {"message": "更多信息"}, "geng_xin_shi_jian": {"message": "更新时间"}, "get_store_products_fail_tip": {"message": "点击确定，前往通过验证以确保正常访问"}, "gong_x_kuan_shang_pin": {"message": "共$amount$款商品", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "供应商"}, "gong_ying_shang_ID": {"message": "供应商ID"}, "gong_ying_shang_deng_ji": {"message": "供应商等级"}, "gong_ying_shang_nian_zhan": {"message": "供应商年长"}, "gong_ying_shang_xin_xi": {"message": "供应商信息"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "供应商主页链接"}, "green_rocket": {"message": "绿色火箭"}, "grey_rocket": {"message": "灰色火箭"}, "gu_ji_shou_jia": {"message": "估计售价"}, "guan_jian_zi": {"message": "关键字"}, "guang_gao_chan_pin": {"message": "广告产品"}, "guang_gao_zhan_bi": {"message": "广告占比"}, "guo_ji_wu_liu_yun_fei": {"message": "国际物流运费"}, "guo_lv_tiao_jian": {"message": "过滤条件"}, "hao_ping_lv": {"message": "好评率"}, "highest_price": {"message": "最高价"}, "historical_trend": {"message": "历史趋势"}, "how_to_screenshot": {"message": "按住鼠标左键选择区域，点击鼠标右键或Esc键退出截屏"}, "howt_it_works": {"message": "怎么用"}, "hui_fu_lv": {"message": "回复率"}, "hui_tou_lv": {"message": "回头率"}, "inquire_freightFee": {"message": "运费查询"}, "inquire_freightFee_Yuan": {"message": "运费/元起"}, "inquire_freightFee_province": {"message": "省份"}, "inquire_freightFee_the": {"message": "运费为$num$ ,表示该地区包邮", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "是否为广告"}, "isf": {"message": "请输入关键词"}, "jia_ge": {"message": "价格"}, "jia_ge_dan_wei": {"message": "商品的价格单位"}, "jia_ge_qu_shi": {"message": "趋势"}, "jia_zai_n_ge_shang_pin": {"message": "加载 $num$ 个商品", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "近30天销量占比"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "近30天销售额占比"}, "jin_30d_xiao_liang": {"message": "近30天销量"}, "jin_30d_xiao_liang__desc": {"message": "最近30天的销量"}, "jin_30d_xiao_shou_e": {"message": "30天销售额"}, "jin_30d_xiao_shou_e__desc": {"message": "最近30天的销售额"}, "jin_90_tian_mai_jia_shu": {"message": "近90天买家数"}, "jin_90_tian_xiao_shou_liang": {"message": "近90天销售量"}, "jing_xuan_huo_yuan": {"message": "精选货源"}, "jing_ying_mo_shi": {"message": "经营模式"}, "jing_ying_mo_shi__gong_chang": {"message": "生产厂家"}, "jiu_fen_jie_jue": {"message": "纠纷解决"}, "jiu_fen_jie_jue__desc": {"message": "商家店铺维权纠纷的计入情况"}, "jiu_fen_lv": {"message": "纠纷率"}, "jiu_fen_lv__desc": {"message": "近30天投诉完结且判定为卖家责任或双方责任的订单比例"}, "kai_dian_ri_qi": {"message": "开店日期"}, "keywords": {"message": "关键词"}, "kua_jin_Select_pan_huo": {"message": "跨境Select货盘"}, "last15_days": {"message": "近15天"}, "last180_days": {"message": "近180天"}, "last30_days": {"message": "近30天"}, "last360_days": {"message": "近360天"}, "last45_days": {"message": "近45天"}, "last60_days": {"message": "近60天"}, "last7_days": {"message": "近7天"}, "last90_days": {"message": "近90天"}, "last_30d_sales": {"message": "最近30天成交量"}, "lei_ji": {"message": "累计"}, "lei_ji_xiao_liang": {"message": "累计销量"}, "lei_ji_xiao_liang__desc": {"message": "产品上架后的所有销量"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "最近30天的累计销量, 从高到低排序"}, "lian_xi_fang_shi": {"message": "联系方式"}, "list_time": {"message": "上架日期"}, "load_more": {"message": "加载更多"}, "login_to_aliprice": {"message": "登录AliPrice"}, "long_link": {"message": "长链接"}, "lowest_price": {"message": "最低价"}, "mai_jia_shu": {"message": "卖家数"}, "mao_li_lv": {"message": "毛利率"}, "mobile_view__dkxbqy": {"message": "打开新标签页"}, "mobile_view__sjdxq": {"message": "手机端详情"}, "mobile_view__sjdxqy": {"message": "手机端详情页"}, "mobile_view__smck": {"message": "扫码查看"}, "mobile_view__smckms": {"message": "请使用相机或APP扫描查看"}, "modified_failed": {"message": "修改失败"}, "modified_successfully": {"message": "修改成功"}, "nav_btn_favorites": {"message": "我的收藏"}, "nav_btn_package": {"message": "包裹"}, "nav_btn_product_info": {"message": "关于产品"}, "nav_btn_viewed": {"message": "浏览历史"}, "no_suggest_search_kw": {"message": "没有下拉词"}, "none": {"message": "无"}, "normal_link": {"message": "普通链接"}, "notice": {"message": "提示"}, "number_reviews": {"message": "评论数"}, "only_show_num": {"message": "商品总数: $allnum$, 已隐藏: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "去掉未勾选"}, "open": {"message": "打开"}, "open_links": {"message": "打开链接"}, "options_page_tab_check_links": {"message": "检查连结"}, "options_page_tab_gernal": {"message": "通用"}, "options_page_tab_notifications": {"message": "通知"}, "options_page_tab_others": {"message": "其他"}, "options_page_tab_sbi": {"message": "以图搜同款"}, "options_page_tab_shortcuts": {"message": "快捷方式"}, "options_page_tab_shortcuts_title": {"message": "快捷方式字体大小"}, "options_page_tab_similar_products": {"message": "同款产品"}, "orange_rocket": {"message": "橙色火箭"}, "order_list_open_links_tip": {"message": "即将打开多个商品详情页"}, "order_list_sku_show_title": {"message": "支持回显sku(内测中)"}, "orders_last30_days": {"message": "最近30天的订单数"}, "pTutorial_favorites_block1_desc1": {"message": "加入追踪价格的产品会显示在这里"}, "pTutorial_favorites_block1_title": {"message": "收藏夹"}, "pTutorial_popup_block1_desc1": {"message": "绿色标签表示有降价产品，数字表示新降价产品数量"}, "pTutorial_popup_block1_title": {"message": "快捷入口和收藏夹"}, "pTutorial_price_history_block1_desc1": {"message": "单击“追踪价格”，将产品添加到收藏夹。 价格下降后，您会收到通知"}, "pTutorial_price_history_block1_title": {"message": "追踪价格"}, "pTutorial_reviews_block1_desc1": {"message": "来自Itao的买家点评和速卖通买家的实拍晒图"}, "pTutorial_reviews_block1_title": {"message": "点评"}, "pTutorial_reviews_block2_desc1": {"message": "下单前看下其他买家的评论总是很有帮助的"}, "pTutorial_same_products_block1_desc1": {"message": "您可以对比同款以做出最佳选择"}, "pTutorial_same_products_block1_desc2": {"message": "点击“更多”以使用“以图像同款”"}, "pTutorial_same_products_block1_title": {"message": "同款产品"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "将产品图片拖到此处，然后选择产品所属类目"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "以图搜同款"}, "pTutorial_seller_analysis_block1_desc1": {"message": "卖家的好评率，评价得分以及卖家的开店时间"}, "pTutorial_seller_analysis_block1_title": {"message": "卖家分析"}, "pTutorial_seller_analysis_block2_desc2": {"message": "卖家评分基于3个指数：产品描述相符度，发货速度和客服能力"}, "pTutorial_seller_analysis_block3_desc3": {"message": "我们使用3种不同的颜色和图标表示不同的卖家可信任等级"}, "page_count": {"message": "页数"}, "pai_chu": {"message": "排除"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "排除中国香港不宜销售商品"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "排除日本不宜销售商品"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "排除韩国不宜销售商品"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "排除哈萨克斯坦不宜销售商品"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "排除中国澳门不宜销售商品"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "排除东欧不宜销售商品"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "排除沙特阿拉伯不宜销售商品"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "排除中国台湾不宜销售商品"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "排除美国不宜销售商品"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "排除越南不宜销售商品"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "排除不宜销售"}, "payable_price_formula": {"message": "价格+运费+优惠"}, "pdd_check_retail_btn_txt": {"message": "查看零售"}, "pdd_pifa_to_retail_btn_txt": {"message": "零售购买"}, "pdp_copy_fail": {"message": "复制失败！"}, "pdp_copy_success": {"message": "复制成功！"}, "pdp_share_modal_subtitle": {"message": "分享截图，他/她会看到你的选择。"}, "pdp_share_modal_title": {"message": "分享你的选择"}, "pdp_share_screenshot": {"message": "分享截图"}, "pei_song": {"message": "配送"}, "pin_lei": {"message": "品类"}, "pin_zhi_ti_yan": {"message": "品质体验"}, "pin_zhi_ti_yan__desc": {"message": "商家店铺的品质退款率"}, "pin_zhi_tui_kuan_lv": {"message": "品质退款率"}, "pin_zhi_tui_kuan_lv__desc": {"message": "近30天仅退款和退款退货完结的订单比例"}, "ping_fen": {"message": "评分"}, "ping_jun_fa_huo_su_du": {"message": "平均发货速度"}, "pkgInfo_hide": {"message": "显示物流：开/关"}, "pkgInfo_no_trace": {"message": "无物流信息"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17网"}, "platform_name__51taoyang": {"message": "淘羊网"}, "platform_name__5ts": {"message": "童商网"}, "platform_name__91jf": {"message": "91家纺网"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "速卖通"}, "platform_name__amazon": {"message": "亚马逊"}, "platform_name__bao66": {"message": "包牛牛"}, "platform_name__chinagoods": {"message": "义乌小商品城"}, "platform_name__dhgate": {"message": "敦煌网"}, "platform_name__e3hui": {"message": "衣衫汇"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "杭州女装网"}, "platform_name__jd": {"message": "京东"}, "platform_name__juyi5": {"message": "聚衣网"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "拼多多"}, "platform_name__pinduoduo_pifa": {"message": "拼多多批发"}, "platform_name__shopee": {"message": "虾皮"}, "platform_name__sjxzw": {"message": "四季星座网"}, "platform_name__sooxie": {"message": "爱搜鞋网"}, "platform_name__taobao": {"message": "淘宝"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "搜款网"}, "platform_name__walmart": {"message": "沃尔玛"}, "platform_name__wsy": {"message": "网商园"}, "platform_name__xingfujie": {"message": "新款网"}, "platform_name__yiwugo": {"message": "义乌购"}, "platform_name__yunchepin": {"message": "云车品"}, "platform_name__zhaojiafang": {"message": "找家纺"}, "popup_go_to_home": {"message": "首页"}, "popup_go_to_platform": {"message": "去$name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "我要购买..."}, "popup_track_package_btn_track": {"message": "查询"}, "popup_track_package_desc": {"message": "多合一包裹查快递神器"}, "popup_track_package_search_placeholder": {"message": "快递号"}, "popup_translate_search_placeholder": {"message": "在 $searchOn$ 上翻译和搜索", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "价格历史"}, "price_history_chart_tip_ae": {"message": "提示：订单数是从上架至今的累计订单数"}, "price_history_chart_tip_coupang": {"message": "提示： Coupang官方会删除作弊订单的订单数"}, "price_history_inm_1688_l1": {"message": "请先安装"}, "price_history_inm_1688_l2": {"message": "1688购物助手"}, "price_history_panel_lowest_price": {"message": "最低价:"}, "price_history_panel_tab_price_tracking": {"message": "价格历史"}, "price_history_panel_tab_seller_analysis": {"message": "卖家分析"}, "price_history_pro_modal_title": {"message": "价格历史和订单历史"}, "privacy_consent__btn_agree": {"message": "同意"}, "privacy_consent__btn_disable_all": {"message": "不接受"}, "privacy_consent__btn_enable_all": {"message": "全部启用"}, "privacy_consent__btn_uninstall": {"message": "移除"}, "privacy_consent__desc_privacy": {"message": "请注意，如果没有数据或Cookie，某些功能将关闭，因为这些功能需要数据或Cookie，但您仍可以使用其他功能。"}, "privacy_consent__desc_privacy_L1": {"message": "如果不允许收集必要数据或添加cookie，插件将无法正常工作。"}, "privacy_consent__desc_privacy_L2": {"message": "请同意我们收集必要数据或添加cookie。否则，可以移除插件。"}, "privacy_consent__item_cookies_desc": {"message": "Cookie，我们仅获取您购物时Cookie中的货币数据用以显示价格历史。"}, "privacy_consent__item_cookies_title": {"message": "所需的<PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1.在浏览器中添加Cookie以匿名标识您的计算机或设备。"}, "privacy_consent__item_functional_desc_L2": {"message": "2.在插件中添加功能数据以启用此功能。"}, "privacy_consent__item_functional_title": {"message": "功能和分析Cookie"}, "privacy_consent__more_desc": {"message": "请注意，我们不会与其他公司共享您的个人数据，也没有广告公司会通过我们的服务收集数据。"}, "privacy_consent__options__btn__desc": {"message": "要使用所有功能，您需要开启它。"}, "privacy_consent__options__btn__label": {"message": "开启"}, "privacy_consent__options__desc_L1": {"message": "我们将收集以下可识别您个人身份的数据："}, "privacy_consent__options__desc_L2": {"message": "- Cookie，我们只会在您在线购物时显示Cookie中的货币数据，以显示产品价格历史。"}, "privacy_consent__options__desc_L3": {"message": "- 并在浏览器中添加Cookie以匿名识别您的计算机或设备。"}, "privacy_consent__options__desc_L4": {"message": "- 其他匿名数据使本插件更加方便使用。"}, "privacy_consent__options__desc_L5": {"message": "请注意，我们不会与其他公司共享您的个人数据，也没有广告公司通过我们的服务收集数据。"}, "privacy_consent__privacy_preferences": {"message": "隐私偏好"}, "privacy_consent__read_more": {"message": "阅读更多>>"}, "privacy_consent__title_privacy": {"message": "隐私"}, "product_info": {"message": "产品信息"}, "product_recommend__name": {"message": "同款产品"}, "product_research": {"message": "产品查询"}, "product_sub__email_desc": {"message": "价格警报邮箱"}, "product_sub__email_edit": {"message": "编辑"}, "product_sub__email_not_verified": {"message": "请验证邮箱"}, "product_sub__email_required": {"message": "请提供邮箱"}, "product_sub__form_countdown": {"message": "$seconds$ 秒后自动关闭", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "添加提醒失败！"}, "product_sub__form_input_price": {"message": "输入价格"}, "product_sub__form_item_country": {"message": "国家"}, "product_sub__form_item_current_price": {"message": "当前价"}, "product_sub__form_item_duration": {"message": "跟踪"}, "product_sub__form_item_higher_price": {"message": "或价格 >"}, "product_sub__form_item_invalid_higher_price": {"message": "价格必须大于 $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "价格必须低于 $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "当价格 <"}, "product_sub__form_submit": {"message": "提交"}, "product_sub__form_success": {"message": "添加提醒成功！"}, "product_sub__high_price_notify": {"message": "涨价时通知我"}, "product_sub__low_price_notify": {"message": "降价时通知我"}, "product_sub__modal_title": {"message": "订阅价格变动提醒"}, "province__an_hui": {"message": "安徽省"}, "province__ao_men": {"message": "澳门"}, "province__bei_jing": {"message": "北京市"}, "province__chong_qing": {"message": "重庆市"}, "province__fu_jian": {"message": "福建省"}, "province__gan_su": {"message": "甘肃省"}, "province__guang_dong": {"message": "广东省"}, "province__guang_xi": {"message": "广西壮族自治区"}, "province__gui_zhou": {"message": "贵州省"}, "province__hai_nan": {"message": "海南省"}, "province__he_bei": {"message": "河北省"}, "province__he_nan": {"message": "河南省"}, "province__hei_long_jiang": {"message": "黑龙江省"}, "province__hu_bei": {"message": "湖北省"}, "province__hu_nan": {"message": "湖南省"}, "province__ji_lin": {"message": "吉林省"}, "province__jiang_su": {"message": "江苏省"}, "province__jiang_xi": {"message": "江西省"}, "province__liao_ning": {"message": "辽宁省"}, "province__nei_meng_gu": {"message": "内蒙古自治区"}, "province__ning_xia": {"message": "宁夏回族自治区"}, "province__qing_hai": {"message": "青海省"}, "province__shan_dong": {"message": "山东省"}, "province__shan_xi": {"message": "陕西省"}, "province__shang_hai": {"message": "上海市"}, "province__si_chuan": {"message": "四川省"}, "province__tai_wan": {"message": "台湾"}, "province__tian_jin": {"message": "天津市"}, "province__xi_zhang": {"message": "西藏自治区"}, "province__xiang_gang": {"message": "香港"}, "province__xin_jiang": {"message": "新疆维吾尔自治区"}, "province__yun_nan": {"message": "云南省"}, "province__zhe_jiang": {"message": "浙江省"}, "purple_rocket": {"message": "紫色火箭"}, "qi_ding_liang": {"message": "起订量"}, "qi_ding_liang_qi_ding_jia": {"message": "起订量&起订价"}, "qi_ye_mian_ji": {"message": "企业面积"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "请至少选择一个产品"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "请至少选择一个字段"}, "qu_deng_lu": {"message": "去登录"}, "quan_guo_yan_xuan": {"message": "全球严选"}, "recommendation_popup_banner_btn_install": {"message": "安装"}, "recommendation_popup_banner_desc": {"message": "显示3/6个月内的价格历史和降价通知"}, "region__all": {"message": "所有地区"}, "region__hai_wai": {"message": "海外"}, "region__hua_bei_qu": {"message": "华北区"}, "region__hua_dong_qu": {"message": "华东区"}, "region__hua_nan_qu": {"message": "华南区"}, "region__hua_zhong_qu": {"message": "华中区"}, "region__jiang_zhe_lu": {"message": "江浙沪"}, "remove_web_limits": {"message": "解除右键限制"}, "ren_zheng_gong_chang": {"message": "认证工厂"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "认证供应商年长"}, "required_to_aliprice_login": {"message": "需要登录AliPrice"}, "revenue_last30_days": {"message": "30天销售额"}, "review_counts": {"message": "收藏人数"}, "rocket": {"message": "小火箭"}, "ru_zhu_nian_xian": {"message": "入驻年限"}, "sales_amount_last30_days": {"message": "最近30天的销售额"}, "sales_last30_days": {"message": "近30天销量"}, "sbi_alibaba_cate__accessories": {"message": "配饰"}, "sbi_alibaba_cate__aqfk": {"message": "安全防护"}, "sbi_alibaba_cate__bags_cases": {"message": "箱包"}, "sbi_alibaba_cate__beauty": {"message": "美妆"}, "sbi_alibaba_cate__beverage": {"message": "瓶饮"}, "sbi_alibaba_cate__bgwh": {"message": "办公文化"}, "sbi_alibaba_cate__bz": {"message": "包装"}, "sbi_alibaba_cate__ccyj": {"message": "餐厨饮具"}, "sbi_alibaba_cate__clothes": {"message": "服饰"}, "sbi_alibaba_cate__cmgd": {"message": "传媒广电"}, "sbi_alibaba_cate__coat_jacket": {"message": "上衣"}, "sbi_alibaba_cate__consumer_electronics": {"message": "数码"}, "sbi_alibaba_cate__cryp": {"message": "成人用品"}, "sbi_alibaba_cate__csyp": {"message": "床上用品"}, "sbi_alibaba_cate__cwyy": {"message": "宠物园艺"}, "sbi_alibaba_cate__cysx": {"message": "餐饮生鲜"}, "sbi_alibaba_cate__dgdq": {"message": "电工电气"}, "sbi_alibaba_cate__dl": {"message": "代理"}, "sbi_alibaba_cate__dress_suits": {"message": "裙装"}, "sbi_alibaba_cate__dszm": {"message": "灯饰照明"}, "sbi_alibaba_cate__dzqj": {"message": "电子器件"}, "sbi_alibaba_cate__essb": {"message": "二手设备"}, "sbi_alibaba_cate__food": {"message": "零食"}, "sbi_alibaba_cate__fspj": {"message": "服饰配件"}, "sbi_alibaba_cate__furniture": {"message": "家具"}, "sbi_alibaba_cate__fzpg": {"message": "纺织皮革"}, "sbi_alibaba_cate__ghjq": {"message": "个护家清"}, "sbi_alibaba_cate__gt": {"message": "钢铁"}, "sbi_alibaba_cate__gyp": {"message": "工艺品"}, "sbi_alibaba_cate__hb": {"message": "环保"}, "sbi_alibaba_cate__hfcz": {"message": "护肤彩妆"}, "sbi_alibaba_cate__hg": {"message": "化工"}, "sbi_alibaba_cate__jg": {"message": "加工"}, "sbi_alibaba_cate__jianccai": {"message": "建材"}, "sbi_alibaba_cate__jichuang": {"message": "机床"}, "sbi_alibaba_cate__jjry": {"message": "居家日用"}, "sbi_alibaba_cate__jtys": {"message": "交通运输"}, "sbi_alibaba_cate__jxsb": {"message": "机械设备"}, "sbi_alibaba_cate__jxwj": {"message": "机械五金"}, "sbi_alibaba_cate__jydq": {"message": "家用电器"}, "sbi_alibaba_cate__jzjc": {"message": "家装建材"}, "sbi_alibaba_cate__jzjf": {"message": "家纺家饰"}, "sbi_alibaba_cate__mj": {"message": "毛巾"}, "sbi_alibaba_cate__myyp": {"message": "母婴用品"}, "sbi_alibaba_cate__nanz": {"message": "男装"}, "sbi_alibaba_cate__nvz": {"message": "女装"}, "sbi_alibaba_cate__ny": {"message": "能源"}, "sbi_alibaba_cate__others": {"message": "其他"}, "sbi_alibaba_cate__qcyp": {"message": "汽车用品"}, "sbi_alibaba_cate__qmpj": {"message": "汽摩配件"}, "sbi_alibaba_cate__shoes": {"message": "鞋"}, "sbi_alibaba_cate__smdn": {"message": "数码电脑"}, "sbi_alibaba_cate__snqj": {"message": "收纳清洁"}, "sbi_alibaba_cate__spjs": {"message": "食品酒水"}, "sbi_alibaba_cate__swfw": {"message": "商务服务"}, "sbi_alibaba_cate__toys_hobbies": {"message": "玩具"}, "sbi_alibaba_cate__trousers_skirt": {"message": "下装"}, "sbi_alibaba_cate__txcp": {"message": "通信产品"}, "sbi_alibaba_cate__tz": {"message": "童装"}, "sbi_alibaba_cate__underwear": {"message": "内衣"}, "sbi_alibaba_cate__wjgj": {"message": "五金工具"}, "sbi_alibaba_cate__xgpi": {"message": "箱包皮具"}, "sbi_alibaba_cate__xmhz": {"message": "项目合作"}, "sbi_alibaba_cate__xs": {"message": "橡塑"}, "sbi_alibaba_cate__ydfs": {"message": "运动服饰"}, "sbi_alibaba_cate__ydhw": {"message": "运动户外"}, "sbi_alibaba_cate__yjkc": {"message": "冶金矿产"}, "sbi_alibaba_cate__yqyb": {"message": "仪器仪表"}, "sbi_alibaba_cate__ys": {"message": "印刷"}, "sbi_alibaba_cate__yyby": {"message": "医药保养"}, "sbi_alibaba_cn_kj_90mjs": {"message": "近90天买家数"}, "sbi_alibaba_cn_kj_90xsl": {"message": "近90天销售量"}, "sbi_alibaba_cn_kj_gjsj": {"message": "估计售价"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "国际物流运费"}, "sbi_alibaba_cn_kj_gjyf": {"message": "国际运费"}, "sbi_alibaba_cn_kj_gssj": {"message": "估算售价"}, "sbi_alibaba_cn_kj_lr": {"message": "利润"}, "sbi_alibaba_cn_kj_lrgs": {"message": "利润=估算售价x利润率"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "平均发货速度"}, "sbi_alibaba_cn_kj_qtfy": {"message": "其他费用"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "其他费用=估算售价x其他费用比例"}, "sbi_alibaba_cn_kj_spjg": {"message": "商品价格"}, "sbi_alibaba_cn_kj_spzl": {"message": "商品重量"}, "sbi_alibaba_cn_kj_szd": {"message": "所在地"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "个"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "件"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "克"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "人"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "台"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "套"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "天"}, "sbi_alibaba_cn_kj_zwbj": {"message": "暂无报价"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "价格"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "可售"}, "sbi_aliprice_alibaba_cn__moren": {"message": "默认"}, "sbi_aliprice_alibaba_cn__queding": {"message": "确定"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "销量"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "家具"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "零食"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "美妆"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "內衣"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "配饰"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "瓶饮"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "其他"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "裙装"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "上衣"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "数码"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "玩具"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "箱包"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "下装"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "鞋子"}, "sbi_aliprice_cate__apparel": {"message": "服饰"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "汽车和摩托车"}, "sbi_aliprice_cate__beauty_health": {"message": "美容与健康"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "手机与通讯"}, "sbi_aliprice_cate__computer_office": {"message": "电脑与办公"}, "sbi_aliprice_cate__consumer_electronics": {"message": "消费类电子产品"}, "sbi_aliprice_cate__education_office_supplies": {"message": "教育和办公用品"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "电子元件及用品"}, "sbi_aliprice_cate__furniture": {"message": "家具类"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "假发"}, "sbi_aliprice_cate__home_garden": {"message": "家居与园艺"}, "sbi_aliprice_cate__home_improvement": {"message": "家居装修"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "珠宝及配饰"}, "sbi_aliprice_cate__luggage_bags": {"message": "行李箱包"}, "sbi_aliprice_cate__mother_kids": {"message": "母婴用品"}, "sbi_aliprice_cate__novelty_special_use": {"message": "新奇产品和特殊用品"}, "sbi_aliprice_cate__security_protection": {"message": "安全防护"}, "sbi_aliprice_cate__shoes": {"message": "鞋类"}, "sbi_aliprice_cate__sports_entertainment": {"message": "体育与娱乐"}, "sbi_aliprice_cate__toys_hobbies": {"message": "玩具与爱好"}, "sbi_aliprice_cate__watches": {"message": "手表"}, "sbi_aliprice_cate__weddings_events": {"message": "婚礼与活动"}, "sbi_btn_capture_txt": {"message": "截图"}, "sbi_btn_source_now_txt": {"message": "立刻搜索"}, "sbi_button__chat_with_me": {"message": "与我聊天"}, "sbi_button__contact_supplier": {"message": "联系"}, "sbi_button__hide_on_this_site": {"message": "不要在这个网站上显示"}, "sbi_button__open_settings": {"message": "设置图片搜索"}, "sbi_capture_shortcut_tip": {"message": "或按键盘上的“回车”键"}, "sbi_capturing_tip": {"message": "正在截取"}, "sbi_composed_rating_45": {"message": "4.5星-5.0星"}, "sbi_crop_and_search": {"message": "搜索"}, "sbi_crop_start": {"message": "使用截图"}, "sbi_err_captcha_action": {"message": "验证"}, "sbi_err_captcha_for_alibaba_cn": {"message": "1688要求您验证,请手动上传图片验证。(查看$video_tutorial$或尝试清除Cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "异常流量，请验证"}, "sbi_err_captcha_for_taobao": {"message": "淘宝要求您验证，请手动上传图片并搜索进行验证。 这个错误源于“淘宝图片搜索”新的验证政策，我们建议您在淘宝$feedback$投诉过于频繁的验证。", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "反馈"}, "sbi_err_captcha_msg": {"message": "$platform$需要您前往网站操作一次图片搜索或完成安全验证，以解除搜索限制", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "检查是否是最新版"}, "sbi_err_cookie_btn_clear": {"message": "清除Cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "试下清除1688上的cookie？（需要重新登录）"}, "sbi_err_desperate_feature_pdd": {"message": "该图搜功能已迁移至拼多多以图搜同款插件"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "如何提高图搜成功率？"}, "sbi_err_img_undersize": {"message": "图片 > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "登录$loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "登录"}, "sbi_err_low_version": {"message": "安装最新版本($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "下载"}, "sbi_err_need_help": {"message": "需要帮助"}, "sbi_err_network": {"message": "网络不通，请确保您能访问网站"}, "sbi_err_not_low_version": {"message": "已安装了最新版（$latestVersion$）", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "再试一次"}, "sbi_err_try_again_action": {"message": "再试一次"}, "sbi_err_visit_and_try": {"message": "再试一次，或访问$website$重试", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "访问下$siteName$首页", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "加载失败，请刷新页面后重试。"}, "sbi_kuajing_filter_area": {"message": "地区"}, "sbi_kuajing_filter_au": {"message": "澳大利亚"}, "sbi_kuajing_filter_btn_confirm": {"message": "确定"}, "sbi_kuajing_filter_de": {"message": "德国"}, "sbi_kuajing_filter_destination_country": {"message": "目的地国家"}, "sbi_kuajing_filter_es": {"message": "西班牙"}, "sbi_kuajing_filter_estimate": {"message": "估算"}, "sbi_kuajing_filter_estimate_price": {"message": "售价估算"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "估算售价公式 = （商品价格 + 国际物流运费）/（1 - 利润率 - 其他费用比例）"}, "sbi_kuajing_filter_fr": {"message": "法国"}, "sbi_kuajing_filter_kw_placeholder": {"message": "输入关键词对标题进行匹配"}, "sbi_kuajing_filter_logistics": {"message": "物流模板"}, "sbi_kuajing_filter_logistics_china_post": {"message": "中国邮政挂号小包"}, "sbi_kuajing_filter_logistics_discount": {"message": "物流折扣"}, "sbi_kuajing_filter_logistics_epacket": {"message": "国际E邮宝"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "国际物流运费 = （重量 x 目的地国家运费单价 + 挂号费）x（1 - 折扣）"}, "sbi_kuajing_filter_others_fee": {"message": "其他费用"}, "sbi_kuajing_filter_profit_percent": {"message": "利润率"}, "sbi_kuajing_filter_prop": {"message": "属性"}, "sbi_kuajing_filter_ru": {"message": "俄罗斯"}, "sbi_kuajing_filter_total": {"message": "匹配$count$个同款货源", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "英国"}, "sbi_kuajing_filter_usa": {"message": "美国"}, "sbi_login_punish_title__pdd_pifa": {"message": "拼多多商家版"}, "sbi_msg_no_result": {"message": "未找到结果,请先登录$loginSite$或换张图试试", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "暂时不支持Safari, 请使用$supportPage$。", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome浏览器及其扩展"}, "sbi_msg_no_result_reinstall_l1": {"message": "未找到结果,请先登录$loginSite$或换张图试试,或重新安装$latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "最新版本", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "调整区域搜索"}, "sbi_shipping_": {"message": "当日发货"}, "sbi_specify_category": {"message": "指定类别："}, "sbi_start_crop": {"message": "调整截图"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688跨境"}, "sbi_tutorial_btn_more": {"message": "更多用法"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "查找淘宝优惠券"}, "sbi_txt__empty_retry": {"message": "抱歉，未找到结果，请重试。"}, "sbi_txt__min_order": {"message": "最小订单量"}, "sbi_visiting": {"message": "正在浏览"}, "sbi_yiwugo__jiagexiangtan": {"message": "价格联系商家"}, "sbi_yiwugo__qigou": {"message": "$num$个起购", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "星"}, "searchByImage_screenshot": {"message": "一键截图"}, "searchByImage_search": {"message": "一键搜索同款"}, "searchByImage_size_type": {"message": "文件大小不能高于$num$MB，仅支持$type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "分析图片"}, "search_by_image_progress_searching": {"message": "搜索产品"}, "search_by_image_progress_sending": {"message": "正在发送图片"}, "search_by_image_response_rate": {"message": "答复率：$responseRate$的买家联系供应商后，在$responseInHour$时内收到了答复。", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "按关键词搜索"}, "select_country_language_modal_title_country": {"message": "国家"}, "select_country_language_modal_title_language": {"message": "语言"}, "select_country_region_modal_title": {"message": "选择国家/地区"}, "select_language_modal_title": {"message": "选择语言:"}, "select_shop": {"message": "选择店铺"}, "sellers_count": {"message": "当前页卖家数"}, "sellers_count_per_page": {"message": "当前页面的卖家数"}, "service_score": {"message": "综合服务评分"}, "set_shortcut_keys": {"message": "设置快捷键"}, "setting_logo_title": {"message": "购物助手"}, "setting_modal_options_position_title": {"message": "插件图标位置"}, "setting_modal_options_position_value_left": {"message": "左下角"}, "setting_modal_options_position_value_right": {"message": "右下角"}, "setting_modal_options_theme_title": {"message": "主题颜色"}, "setting_modal_options_theme_value_dark": {"message": "黑色"}, "setting_modal_options_theme_value_light": {"message": "白色"}, "setting_modal_title": {"message": "设置"}, "setting_options_country_title": {"message": "国家/地区"}, "setting_options_hover_zoom_desc": {"message": "光标悬停在图片上以放大图片"}, "setting_options_hover_zoom_title": {"message": "悬停放大"}, "setting_options_jd_coupon_desc": {"message": "找到优惠券"}, "setting_options_jd_coupon_title": {"message": "京东优惠券"}, "setting_options_language_title": {"message": "语言"}, "setting_options_price_drop_alert_desc": {"message": "当收藏夹中的产品价格下降时，您会收到降价推送提醒"}, "setting_options_price_drop_alert_title": {"message": "降价提醒"}, "setting_options_price_history_on_list_page_desc": {"message": "在产品搜索列表页显示价格历史"}, "setting_options_price_history_on_list_page_title": {"message": "价格历史(列表页)"}, "setting_options_price_history_on_produt_page_desc": {"message": "在产品详情页显示价格历史"}, "setting_options_price_history_on_produt_page_title": {"message": "价格历史(详情页)"}, "setting_options_sales_analysis_desc": {"message": "支持统计$platforms$产品列表页的价格、销量、卖家数和店铺销量占比等信息", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "销售统计分析"}, "setting_options_save_success_msg": {"message": "成功"}, "setting_options_tacking_price_title": {"message": "价格变动提醒"}, "setting_options_value_off": {"message": "关"}, "setting_options_value_on": {"message": "开"}, "setting_pkg_quick_view_desc": {"message": "支持：1688 & 淘宝"}, "setting_saved_message": {"message": "更改已成功保存"}, "setting_section_enable_platform_title": {"message": "主开关"}, "setting_section_setting_title": {"message": "设置"}, "setting_section_shortcuts_title": {"message": "快捷入口"}, "settings_aliprice_agent__desc": {"message": "显示在$platforms$产品详情页", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "代购"}, "settings_copy_link__desc": {"message": "显示在产品详情页"}, "settings_copy_link__title": {"message": "复制按钮和搜索标题"}, "settings_currency_desc__for_detail": {"message": "支持1688产品详情页"}, "settings_currency_desc__for_list": {"message": "图片搜索（包括1688/1688海外/淘宝）"}, "settings_currency_desc__for_sbi": {"message": "选中价格"}, "settings_currency_desc_display_for_list": {"message": "显示在图片搜索（包括1688/1688海外/淘宝）"}, "settings_currency_rate_desc": {"message": "汇率更新来自\"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "中国银行"}, "settings_download_images__desc": {"message": "支持下载$platforms$的图片", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "下载图像按钮"}, "settings_download_reviews__desc": {"message": "显示在$platforms$产品详情页。", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "下载评论图片"}, "settings_google_translate_desc": {"message": "右键单击以获取谷歌翻译条"}, "settings_google_translate_title": {"message": "网页翻译"}, "settings_historical_trend_desc": {"message": "显示在产品列表页的图片右下角"}, "settings_modal_btn_more": {"message": "更多设置"}, "settings_productInfo_desc": {"message": "在列表页显示产品的更多详细信息，开启后可能会加大电脑负荷，导致页面卡顿。如有影响建议关闭"}, "settings_product_recommend__desc": {"message": "\n\n\n显示在$platforms$产品详情页主图下方", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "\n\n产品推荐"}, "settings_research_desc": {"message": "在列表页查询产品的更多详细信息"}, "settings_sbi_add_to_list": {"message": "加入$listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "图搜结果缩略图"}, "settings_sbi_remove_from_list": {"message": "清除$listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "加入黑名单"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "调整入口位置"}, "settings_search_by_image_blacklist_desc": {"message": "不在黑名单中的网站上显示图标。"}, "settings_search_by_image_blacklist_title": {"message": "黑名单"}, "settings_search_by_image_bottom_left": {"message": "左下"}, "settings_search_by_image_bottom_right": {"message": "右下"}, "settings_search_by_image_clear_blacklist": {"message": "清除黑名单"}, "settings_search_by_image_detail_page_icon_title": {"message": "缩略图"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "变大"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "变小"}, "settings_search_by_image_display_button_desc": {"message": "一键按图搜同款"}, "settings_search_by_image_display_button_title": {"message": "图像上的图标"}, "settings_search_by_image_sourece_websites_desc": {"message": "在这些网站上搜索同款货源"}, "settings_search_by_image_sourece_websites_title": {"message": "图片搜索结果"}, "settings_search_by_image_top_left": {"message": "左上"}, "settings_search_by_image_top_right": {"message": "右上"}, "settings_search_keyword_on_x__desc": {"message": "在$platform$上搜索文字", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "选择文字时显示$platform$图标", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "试着在这些网站（最多5个）上搜索同款产品"}, "settings_similar_products_title": {"message": "搜索同款产品"}, "settings_toolbar_expand_title": {"message": "插件最小化"}, "settings_top_toolbar_desc": {"message": "页面顶部的搜索栏"}, "settings_top_toolbar_title": {"message": "搜索栏"}, "settings_translate_search_desc": {"message": "翻译成中文并搜索"}, "settings_translate_search_title": {"message": "多语言搜索"}, "settings_translator_contextmenu_title": {"message": "截图翻译"}, "settings_translator_title": {"message": "翻译"}, "shai_xuan_dao_chu": {"message": "筛选导出"}, "shai_xuan_zi_duan": {"message": "筛选字段"}, "shang_jia_shi_jian": {"message": "上架时间"}, "shang_pin_biao_ti": {"message": "商品标题"}, "shang_pin_dui_bi": {"message": "商品对比"}, "shang_pin_lian_jie": {"message": "商品链接"}, "shang_pin_xin_xi": {"message": "商品信息"}, "share_modal__content": {"message": "与你的朋友们分享"}, "share_modal__disable_for_while": {"message": "近日不显示"}, "share_modal__title": {"message": "你喜欢$extensionName$吗？", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "商品剩余的件数"}, "shi_fou_ke_ding_zhi": {"message": "是否可定制"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "是否认证供应商"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "是否认证供应商总数"}, "shi_fou_you_mao_yi_dan_bao": {"message": "是否有贸易担保"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "是否有贸易担保总数"}, "shipping_fee": {"message": "运费"}, "shop_followers": {"message": "店铺粉丝数"}, "shou_qi": {"message": "收起"}, "similar_products_warn_max_platforms": {"message": "最多5个"}, "sku_calc_price": {"message": "计算价格"}, "sku_calc_price_settings": {"message": "计算价格设置"}, "sku_formula": {"message": "公式"}, "sku_formula_desc": {"message": "公式说明"}, "sku_formula_desc_text": {"message": "支持复杂的数学公式，A表示原价，B表示运费\n<br/>\n支持括号()、加号+、减号-、乘号*、除号/\n<br/>\n示例：\n<br/>\n1. 实现原价的1.2倍，再加运费，公式为：A*1.2+B\n<br/>\n2. 实现原价加1元，再乘以1.2倍，公式为：(A+1)*1.2\n<br/>\n3. 实现原价加10元，再乘以1.2倍，再减3元，公式为：(A+10)*1.2-3\n"}, "sku_in_stock": {"message": "有库存"}, "sku_invalid_formula_format": {"message": "计算公式格式错误"}, "sku_inventory": {"message": "库存"}, "sku_link_copy_fail": {"message": "复制成功, 未选中sku规格与属性"}, "sku_link_copy_success": {"message": "复制成功, 已选中sku规格与属性"}, "sku_list": {"message": "SKU列表"}, "sku_min_qrder_qty": {"message": "起批量"}, "sku_name": {"message": "SKU名称"}, "sku_no": {"message": "序号"}, "sku_original_price": {"message": "原价"}, "sku_price": {"message": "SKU价格"}, "stop_track_time_label": {"message": "跟踪截止日期："}, "suo_zai_di_qu": {"message": "所在地区"}, "tab_pkg_quick_view": {"message": "监控物流信息"}, "tab_product_details_price_history": {"message": "价格历史"}, "tab_product_details_reviews": {"message": "晒图评论"}, "tab_product_details_seller_analysis": {"message": "卖家分析"}, "tab_product_details_similar_products": {"message": "同款产品"}, "total_days_listed_per_product": {"message": "上架天数总和 ÷ 产品数"}, "total_items": {"message": "商品总数"}, "total_price_per_product": {"message": "价格总和 ÷ 产品数"}, "total_rating_per_product": {"message": "评分总和 ÷ 产品数"}, "total_revenue": {"message": "总销售额"}, "total_revenue40_items": {"message": "当前页$amount$个商品的总销售额", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "总销量"}, "total_sales40_items": {"message": "当前页$amount$个商品的总销量", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "追踪时长：1年"}, "track_for_3_months": {"message": "追踪时长：3个月"}, "track_for_6_months": {"message": "追踪时长：6个月"}, "tracking_price_email_add_btn": {"message": "新增电邮"}, "tracking_price_email_edit_btn": {"message": "编辑电邮"}, "tracking_price_email_intro": {"message": "我们将通过电子邮件通知您。"}, "tracking_price_email_invalid": {"message": "请提供有效的电邮"}, "tracking_price_email_verified_desc": {"message": "现在您可以收到我们的降价提醒。"}, "tracking_price_email_verified_title": {"message": "验证成功"}, "tracking_price_email_verify_desc_line1": {"message": "我们已将验证链接发送到您的电子邮箱，"}, "tracking_price_email_verify_desc_line2": {"message": "请检查您的收件箱。"}, "tracking_price_email_verify_title": {"message": "验证电邮"}, "tracking_price_web_push_notification_intro": {"message": "在桌面端：AliPrice可以为您监视任何产品，并在价格变化后向您发送Web推送通知。"}, "tracking_price_web_push_notification_title": {"message": "Web推送通知"}, "translate_im__login_required": {"message": "由AliPrice提供翻译支持, 请登录$loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "已翻译并复制到剪贴板，但由于阿里旺旺的限制，需要您手动粘贴！"}, "translate_im__send": {"message": "翻译&发送"}, "translate_search": {"message": "翻译和搜索"}, "translation_originals_translated": {"message": "原文和中文"}, "translation_translated": {"message": "中文"}, "translator_btn_capture_txt": {"message": "翻译"}, "translator_language_auto_detect": {"message": "自动检测"}, "translator_language_detected": {"message": "自动检测"}, "translator_language_search_placeholder": {"message": "搜索语言"}, "try_again": {"message": "再试一次"}, "tu_pian_chi_cun": {"message": "图片尺寸:"}, "tu_pian_lian_jie": {"message": "图片链接"}, "tui_huan_ti_yan": {"message": "退换体验"}, "tui_huan_ti_yan__desc": {"message": "考核商家的售后指标"}, "tutorial__show_all": {"message": "所有功能"}, "tutorial_ae_popup_title": {"message": "固定扩展程序，打开速卖通详情页"}, "tutorial_aliexpress_reviews_analysis": {"message": "速卖通评论分析"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "支持USD KRW JPY HKD TWD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "运输至韩国/日本/中国内地"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688支持直购"}, "tutorial_auto_apply_coupon_title": {"message": "自动填写优惠券"}, "tutorial_btn_end": {"message": "结束"}, "tutorial_btn_example": {"message": "示例"}, "tutorial_btn_have_a_try": {"message": "好的，我试试"}, "tutorial_btn_next": {"message": "下一页"}, "tutorial_btn_see_more": {"message": "更多功能"}, "tutorial_compare_products": {"message": "对比同款"}, "tutorial_currency_convert_title": {"message": "汇率转换"}, "tutorial_export_shopping_cart": {"message": "导出CSV，支持淘宝和1688"}, "tutorial_export_shopping_cart_title": {"message": "导出购物车"}, "tutorial_price_history_pro": {"message": "显示在产品详情页上。\n支持Shopee，Lazada，Amazon和Ebay"}, "tutorial_price_history_pro_title": {"message": "全年价格历史和订单历史"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "截图搜索同款"}, "tutorial_translate_search": {"message": "翻译即搜索"}, "tutorial_translate_search_and_package_tracking": {"message": "翻译搜索和包裹跟踪"}, "unit_bao": {"message": "包"}, "unit_ben": {"message": "本"}, "unit_bi": {"message": "笔"}, "unit_chuang": {"message": "床"}, "unit_dai": {"message": "袋"}, "unit_dui": {"message": "对"}, "unit_fen": {"message": "份"}, "unit_ge": {"message": "个"}, "unit_he": {"message": "盒"}, "unit_jian": {"message": "件"}, "unit_li_fang_mi": {"message": "立方米"}, "unit_ping": {"message": "瓶"}, "unit_ping_fang_mi": {"message": "平方米"}, "unit_shuang": {"message": "双"}, "unit_tai": {"message": "台"}, "unit_ti": {"message": "提"}, "unit_tiao": {"message": "条"}, "unit_xiang": {"message": "箱"}, "unit_zhang": {"message": "张"}, "unit_zhi": {"message": "只"}, "verify_contact_support": {"message": "联系客服"}, "verify_human_verification": {"message": "人机验证"}, "verify_unusual_access": {"message": "您的访问异常"}, "view_history_clean_all": {"message": "全部清理"}, "view_history_clean_all_warring": {"message": "清除所有浏览记录？"}, "view_history_clean_all_warring_title": {"message": "警告"}, "view_history_viewd": {"message": "浏览记录"}, "website": {"message": "网站"}, "weight": {"message": "重量"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "无法获取到该数据"}, "wu_liu_shi_xiao": {"message": "物流时效"}, "wu_liu_shi_xiao__desc": {"message": "商家店铺的48小时揽收率和履约率"}, "xia_dan_jia": {"message": "下单价"}, "xian_xuan_ze_product_attributes": {"message": "先选择产品属性"}, "xiao_liang": {"message": "销量"}, "xiao_liang_zhan_bi": {"message": "销量占比"}, "xiao_shi": {"message": "$num$小时", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "销售额"}, "xiao_shou_e_zhan_bi": {"message": "销售额占比"}, "xuan_zhong_x_tiao_ji_lu": {"message": "选中$amount$条记录", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "严选"}, "yi_ding_zai_zuo_ce": {"message": "已钉住"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "已加载完所有商品"}, "yi_nian_xiao_liang": {"message": "年销量"}, "yi_nian_xiao_liang_zhan_bi": {"message": "年销量占比"}, "yi_nian_xiao_shou_e": {"message": "年销售额"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "年销售额占比"}, "yi_shua_xin": {"message": "已刷新"}, "yin_cang_xiang_tong_dian": {"message": "隐藏相同点"}, "you_xiao_liang": {"message": "有销量"}, "yu_ji_dao_da_shi_jian": {"message": "预计到达时间"}, "yuan_gong_ren_shu": {"message": "员工人数"}, "yue_cheng_jiao": {"message": "月成交"}, "yue_dai_xiao": {"message": "月代销"}, "yue_dai_xiao__desc": {"message": "最近30天的一件代发的销量"}, "yue_dai_xiao_pai_xu__desc": {"message": "最近30天的一件代发的销量, 从高到低排序"}, "yue_xiao_liang__desc": {"message": "最近30天的销量"}, "zhan_kai": {"message": "更多"}, "zhe_kou": {"message": "商品的折扣百分比"}, "zhi_chi_yi_jian_dai_fa": {"message": "支持一件代发"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "支持包邮(一件代发包邮)"}, "zhi_fu_ding_dan_shu": {"message": "支付订单数"}, "zhi_fu_ding_dan_shu__desc": {"message": "该商品近30天的订单数"}, "zhu_ce_xing_zhi": {"message": "注册性质"}, "zi_ding_yi_tiao_jian": {"message": "自定义条件"}, "zi_duan": {"message": "字段"}, "zi_ti_xiao_liang": {"message": "子体销量"}, "zong_he_fu_wu_fen": {"message": "综合服务分"}, "zong_he_fu_wu_fen__desc": {"message": "对卖家服务的综合评分"}, "zong_he_fu_wu_fen__short": {"message": "综合服务分"}, "zong_he_ti_yan_fen": {"message": "综合体验分"}, "zong_he_ti_yan_fen_3": {"message": "4星以下"}, "zong_he_ti_yan_fen_4": {"message": "4星-4.5星"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5星-5.0星"}, "zong_he_ti_yan_fen_5": {"message": "5星"}, "zong_ku_cun": {"message": "总库存"}, "zong_xiao_liang": {"message": "总销量"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "最近30天3分钟响应率"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "最近30天48H揽收率"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "最近30天48H履约率"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "最近30天交易记录"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "最近30天交易记录"}, "zui_jin_30D_jiu_fen_lv": {"message": "最近30天纠纷率"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "最近30天品质退款率"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "最近30天支付订单数"}}