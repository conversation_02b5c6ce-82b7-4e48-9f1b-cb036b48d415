{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Трансграничная горячая точка продаж"}, "1688_shi_li_ren_zheng": {"message": "1688 сертификат прочности"}, "1_jian_qi_pi": {"message": "МОК: 1"}, "1year_yi_shang": {"message": "Более 1 года"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Отправлено в течение 24 часов"}, "24H_lan_shou_lv": {"message": "Стоимость упаковки за 24 часа"}, "30D_shang_xin": {"message": "Ежемесячные новинки"}, "30d_sales": {"message": "$amount$ продано за 30 дней", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Ответ в течение 3 минут."}, "3Min_xiang_ying_lv__desc": {"message": "Доля эффективных ответов Wangwang на сообщения с запросами покупателей в течение 3 минут за последние 30 дней."}, "48H": {"message": "48 ч."}, "48H_fa_huo": {"message": "Отправлено в течение 48 часов"}, "48H_lan_shou_lv": {"message": "Стоимость упаковки за 48 часов"}, "48H_lan_shou_lv__desc": {"message": "Отношение количества принятых заказов в течение 48 часов к общему количеству заказов"}, "48H_lv_yue_lv": {"message": "48-часовая производительность"}, "48H_lv_yue_lv__desc": {"message": "Отношение количества забранных или доставленных заказов в течение 48 часов к общему количеству заказов"}, "72H": {"message": "72 ч."}, "7D_shang_xin": {"message": "Еженедельные новинки"}, "7D_wu_li_you": {"message": "7 дней без ухода"}, "ABS_title_text": {"message": "В этом листинге есть история бренда"}, "AC_title_text": {"message": "В этом листинге есть значок Amazon's Choice"}, "A_title_text": {"message": "В этом листинге есть страница с контентом A+"}, "BS_title_text": {"message": "Этот листинг ранжируется как $num$ Бестселлер в категории $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) означает, что этот листинг является частью мероприятия «7-дневная акция»"}, "NR_title_text": {"message": "Этот листинг ранжируется как $num$ Новинка в категории $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "В этом листинге есть видеореклама, тип рекламы PPC, которая обычно отображается в середине результатов поиска"}, "SB_title_text": {"message": "В этом листинге есть реклама бренда, тип рекламы PPC, которая обычно отображается вверху или внизу результатов поиска"}, "SP_title_text": {"message": "В этом листинге есть реклама спонсируемого продукта"}, "V_title_text": {"message": "В этом листинге есть видеопрезентация"}, "advanced_research": {"message": "Расширенные исследования"}, "agent_ds1688___my_order": {"message": "Мои заказы"}, "agent_ds1688__add_to_cart": {"message": "Зарубежная покупка"}, "agent_ds1688__cart": {"message": "Корзина"}, "agent_ds1688__desc": {"message": "Предоставляется 1688. Он поддерживает прямые закупки из-за рубежа, оплату в долларах и доставку на ваш транзитный склад в Китае."}, "agent_ds1688__freight": {"message": "Калькулятор стоимости доставки"}, "agent_ds1688__help": {"message": "Помощь"}, "agent_ds1688__packages": {"message": "Путевой лист"}, "agent_ds1688__profile": {"message": "Персональный центр"}, "agent_ds1688__warehouse": {"message": "Мой склад"}, "ai_comment_analysis_advantage": {"message": "Плюсы"}, "ai_comment_analysis_ai": {"message": "Ана<PERSON><PERSON><PERSON> отзывов AI"}, "ai_comment_analysis_available": {"message": "Доступно"}, "ai_comment_analysis_balance": {"message": "Недостаточно монет, пополните счет"}, "ai_comment_analysis_behavior": {"message": "Поведение"}, "ai_comment_analysis_characteristic": {"message": "Характеристики толпы"}, "ai_comment_analysis_comment": {"message": "У продукта недостаточно отзывов, чтобы сделать точные выводы, выберите продукт с большим количеством отзывов."}, "ai_comment_analysis_consume": {"message": "Предполагаемое потребление"}, "ai_comment_analysis_default": {"message": "Отзывы по умолчанию"}, "ai_comment_analysis_desire": {"message": "Ожидания клиентов"}, "ai_comment_analysis_disadvantage": {"message": "Мин<PERSON><PERSON>ы"}, "ai_comment_analysis_free": {"message": "Бесплатные попытки"}, "ai_comment_analysis_freeNum": {"message": "Будет использован 1 бесплатный кредит"}, "ai_comment_analysis_go_recharge": {"message": "Перейти к пополнению"}, "ai_comment_analysis_intelligence": {"message": "Интеллектуальный анализ отзывов"}, "ai_comment_analysis_location": {"message": "Местоположение"}, "ai_comment_analysis_motive": {"message": "Мотивация покупки"}, "ai_comment_analysis_network_error": {"message": "Ошибка сети, попробуйте еще раз"}, "ai_comment_analysis_normal": {"message": "Фотоотзывы"}, "ai_comment_analysis_number_reviews": {"message": "Количество отзывов: $num$, Расчетное потребление: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Общие комментарии"}, "ai_comment_analysis_percentage": {"message": "Процент"}, "ai_comment_analysis_problem": {"message": "Проблемы с оплатой"}, "ai_comment_analysis_reanalysis": {"message": "Провести повторный анализ"}, "ai_comment_analysis_reason": {"message": "Причина"}, "ai_comment_analysis_recharge": {"message": "Пополнить"}, "ai_comment_analysis_recharged": {"message": "Я пополнил счет"}, "ai_comment_analysis_retry": {"message": "Повторить попытку"}, "ai_comment_analysis_scene": {"message": "Сценарий использования"}, "ai_comment_analysis_start": {"message": "Начать анализ"}, "ai_comment_analysis_subject": {"message": "Темы"}, "ai_comment_analysis_time": {"message": "Время использования"}, "ai_comment_analysis_tool": {"message": "Инструмент ИИ"}, "ai_comment_analysis_user_portrait": {"message": "Профиль пользователя"}, "ai_comment_analysis_welcome": {"message": "Добро пожаловать в анализ отзывов AI"}, "ai_comment_analysis_year": {"message": "Комментарии за прошлый год"}, "ai_listing_Exclude_keywords": {"message": "Исключить ключевые слова"}, "ai_listing_Login_the_feature": {"message": "Для использования функции требуется вход"}, "ai_listing_aI_generation": {"message": "поколение ИИ"}, "ai_listing_add_automatic": {"message": "Автоматический"}, "ai_listing_add_dictionary_new": {"message": "Создать новую библиотеку"}, "ai_listing_add_enter_keywords": {"message": "Введите ключевые слова"}, "ai_listing_add_inputkey_selling": {"message": "Введите точку продажи и нажмите $key$, чтобы завершить добавление.", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Превышен лимит, до $amount$ баллов.", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Добавить ключевые слова"}, "ai_listing_add_manually": {"message": "Добавить вручную"}, "ai_listing_add_selling": {"message": "Добавьте точки продажи"}, "ai_listing_added_keywords": {"message": "Добавлены ключевые слова"}, "ai_listing_added_successfully": {"message": "Добавлено успешно"}, "ai_listing_addexcluded_keywords": {"message": "Введите исключенные ключевые слова и нажмите Enter, чтобы завершить добавление."}, "ai_listing_adding_selling": {"message": "Добавлены точки продажи"}, "ai_listing_addkeyword_enter": {"message": "Введите ключевые слова-атрибуты и нажмите Enter, чтобы завершить добавление."}, "ai_listing_ai_description": {"message": "Библиотека слов описания AI"}, "ai_listing_ai_dictionary": {"message": "Библиотека заголовочных слов AI"}, "ai_listing_ai_title": {"message": "AI-заголовок"}, "ai_listing_aidescription_repeated": {"message": "Имя библиотеки слов описания AI не может повторяться"}, "ai_listing_aititle_repeated": {"message": "Имя библиотеки слов заголовка AI не может повторяться."}, "ai_listing_data_comes_from": {"message": "Эти данные взяты из:"}, "ai_listing_deleted_successfully": {"message": "Удален успешно"}, "ai_listing_dictionary_name": {"message": "Название библиотеки"}, "ai_listing_edit_dictionary": {"message": "Изменить библиотеку..."}, "ai_listing_edit_word_library": {"message": "Редактировать библиотеку слов"}, "ai_listing_enter_keywords": {"message": "Введите ключевые слова и нажмите $key$, чтобы завершить добавление.", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Превышен лимит, максимальное количество ключевых слов: $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Исключенная библиотека слов"}, "ai_listing_generate_characters": {"message": "Генерация персонажей"}, "ai_listing_generation_platform": {"message": "Платформа генерации"}, "ai_listing_help_optimize": {"message": "Помогите мне оптимизировать название продукта, исходное название"}, "ai_listing_include_selling": {"message": "Другие аргументы в пользу продажи включали:"}, "ai_listing_included_keyword": {"message": "Включенные ключевые слова"}, "ai_listing_included_keywords": {"message": "Включенные ключевые слова"}, "ai_listing_input_selling": {"message": "Введите точку продажи"}, "ai_listing_input_selling_fit": {"message": "Введите точки продажи, соответствующие названию"}, "ai_listing_input_selling_please": {"message": "Пожалуйста, введите пункты продажи"}, "ai_listing_intelligently_title": {"message": "Введите необходимый контент выше, чтобы разумно сгенерировать заголовок."}, "ai_listing_keyword_product_title": {"message": "Ключевое слово в названии продукта"}, "ai_listing_keywords_repeated": {"message": "Ключевые слова не могут повторяться"}, "ai_listing_listed_selling_points": {"message": "Включенные пункты продажи"}, "ai_listing_long_title_1": {"message": "Содержит основную информацию, такую ​​как название бренда, тип продукта, характеристики продукта и т. д."}, "ai_listing_long_title_2": {"message": "На основе стандартного названия продукта добавляются ключевые слова, способствующие SEO."}, "ai_listing_long_title_3": {"message": "В дополнение к названию бренда, типу продукта, характеристикам продукта и ключевым словам также включены ключевые слова с длинным хвостом для достижения более высокого рейтинга в конкретных сегментированных поисковых запросах."}, "ai_listing_longtail_keyword_product_title": {"message": "Название продукта с длинным хвостом ключевого слова"}, "ai_listing_manually_enter": {"message": "Ввести вручную..."}, "ai_listing_network_not_working": {"message": "Интернет недоступен, для доступа к ChatGPT требуется VPN"}, "ai_listing_new_dictionary": {"message": "Создать новую библиотеку слов..."}, "ai_listing_new_generate": {"message": "Генерировать"}, "ai_listing_optional_words": {"message": "Дополнительные слова"}, "ai_listing_original_title": {"message": "Оригинальное название"}, "ai_listing_other_keywords_included": {"message": "Другие ключевые слова включали:"}, "ai_listing_please_again": {"message": "Пожалуйста, попробуйте снова"}, "ai_listing_please_select": {"message": "Для вас были созданы следующие заголовки, выберите:"}, "ai_listing_product_category": {"message": "Категория продукта"}, "ai_listing_product_category_is": {"message": "Категория продукта"}, "ai_listing_product_category_to": {"message": "К какой категории относится товар?"}, "ai_listing_random_keywords": {"message": "Случайные ключевые слова ($amount$)", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Случайные $amount$ бонусов за продажу", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Рандомизировать из библиотеки слов"}, "ai_listing_search_selling": {"message": "Поиск по месту продажи"}, "ai_listing_select_product_categories": {"message": "Автоматический выбор категорий товаров."}, "ai_listing_select_product_selling_points": {"message": "Автоматически выбирать точки продажи продукта"}, "ai_listing_select_word_library": {"message": "Выбрать библиотеку слов"}, "ai_listing_selling": {"message": "Пункты продажи"}, "ai_listing_selling_ask": {"message": "Какие еще требования предъявляются к названию?"}, "ai_listing_selling_optional": {"message": "Дополнительные точки продажи"}, "ai_listing_selling_repeat": {"message": "Баллы не могут быть дублированы."}, "ai_listing_set_excluded": {"message": "Сделать библиотекой исключенных слов"}, "ai_listing_set_include_selling_points": {"message": "Включите пункты продажи"}, "ai_listing_set_included": {"message": "Установить как включенную библиотеку слов"}, "ai_listing_set_selling_dictionary": {"message": "Установить как библиотеку торговых точек"}, "ai_listing_standard_product_title": {"message": "Стандартное название продукта"}, "ai_listing_translated_title": {"message": "Переведенное название"}, "ai_listing_visit_chatGPT": {"message": "Посетите чатGPT"}, "ai_listing_what_other_keywords": {"message": "Какие еще ключевые слова нужны для заголовка?"}, "aliprice_coupons_apply_again": {"message": "Подать заявку снова"}, "aliprice_coupons_apply_coupons": {"message": "Применить купоны"}, "aliprice_coupons_apply_success": {"message": "Найден купон: сохраните $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Тестирование кодов для лучших предложений..."}, "aliprice_coupons_applying_desc": {"message": "Проверка: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Перейти к оформлению заказа"}, "aliprice_coupons_found_coupons": {"message": "Мы нашли купонов на $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Готовы оформить заказ? Позаботимся о том, чтобы вы получили лучшую цену!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Эти коды не работали. Ничего страшного — вы уже получаете лучшую цену."}, "aliprice_coupons_toolbar_btn": {"message": "Получить купоны"}, "aliww_translate": {"message": "Переводчик чата Aliwangwang"}, "aliww_translate_supports": {"message": "Поддержка: 1688 и Таобао"}, "amazon_extended_keywords_Keywords": {"message": "Ключевые слова"}, "amazon_extended_keywords_copy_all": {"message": "Скопировать все"}, "amazon_extended_keywords_more": {"message": "Больше"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Сортировать по совокупным продажам"}, "an_lei_xing_cha_kan": {"message": "Просмотр по типу"}, "an_yue_dai_xiao_pai_xu": {"message": "Рейтинг по дропшиппинг-продажам"}, "apra_btn__cat_name": {"message": "Ана<PERSON><PERSON><PERSON> отзывов"}, "apra_chart__name": {"message": "Процент продаж продукции по странам"}, "apra_chart__update_at": {"message": "Время обновления $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Статистика продаж стран"}, "auto_opening": {"message": "Автоматическое открытие через $num$ сек.", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Следующие $autoPaging$ страниц", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Среднее количество дней на полке"}, "average_hui_fu_lv": {"message": "Средняя частота ответов"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Средний уровень поставщика"}, "average_price": {"message": "Средняя цена"}, "average_qi_ding_liang": {"message": "Средний минимальный заказ"}, "average_rating": {"message": "Средний рейтинг"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Среднее количество лет сертификации"}, "average_revenue": {"message": "Средний доход"}, "average_revenue_per_product": {"message": "Общий доход ÷ Количество товаров"}, "average_sales": {"message": "Средние продажи"}, "average_sales_per_product": {"message": "Общие продажи ÷ Количество товаров"}, "bao_han": {"message": "Соде<PERSON><PERSON><PERSON>т"}, "bao_zheng_jin": {"message": "Допуск"}, "bian_ti_shu": {"message": "Количество вариантов"}, "biao_ti": {"message": "Заголовок"}, "blacklist_add_blacklist": {"message": "Заблокировать этот магазин"}, "blacklist_address_incorrect": {"message": "Адрес неверный. Пожалуйста, проверь это."}, "blacklist_blacked_out": {"message": "Мага<PERSON><PERSON><PERSON> заблокирован"}, "blacklist_blacklist": {"message": "Черный список"}, "blacklist_no_records_yet": {"message": "Пока нет записи!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON>р<PERSON><PERSON>д"}, "btn_aliprice_agent__daigou": {"message": "Посредник покупок"}, "btn_aliprice_agent__dropshipping": {"message": "Дропши<PERSON><PERSON>инг"}, "btn_have_a_try": {"message": "Попробуй это сейчас"}, "btn_refresh": {"message": "обновление"}, "btn_try_it_now": {"message": "Попробуй это сейчас"}, "btn_txt_view_on_aliprice": {"message": "Посмотреть на AliPrice"}, "bu_bao_han": {"message": "Не содержит"}, "bulk_copy_links": {"message": "Массовое копирование ссылок"}, "bulk_copy_products": {"message": "Массовое копирование товаров"}, "cai_gou_zi_xun": {"message": "Обслуживание клиентов"}, "cai_gou_zi_xun__desc": {"message": "Трехминутная скорость ответа продавца"}, "can_ping_lei_xing": {"message": "Тип"}, "cao_zuo": {"message": "Операция"}, "chan_pin_ID": {"message": "Код товара"}, "chan_pin_e_wai_xin_xi": {"message": "Дополнительная информация о продукте"}, "chan_pin_lian_jie": {"message": "Ссылка на продукт"}, "cheng_li_shi_jian": {"message": "Время основания"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Нажмите «Копировать информацию о продукте»"}, "commmon_txt_expired": {"message": "Истекший"}, "common__date_range_12m": {"message": "1 год"}, "common__date_range_1m": {"message": "1 месяц"}, "common__date_range_1w": {"message": "1 неделя"}, "common__date_range_2w": {"message": "2 недели"}, "common__date_range_3m": {"message": "3 месяца"}, "common__date_range_3w": {"message": "3 недели"}, "common__date_range_6m": {"message": "6 месяцев"}, "common_btn_cancel": {"message": "Отменить"}, "common_btn_close": {"message": "Закрыть"}, "common_btn_save": {"message": "Сохранить"}, "common_btn_setting": {"message": "Настройка"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "Нет данных"}, "common_error_msg_no_result": {"message": "К сожалению, никакого результата не найдено."}, "common_favorites": {"message": "Избранные"}, "common_feedback": {"message": "Отзыв"}, "common_help": {"message": "Помощь"}, "common_loading": {"message": "Загрузка"}, "common_login": {"message": "Вход"}, "common_logout": {"message": "Выход из системы"}, "common_no": {"message": "Нет"}, "common_powered_by_aliprice": {"message": "При поддержке AliPrice.com"}, "common_setting": {"message": "Настройки"}, "common_sign_up": {"message": "Регистрация"}, "common_system_upgrading_title": {"message": "Обновление системы"}, "common_system_upgrading_txt": {"message": "Пожалуйста, попробуйте позже"}, "common_txt__currency": {"message": "Валюта"}, "common_txt__video_tutorial": {"message": "Видеоурок"}, "common_txt_ago_time": {"message": "$time$ дней назад", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "все"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Почти никогда не использовался"}, "common_txt_biaoti_link": {"message": "Название+Ссылка"}, "common_txt_biaoti_link_dian_pu": {"message": "Название+Ссылка+Название магазина"}, "common_txt_blacklist": {"message": "Черный список"}, "common_txt_cancel": {"message": "Отменить"}, "common_txt_category": {"message": "Категория"}, "common_txt_chakan": {"message": "Проверять"}, "common_txt_colors": {"message": "цвета"}, "common_txt_confirm": {"message": "Подтверждать"}, "common_txt_copied": {"message": "Скопировано"}, "common_txt_copy": {"message": "Копировать"}, "common_txt_copy_link": {"message": "Копировать ссылку"}, "common_txt_copy_title": {"message": "Копировать заголовок"}, "common_txt_copy_title__link": {"message": "Копировать заголовок&ссылку"}, "common_txt_day": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_day__short": {"message": "Д"}, "common_txt_delete": {"message": "Удалять"}, "common_txt_dian_pu_link": {"message": "Скопировать название магазина + ссылку"}, "common_txt_download": {"message": "Скачать"}, "common_txt_downloaded": {"message": "Продолжить загрузку"}, "common_txt_export_as_csv": {"message": "Экспорт в Excel"}, "common_txt_export_as_txt": {"message": "Экспорт в TXT"}, "common_txt_fail": {"message": "Неудача"}, "common_txt_format": {"message": "Формат"}, "common_txt_get": {"message": "получать"}, "common_txt_incert_selection": {"message": "Инвертировать выбор"}, "common_txt_install": {"message": "Установить"}, "common_txt_load_failed": {"message": "Ошибка загрузки"}, "common_txt_month": {"message": "мес<PERSON><PERSON>"}, "common_txt_more": {"message": "Больше"}, "common_txt_new_unused": {"message": "Совершенно новый, неиспользованный"}, "common_txt_next": {"message": "Следующяя"}, "common_txt_no_limit": {"message": "Неограниченный"}, "common_txt_no_noticeable": {"message": "Никаких видимых царапин или грязи."}, "common_txt_on_sale": {"message": "Доступный"}, "common_txt_opt_in_out": {"message": "Вкл/выкл"}, "common_txt_order": {"message": "Зак<PERSON>з"}, "common_txt_others": {"message": "Другие"}, "common_txt_overall_poor_condition": {"message": "Общее плохое состояние"}, "common_txt_patterns": {"message": "узоры"}, "common_txt_platform": {"message": "Платформы"}, "common_txt_please_select": {"message": "Пожалуйста, выберите"}, "common_txt_prev": {"message": "Предыдущая"}, "common_txt_price": {"message": "Цена"}, "common_txt_privacy_policy": {"message": "Политика конфиденциальности"}, "common_txt_product_condition": {"message": "Статус продукта"}, "common_txt_rating": {"message": "<PERSON>ей<PERSON>инг"}, "common_txt_ratings": {"message": "Рейтинги"}, "common_txt_reload": {"message": "Перезагрузить"}, "common_txt_reset": {"message": "Перезагрузить"}, "common_txt_retail": {"message": "Розничная"}, "common_txt_review": {"message": "Обзоры"}, "common_txt_sale": {"message": "Доступный"}, "common_txt_same": {"message": "Схожие"}, "common_txt_scratches_and_dirt": {"message": "С царапинами и грязью"}, "common_txt_search_title": {"message": "Поиск названия"}, "common_txt_select_all": {"message": "Выбрать все"}, "common_txt_selected": {"message": "Выбрано"}, "common_txt_share": {"message": "Поделись"}, "common_txt_sold": {"message": "продано"}, "common_txt_sold_out": {"message": "распроданный"}, "common_txt_some_scratches": {"message": "Некоторые царапины и грязь"}, "common_txt_sort_by": {"message": "Сортировать по"}, "common_txt_state": {"message": "состояние"}, "common_txt_success": {"message": "Успех"}, "common_txt_sys_err": {"message": "Системная ошибка"}, "common_txt_today": {"message": "Сегодня"}, "common_txt_total": {"message": "Всего"}, "common_txt_unselect_all": {"message": "Наоборот"}, "common_txt_upload_image": {"message": "Загрузить фото"}, "common_txt_visit": {"message": "Посещать"}, "common_txt_whitelist": {"message": "Белый список"}, "common_txt_wholesale": {"message": "Оптовая"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Год"}, "common_yes": {"message": "Да"}, "compare_tool_btn_clear_all": {"message": "Очистить все"}, "compare_tool_btn_compare": {"message": "Сравнить"}, "compare_tool_btn_contact": {"message": "Связаться с нами"}, "compare_tool_tips_max_compared": {"message": "Dodajte do $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Настроить уведомления"}, "contact_us": {"message": "Свяжитесь с нами"}, "context_menu_screenshot_search": {"message": "Скриншот поиска в том же стиле"}, "context_menus_aliprice_search_by_image": {"message": "Найти это изображение на AliPrice"}, "context_menus_goote_trans": {"message": "Перевести/Показать оригинал"}, "context_menus_search_by_image": {"message": "Поиск по изображению на $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Захватить в $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Захват для перевода"}, "converter_modal_amount_placeholder": {"message": "Введите сумму здесь"}, "converter_modal_btn_convert": {"message": "Перерабатывать"}, "converter_modal_exchange_rate_source": {"message": "Данные получены из валютного курса $boc$. Время обновления: $updatedAt$.", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Конвертер валют"}, "converter_modal_search_placeholder": {"message": "Валюта поиска"}, "copy_all_contact_us_notice": {"message": "В данный момент этот сайт не поддерживается. Свяжитесь с нами."}, "copy_product_info": {"message": "Скопировать информацию о продукте"}, "copy_suggest_search_kw": {"message": "Копирование раскрывающихся списков"}, "country__han_gou": {"message": "Южная Корея"}, "country__ri_ben": {"message": "Япония"}, "country__yue_nan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "currency_convert__custom": {"message": "Пользовательский курс обмена"}, "currency_convert__sync_server": {"message": "Синхронизировать сервер"}, "dang_ri_fa_huo": {"message": "Доставка в тот же день"}, "dao_chu_quan_dian_shang_pin": {"message": "Экспортировать все"}, "dao_chu_wei_CSV": {"message": "Экспорт"}, "dao_chu_zi_duan": {"message": "Экспортировать поля"}, "delivery_address": {"message": "Адрес отправки"}, "delivery_company": {"message": "Компания доставки"}, "di_zhi": {"message": "адрес"}, "dian_ji_cha_xun": {"message": "Нажмите, чтобы задать вопрос"}, "dian_pu_ID": {"message": "Идентификатор магазина"}, "dian_pu_di_zhi": {"message": "Адрес магазина"}, "dian_pu_lian_jie": {"message": "Ссылка на магазин"}, "dian_pu_ming": {"message": "Название магазина"}, "dian_pu_ming_cheng": {"message": "Название магазина"}, "dian_pu_shang_pin_zong_hsu": {"message": "Общее количество товаров в магазине: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Информация о магазине"}, "ding_zai_zuo_ce": {"message": "прибит к левому"}, "download_image__SKU_variant_images": {"message": "Изображения вариантов артикула"}, "download_image__assume": {"message": "На<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, у нас есть 2 изображения, product1.jpg и product2.gif.\nimg_{$no$} будет переименован в img_01.jpg, img_02.gif;\n{$group$}_{$no$} будет переименован в main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Пакетная загрузка"}, "download_image__combined_image": {"message": "Изображение комбинированного продукта"}, "download_image__continue_downloading": {"message": "Продолжить загрузку"}, "download_image__description_images": {"message": "Изображения описаний"}, "download_image__download_combined_image": {"message": "Загрузить изображение комбинированного продукта"}, "download_image__download_zip": {"message": "Загрузить как сжатый файл"}, "download_image__enlarge_check": {"message": "Поддерживаются только изображения JPEG, JPG, GIF и PNG, максимальный размер одного изображения: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Увеличить изображение"}, "download_image__export": {"message": "Экспорт ссылок"}, "download_image__height": {"message": "Высота"}, "download_image__ignore_videos": {"message": "Видео было проигнорировано, так как его нельзя экспортировать"}, "download_image__img_translate": {"message": "Перевод фото"}, "download_image__main_image": {"message": "Основное изображение"}, "download_image__multi_folder": {"message": "Несколько папок"}, "download_image__name": {"message": "Скачать изображение"}, "download_image__notice_content": {"message": "Пожалуйста, не отмечайте \"Спрашивать, куда сохранить каждый файл перед загрузкой\" в настройках загрузки вашего браузера!!! Иначе будет много диалоговых окон."}, "download_image__notice_ignore": {"message": "Больше не запрашивать это сообщение"}, "download_image__order_number": {"message": "{$no$} серийный номер; {$group$} имя группы; {$date$} временная метка", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Обзор"}, "download_image__prompt_download_zip": {"message": "Изображений слишком много, лучше загрузите их в виде zip-папки."}, "download_image__rename": {"message": "Переименовать"}, "download_image__rule": {"message": "Правила именования"}, "download_image__single_folder": {"message": "Одна папка"}, "download_image__sku_image": {"message": "Изображение артикула"}, "download_image__video": {"message": "Видео"}, "download_image__width": {"message": "Ши<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Скачать фото обзора"}, "download_reviews__dropdown_title": {"message": "Скачать фото обзора"}, "download_reviews__export_csv": {"message": "Экспорт CSV"}, "download_reviews__no_images": {"message": "0 изображений доступны для скачивания"}, "download_reviews__no_reviews": {"message": "Нет обзора для скачивания!"}, "download_reviews__notice": {"message": "Намекать:"}, "download_reviews__notice__chrome_settings": {"message": "Настройте браузер Chrome, чтобы спрашивать, где сохранить каждый файл перед загрузкой, установите значение «Выкл.»."}, "download_reviews__notice__wait": {"message": "В зависимости от количества отзывов время ожидания может увеличиться."}, "download_reviews__pages_list__all": {"message": "Все"}, "download_reviews__pages_list__page": {"message": "Предыдущие $page$ страницы", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Диапазон выбора"}, "export_shopping_cart__csv_filed__details_url": {"message": "Ссылка на товар"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Показать выбранные варианты в общих ссылках"}, "export_shopping_cart__csv_filed__images": {"message": "Ссылка на изображение"}, "export_shopping_cart__csv_filed__quantity": {"message": "Количество"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Цена"}, "export_shopping_cart__csv_filed__specs": {"message": "Характеристики"}, "export_shopping_cart__csv_filed__store_name": {"message": "Название магазина"}, "export_shopping_cart__csv_filed__store_url": {"message": "Ссылка на магазин"}, "export_shopping_cart__csv_filed__title": {"message": "Наименование товара"}, "export_shopping_cart__export_btn": {"message": "Экспорт"}, "export_shopping_cart__export_empty": {"message": "Пожалуйста, выберите товар!"}, "fa_huo_shi_jian": {"message": "Перевозки"}, "favorite_add_email": {"message": "Добавить адрес Email"}, "favorite_add_favorites": {"message": "Добавить в избранное"}, "favorite_added": {"message": "Добавлено"}, "favorite_btn_add": {"message": "Сообщите мне когда цена упадет."}, "favorite_btn_notify": {"message": "Следить за ценой"}, "favorite_cate_name_all": {"message": "Все продукты"}, "favorite_current_price": {"message": "Текущая цена"}, "favorite_due_date": {"message": "Дата оплаты"}, "favorite_enable_notification": {"message": "Пожалуйста, включите уведомление по электронной почте"}, "favorite_expired": {"message": "Истек"}, "favorite_go_to_enable": {"message": "Перейти к включению"}, "favorite_msg_add_success": {"message": "Добавленно в избранное"}, "favorite_msg_del_success": {"message": "Удаленно из избранного"}, "favorite_msg_failure": {"message": "Ошибка! Обновите страницу и повторите снова."}, "favorite_please_add_email": {"message": "Пожалуйста, добавьте адрес Email"}, "favorite_price_drop": {"message": "Снижение"}, "favorite_price_rise": {"message": "Рост"}, "favorite_price_untracked": {"message": "Цена не отслеживается"}, "favorite_saved_price": {"message": "Сохраненная цена"}, "favorite_stop_tracking": {"message": "Остановить отслеживание"}, "favorite_sub_email_address": {"message": "Адрес электронной почты для подписки"}, "favorite_tracking_period": {"message": "Период отслеживания"}, "favorite_tracking_prices": {"message": "Отслеживание цен"}, "favorite_verify_email": {"message": "Подтвердите адрес электронной почты"}, "favorites_list_remove_prompt_msg": {"message": "Вы уверены, что удалите его?"}, "favorites_update_button": {"message": "Обновить цены сейчас"}, "fen_lei": {"message": "Категория"}, "fen_xia_yan_xuan": {"message": "Выбор дистрибьютора"}, "find_similar": {"message": "Найти похожие"}, "first_ali_price_date": {"message": "Дата первого захвата веб-пауком AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Нет купонов"}, "fooview_coupons_modal_title": {"message": "Купоны"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Цена <$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "или цена> $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Крайний срок"}, "fooview_favorites_error_msg_no_favorites": {"message": "Добавьте избранные товары здесь чтобы получать предупреждение о падении цены."}, "fooview_favorites_filter_latest": {"message": "Последние"}, "fooview_favorites_filter_price_drop": {"message": "ВНИЗ"}, "fooview_favorites_filter_price_up": {"message": "ВВЕРХ"}, "fooview_favorites_modal_title": {"message": "Моё избранное"}, "fooview_favorites_modal_title_title": {"message": "Перейдите в избранное AliPrice"}, "fooview_favorites_track_price": {"message": "Чтобы отслеживать цену"}, "fooview_price_history_app_price": {"message": "Цена в приложении :"}, "fooview_price_history_title": {"message": "Отслеживание цен"}, "fooview_product_list_feedback": {"message": "<PERSON>ей<PERSON>инг"}, "fooview_product_list_orders": {"message": "Зак<PERSON>з"}, "fooview_product_list_price": {"message": "цен"}, "fooview_reviews_error_msg_no_review": {"message": "Мы не нашли отзыв об этом товаре."}, "fooview_reviews_filter_buyer_reviews": {"message": "Фото от покупателей"}, "fooview_reviews_modal_title": {"message": "Обзоры"}, "fooview_same_product_choose_category": {"message": "Выбрать категорию"}, "fooview_same_product_filter_feedback": {"message": "<PERSON>ей<PERSON>инг"}, "fooview_same_product_filter_orders": {"message": "Зак<PERSON>з"}, "fooview_same_product_filter_price": {"message": "цен"}, "fooview_same_product_filter_rating": {"message": "<PERSON>ей<PERSON>инг"}, "fooview_same_product_modal_title": {"message": "Найти схожие товары"}, "fooview_same_product_search_by_image": {"message": "Поиск по картинке"}, "fooview_seller_analysis_modal_title": {"message": "Анализ продавца"}, "for_12_months": {"message": "На 1 год"}, "for_12_months_list_pro": {"message": "12 месяцев"}, "for_12_months_nei": {"message": "В течение 12 месяцев"}, "for_1_months": {"message": "1 месяц"}, "for_1_months_nei": {"message": "В течение 1 месяца"}, "for_3_months": {"message": "За 3 месяца(ев)"}, "for_3_months_nei": {"message": "В течение 3 месяцев"}, "for_6_months": {"message": "За 6 месяца(ев)"}, "for_6_months_nei": {"message": "В течение 6 месяцев"}, "for_9_months": {"message": "9 месяцев"}, "for_9_months_nei": {"message": "В течение 9 месяцев"}, "fu_gou_lv": {"message": "Курс выкупа"}, "gao_liang_bu_tong_dian": {"message": "выделить различия"}, "gao_liang_guang_gao_chan_pin": {"message": "Выделите рекламные продукты"}, "geng_duo_xin_xi": {"message": "Больше информации"}, "geng_xin_shi_jian": {"message": "Время обновления"}, "get_store_products_fail_tip": {"message": "Нажмите «ОК», чтобы перейти к проверке и обеспечить нормальный доступ."}, "gong_x_kuan_shang_pin": {"message": "Всего $amount$ товаров", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Поставщик"}, "gong_ying_shang_ID": {"message": "Идентификатор поставщика"}, "gong_ying_shang_deng_ji": {"message": "Рейтинг поставщиков"}, "gong_ying_shang_nian_zhan": {"message": "Поставщик старше"}, "gong_ying_shang_xin_xi": {"message": "информация о поставщике"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Ссылка на домашнюю страницу поставщика"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Ориентировочная цена продажи"}, "guan_jian_zi": {"message": "Ключевое слово"}, "guang_gao_chan_pin": {"message": "Рекламные продукты"}, "guang_gao_zhan_bi": {"message": "Рекламные"}, "guo_ji_wu_liu_yun_fei": {"message": "Стоимость международной доставки"}, "guo_lv_tiao_jian": {"message": "Фильтры"}, "hao_ping_lv": {"message": "Положительный рейтинг"}, "highest_price": {"message": "Высокая"}, "historical_trend": {"message": "Историческая тенденция"}, "how_to_screenshot": {"message": "Удерживайте левую кнопку мыши, чтобы выбрать область, нажмите правую кнопку мыши или клавишу Esc, чтобы выйти из скриншота."}, "howt_it_works": {"message": "Как пользоваться"}, "hui_fu_lv": {"message": "Скорость отклика"}, "hui_tou_lv": {"message": "скорость возврата"}, "inquire_freightFee": {"message": "Запрос на перевозку"}, "inquire_freightFee_Yuan": {"message": "Перевозка/Юань"}, "inquire_freightFee_province": {"message": "Провинция"}, "inquire_freightFee_the": {"message": "Стоимость перевозки составляет $num$, что означает, что в регионе действует бесплатная доставка.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Объявление."}, "jia_ge": {"message": "Цена"}, "jia_ge_dan_wei": {"message": "Единица"}, "jia_ge_qu_shi": {"message": "Тенденция"}, "jia_zai_n_ge_shang_pin": {"message": "Загрузить $num$ товаров", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Процент от объема продаж за последние 30 дней"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Процент от дохода за последние 30 дней"}, "jin_30d_xiao_liang": {"message": "Продажи"}, "jin_30d_xiao_liang__desc": {"message": "Общий объем продаж за последние 30 дней"}, "jin_30d_xiao_shou_e": {"message": "Оборот"}, "jin_30d_xiao_shou_e__desc": {"message": "Общий оборот за последние 30 дней"}, "jin_90_tian_mai_jia_shu": {"message": "Покупатели за последние 90 дней"}, "jin_90_tian_xiao_shou_liang": {"message": "Продажи за последние 90 дней"}, "jing_xuan_huo_yuan": {"message": "Избранные источники"}, "jing_ying_mo_shi": {"message": "Бизнес модель"}, "jing_ying_mo_shi__gong_chang": {"message": "Производитель"}, "jiu_fen_jie_jue": {"message": "Разрешение спора"}, "jiu_fen_jie_jue__desc": {"message": "Учет споров о правах магазинов продавцов"}, "jiu_fen_lv": {"message": "Уровень споров"}, "jiu_fen_lv__desc": {"message": "Доля заказов с жалобами, выполненных за последние 30 дней и признанных ответственностью продавца или обеих сторон"}, "kai_dian_ri_qi": {"message": "Дата открытия"}, "keywords": {"message": "Ключевые слова"}, "kua_jin_Select_pan_huo": {"message": "Товары для экспорта"}, "last15_days": {"message": "Последние 15 дней"}, "last180_days": {"message": "Последние 180 дней"}, "last30_days": {"message": "За последние 30 дней"}, "last360_days": {"message": "Последние 360 дней"}, "last45_days": {"message": "Последние 45 дней"}, "last60_days": {"message": "Последние 60 дней"}, "last7_days": {"message": "Последние 7 дней"}, "last90_days": {"message": "Последние 90 дней"}, "last_30d_sales": {"message": "Продажи за последние 30 дней"}, "lei_ji": {"message": "кумулятивный"}, "lei_ji_xiao_liang": {"message": "Общий"}, "lei_ji_xiao_liang__desc": {"message": "Все продажи после того, как товар окажется на полке"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Совокупный объем продаж за последние 30 дней, отсортированный от большего к меньшему."}, "lian_xi_fang_shi": {"message": "Контактная информация"}, "list_time": {"message": "Дата хранения"}, "load_more": {"message": "Загрузить еще"}, "login_to_aliprice": {"message": "Войти в AliPrice"}, "long_link": {"message": "Длинная ссылка"}, "lowest_price": {"message": "Низкая"}, "mai_jia_shu": {"message": "Количество продавцов"}, "mao_li_lv": {"message": "Валовая прибыль"}, "mobile_view__dkxbqy": {"message": "Открыть новую вкладку"}, "mobile_view__sjdxq": {"message": "Подробности в приложении"}, "mobile_view__sjdxqy": {"message": "Подробная страница в приложении"}, "mobile_view__smck": {"message": "Сканировать"}, "mobile_view__smckms": {"message": "Используйте камеру или приложение для сканирования и просмотра"}, "modified_failed": {"message": "Модификация не удалась"}, "modified_successfully": {"message": "Изменено успешно"}, "nav_btn_favorites": {"message": "Мои коллекции"}, "nav_btn_package": {"message": "Пак<PERSON>т"}, "nav_btn_product_info": {"message": "О товаре"}, "nav_btn_viewed": {"message": "Рассматриваемые"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Нет"}, "normal_link": {"message": "Обычная ссылка"}, "notice": {"message": "Примечание"}, "number_reviews": {"message": "Отзывы"}, "only_show_num": {"message": "Всего товаров: $allnum$, Скрыто: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Удалить неотмеченное"}, "open": {"message": "Открыть"}, "open_links": {"message": "Открыть ссылки"}, "options_page_tab_check_links": {"message": "Проверьте ссылки"}, "options_page_tab_gernal": {"message": "Общие"}, "options_page_tab_notifications": {"message": "Уведомления"}, "options_page_tab_others": {"message": "Другие"}, "options_page_tab_sbi": {"message": "Поиск по картинке"}, "options_page_tab_shortcuts": {"message": "Ярлыки"}, "options_page_tab_shortcuts_title": {"message": "Размер шрифта ярлыков"}, "options_page_tab_similar_products": {"message": "Схожие"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Сейчас откроется несколько ссылок на продукты"}, "order_list_sku_show_title": {"message": "Показать выбранные варианты в общих ссылках"}, "orders_last30_days": {"message": "Количество заказов за последние 30 дней"}, "pTutorial_favorites_block1_desc1": {"message": "Товары, которые вы отслеживали, перечислены здесь"}, "pTutorial_favorites_block1_title": {"message": "Избранные"}, "pTutorial_popup_block1_desc1": {"message": "Зеленая этикетка означает, что цены снижены."}, "pTutorial_popup_block1_title": {"message": "Ярлыки и избранные"}, "pTutorial_price_history_block1_desc1": {"message": "Нажинай кнопку «Отслеживать цену», добавите товары в Избранные. Как только их цены упадут, вы будете получать уведомления"}, "pTutorial_price_history_block1_title": {"message": "Отслеживать цену"}, "pTutorial_reviews_block1_desc1": {"message": "Отзывы от Itao и реальные фото собранные из AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Отзывы"}, "pTutorial_reviews_block2_desc1": {"message": "Всегда полезно проверять отзывы от других покупателей"}, "pTutorial_same_products_block1_desc1": {"message": "Вы можете сравнить их, чтобы сделать лучший выбор"}, "pTutorial_same_products_block1_desc2": {"message": "Нажмите «Больше» для «Поиска по картинке»"}, "pTutorial_same_products_block1_title": {"message": "Похожие товары"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Перетащите картинке товара туда и выберите категорию"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Поиск по картинке"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Положительные отзывы о продавце, счеты отзывов и сколько времени он был на рынке"}, "pTutorial_seller_analysis_block1_title": {"message": "Рейтинг продавца"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Рейтинг продавца основан на 3 показателях: описанный товар, Скорость доставки сообщения"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Мы используем 3 цвета и иконки, чтобы указать уровень доверия продавцов"}, "page_count": {"message": "Количество страниц"}, "pai_chu": {"message": "Исключенные"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Гонконге"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Японии"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Корее"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Казахстане"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Макао"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Восточной Европе"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в Саудовской Аравии"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые на Тайване"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые в США"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые во Вьетнаме"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Исключить запрещённые товары"}, "payable_price_formula": {"message": "Цена + Доставка + Скидка"}, "pdd_check_retail_btn_txt": {"message": "Проверить розницу"}, "pdd_pifa_to_retail_btn_txt": {"message": "Купить в розницу"}, "pdp_copy_fail": {"message": "Не удалось скопировать!"}, "pdp_copy_success": {"message": "Копирование выполнено!"}, "pdp_share_modal_subtitle": {"message": "Поделитесь снимком экрана, он увидит вашу невесту."}, "pdp_share_modal_title": {"message": "Поделитесь своим выбором"}, "pdp_share_screenshot": {"message": "Поделиться снимком экрана"}, "pei_song": {"message": "Способ доставки"}, "pin_lei": {"message": "Категория"}, "pin_zhi_ti_yan": {"message": "Качество продукта"}, "pin_zhi_ti_yan__desc": {"message": "Коэффициент возврата качества магазина продавца"}, "pin_zhi_tui_kuan_lv": {"message": "Ставка возврата"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Доля заказов, возврат средств по которым был произведен и возвращен только за последние 30 дней."}, "ping_fen": {"message": "<PERSON>ей<PERSON>инг"}, "ping_jun_fa_huo_su_du": {"message": "Средняя скорость доставки"}, "pkgInfo_hide": {"message": "Информация о логистике: вкл./выкл."}, "pkgInfo_no_trace": {"message": "Нет информации о логистике"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Пиндуодуо оптом"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "дома"}, "popup_go_to_platform": {"message": "Зайти на $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Я ищу..."}, "popup_track_package_btn_track": {"message": "ОТСЛЕДИТЬ"}, "popup_track_package_desc": {"message": "Универсальное отслеживание посылок"}, "popup_track_package_search_placeholder": {"message": "Трек номер"}, "popup_translate_search_placeholder": {"message": "Перевод и поиск на $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Отслеживание цен"}, "price_history_chart_tip_ae": {"message": "Совет: количество заказов — это общее число заказов с момента размещения товара."}, "price_history_chart_tip_coupang": {"message": "Совет: <PERSON><PERSON><PERSON> удали<PERSON> количество мошеннических заказов."}, "price_history_inm_1688_l1": {"message": "Пожалуйста, установите"}, "price_history_inm_1688_l2": {"message": "Помощник по покупкам AliPrice для 1688"}, "price_history_panel_lowest_price": {"message": "Самая низкая цена: "}, "price_history_panel_tab_price_tracking": {"message": "Отслеживание цен"}, "price_history_panel_tab_seller_analysis": {"message": "Анализ продавца"}, "price_history_pro_modal_title": {"message": "История цен и история заказов"}, "privacy_consent__btn_agree": {"message": "Пересмотреть согласие на сбор данных"}, "privacy_consent__btn_disable_all": {"message": "Не принимать"}, "privacy_consent__btn_enable_all": {"message": "Включить все"}, "privacy_consent__btn_uninstall": {"message": "удалять"}, "privacy_consent__desc_privacy": {"message": "Обратите внимание, что без данных или файлов cookie некоторые функции будут отключены, потому что эти функции требуют объяснения данных или файлов cookie, но вы все равно можете использовать другие функции."}, "privacy_consent__desc_privacy_L1": {"message": "К сожалению, без данных или файлов cookie это не сработает, потому что нам нужно объяснение данных или файлов cookie."}, "privacy_consent__desc_privacy_L2": {"message": "Если вы не разрешаете нам собирать эту информацию, удалите ее."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, мы получаем данные о вашей валюте в файлах cookie только при совершении покупок в Интернете, чтобы отображать историю цен."}, "privacy_consent__item_cookies_title": {"message": "Обязательные файлы cookie"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Добавьте файлы cookie в браузер, чтобы анонимно идентифицировать ваш компьютер или устройство."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Добавьте функциональные данные в аддон для работы с функцией."}, "privacy_consent__item_functional_title": {"message": "Функциональные и аналитические файлы cookie"}, "privacy_consent__more_desc": {"message": "Пожал<PERSON>йста, <PERSON><PERSON><PERSON><PERSON><PERSON>, что мы не передаем ваши личные данные другим компаниям, и никакие рекламные компании не собирают данные через нашу службу."}, "privacy_consent__options__btn__desc": {"message": "Чтобы использовать все функции, вам необходимо включить его."}, "privacy_consent__options__btn__label": {"message": "Включи"}, "privacy_consent__options__desc_L1": {"message": "Мы будем собирать следующие данные, которые идентифицируют вас:"}, "privacy_consent__options__desc_L2": {"message": "- куки, мы получаем ваши данные валюты в куки, когда вы делаете покупки онлайн, чтобы показать историю цен."}, "privacy_consent__options__desc_L3": {"message": "- и добавьте куки в браузер, чтобы анонимно идентифицировать ваш компьютер или устройство."}, "privacy_consent__options__desc_L4": {"message": "- другие анонимные данные делают это расширение более удобным."}, "privacy_consent__options__desc_L5": {"message": "Обратите внимание, что мы не передаем ваши личные данные другим компаниям, и ни одна рекламная компания не собирает данные через наш сервис."}, "privacy_consent__privacy_preferences": {"message": "Настройки конфиденциальности"}, "privacy_consent__read_more": {"message": "Читать дальше >>"}, "privacy_consent__title_privacy": {"message": "Конфиденциальность"}, "product_info": {"message": "Информация о продукте"}, "product_recommend__name": {"message": "Схожие"}, "product_research": {"message": "Исследование товаров"}, "product_sub__email_desc": {"message": "Электронная почта для уведомления о цене"}, "product_sub__email_edit": {"message": "Редактировать"}, "product_sub__email_not_verified": {"message": "Пожалуйста, подтвердите электронную почту"}, "product_sub__email_required": {"message": "Пожалуйста, укажите электронную почту"}, "product_sub__form_countdown": {"message": "Автоматическое закрытие через $seconds$ sec.", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Добавить напоминание не удалось!"}, "product_sub__form_input_price": {"message": "Входная цена"}, "product_sub__form_item_country": {"message": "Страна"}, "product_sub__form_item_current_price": {"message": "Текущая цена"}, "product_sub__form_item_duration": {"message": "Трек для"}, "product_sub__form_item_higher_price": {"message": "или цена >"}, "product_sub__form_item_invalid_higher_price": {"message": "Цена должна быть больше $price$.", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Цена должна быть ниже $price$.", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Когда цена <"}, "product_sub__form_submit": {"message": "Представлять"}, "product_sub__form_success": {"message": "Добавьте напоминание об успехе!"}, "product_sub__high_price_notify": {"message": "Сообщите мне о повышении цен"}, "product_sub__low_price_notify": {"message": "Сообщите мне о снижении цен"}, "product_sub__modal_title": {"message": "Напоминание об изменении цены подписки"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "минимальный заказ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Зона предприятия"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Пожалуйста, выберите хотя бы один товар"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Пожалуйста, выберите хотя бы одно поле"}, "qu_deng_lu": {"message": "Войти"}, "quan_guo_yan_xuan": {"message": "Мировой выбор"}, "recommendation_popup_banner_btn_install": {"message": "Установите это"}, "recommendation_popup_banner_desc": {"message": "Отображение истории цен за 3/6 месяцев и уведомление о падении цен"}, "region__all": {"message": "Все регионы"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Включить правый клик"}, "ren_zheng_gong_chang": {"message": "Сертифицированная фабрика"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Лет в качестве сертифицированного поставщика"}, "required_to_aliprice_login": {"message": "Необходимо войти в AliPrice"}, "revenue_last30_days": {"message": "Оборот за последние 30 дней"}, "review_counts": {"message": "Количество коллекционеров"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Входной период"}, "sales_amount_last30_days": {"message": "Общий оборот за последние 30 дней"}, "sales_last30_days": {"message": "Продажи за последние 30 дней"}, "sbi_alibaba_cate__accessories": {"message": "Аксессуары"}, "sbi_alibaba_cate__aqfk": {"message": "Безопасность"}, "sbi_alibaba_cate__bags_cases": {"message": "Сумки и чехлы"}, "sbi_alibaba_cate__beauty": {"message": "Красота"}, "sbi_alibaba_cate__beverage": {"message": "Напитки"}, "sbi_alibaba_cate__bgwh": {"message": "Офисная культура"}, "sbi_alibaba_cate__bz": {"message": "Упаковка"}, "sbi_alibaba_cate__ccyj": {"message": "Кухонная утварь"}, "sbi_alibaba_cate__clothes": {"message": "Одежда"}, "sbi_alibaba_cate__cmgd": {"message": "Медиавещание"}, "sbi_alibaba_cate__coat_jacket": {"message": "Пальто и куртка"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Бытовая электроника"}, "sbi_alibaba_cate__cryp": {"message": "Товары для взрослых"}, "sbi_alibaba_cate__csyp": {"message": "Подкладки для кроватей"}, "sbi_alibaba_cate__cwyy": {"message": "Садоводство для домашних животных"}, "sbi_alibaba_cate__cysx": {"message": "Кейтеринг фреш"}, "sbi_alibaba_cate__dgdq": {"message": "Электрик"}, "sbi_alibaba_cate__dl": {"message": "Играет роль"}, "sbi_alibaba_cate__dress_suits": {"message": "Платье и костюмы"}, "sbi_alibaba_cate__dszm": {"message": "Осветительные приборы"}, "sbi_alibaba_cate__dzqj": {"message": "Электрическое устройство"}, "sbi_alibaba_cate__essb": {"message": "Подержанное оборудование"}, "sbi_alibaba_cate__food": {"message": "Питание"}, "sbi_alibaba_cate__fspj": {"message": "Одежда и аксессуары"}, "sbi_alibaba_cate__furniture": {"message": "Мебель"}, "sbi_alibaba_cate__fzpg": {"message": "Текстильная кожа"}, "sbi_alibaba_cate__ghjq": {"message": "Личная гигиена"}, "sbi_alibaba_cate__gt": {"message": "Стали"}, "sbi_alibaba_cate__gyp": {"message": "ремесла"}, "sbi_alibaba_cate__hb": {"message": "Экологически чистый"}, "sbi_alibaba_cate__hfcz": {"message": "Уход за кожей"}, "sbi_alibaba_cate__hg": {"message": "Химическая индустрия"}, "sbi_alibaba_cate__jg": {"message": "Обработка"}, "sbi_alibaba_cate__jianccai": {"message": "Строительные материалы"}, "sbi_alibaba_cate__jichuang": {"message": "Станок"}, "sbi_alibaba_cate__jjry": {"message": "Бытовое ежедневное использование"}, "sbi_alibaba_cate__jtys": {"message": "Транспорт"}, "sbi_alibaba_cate__jxsb": {"message": "Оборудование"}, "sbi_alibaba_cate__jxwj": {"message": "Механическое оборудование"}, "sbi_alibaba_cate__jydq": {"message": "Бытовая техника"}, "sbi_alibaba_cate__jzjc": {"message": "Строительные материалы для благоустройства дома"}, "sbi_alibaba_cate__jzjf": {"message": "Домашний текстиль"}, "sbi_alibaba_cate__mj": {"message": "Полотенце"}, "sbi_alibaba_cate__myyp": {"message": "Детские товары"}, "sbi_alibaba_cate__nanz": {"message": "Мужской"}, "sbi_alibaba_cate__nvz": {"message": "Женская одежда"}, "sbi_alibaba_cate__ny": {"message": "Энергия"}, "sbi_alibaba_cate__others": {"message": "Другие"}, "sbi_alibaba_cate__qcyp": {"message": "Автоаксессуары"}, "sbi_alibaba_cate__qmpj": {"message": "Автозапчасти"}, "sbi_alibaba_cate__shoes": {"message": "Обувь"}, "sbi_alibaba_cate__smdn": {"message": "Цифровой компьютер"}, "sbi_alibaba_cate__snqj": {"message": "Хранение и очистка"}, "sbi_alibaba_cate__spjs": {"message": "Еда, напиток"}, "sbi_alibaba_cate__swfw": {"message": "Бизнес-услуги"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Игрушка"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Брюки и юбка"}, "sbi_alibaba_cate__txcp": {"message": "Коммуникационные продукты"}, "sbi_alibaba_cate__tz": {"message": "Детская одежда"}, "sbi_alibaba_cate__underwear": {"message": "Нижнее белье"}, "sbi_alibaba_cate__wjgj": {"message": "Аппаратные средства"}, "sbi_alibaba_cate__xgpi": {"message": "Кожаные сумки"}, "sbi_alibaba_cate__xmhz": {"message": "проектное сотрудничество"}, "sbi_alibaba_cate__xs": {"message": "Резина"}, "sbi_alibaba_cate__ydfs": {"message": "Спортивная одежда"}, "sbi_alibaba_cate__ydhw": {"message": "Открытый спорт"}, "sbi_alibaba_cate__yjkc": {"message": "Металлургические минералы"}, "sbi_alibaba_cate__yqyb": {"message": "Инструментарий"}, "sbi_alibaba_cate__ys": {"message": "Распечатать"}, "sbi_alibaba_cate__yyby": {"message": "Медицинская помощь"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Количество покупателей за последние 90 дней"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Объем продаж за последние 90 дней"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Ориентировочная цена"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Стоимость международной доставки"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Стоимость доставки"}, "sbi_alibaba_cn_kj_gssj": {"message": "Ориентировочная цена"}, "sbi_alibaba_cn_kj_lr": {"message": "Выгода"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Прибыль = расчетная цена x маржа"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Средняя скорость доставки"}, "sbi_alibaba_cn_kj_qtfy": {"message": "другая плата"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Прочие затраты = расчетная цена x коэффициент прочих затрат"}, "sbi_alibaba_cn_kj_spjg": {"message": "Цена"}, "sbi_alibaba_cn_kj_spzl": {"message": "Масса"}, "sbi_alibaba_cn_kj_szd": {"message": "Место расположения"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Шт"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Шт"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "<PERSON>р<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Покупатели"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Шт"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Наборы"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>и"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Нет цены"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "цена"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Доступен для продажи"}, "sbi_aliprice_alibaba_cn__moren": {"message": "дефолт"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Конечно"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Продажи"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "мебель"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "легкая закуска"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "макияж"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Нижнее белье"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Аксессуары"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Бутилированный напиток"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Другие"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Юбка"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Куртка"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Электроника"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Игрушка"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Баг<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Низ"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "обувь"}, "sbi_aliprice_cate__apparel": {"message": "Одежда"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Автомобили и мотоциклы"}, "sbi_aliprice_cate__beauty_health": {"message": "Красота и Здоровье"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Мобильные телефоны и телекоммуникации"}, "sbi_aliprice_cate__computer_office": {"message": "Компьютер и Офис"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Бытовая электроника"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Образование и офисные принадлежности"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Электронные компоненты и принадлежности"}, "sbi_aliprice_cate__furniture": {"message": "Мебель"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Наращивание волос и парики"}, "sbi_aliprice_cate__home_garden": {"message": "Дом и Сад"}, "sbi_aliprice_cate__home_improvement": {"message": "Обустройство дома"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Ювелирные изделия и аксессуары"}, "sbi_aliprice_cate__luggage_bags": {"message": "Багаж и сумки"}, "sbi_aliprice_cate__mother_kids": {"message": "Мать и дети"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Новинка и специальное использование"}, "sbi_aliprice_cate__security_protection": {"message": "Безопасность и защита"}, "sbi_aliprice_cate__shoes": {"message": "обувь"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Спорт и развлечения"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Игрушки и хобби"}, "sbi_aliprice_cate__watches": {"message": "<PERSON>а<PERSON>ы"}, "sbi_aliprice_cate__weddings_events": {"message": "Свадьбы и События"}, "sbi_btn_capture_txt": {"message": "Захватить"}, "sbi_btn_source_now_txt": {"message": "Источник сейчас"}, "sbi_button__chat_with_me": {"message": "Общайтесь со мной"}, "sbi_button__contact_supplier": {"message": "Кон<PERSON><PERSON><PERSON>т"}, "sbi_button__hide_on_this_site": {"message": "Не показывать на этом сайте"}, "sbi_button__open_settings": {"message": "Настроить поиск по изображению"}, "sbi_capture_shortcut_tip": {"message": "или нажмите клавишу «Ввод» на клавиатуре"}, "sbi_capturing_tip": {"message": "Capturing"}, "sbi_composed_rating_45": {"message": "4,5–5,0 звезд"}, "sbi_crop_and_search": {"message": "Поиск"}, "sbi_crop_start": {"message": "Использовать скриншот"}, "sbi_err_captcha_action": {"message": "Проверять"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Требуется проверка. Загрузите изображение для проверки. (Просмотрите $video_tutorial$ или попробуйте удалить файлы cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Необычный трафик, пожалуйста, подтвердите"}, "sbi_err_captcha_for_taobao": {"message": "Taobao просит вас подтвердить, пожалуйста, загрузите изображение вручную и выполните поиск, чтобы проверить его. Эта ошибка связана с новой политикой проверки \"Поиск по изображению на TaoBao\". Мы рекомендуем вам слишком часто проверять жалобы на Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "Обратная связь"}, "sbi_err_captcha_msg": {"message": "$platform$ требует, чтобы вы загрузили изображение для поиска или прошли проверку безопасности, чтобы снять ограничения поиска.", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Проверьте, последняя ли это версия"}, "sbi_err_cookie_btn_clear": {"message": "Очистить файлы cookie"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Попробуйте очистить файлы cookie 1688? (Необходимо снова войти в систему)"}, "sbi_err_desperate_feature_pdd": {"message": "Функция поиска изображений была перенесена в расширение Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Как повысить вероятность успеха поиска по фото?"}, "sbi_err_img_undersize": {"message": "Изображение > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Авторизуйтесь $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Авторизоваться"}, "sbi_err_low_version": {"message": "Установите последнюю версию ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Скачать"}, "sbi_err_need_help": {"message": "Нужна помощь"}, "sbi_err_network": {"message": "Ошибка сети. Убедитесь, что вы можете посетить веб-сайт."}, "sbi_err_not_low_version": {"message": "Последняя версия уже установлена ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Попробуй снова"}, "sbi_err_try_again_action": {"message": "Попробуй снова"}, "sbi_err_visit_and_try": {"message": "Попробуйте еще раз или посетите $website$, чтобы повторить попытку.", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Посетите главную страницу $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Загрузка не удалась, пожалуйста, обновите страницу и попробуйте снова."}, "sbi_kuajing_filter_area": {"message": "Площадь"}, "sbi_kuajing_filter_au": {"message": "Австралия"}, "sbi_kuajing_filter_btn_confirm": {"message": "Подтверждать"}, "sbi_kuajing_filter_de": {"message": "Германия"}, "sbi_kuajing_filter_destination_country": {"message": "Страна назначения"}, "sbi_kuajing_filter_es": {"message": "Испания"}, "sbi_kuajing_filter_estimate": {"message": "Оценивать"}, "sbi_kuajing_filter_estimate_price": {"message": "Ориентировочная цена"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Формула расчетной цены = (цена товара + международные логистические перевозки) / (1 - норма прибыли - другое соотношение затрат)"}, "sbi_kuajing_filter_fr": {"message": "Франция"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Введите ключевые слова, соответствующие названию"}, "sbi_kuajing_filter_logistics": {"message": "Шаблон логистики"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Авиапочта Китая"}, "sbi_kuajing_filter_logistics_discount": {"message": "Скидка на логистику"}, "sbi_kuajing_filter_logistics_epacket": {"message": "эпакет"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Международные логистические перевозки = (вес x цена доставки + регистрационный сбор) x (1 - скидка)"}, "sbi_kuajing_filter_others_fee": {"message": "Другой сбор"}, "sbi_kuajing_filter_profit_percent": {"message": "Рентабельность"}, "sbi_kuajing_filter_prop": {"message": "Атрибуты"}, "sbi_kuajing_filter_ru": {"message": "Россия"}, "sbi_kuajing_filter_total": {"message": "Совпадение $count$ похожих товаров", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "СОЕДИНЕННОЕ КОРОЛЕВСТВО"}, "sbi_kuajing_filter_usa": {"message": "Америка"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> оптом"}, "sbi_msg_no_result": {"message": "Результатов не найдено,войдите на $loginSite$ или попробуйте другое изображение", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Временно недоступно для Safari, используйте $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Браузер Chrome и его расширения"}, "sbi_msg_no_result_reinstall_l1": {"message": "Ничего не найдено, войдите на $loginSite$ или попробуйте другое изображение, или переустановите $latestExtUrl$ ", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Последняя версия", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Выбранная область"}, "sbi_shipping_": {"message": "Доставка в тот же день"}, "sbi_specify_category": {"message": "Укажите категорию:"}, "sbi_start_crop": {"message": "Выбрать район"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 за границей"}, "sbi_tutorial_btn_more": {"message": "Больше способов"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Отображаются купоны Taoбao"}, "sbi_txt__empty_retry": {"message": "Извините, результатов не найдено, попробуйте еще раз."}, "sbi_txt__min_order": {"message": "Минимум приказ"}, "sbi_visiting": {"message": "Просмотр"}, "sbi_yiwugo__jiagexiangtan": {"message": "Связаться с продавцом"}, "sbi_yiwugo__qigou": {"message": "$num$ штук (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Звезды"}, "searchByImage_screenshot": {"message": "Скриншот в один клик"}, "searchByImage_search": {"message": "Поиск одинаковых предметов в один клик"}, "searchByImage_size_type": {"message": "Размер файла не может превышать $num$ МБ, только $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Анализировать изображение"}, "search_by_image_progress_searching": {"message": "Поиск товаров"}, "search_by_image_progress_sending": {"message": "Отправка изображения"}, "search_by_image_response_rate": {"message": "Частота ответов: $responseRate$ покупателей, которые связались с этим поставщиком, получили ответ в течение $responseInHour$ часов.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Поиск по ключевым словам:"}, "select_country_language_modal_title_country": {"message": "Страна"}, "select_country_language_modal_title_language": {"message": "Язык"}, "select_country_region_modal_title": {"message": "Выберите страну /регион"}, "select_language_modal_title": {"message": "Выберите язык:"}, "select_shop": {"message": "Выберите магазин"}, "sellers_count": {"message": "Количество продавцов на текущей странице"}, "sellers_count_per_page": {"message": "Количество продавцов на текущей странице"}, "service_score": {"message": "Комплексный рейтинг обслуживания"}, "set_shortcut_keys": {"message": "Установить сочетания клавиш"}, "setting_logo_title": {"message": "Торговый помощник"}, "setting_modal_options_position_title": {"message": "Расположение"}, "setting_modal_options_position_value_left": {"message": "Слева"}, "setting_modal_options_position_value_right": {"message": "Справа"}, "setting_modal_options_theme_title": {"message": "Цвет темы"}, "setting_modal_options_theme_value_dark": {"message": "Темно"}, "setting_modal_options_theme_value_light": {"message": "Светло"}, "setting_modal_title": {"message": "Настройки"}, "setting_options_country_title": {"message": "Страна / Регион"}, "setting_options_hover_zoom_desc": {"message": "«Наведите курсор, чтобы увеличить»"}, "setting_options_hover_zoom_title": {"message": "Наведите и увеличьте"}, "setting_options_jd_coupon_desc": {"message": "Найден купон на JD.com"}, "setting_options_jd_coupon_title": {"message": "Купон JD.com"}, "setting_options_language_title": {"message": "Язык"}, "setting_options_price_drop_alert_desc": {"message": "Как только цена на товары в «Моём Избранном» упадёт, вам придёт уведомление."}, "setting_options_price_drop_alert_title": {"message": "Уведомление о падении цен"}, "setting_options_price_history_on_list_page_desc": {"message": "Отображать информацию о цене на странице поиска товаров"}, "setting_options_price_history_on_list_page_title": {"message": "История цен (страница списка)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Отображать информацию о историии цен на странице товара"}, "setting_options_price_history_on_produt_page_title": {"message": "История цен (страница подробностей)"}, "setting_options_sales_analysis_desc": {"message": "Поддерживать статистику цены, объема продаж, количества продавцов и коэффициента продаж магазина на странице списка товаров $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Анализ продаж"}, "setting_options_save_success_msg": {"message": "ОТПРАВИТЬ"}, "setting_options_tacking_price_title": {"message": "Предупреждение об изменении цены"}, "setting_options_value_off": {"message": "Вы."}, "setting_options_value_on": {"message": "В."}, "setting_pkg_quick_view_desc": {"message": "Поддержка: 1688 и Таобао"}, "setting_saved_message": {"message": "Изменения успешно сохранены"}, "setting_section_enable_platform_title": {"message": "Вкл/выкл"}, "setting_section_setting_title": {"message": "Настройки"}, "setting_section_shortcuts_title": {"message": "Быстрые доступы :"}, "settings_aliprice_agent__desc": {"message": "Отображается на странице сведений о продукте $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Купи для меня"}, "settings_copy_link__desc": {"message": "Отображается на странице товара"}, "settings_copy_link__title": {"message": "Кнопка «Копировать» и заголовок поиска"}, "settings_currency_desc__for_detail": {"message": "Работает на странице товара 1688"}, "settings_currency_desc__for_list": {"message": "Поиск по фото (включая 1688/1688 за рубежом / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Выберите цену"}, "settings_currency_desc_display_for_list": {"message": "Показан при поиске изображений (включая 1688/1688 за рубежом/Taobao)"}, "settings_currency_rate_desc": {"message": "Обновление обменного курса с \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Банк Китая"}, "settings_download_images__desc": {"message": "Поддержка загрузки изображений с $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "Кнопка загрузки фото"}, "settings_download_reviews__desc": {"message": "Отображается на странице сведений о продукте $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Скачать изображения для обзора"}, "settings_google_translate_desc": {"message": "Щелкните правой кнопкой мыши, чтобы открыть панель перевода Google"}, "settings_google_translate_title": {"message": "перевод веб-страницы"}, "settings_historical_trend_desc": {"message": "Отображать в правом нижнем углу изображения на странице списка товаров"}, "settings_modal_btn_more": {"message": "Больше настроек"}, "settings_productInfo_desc": {"message": "Отобразить более подробную информацию о продукте на странице списка продуктов. Включение этого параметра может увеличить нагрузку на компьютер и вызвать задержку страницы. Если это влияет на производительность, рекомендуется отключить это."}, "settings_product_recommend__desc": {"message": "Отображается под основным изображением на странице сведений о продукте $platforms$.", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Рекомендуемые продукты"}, "settings_research_desc": {"message": "Запросить более подробную информацию на странице списка продуктов"}, "settings_sbi_add_to_list": {"message": "Добавить в $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Миниатюра результатов поиска по фото"}, "settings_sbi_remove_from_list": {"message": "Удалить из $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Добавить в черный список"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Отрегулируйте положение входа"}, "settings_search_by_image_blacklist_desc": {"message": "Не показывать значок на сайтах в черном списке."}, "settings_search_by_image_blacklist_title": {"message": "Черный список"}, "settings_search_by_image_bottom_left": {"message": "Слева внизу"}, "settings_search_by_image_bottom_right": {"message": "Справа внизу"}, "settings_search_by_image_clear_blacklist": {"message": "Очистить черный список"}, "settings_search_by_image_detail_page_icon_title": {"message": "Миниатюра"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "Больше"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Меньше"}, "settings_search_by_image_display_button_desc": {"message": "Один щелчок по значку для поиска по изображению"}, "settings_search_by_image_display_button_title": {"message": "Значок на фото"}, "settings_search_by_image_sourece_websites_desc": {"message": "Найдите исходный продукт на этих сайтах"}, "settings_search_by_image_sourece_websites_title": {"message": "Результат поиска по фото"}, "settings_search_by_image_top_left": {"message": "Слева вверху"}, "settings_search_by_image_top_right": {"message": "Справа вверху"}, "settings_search_keyword_on_x__desc": {"message": "Текст для поиска на $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Показывать значок $platform$ при выделении текста", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Попробуйте найти схожие товары на этих сайтах (максимум до 5)"}, "settings_similar_products_title": {"message": "Найдите такой же продукт"}, "settings_toolbar_expand_title": {"message": "Пла<PERSON>ин свернуть"}, "settings_top_toolbar_desc": {"message": "Панель поиска вверху страницы"}, "settings_top_toolbar_title": {"message": "Панель поиска"}, "settings_translate_search_desc": {"message": "Переведи на китайский и найди."}, "settings_translate_search_title": {"message": "Многоязычный поиск"}, "settings_translator_contextmenu_title": {"message": "Захват для перевода"}, "settings_translator_title": {"message": "Перевести"}, "shai_xuan_dao_chu": {"message": "Фильтр для экспорта"}, "shai_xuan_zi_duan": {"message": "Поля фильтра"}, "shang_jia_shi_jian": {"message": "На полке"}, "shang_pin_biao_ti": {"message": "название продукта"}, "shang_pin_dui_bi": {"message": "Сравнение продуктов"}, "shang_pin_lian_jie": {"message": "ссылка на продукт"}, "shang_pin_xin_xi": {"message": "Информация о продукте"}, "share_modal__content": {"message": "Поделись с друзьями"}, "share_modal__disable_for_while": {"message": "Я  ни чем не хочу делиться"}, "share_modal__title": {"message": "Тебе нравится ААА?"}, "sheng_yu_ku_cun": {"message": "Осталось"}, "shi_fou_ke_ding_zhi": {"message": "Это настраиваемый?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Сертифицированный поставщик"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Сертифицированные поставщики"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Торговая гарантия"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Торговые гарантии"}, "shipping_fee": {"message": "Стоимость доставки"}, "shop_followers": {"message": "Подписчики магазина"}, "shou_qi": {"message": "Меньше"}, "similar_products_warn_max_platforms": {"message": "Макс до 5"}, "sku_calc_price": {"message": "Расчетная цена"}, "sku_calc_price_settings": {"message": "Настройки расчетной цены"}, "sku_formula": {"message": "Формула"}, "sku_formula_desc": {"message": "Описание формулы"}, "sku_formula_desc_text": {"message": "Поддерживает сложные математические формулы, где исходная цена представлена ​​как A, а фрахт представлен как B\n\n<br/>\n\nПоддерживает скобки (), плюс +, минус -, умножение * и деление /\n\n<br/>\n\nПример:\n\n<br/>\n\n1. Чтобы получить 1,2 раза исходной цены, а затем добавить фрахт, формула выглядит так: A*1,2+B\n\n<br/>\n\n2. Чтобы получить исходную цену плюс 1 юань, затем умножьте на 1,2 раза, формула выглядит так: (A+1)*1,2\n\n<br/>\n\n3. Чтобы получить исходную цену плюс 10 юаней, затем умножьте на 1,2 раза, а затем вычтите 3 юаня, формула выглядит так: (A+10)*1,2-3"}, "sku_in_stock": {"message": "В наличии"}, "sku_invalid_formula_format": {"message": "Неверный формат формулы"}, "sku_inventory": {"message": "Запасы"}, "sku_link_copy_fail": {"message": "Скопировано успешно, характеристики и атрибуты артикула не выбраны."}, "sku_link_copy_success": {"message": "Скопировано успешно, выбраны характеристики и атрибуты артикула."}, "sku_list": {"message": "Список SKU"}, "sku_min_qrder_qty": {"message": "Минимальное количество заказа"}, "sku_name": {"message": "Название SKU"}, "sku_no": {"message": "Количество"}, "sku_original_price": {"message": "Исходная цена"}, "sku_price": {"message": "Цена SKU"}, "stop_track_time_label": {"message": "Срок отслеживания:"}, "suo_zai_di_qu": {"message": "расположение"}, "tab_pkg_quick_view": {"message": "Мони<PERSON><PERSON>р Логистики"}, "tab_product_details_price_history": {"message": "история"}, "tab_product_details_reviews": {"message": "Отзывы"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "Схожие"}, "total_days_listed_per_product": {"message": "Сумма дней на полке ÷ Количество товаров"}, "total_items": {"message": "Общее количество товаров"}, "total_price_per_product": {"message": "Сумма цен ÷ Количество товаров"}, "total_rating_per_product": {"message": "Сумма рейтингов ÷ Количество товаров"}, "total_revenue": {"message": "Общий доход"}, "total_revenue40_items": {"message": "Общий доход от $amount$ товаров на текущей странице", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Общие продажи"}, "total_sales40_items": {"message": "Общие продажи $amount$ товаров на текущей странице", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Трек за: 1 год"}, "track_for_3_months": {"message": "Трек за: 3 месяца"}, "track_for_6_months": {"message": "Трек за: 6 месяцев"}, "tracking_price_email_add_btn": {"message": "Доб<PERSON><PERSON><PERSON><PERSON><PERSON> Email"}, "tracking_price_email_edit_btn": {"message": "Изменить Email"}, "tracking_price_email_intro": {"message": "Мы сообщим вам по электронной почте."}, "tracking_price_email_invalid": {"message": "Пожалуйста, предоставьте действительный Email"}, "tracking_price_email_verified_desc": {"message": "Теперь вы можете получать наши уведомления о снижении цен."}, "tracking_price_email_verified_title": {"message": "Успешно подтверждено"}, "tracking_price_email_verify_desc_line1": {"message": "Мы отправили ссылку для подтверждения на ваш Email"}, "tracking_price_email_verify_desc_line2": {"message": "пожалуйста, проверьте свою электронную почту"}, "tracking_price_email_verify_title": {"message": "Подтвердить Email"}, "tracking_price_web_push_notification_intro": {"message": "Для настольных: <PERSON><PERSON><PERSON> может отслеживать любой продукт для вас и отправлять вам веб-уведомления, когда цена меняется."}, "tracking_price_web_push_notification_title": {"message": "Веб-уведомления"}, "translate_im__login_required": {"message": "Переведено AliPrice, войдите в $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Переведено и скопировано в буфер обмена, но из-за ограничений Aliwangwang вам нужно вставить его вручную!"}, "translate_im__send": {"message": "Перевести и отправить"}, "translate_search": {"message": "Перевести и поиск"}, "translation_originals_translated": {"message": "Оригинальный и китайский"}, "translation_translated": {"message": "Китайский"}, "translator_btn_capture_txt": {"message": "Перевести"}, "translator_language_auto_detect": {"message": "Определен автоматически"}, "translator_language_detected": {"message": "Обнаруж<PERSON>н"}, "translator_language_search_placeholder": {"message": "Язык поиска"}, "try_again": {"message": "Попробуйте еще раз"}, "tu_pian_chi_cun": {"message": "Размер изображения:"}, "tu_pian_lian_jie": {"message": "Ссылка на изображение"}, "tui_huan_ti_yan": {"message": "Возвратный опыт"}, "tui_huan_ti_yan__desc": {"message": "Оценить послепродажные показатели продавцов"}, "tutorial__show_all": {"message": "Все функции"}, "tutorial_ae_popup_title": {"message": "Прикрепите расширение, откройте Алиэкспресс"}, "tutorial_aliexpress_reviews_analysis": {"message": "Анализ отзывов AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Поддержка USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Доставка в Корею/Японию/материковый Китай."}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 поддерживает зарубежные покупки"}, "tutorial_auto_apply_coupon_title": {"message": "Автоматическое применение купона"}, "tutorial_btn_end": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tutorial_btn_example": {"message": "Пример"}, "tutorial_btn_have_a_try": {"message": "Хорошо, попробую"}, "tutorial_btn_next": {"message": "Дальше"}, "tutorial_btn_see_more": {"message": "Более"}, "tutorial_compare_products": {"message": "Сравните с таким же стилем"}, "tutorial_currency_convert_title": {"message": "конвертация обменного курса"}, "tutorial_export_shopping_cart": {"message": "Экспорт CSV, поддержка Taobao и 1688"}, "tutorial_export_shopping_cart_title": {"message": "экспортная тележка"}, "tutorial_price_history_pro": {"message": "Отображается на странице сведений о продукте.\nПоддержка Shopee, Lazada, Amazon и Ebay"}, "tutorial_price_history_pro_title": {"message": "Полная история цен за год и история заказов"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Скриншот поиска в том же стиле"}, "tutorial_translate_search": {"message": "Перевести в поиск"}, "tutorial_translate_search_and_package_tracking": {"message": "Перевести текст для поиска и отслеживание посылок"}, "unit_bao": {"message": "шт."}, "unit_ben": {"message": "шт."}, "unit_bi": {"message": "заказы"}, "unit_chuang": {"message": "шт."}, "unit_dai": {"message": "шт."}, "unit_dui": {"message": "пара"}, "unit_fen": {"message": "шт."}, "unit_ge": {"message": "шт."}, "unit_he": {"message": "шт."}, "unit_jian": {"message": "шт."}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "шт."}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "пара"}, "unit_tai": {"message": "шт."}, "unit_ti": {"message": "шт."}, "unit_tiao": {"message": "шт."}, "unit_xiang": {"message": "шт."}, "unit_zhang": {"message": "шт."}, "unit_zhi": {"message": "шт."}, "verify_contact_support": {"message": "Связаться со службой поддержки"}, "verify_human_verification": {"message": "Проверка человеком"}, "verify_unusual_access": {"message": "Обнаружен необычный доступ"}, "view_history_clean_all": {"message": "Очистить все"}, "view_history_clean_all_warring": {"message": "Очистить все просмотренные записи?"}, "view_history_clean_all_warring_title": {"message": "Предупреждение"}, "view_history_viewd": {"message": "Рассматриваемые"}, "website": {"message": "веб-сайт"}, "weight": {"message": "<PERSON>е<PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Не удалось получить данные"}, "wu_liu_shi_xiao": {"message": "Своевременная отгрузка"}, "wu_liu_shi_xiao__desc": {"message": "Скорость сбора за 48 часов и скорость выполнения магазина продавца"}, "xia_dan_jia": {"message": "Окончательная цена"}, "xian_xuan_ze_product_attributes": {"message": "Выберите атрибуты продукта"}, "xiao_liang": {"message": "Объем продаж"}, "xiao_liang_zhan_bi": {"message": "Процент от объема продаж"}, "xiao_shi": {"message": "$num$ часов", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Доход"}, "xiao_shou_e_zhan_bi": {"message": "Процент от дохода"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Выберите $amount$ записи", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Выбор"}, "yi_ding_zai_zuo_ce": {"message": "Закреплено"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Все товары загружены"}, "yi_nian_xiao_liang": {"message": "Годовой объем продаж"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Доля годового объема продаж"}, "yi_nian_xiao_shou_e": {"message": "Годовой оборот"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Доля годового оборота"}, "yi_shua_xin": {"message": "Обновленные"}, "yin_cang_xiang_tong_dian": {"message": "скрыть сходство"}, "you_xiao_liang": {"message": "С объемом продаж"}, "yu_ji_dao_da_shi_jian": {"message": "Предполагаемое время прибытия"}, "yuan_gong_ren_shu": {"message": "Количество работников"}, "yue_cheng_jiao": {"message": "Ежемесячный объем"}, "yue_dai_xiao": {"message": "Дропши<PERSON><PERSON>инг"}, "yue_dai_xiao__desc": {"message": "Дропшиппинг-продажи за последние 30 дней"}, "yue_dai_xiao_pai_xu__desc": {"message": "Продажи по дропшиппингу за последние 30 дней, отсортированы в порядке убывания."}, "yue_xiao_liang__desc": {"message": "Объем продаж за последние 30 дней"}, "zhan_kai": {"message": "Больше"}, "zhe_kou": {"message": "Скидка"}, "zhi_chi_yi_jian_dai_fa": {"message": "Дропши<PERSON><PERSON>инг"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Бесплатная доставка"}, "zhi_fu_ding_dan_shu": {"message": "Оплаченные заказы"}, "zhi_fu_ding_dan_shu__desc": {"message": "Количество заказов на этот товар (30 дней)"}, "zhu_ce_xing_zhi": {"message": "Характер регистрации"}, "zi_ding_yi_tiao_jian": {"message": "Пользовательские условия"}, "zi_duan": {"message": "Поля"}, "zi_ti_xiao_liang": {"message": "Продажи вариа<PERSON><PERSON>ов"}, "zong_he_fu_wu_fen": {"message": "Общая оценка"}, "zong_he_fu_wu_fen__desc": {"message": "Общий рейтинг сервиса продавца"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON>ей<PERSON>инг"}, "zong_he_ti_yan_fen": {"message": "<PERSON>ей<PERSON>инг"}, "zong_he_ti_yan_fen_3": {"message": "Ниже 4 звезд"}, "zong_he_ti_yan_fen_4": {"message": "4–4,5 звезды"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5–5,0 звезд"}, "zong_he_ti_yan_fen_5": {"message": "5 звезд"}, "zong_ku_cun": {"message": "Об<PERSON>ий запас"}, "zong_xiao_liang": {"message": "Тотальная распродажа"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3-минутная частота ответов за последние 30 дней"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Скорость восстановления 48 часов за последние 30 дней"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48 часов производительности за последние 30 дней"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Торговый рекорд(30 дней)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Торговый рекорд(30 дней)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Количество споров за последние 30 дней"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Процент возврата средств за качество за последние 30 дней"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Количество платежных поручений за последние 30 дней"}}