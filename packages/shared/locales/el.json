{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Διασυνοριακό Hot Selling Spot"}, "1688_shi_li_ren_zheng": {"message": "Πιστοποίη<PERSON>η αντοχής 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Πάνω από 1 χρόνο"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Παράδοση εντός 24 ωρών"}, "24H_lan_shou_lv": {"message": "24ωρη τιμή συσκευασίας"}, "30D_shang_xin": {"message": "Μην<PERSON><PERSON><PERSON><PERSON><PERSON> Νέες Αφίξεις"}, "30d_sales": {"message": "Μηνιαίες πωλήσεις:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Απάντηση εντός 3 λεπτών."}, "3Min_xiang_ying_lv__desc": {"message": "Το ποσοστό των αποτελεσματικ<PERSON>ν απαντήσεων της <PERSON>wang σε μηνύματα ερωτήματος αγοραστή εντός 3 λεπτών τις τελευταίες 30 ημέρες"}, "48H": {"message": "48Η"}, "48H_fa_huo": {"message": "Παράδοση εντός 48 ωρών"}, "48H_lan_shou_lv": {"message": "Τιμή συσκευασίας 48 ωρών"}, "48H_lan_shou_lv__desc": {"message": "Αναλογία του αριθμού παραγγελίας που παραλήφθηκε εντός 48 ωρών προς τον συνολικό αριθμό παραγγελιών"}, "48H_lv_yue_lv": {"message": "Ρυθμός απόδοσης 48 ωρών"}, "48H_lv_yue_lv__desc": {"message": "Αναλογία του αριθμού παραγγελίας που παραλήφθηκε ή παραδόθηκε εντός 48 ωρών προς τον συνολικό αριθμό παραγγελιών"}, "72H": {"message": "72Η"}, "7D_shang_xin": {"message": "Εβδομαδια<PERSON><PERSON>ς Νέες Αφίξεις"}, "7D_wu_li_you": {"message": "7 ημέρες χωρίς φροντίδα"}, "ABS_title_text": {"message": "Αυτή η καταχώριση περιλαμβάνει μια ιστορία επωνυμίας"}, "AC_title_text": {"message": "Αυτή η καταχώριση έχει το σήμα Επιλογή του Amazon"}, "A_title_text": {"message": "Αυτή η καταχώριση έχει μια σελίδα περιεχομένου A+"}, "BS_title_text": {"message": "Αυτή η καταχώριση κατατάσσεται ως το $num$ Best Seller στην κατηγορία $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) σημαίνει ότι αυτή η καταχώριση είναι μέρος μιας εκδήλωσης \"προώθησης 7 ημερών\""}, "NR_title_text": {"message": "Αυτή η καταχώριση κατατάσσεται ως η νέα έκδοση $num$ στην κατηγορία $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Αυτή η καταχώριση έχει μια διαφήμιση βίντεο, έναν τύπο διαφήμισης PPC που εμφανίζεται συνήθως στη μέση των αποτελεσμάτων αναζήτησης"}, "SB_title_text": {"message": "Αυτή η καταχώριση έχει μια διαφήμιση επωνυμίας, έναν τύπο διαφήμισης PPC που εμφανίζεται συνήθως στην κορυφή ή στο κάτω μέρος των αποτελεσμάτων αναζήτησης"}, "SP_title_text": {"message": "Αυτή η καταχώριση έχει μια διαφήμιση προϊόντων διαφημιζόμενων"}, "V_title_text": {"message": "Αυτή η καταχώριση έχει μια εισαγωγή βίντεο"}, "advanced_research": {"message": "Προηγμένη Έρευνα"}, "agent_ds1688___my_order": {"message": "Οι παραγγελίες μου"}, "agent_ds1688__add_to_cart": {"message": "Αγορά στο εξωτερικό"}, "agent_ds1688__cart": {"message": "Καλάθι αγορών"}, "agent_ds1688__desc": {"message": "Παρέχετ<PERSON><PERSON> από το 1688. Υποστηρίζει απευθείας αγορά από το εξωτερικ<PERSON>, πληρωμή σε USD και παράδοση στην αποθήκη διαμετακόμισης στην Κίνα."}, "agent_ds1688__freight": {"message": "Υπολογιστής κόστους αποστολής"}, "agent_ds1688__help": {"message": "Βοήθεια"}, "agent_ds1688__packages": {"message": "Φορτωτική"}, "agent_ds1688__profile": {"message": "Προσωπ<PERSON><PERSON><PERSON>έντρο"}, "agent_ds1688__warehouse": {"message": "Η αποθήκη μου"}, "ai_comment_analysis_advantage": {"message": "Πλεονεκτήματα"}, "ai_comment_analysis_ai": {"message": "Ανάλυση ανασκόπησης AI"}, "ai_comment_analysis_available": {"message": "Διαθέσιμος"}, "ai_comment_analysis_balance": {"message": "Ανεπαρκή νομίσματα, συμπληρώστε"}, "ai_comment_analysis_behavior": {"message": "Η ΣΥΜΠΕΡΙΦΟΡΑ"}, "ai_comment_analysis_characteristic": {"message": "Χ<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ιστικά πλήθους"}, "ai_comment_analysis_comment": {"message": "Το προϊόν δεν έχει αρκετές κριτικές για να βγάλετε ακριβή συμπεράσματα, επιλέξτε ένα προϊόν με περισσότερες κριτικές."}, "ai_comment_analysis_consume": {"message": "Εκτιμώμενη Κατανάλωση"}, "ai_comment_analysis_default": {"message": "Προεπιλεγμένες κριτικές"}, "ai_comment_analysis_desire": {"message": "Προσδο<PERSON><PERSON><PERSON>ς πελατών"}, "ai_comment_analysis_disadvantage": {"message": "Μειονεκτήματα"}, "ai_comment_analysis_free": {"message": "Δωρεάν προσπάθειες"}, "ai_comment_analysis_freeNum": {"message": "Θα χρησιμοποιηθεί 1 δωρεάν πίστωση"}, "ai_comment_analysis_go_recharge": {"message": "Μεταβείτε στην ανανέωση"}, "ai_comment_analysis_intelligence": {"message": "Έξυπνη ανάλυση αναθεώρησης"}, "ai_comment_analysis_location": {"message": "Τοποθεσία"}, "ai_comment_analysis_motive": {"message": "Κίνητρο αγοράς"}, "ai_comment_analysis_network_error": {"message": "Σφάλμα δικτύου, δοκιμάστε ξανά"}, "ai_comment_analysis_normal": {"message": "Κριτικές φωτογραφιών"}, "ai_comment_analysis_number_reviews": {"message": "Αριθμός κριτικών: $num$, Εκτιμώμενη κατανάλωση: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Γενικά σχόλια"}, "ai_comment_analysis_percentage": {"message": "Ποσοστό"}, "ai_comment_analysis_problem": {"message": "Προβλήματα με την πληρωμή"}, "ai_comment_analysis_reanalysis": {"message": "αναλύστε ξανά"}, "ai_comment_analysis_reason": {"message": "Λόγ<PERSON>"}, "ai_comment_analysis_recharge": {"message": "Ξεχειλίζω"}, "ai_comment_analysis_recharged": {"message": "Έχω συμπληρώσει"}, "ai_comment_analysis_retry": {"message": "Ξαναδοκιμάσετε"}, "ai_comment_analysis_scene": {"message": "Σενάριο χρήσης"}, "ai_comment_analysis_start": {"message": "Ξεκινήστε να αναλύετε"}, "ai_comment_analysis_subject": {"message": "Θέματα"}, "ai_comment_analysis_time": {"message": "<PERSON>ρ<PERSON><PERSON><PERSON> χρήσης"}, "ai_comment_analysis_tool": {"message": "Εργαλείο AI"}, "ai_comment_analysis_user_portrait": {"message": "<PERSON>ρο<PERSON><PERSON><PERSON>η"}, "ai_comment_analysis_welcome": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθατε στην ανάλυση αναθεώρησης AI"}, "ai_comment_analysis_year": {"message": "Σχόλια από την περασμένη χρονιά"}, "ai_listing_Exclude_keywords": {"message": "Εξαίρεση λέξεων-κλειδιών"}, "ai_listing_Login_the_feature": {"message": "Απαιτείται σύνδεση για τη λειτουργία"}, "ai_listing_aI_generation": {"message": "γενιά AI"}, "ai_listing_add_automatic": {"message": "Αυτόματο"}, "ai_listing_add_dictionary_new": {"message": "Δημιουργήστε μια νέα βιβλιοθήκη"}, "ai_listing_add_enter_keywords": {"message": "Εισάγετε λέξεις - κλειδιά"}, "ai_listing_add_inputkey_selling": {"message": "Εισαγάγετε ένα σημείο πώλησης και πατήστε $key$ για να ολοκληρώσετε την προσθήκη", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Υπέρβαση του ορίου, έως και $amount$ σημεία πώλησης", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Προσθήκη λέξεων-κλειδιών"}, "ai_listing_add_manually": {"message": "Μη αυτόματη προσθήκη"}, "ai_listing_add_selling": {"message": "Προσθέστε σημεία πώλησης"}, "ai_listing_added_keywords": {"message": "Προστέθηκαν λέξεις-κλειδιά"}, "ai_listing_added_successfully": {"message": "Προστέθηκε με επιτυχία"}, "ai_listing_addexcluded_keywords": {"message": "Εισαγάγετε τις εξαιρούμενες λέξεις-κλειδι<PERSON>, πατήστε enter για να ολοκληρώσετε την προσθήκη."}, "ai_listing_adding_selling": {"message": "Προστέθηκαν σημεία πώλησης"}, "ai_listing_addkeyword_enter": {"message": "Πληκτρολογήστε τις λέξεις του βασικού χαρακτηριστικού και πατήστε enter για να ολοκληρώσετε την προσθήκη"}, "ai_listing_ai_description": {"message": "Βιβλιοθήκη λέξεων περιγραφής AI"}, "ai_listing_ai_dictionary": {"message": "Βιβλιοθήκη λέξεων τίτλου AI"}, "ai_listing_ai_title": {"message": "Τίτλος AI"}, "ai_listing_aidescription_repeated": {"message": "Το όνομα βιβλιοθήκης λέξης περιγραφής AI δεν μπορεί να επαναληφθεί"}, "ai_listing_aititle_repeated": {"message": "Το όνομα της βιβλιοθήκης λέξης τίτλου AI δεν μπορεί να επαναληφθεί"}, "ai_listing_data_comes_from": {"message": "Αυτά τα δεδομένα προέρχονται από:"}, "ai_listing_deleted_successfully": {"message": "Διαγράφηκε με επιτυχία"}, "ai_listing_dictionary_name": {"message": "Όνομα βιβλιοθήκης"}, "ai_listing_edit_dictionary": {"message": "Τροποποίηση βιβλιοθήκης..."}, "ai_listing_edit_word_library": {"message": "Επεξεργαστείτε τη λέξη βιβλιοθήκη"}, "ai_listing_enter_keywords": {"message": "Εισαγάγετε λέξεις-κλειδι<PERSON> και πατήστε $key$ για να ολοκληρώσετε την προσθήκη", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Έχει ξεπεραστεί το όριο, μέγιστο $amount$ λέξεις-κλειδιά", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Εξαιρείται η βιβλιοθήκη λέξεων"}, "ai_listing_generate_characters": {"message": "Δημιουργήστε χαρακτήρες"}, "ai_listing_generation_platform": {"message": "Πλατφόρμα γενιάς"}, "ai_listing_help_optimize": {"message": "Βοηθήστε με να βελτιστοποιήσω τον τίτλο του προϊόντος, ο αρχικός τίτλος είναι"}, "ai_listing_include_selling": {"message": "Άλλα σημεία πώλησης περιλαμβάνουν:"}, "ai_listing_included_keyword": {"message": "Συμπεριλαμβανόμενες λέξεις-κλειδιά"}, "ai_listing_included_keywords": {"message": "Συμπεριλαμβανόμενες λέξεις-κλειδιά"}, "ai_listing_input_selling": {"message": "Εισαγ<PERSON><PERSON><PERSON><PERSON>ε ένα σημείο πώλησης"}, "ai_listing_input_selling_fit": {"message": "Εισαγάγετε σημεία πώλησης για να ταιριάζει με τον τίτλο"}, "ai_listing_input_selling_please": {"message": "Εισαγάγετε τα σημεία πώλησης"}, "ai_listing_intelligently_title": {"message": "Εισαγάγετε το απαιτούμενο περιεχόμενο παραπάνω για να δημιουργήσετε τον τίτλο έξυπνα"}, "ai_listing_keyword_product_title": {"message": "Λέξη-κλειδί τίτλος προϊόντος"}, "ai_listing_keywords_repeated": {"message": "Οι λέξεις-κλειδι<PERSON> δεν μπορούν να επαναληφθούν"}, "ai_listing_listed_selling_points": {"message": "Περιλα<PERSON>βάνονται σημεία πώλησης"}, "ai_listing_long_title_1": {"message": "Περιέχει βασικές πληροφορίες όπως επωνυμία, τύπο προϊόντος, χαρακτηριστικά προϊόντος κ.λπ."}, "ai_listing_long_title_2": {"message": "Με βάση τον τυπικό τίτλο του προϊόντος, προστίθενται λέξεις-κλειδιά που ευνοούν το SEO."}, "ai_listing_long_title_3": {"message": "Εκτός από το ότι περιέχουν την επωνυμία, τον τύπο προϊόντος, τα χαρακτηριστικά του προϊόντος και τις λέξεις-κλ<PERSON><PERSON><PERSON><PERSON><PERSON>, περιλα<PERSON>βάνονται επίσης λέξεις-κλειδιά μακράς ουράς για την επίτευξη υψηλότερης κατάταξης σε συγκεκριμένα, τμηματοποιημένα ερωτήματα αναζήτησης."}, "ai_listing_longtail_keyword_product_title": {"message": "Τίτλος προϊόντος λέξης-κλειδιού μακριάς ουράς"}, "ai_listing_manually_enter": {"message": "Χειροκίνητη εισαγωγή..."}, "ai_listing_network_not_working": {"message": "Το Διαδίκτυο δεν είναι διαθέσιμο, απαιτείται VPN για πρόσβαση στο ChatGPT"}, "ai_listing_new_dictionary": {"message": "Δημιουργία νέας βιβλιοθήκης λέξεων..."}, "ai_listing_new_generate": {"message": "Παράγω"}, "ai_listing_optional_words": {"message": "Προαιρετικές λέξεις"}, "ai_listing_original_title": {"message": "Πρωτότυπος τίτλος"}, "ai_listing_other_keywords_included": {"message": "Άλλες λέξεις-κλειδιά περιλαμβάνονται:"}, "ai_listing_please_again": {"message": "ΠΑΡΑΚΑΛΩ προσπαθησε ξανα"}, "ai_listing_please_select": {"message": "Οι παρακάτω τίτλοι έχουν δημιουργηθεί για εσάς, επιλέξτε:"}, "ai_listing_product_category": {"message": "Κατηγορία προιόντος"}, "ai_listing_product_category_is": {"message": "Η κατηγορία προϊόντων είναι"}, "ai_listing_product_category_to": {"message": "Σε ποια κατηγορία ανήκει το προϊόν;"}, "ai_listing_random_keywords": {"message": "Τυχαίες λέξεις-κλειδιά $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Τυχαία σημεία πώλησης $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Τυχαιοποίηση από τη λέξη βιβλιοθήκη"}, "ai_listing_search_selling": {"message": "Αναζήτηση ανά σημείο πώλησης"}, "ai_listing_select_product_categories": {"message": "Αυτόματη επιλογή κατηγοριών προϊόντων."}, "ai_listing_select_product_selling_points": {"message": "Αυτόματη επιλογή σημείων πώλησης προϊόντων"}, "ai_listing_select_word_library": {"message": "Επιλέξτε βιβλιοθήκη λέξεων"}, "ai_listing_selling": {"message": "Σημεία πώλησης"}, "ai_listing_selling_ask": {"message": "Ποιες άλλες απαιτήσεις σημείων πώλησης υπάρχουν για τον τίτλο;"}, "ai_listing_selling_optional": {"message": "Προαιρε<PERSON>ι<PERSON><PERSON> σημεία πώλησης"}, "ai_listing_selling_repeat": {"message": "Οι πόντοι δεν μπορούν να αντιγραφούν"}, "ai_listing_set_excluded": {"message": "Ορισμός ως εξαιρούμενη βιβλιοθήκη λέξεων"}, "ai_listing_set_include_selling_points": {"message": "Συμπεριλάβετε σημεία πώλησης"}, "ai_listing_set_included": {"message": "Ορισμός ως περιλαμβανόμενη βιβλιοθήκη λέξεων"}, "ai_listing_set_selling_dictionary": {"message": "Ορισμός ως βιβλιοθήκη σημείων πώλησης"}, "ai_listing_standard_product_title": {"message": "Τυπικός τίτλος προϊόντος"}, "ai_listing_translated_title": {"message": "Μετα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Επισκεφτείτε το ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Ποι<PERSON>ς άλλες λέξεις-κλειδι<PERSON> απαιτούνται για τον τίτλο;"}, "aliprice_coupons_apply_again": {"message": "Εφαρμόστε ξανά"}, "aliprice_coupons_apply_coupons": {"message": "Εφαρμόστε κουπόνια"}, "aliprice_coupons_apply_success": {"message": "Βρέθηκε κουπόνι: Εξοικονομήστε $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Δοκι<PERSON><PERSON> κωδικών για τις καλύτερες προσφορές..."}, "aliprice_coupons_applying_desc": {"message": "Αναχώρηση: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Συνεχίστε στο Checkout"}, "aliprice_coupons_found_coupons": {"message": "Βρήκαμε κουπόνια $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Έτοιμοι για ταμείο; Ας βεβαιωθούμε\nέχετε την καλύτερη τιμή!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Αυτοί οι κωδικοί δεν λειτούργησαν.\nΌχι μεγάλος - είσαι ήδη\nνα πάρει την καλύτερη τιμή."}, "aliprice_coupons_toolbar_btn": {"message": "Λάβετε κουπόνια"}, "aliww_translate": {"message": "Μετα<PERSON>ραστής συνομιλ<PERSON><PERSON><PERSON>"}, "aliww_translate_supports": {"message": "Υποστήριξη: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Λέξεις-κλειδιά"}, "amazon_extended_keywords_copy_all": {"message": "Αντιγρά<PERSON><PERSON>ε όλα"}, "amazon_extended_keywords_more": {"message": "Περισσότερο"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Ταξινόμηση κατά αθροιστικές πωλήσεις"}, "an_lei_xing_cha_kan": {"message": "Τύπος"}, "an_yue_dai_xiao_pai_xu": {"message": "Κατάταξη βάσει dropshipping πωλήσεων"}, "apra_btn__cat_name": {"message": "Ανάλυση κριτικών"}, "apra_chart__name": {"message": "Ποσοστό πωλήσεων προϊόντων ανά χώρα"}, "apra_chart__update_at": {"message": "Timeρα ενημέρωσης $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Στατιστι<PERSON><PERSON>ς πωλήσεων χωρών"}, "auto_opening": {"message": "Αυτόματο άνοιγμα σε $num$ δευτερόλεπτα", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {}, "average_days_listed": {"message": "<PERSON><PERSON><PERSON><PERSON> όρος σε ημέρες αποθήκευσης"}, "average_hui_fu_lv": {"message": "<PERSON><PERSON><PERSON><PERSON> ποσοστό απάντησης"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Μέσ<PERSON> επίπεδο προμηθευτή"}, "average_price": {"message": "Μέση τιμή"}, "average_qi_ding_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "average_rating": {"message": "Μέση βαθμολογία"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON><PERSON><PERSON><PERSON> ό<PERSON><PERSON> ε<PERSON>ών πιστοποίησης"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON><PERSON> όρος εσόδων"}, "average_revenue_per_product": {"message": "Συνολικά έσοδα ÷ Αριθμός προϊόντων"}, "average_sales": {"message": "Μέ<PERSON><PERSON>ς πωλήσεις"}, "average_sales_per_product": {"message": "Συνολικές πωλήσεις ÷ Αριθμός προϊόντων"}, "bao_han": {"message": "Περιέχει"}, "bao_zheng_jin": {"message": "Περιθώριο"}, "bian_ti_shu": {"message": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς"}, "biao_ti": {"message": "Τίτλος"}, "blacklist_add_blacklist": {"message": "Αποκλ<PERSON>ισμ<PERSON>ς αυτού του καταστήματος"}, "blacklist_address_incorrect": {"message": "Η διεύθυνση δεν είναι σωστή. Παρακαλώ Έλεγξέ το."}, "blacklist_blacked_out": {"message": "Το κατάστημα έχει αποκλειστεί"}, "blacklist_blacklist": {"message": "Προγράφω"}, "blacklist_no_records_yet": {"message": "Δεν υπάρχει ακόμα εγγραφή!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Μάρκα"}, "btn_aliprice_agent__daigou": {"message": "Διαμεσολαβη<PERSON>ής αγοράς"}, "btn_aliprice_agent__dropshipping": {"message": "Η ναυτιλία πτώσης"}, "btn_have_a_try": {"message": "Κάνε μια προσπάθεια"}, "btn_refresh": {"message": "Φρεσκάρω"}, "btn_try_it_now": {"message": "Δοκίμασέ το τώρα"}, "btn_txt_view_on_aliprice": {"message": "Προβολή στο AliPrice"}, "bu_bao_han": {"message": "Δεν περιέχει"}, "bulk_copy_links": {"message": "Μαζική αντιγραφή συνδέσμων"}, "bulk_copy_products": {"message": "Προϊόντα μαζικής αντιγραφής"}, "cai_gou_zi_xun": {"message": "Εξυπηρέτηση πελατών"}, "cai_gou_zi_xun__desc": {"message": "Πο<PERSON><PERSON><PERSON><PERSON><PERSON> απόκρισης τριών λεπτών του πωλητή"}, "can_ping_lei_xing": {"message": "Τύπος"}, "cao_zuo": {"message": "Λειτουργία"}, "chan_pin_ID": {"message": "Αναγνωριστικό προϊόντος"}, "chan_pin_e_wai_xin_xi": {"message": "Επιπλέον πληροφορίες προϊόντος"}, "chan_pin_lian_jie": {"message": "Σύνδεσμος προϊόντος"}, "cheng_li_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ίδρυσης"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Κάντε κλικ στην επιλογή Αντιγραφή πληροφοριών προϊόντος"}, "commmon_txt_expired": {"message": "έχει λήξει"}, "common__date_range_12m": {"message": "1 χρόνος"}, "common__date_range_1m": {"message": "1 μήνα"}, "common__date_range_1w": {"message": "1 εβδομάδα"}, "common__date_range_2w": {"message": "2 εβδομάδες"}, "common__date_range_3m": {"message": "3 μήνες"}, "common__date_range_3w": {"message": "3 εβδομάδες"}, "common__date_range_6m": {"message": "6 μήνες"}, "common_btn_cancel": {"message": "Ματαίωση"}, "common_btn_close": {"message": "Κλείσε"}, "common_btn_save": {"message": "Αποθηκεύσετε"}, "common_btn_setting": {"message": "Ρύθμιση"}, "common_email": {"message": "ΗΛΕΚΤΡΟΝΙΚΗ ΔΙΕΥΘΥΝΣΗ"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> δεδομένα"}, "common_error_msg_no_result": {"message": "Συγγνώμη, δεν βρέθηκε αποτέλεσμα."}, "common_favorites": {"message": "Αγαπημένα"}, "common_feedback": {"message": "Ανατροφοδότηση"}, "common_help": {"message": "Βοήθεια"}, "common_loading": {"message": "Φόρτωση"}, "common_login": {"message": "Σύνδεση"}, "common_logout": {"message": "Αποσύνδεση"}, "common_no": {"message": "Οχι"}, "common_powered_by_aliprice": {"message": "Με την υποστήριξη του AliPrice.com"}, "common_setting": {"message": "Σύνθεση"}, "common_sign_up": {"message": "Εγγραφείτε"}, "common_system_upgrading_title": {"message": "Αναβάθμιση συστήματος"}, "common_system_upgrading_txt": {"message": "Δοκιμάστε το αργότερα"}, "common_txt__currency": {"message": "Νόμισμα"}, "common_txt__video_tutorial": {"message": "Εκμάθηση βίντεο"}, "common_txt_ago_time": {"message": "$time$ πριν από ημέρες", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "όλοι"}, "common_txt_analysis": {"message": "Ανάλυση"}, "common_txt_basically_used": {"message": "Σχεδ<PERSON>ν ποτέ δεν χρησιμοποιήθηκε"}, "common_txt_biaoti_link": {"message": "Τίτλος + Σύνδεσμος"}, "common_txt_biaoti_link_dian_pu": {"message": "Τίτλος + σύνδεσμος + όνομα καταστήματος"}, "common_txt_blacklist": {"message": "Λίστα των μπλοκαρισμένων"}, "common_txt_cancel": {"message": "Ματαίωση"}, "common_txt_category": {"message": "Κατηγορία"}, "common_txt_chakan": {"message": "Ελε<PERSON><PERSON><PERSON>"}, "common_txt_colors": {"message": "χρωματιστά"}, "common_txt_confirm": {"message": "Επιβεβαιώνω"}, "common_txt_copied": {"message": "Αντιγράφηκε"}, "common_txt_copy": {"message": "αντίγραφο"}, "common_txt_copy_link": {"message": "Αντιγραφ<PERSON> συνδέσμου"}, "common_txt_copy_title": {"message": "Αντιγραφή τίτλου"}, "common_txt_day": {"message": "ουρανός"}, "common_txt_delete": {"message": "Διαγράφω"}, "common_txt_dian_pu_link": {"message": "Αντιγρα<PERSON><PERSON> ονόματος καταστήματος + συνδέσμου"}, "common_txt_download": {"message": "Κατεβάστε"}, "common_txt_downloaded": {"message": "λήψη"}, "common_txt_export_as_csv": {"message": "Εξαγωγή Excel"}, "common_txt_export_as_txt": {"message": "Εξαγωγή Txt"}, "common_txt_fail": {"message": "Αποτυγχ<PERSON>νω"}, "common_txt_format": {"message": "Μορφή"}, "common_txt_get": {"message": "παίρνω"}, "common_txt_incert_selection": {"message": "Αντιστροφή επιλογής"}, "common_txt_install": {"message": "Εγκαθιστώ"}, "common_txt_load_failed": {"message": "Απέτυχε να φορτώσει"}, "common_txt_month": {"message": "μήνα"}, "common_txt_more": {"message": "Περισσότερο"}, "common_txt_new_unused": {"message": "Ολ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, αχρησιμοποίητο"}, "common_txt_next": {"message": "Επόμενο"}, "common_txt_no_limit": {"message": "Απεριόριστος"}, "common_txt_no_noticeable": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ορατές γρατσουνιές ή βρωμιές"}, "common_txt_on_sale": {"message": "Διαθέσιμος"}, "common_txt_opt_in_out": {"message": "On/Off"}, "common_txt_order": {"message": "Σειρά"}, "common_txt_others": {"message": "Οι υπολοιποι"}, "common_txt_overall_poor_condition": {"message": "Συνολικά κακή κατάσταση"}, "common_txt_patterns": {"message": "μοτίβα"}, "common_txt_platform": {"message": "Πλατφόρμες"}, "common_txt_please_select": {"message": "Παρακ<PERSON><PERSON><PERSON> επιλέξτε"}, "common_txt_prev": {"message": "Προηγ"}, "common_txt_price": {"message": "Τιμή"}, "common_txt_privacy_policy": {"message": "Πολιτική Απορρήτου"}, "common_txt_product_condition": {"message": "Κατάσταση προϊόντος"}, "common_txt_rating": {"message": "Εκτίμηση"}, "common_txt_ratings": {"message": "Ακροαματικότητα"}, "common_txt_reload": {"message": "Γεμίζω πάλι"}, "common_txt_reset": {"message": "Επαναφορά"}, "common_txt_review": {"message": "Ανασκόπηση"}, "common_txt_sale": {"message": "Διαθέσιμος"}, "common_txt_same": {"message": "Ιδιο"}, "common_txt_scratches_and_dirt": {"message": "Με γρατζουνιές και βρωμιά"}, "common_txt_search_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτησης"}, "common_txt_select_all": {"message": "Επιλογ<PERSON> όλων"}, "common_txt_selected": {"message": "επιλεγμένο"}, "common_txt_share": {"message": "Μερίδιο"}, "common_txt_sold": {"message": "πωληθεί"}, "common_txt_sold_out": {"message": "Εξαντλημένα"}, "common_txt_some_scratches": {"message": "Μερικές γρατσουνιές και βρωμιές"}, "common_txt_sort_by": {"message": "Ταξινόμηση κατά"}, "common_txt_state": {"message": "Κατάσταση"}, "common_txt_success": {"message": "Επιτυχία"}, "common_txt_sys_err": {"message": "σφάλμα συστήματος"}, "common_txt_today": {"message": "Σήμερα"}, "common_txt_total": {"message": "όλα"}, "common_txt_unselect_all": {"message": "Αντιστροφή επιλογής"}, "common_txt_upload_image": {"message": "Μεταφόρτωση εικόνας"}, "common_txt_visit": {"message": "Επίσκεψη"}, "common_txt_whitelist": {"message": "Λευκή λίστα"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Ετος"}, "common_yes": {"message": "Ναί"}, "compare_tool_btn_clear_all": {"message": "Τα καθαρίζω όλα"}, "compare_tool_btn_compare": {"message": "Συγκρίνω"}, "compare_tool_btn_contact": {"message": "Επικοινωνία"}, "compare_tool_tips_max_compared": {"message": "Προσθέστε έως και $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Διαμόρφωση ειδοποιήσεων"}, "contact_us": {"message": "Επικοινωνήστε μαζί μας"}, "context_menu_screenshot_search": {"message": "Λήψη για αναζήτηση με εικόνα"}, "context_menus_aliprice_search_by_image": {"message": "Αναζήτηση εικόνας στο AliPrice"}, "context_menus_search_by_image": {"message": "Αναζήτηση με εικόνα στο $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Καταγραφή στο $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Λή<PERSON>η για μετάφραση"}, "converter_modal_amount_placeholder": {"message": "Εισαγάγετε το ποσό εδώ"}, "converter_modal_btn_convert": {"message": "μετατρέπω"}, "converter_modal_exchange_rate_source": {"message": "Τα δεδομένα προέρχονται από την ισοτιμία συναλλάγματος $boc$ Χρόνος ενημέρωσης: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "μετατροπή νομίσματος"}, "converter_modal_search_placeholder": {"message": "νόμισμα αναζήτησης"}, "copy_all_contact_us_notice": {"message": "Αυτή η τοποθεσία δεν υποστηρίζεται αυτήν τη στιγμή, επικοινωνήστε μαζί μας"}, "copy_product_info": {"message": "Αντιγράψτε τις πληροφορίες προϊόντος"}, "copy_suggest_search_kw": {"message": "Αντιγρα<PERSON><PERSON> αναπτυσσόμενων λιστών"}, "country__han_gou": {"message": "Νότια Κορέα"}, "country__ri_ben": {"message": "Ιαπωνία"}, "country__yue_nan": {"message": "Βιετνάμ"}, "currency_convert__custom": {"message": "Προσαρμοσμένη συναλλαγματική ισοτιμία"}, "currency_convert__sync_server": {"message": "Συγχρονισμός διακομιστή"}, "dang_ri_fa_huo": {"message": "Αποστολή αυθημερόν"}, "dao_chu_quan_dian_shang_pin": {"message": "Εξαγωγή όλων των προϊόντων καταστήματος"}, "dao_chu_wei_CSV": {"message": "Εξαγωγή"}, "dao_chu_zi_duan": {"message": "Πεδία εξαγωγής"}, "delivery_address": {"message": "Διεύθυνση αποστολής"}, "delivery_company": {"message": "Εταιρεία παράδοσης"}, "di_zhi": {"message": "διεύθυνση"}, "dian_ji_cha_xun": {"message": "Κάντε κλικ για ερώτημα"}, "dian_pu_ID": {"message": "Ταυτότητα καταστήματος"}, "dian_pu_di_zhi": {"message": "Διεύθυνση καταστήματος"}, "dian_pu_lian_jie": {"message": "Σύνδεσμος καταστήματος"}, "dian_pu_ming": {"message": "Όνομα καταστήματος"}, "dian_pu_ming_cheng": {"message": "Όνομα καταστήματος"}, "dian_pu_shang_pin_zong_hsu": {"message": "Συνολικός αριθμός προϊόντων στο κατάστημα: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Πληροφορίες καταστήματος"}, "ding_zai_zuo_ce": {"message": "καρφω<PERSON><PERSON><PERSON>ος στα αριστερά"}, "disable_old_version_tips_disable_btn_title": {"message": "Απενεργοποιήστε την παλιά έκδοση"}, "download_image__SKU_variant_images": {"message": "Εικόνες παραλλαγής SKU"}, "download_image__assume": {"message": "Για παράδειγμα, έχουμε 2 εικόνες, product1.jpg και product2.gif.\nΤο img_{$no$} θα μετονομαστεί σε img_01.jpg, img_02.gif;\nΤο {$group$}_{$no$} θα μετονομαστεί σε main_image_01.jpg, main_image_02.gif.", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Μαζική λήψη"}, "download_image__combined_image": {"message": "Συνδυασμένη εικόνα λεπτομερειών προϊόντος"}, "download_image__continue_downloading": {"message": "Συνεχίστε τη λήψη"}, "download_image__description_images": {"message": "Εικόνες περιγραφής"}, "download_image__download_combined_image": {"message": "Λήψη συνδυασμένης εικόνας λεπτομερειών προϊόντος"}, "download_image__download_zip": {"message": "Λήψη zip"}, "download_image__enlarge_check": {"message": "Υποστηρίζει μόνο εικό<PERSON>ε<PERSON>, JPG, GIF και PNG, μέγιστο μέγεθος μίας εικόνας: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Μεγέθυνση εικόνας"}, "download_image__export": {"message": "Εξαγωγή"}, "download_image__height": {"message": "Υψος"}, "download_image__ignore_videos": {"message": "Το βίντεο αγνοήθηκε, καθ<PERSON>ς δεν είναι δυνατή η εξαγωγή του"}, "download_image__img_translate": {"message": "Μετάφραση εικόνας"}, "download_image__main_image": {"message": "κύρια εικόνα"}, "download_image__multi_folder": {"message": "Πολλα<PERSON><PERSON><PERSON><PERSON>άκελος"}, "download_image__name": {"message": "λήψη εικόνας"}, "download_image__notice_content": {"message": "Μην τσεκάρετε \"Ρωτήστε πού να αποθηκεύσετε κάθε αρχείο πριν από τη λήψη\" στις ρυθμίσεις λήψης του προγράμματος περιήγησής σας!!! Διαφορετικά θα υπάρχουν πολλά παράθυρα διαλόγου."}, "download_image__notice_ignore": {"message": "Μην ζητήσετε ξανά αυτό το μήνυμα"}, "download_image__order_number": {"message": "Σειριακ<PERSON>ς αριθμός {$no$}. Όνομα ομάδας {$group$}. Χρονική σήμανση {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "ΣΦΑΙΡΙΚΗ ΕΙΚΟΝΑ"}, "download_image__prompt_download_zip": {"message": "Υπάρχουν πάρα πολλές εικόνες, καλύτερα να τις κατεβάσετε ως φάκελο zip."}, "download_image__rename": {"message": "Μετονομάζω"}, "download_image__rule": {"message": "Κανόνες ονομασίας"}, "download_image__single_folder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__sku_image": {"message": "Εικόνες SKU"}, "download_image__video": {"message": "βίντεο"}, "download_image__width": {"message": "<PERSON>λ<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Λήψη εικόνας κριτικής"}, "download_reviews__dropdown_title": {"message": "Λήψη εικόνας κριτικής"}, "download_reviews__export_csv": {"message": "εξαγωγή CSV"}, "download_reviews__no_images": {"message": "Δείγμα αντιγράφου: 0 εικόνες για λήψη"}, "download_reviews__no_reviews": {"message": "Δεν υπάρχουν σχόλια διαθέσιμα για λήψη!"}, "download_reviews__notice": {"message": "Υπόδειξη:"}, "download_reviews__notice__chrome_settings": {"message": "Ρυθμίστε το πρόγραμμα περιήγησης Chrome να ρωτά πού να αποθηκεύεται κάθε αρχείο πριν από τη λήψη, ρυθμίστε σε \"Απενεργοποίηση\""}, "download_reviews__notice__wait": {"message": "Ανάλογα με τον αριθμό των κριτικών, ο χρόνος αναμονής μπορεί να είναι μεγαλύτερος"}, "download_reviews__pages_list__all": {"message": "Ολα"}, "download_reviews__pages_list__page": {"message": "Προηγούμενες σελίδες $page$", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON><PERSON><PERSON> επιλογής"}, "export_shopping_cart__csv_filed__details_url": {"message": "Σύνδεσμος προϊόντος"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Σύνδεσμος Echo SKU"}, "export_shopping_cart__csv_filed__images": {"message": "Σύνδεσμος εικόνας"}, "export_shopping_cart__csv_filed__quantity": {"message": "Ποσότητα"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Τιμή"}, "export_shopping_cart__csv_filed__specs": {"message": "Προδιαγραφ<PERSON>ς"}, "export_shopping_cart__csv_filed__store_name": {"message": "Όνομα καταστήματος"}, "export_shopping_cart__csv_filed__store_url": {"message": "Σύνδεσμος καταστήματος"}, "export_shopping_cart__csv_filed__title": {"message": "Ονομασία προϊόντος"}, "export_shopping_cart__export_btn": {"message": "Εξαγωγή"}, "export_shopping_cart__export_empty": {"message": "Επιλέξτε ένα προϊόν!"}, "fa_huo_shi_jian": {"message": "Αποστολή"}, "favorite_add_email": {"message": "Προσθήκη email"}, "favorite_add_favorites": {"message": "Προσθήκη στα Αγαπημένα"}, "favorite_added": {"message": "Προστέθηκε"}, "favorite_btn_add": {"message": "Ειδοποίηση πτώσης τιμής."}, "favorite_btn_notify": {"message": "Παρα<PERSON><PERSON>λούθηση τιμής"}, "favorite_cate_name_all": {"message": "Ολα τα προϊόντα"}, "favorite_current_price": {"message": "Τρέχουσα τιμή"}, "favorite_due_date": {"message": "Ημερομηνία λήξης"}, "favorite_enable_notification": {"message": "Ενεργοποιήστε την ειδοποίηση μέσω email"}, "favorite_expired": {"message": "Λήξη"}, "favorite_go_to_enable": {"message": "Μεταβείτε στην ενεργοποίηση"}, "favorite_msg_add_success": {"message": "Προστέθηκε στα αγαπημένα"}, "favorite_msg_del_success": {"message": "Διαγράφηκε από τα αγαπημένα"}, "favorite_msg_failure": {"message": "Αποτυγχάνω! Ανανέωση σελίδας και δοκιμάστε ξανά."}, "favorite_please_add_email": {"message": "Προσθέστε email"}, "favorite_price_drop": {"message": "Κάτω"}, "favorite_price_rise": {"message": "Επάνω"}, "favorite_price_untracked": {"message": "Η τιμή δεν παρακολουθήθηκε"}, "favorite_saved_price": {"message": "Αποθηκευμένη τιμή"}, "favorite_stop_tracking": {"message": "Διακοπ<PERSON> παρακολούθησης"}, "favorite_sub_email_address": {"message": "Διεύθυνση email συνδρομής"}, "favorite_tracking_period": {"message": "Περ<PERSON><PERSON><PERSON><PERSON> παρακολούθησης"}, "favorite_tracking_prices": {"message": "Παρα<PERSON><PERSON>λούθηση τιμών"}, "favorite_verify_email": {"message": "Επαλήθευση διεύθυνσης email"}, "favorites_list_remove_prompt_msg": {"message": "Είστε σίγουροι ότι θα το διαγράψετε;"}, "favorites_update_button": {"message": "Ενημερώστε τις τιμές τώρα"}, "fen_lei": {"message": "Κατηγορία"}, "fen_xia_yan_xuan": {"message": "Επιλογή διανομέα"}, "find_similar": {"message": "Βρείτε παρόμοια"}, "first_ali_price_date": {"message": "Η ημερομηνία που καταγράφηκε για πρώτη φορά από το πρόγραμμα ανίχνευσης AliPrice"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> κουπόνια"}, "fooview_coupons_modal_title": {"message": "Κουπόνια"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Τιμή < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "ή τιμή > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Προθεσμία"}, "fooview_favorites_error_msg_no_favorites": {"message": "Προσθέστε εδώ τα αγαπημένα σας προϊόντα για να λαμβάνετε ειδοποίηση για πτώση τιμών."}, "fooview_favorites_filter_latest": {"message": "Αργότερο"}, "fooview_favorites_filter_price_drop": {"message": "περικοπή τιμής"}, "fooview_favorites_filter_price_up": {"message": "ΑΥΞΗΣΗ ΤΙΜΗΣ"}, "fooview_favorites_modal_title": {"message": "Τα αγαπημένα μου"}, "fooview_favorites_modal_title_title": {"message": "Μεταβείτε στο AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "Για να παρακολουθείτε την τιμή"}, "fooview_price_history_app_price": {"message": "Τιμή APP:"}, "fooview_price_history_title": {"message": "Ιστορικό τιμών"}, "fooview_product_list_feedback": {"message": "Ανατροφοδότηση"}, "fooview_product_list_orders": {"message": "Παραγγελίες"}, "fooview_product_list_price": {"message": "Τιμή"}, "fooview_reviews_error_msg_no_review": {"message": "Δεν βρήκαμε κριτικές για αυτό το προϊόν."}, "fooview_reviews_filter_buyer_reviews": {"message": "Φωτογρα<PERSON><PERSON><PERSON>ς αγοραστών"}, "fooview_reviews_modal_title": {"message": "Κριτικές"}, "fooview_same_product_choose_category": {"message": "Επιλέξτε κατηγορία"}, "fooview_same_product_filter_feedback": {"message": "Ανατροφοδότηση"}, "fooview_same_product_filter_orders": {"message": "Παραγγελίες"}, "fooview_same_product_filter_price": {"message": "Τιμή"}, "fooview_same_product_filter_rating": {"message": "Εκτίμηση"}, "fooview_same_product_modal_title": {"message": "Βρείτε το ίδιο προϊόν"}, "fooview_same_product_search_by_image": {"message": "Αναζήτηση με εικόνα"}, "fooview_seller_analysis_modal_title": {"message": "Ανάλυση πωλητή"}, "for_12_months": {"message": "Για 1 έτος"}, "for_12_months_list_pro": {"message": "12 μήνες"}, "for_12_months_nei": {"message": "Μέσα σε 12 μήνες"}, "for_1_months": {"message": "1 μήνα"}, "for_1_months_nei": {"message": "Μέσα σε 1 μήνα"}, "for_3_months": {"message": "Για 3 μήνες"}, "for_3_months_nei": {"message": "Μέσα σε 3 μήνες"}, "for_6_months": {"message": "Για 6 μήνες"}, "for_6_months_nei": {"message": "Μέσα σε 6 μήνες"}, "for_9_months": {"message": "9 μήνες"}, "for_9_months_nei": {"message": "Μέσα σε 9 μήνες"}, "fu_gou_lv": {"message": "Ποσοστ<PERSON> επαναγοράς"}, "gao_liang_bu_tong_dian": {"message": "επισημάνετε τις διαφορές"}, "gao_liang_guang_gao_chan_pin": {"message": "Επισημάνετε τα προϊόντα διαφήμισης"}, "geng_duo_xin_xi": {"message": "Περισσότερες πληροφορίες"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ενημέρωσης"}, "get_store_products_fail_tip": {"message": "Κάντε κλικ στο OK για να μεταβείτε στην επαλήθευση για να διασφαλίσετε την κανονική πρόσβαση"}, "gong_x_kuan_shang_pin": {"message": "Σύνολο $amount$ προϊόντων", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Προμηθευτής"}, "gong_ying_shang_ID": {"message": "Ταυτότητα προμηθευτή"}, "gong_ying_shang_deng_ji": {"message": "Αξιολόγηση προμηθευτή"}, "gong_ying_shang_nian_zhan": {"message": "Ο προμηθευτής είναι παλαιότερος"}, "gong_ying_shang_xin_xi": {"message": "πληροφορίες προμηθευτή"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Σύνδεσμος αρχικής σελίδας προμηθευτή"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Εκτιμώμενη τιμή πώλησης"}, "guan_jian_zi": {"message": "Λέξη-κλειδί"}, "guang_gao_chan_pin": {"message": "Διαφήμιση. προϊόντα"}, "guang_gao_zhan_bi": {"message": "Διαφήμιση. αναλογία"}, "guo_ji_wu_liu_yun_fei": {"message": "Τέλος διεθνούς αποστολής"}, "guo_lv_tiao_jian": {"message": "Φίλτρα"}, "hao_ping_lv": {"message": "Θετική βαθμολογία"}, "highest_price": {"message": "Υψηλός"}, "historical_trend": {"message": "Ιστορική τάση"}, "how_to_screenshot": {"message": "Κρατήστε πατημένο το αριστερ<PERSON> κουμπί του ποντικιού για να επιλέξετε την περιοχή, πατήστε το δεξί κουμπί του ποντικιού ή το πλήκτρο Esc για έξοδο από το στιγμιότυπο οθόνης"}, "howt_it_works": {"message": "Πως δουλεύει"}, "hui_fu_lv": {"message": "Ποσοστ<PERSON> απόκρισης"}, "hui_tou_lv": {"message": "ποσοστό επιστροφής"}, "inquire_freightFee": {"message": "Έρευνα φορτίου"}, "inquire_freightFee_Yuan": {"message": "Φορτίο/Γουάν"}, "inquire_freightFee_province": {"message": "Επαρχία"}, "inquire_freightFee_the": {"message": "Το ναύλο είναι $num$, που σημαίνει ότι η περιοχή έχει δωρεάν αποστολή.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Ενα δ."}, "jia_ge": {"message": "Τιμή"}, "jia_ge_dan_wei": {"message": "Μονάδα"}, "jia_ge_qu_shi": {"message": "Τάσεις"}, "jia_zai_n_ge_shang_pin": {"message": "Φόρτωση προϊόντων $num$", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Ποσ<PERSON><PERSON><PERSON><PERSON> όγκου πωλήσεων τις τελευταίες 30 ημέρες"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Ποσοστ<PERSON> εσόδων τις τελευταίες 30 ημέρες"}, "jin_90_tian_mai_jia_shu": {"message": "Αγορα<PERSON>τ<PERSON>ς τις τελευταίες 90 ημέρες"}, "jin_90_tian_xiao_shou_liang": {"message": "Εκπτώσεις τις τελευταίες 90 ημέρες"}, "jing_xuan_huo_yuan": {"message": "Επιλεγμένες πηγές"}, "jing_ying_mo_shi": {"message": "Επιχειρηματικ<PERSON> μοντέλο"}, "jiu_fen_jie_jue": {"message": "Επίλυση διαφοράς"}, "jiu_fen_jie_jue__desc": {"message": "Λογιστική των διαφορών για τα δικαιώματα καταστημάτων των πωλητών"}, "jiu_fen_lv": {"message": "Ποσοστ<PERSON> αμφισβήτησης"}, "jiu_fen_lv__desc": {"message": "Ποσοστ<PERSON> παραγγελιών με παράπονα που ολοκληρώθηκαν τις τελευταίες 30 ημέρες και κρίθηκε ότι είναι ευθύνη του πωλητή ή και των δύο μερών"}, "kai_dian_ri_qi": {"message": "Ημερομην<PERSON>α έναρξης λειτουργίας"}, "keywords": {"message": "Λέξεις-κλειδιά"}, "kua_jin_Select_pan_huo": {"message": "Διασυνοριακή επιλογή"}, "last15_days": {"message": "Τελευταίες 15 μέρες"}, "last180_days": {"message": "Τελευταίες 180 ημέρες"}, "last30_days": {"message": "Τις τελευταίες 30 ημέρες"}, "last360_days": {"message": "Τελευταίες 360 ημέρες"}, "last45_days": {"message": "Τελευταίες 45 ημέρες"}, "last60_days": {"message": "Τελευταίες 60 ημέρες"}, "last7_days": {"message": "Τελευταίες 7 ημέρες"}, "last90_days": {"message": "Τελευταίες 90 ημέρες"}, "last_30d_sales": {"message": "Εκπτώσεις των τελευταίων 30 ημερών"}, "lei_ji": {"message": "Σωρευτικ<PERSON>ς"}, "lei_ji_xiao_liang": {"message": "Σύνολο"}, "lei_ji_xiao_liang__desc": {"message": "Όλες οι πωλήσεις μετά το προϊόν στο ράφι"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Σωρευτι<PERSON><PERSON><PERSON> όγκος πωλήσεων τις τελευταίες 30 ημέρες, ταξινομημένος από υψηλό σε χαμηλό"}, "lian_xi_fang_shi": {"message": "Στοιχεία επικοινωνίας"}, "list_time": {"message": "Σε χρόνο αποθήκευσης"}, "load_more": {"message": "Φόρτωση περισσότερων"}, "login_to_aliprice": {"message": "Συνδεθείτε στο AliPrice"}, "long_link": {"message": "<PERSON>α<PERSON><PERSON><PERSON>ς σύνδεσμος"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "Πωλητές"}, "mao_li_lv": {"message": "Μικτό περιθώριο"}, "mobile_view__dkxbqy": {"message": "Ανοίξτε μια νέα καρτέλα"}, "mobile_view__sjdxq": {"message": "Λεπτομέρειες στην εφαρμογή"}, "mobile_view__sjdxqy": {"message": "Σελίδα λεπτομερειών στην εφαρμογή"}, "mobile_view__smck": {"message": "Σάρωση για προβολή"}, "mobile_view__smckms": {"message": "Χρησιμοποιήστε την κάμερα ή την εφαρμογή για σάρωση και προβολή"}, "modified_failed": {"message": "Η τροποποίηση απέτυχε"}, "modified_successfully": {"message": "Τροποποιήθηκε με επιτυχία"}, "nav_btn_favorites": {"message": "Οι συλλογές μου"}, "nav_btn_package": {"message": "Πακ<PERSON>το"}, "nav_btn_product_info": {"message": "Σχετικά με το προϊόν"}, "nav_btn_viewed": {"message": "Προβολή"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Κανένας"}, "normal_link": {"message": "Καν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεσμος"}, "notice": {"message": "ίχνος"}, "number_reviews": {"message": "Κριτικές"}, "only_show_num": {"message": "Σύνολο προϊόντων: $allnum$, Κρυφό: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Αφαίρεση Μη επιλεγμένο"}, "open": {"message": "Ανοιχτό"}, "open_links": {"message": "Ανοίξτε συνδέσμους"}, "options_page_tab_check_links": {"message": "Ελέγξτε τους συνδέσμους"}, "options_page_tab_gernal": {"message": "Γ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_notifications": {"message": "Ειδοποιήσεις"}, "options_page_tab_others": {"message": "Οι υπολοιποι"}, "options_page_tab_sbi": {"message": "Αναζήτηση με εικόνα"}, "options_page_tab_shortcuts": {"message": "Συντομεύσεις"}, "options_page_tab_shortcuts_title": {"message": "Μέγεθος γραμματοσειράς για συντομεύσεις"}, "options_page_tab_similar_products": {"message": "Ίδια προϊόντα"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Πολλοί σύνδεσμοι προϊόντων πρόκειται να ανοίξουν"}, "order_list_sku_show_title": {"message": "Εμφάνιση επιλεγμένων παραλ<PERSON><PERSON><PERSON><PERSON><PERSON> στους κοινόχρηστους συνδέσμους"}, "orders_last30_days": {"message": "Αριθμός παραγγελιών τις τελευταίες 30 ημέρες"}, "pTutorial_favorites_block1_desc1": {"message": "Τα προϊόντα που παρακολουθήσατε παρατίθενται εδώ"}, "pTutorial_favorites_block1_title": {"message": "Αγαπημένα"}, "pTutorial_popup_block1_desc1": {"message": "Μια πράσινη ετικέτα σημαίνει ότι υπάρχουν προϊόντα που έχουν μειωθεί στις τιμές"}, "pTutorial_popup_block1_title": {"message": "Συντομεύσεις και Αγαπημένα"}, "pTutorial_price_history_block1_desc1": {"message": "Κάντε κλικ στο \"Παρακολούθηση τιμής\", προσθέστε προϊόντα στα Αγαπημένα. Μόλις μειωθούν οι τιμές τους, θα λάβετε ειδοποιήσεις"}, "pTutorial_price_history_block1_title": {"message": "Παρα<PERSON><PERSON>λούθηση τιμής"}, "pTutorial_reviews_block1_desc1": {"message": "Σχόλια αγοραστών από το Itao και πραγματικές φωτογραφίες από τα σχόλια AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Κριτικές"}, "pTutorial_reviews_block2_desc1": {"message": "Είναι πάντα χρήσιμο να ελέγχετε κριτικές από άλλους αγοραστές"}, "pTutorial_same_products_block1_desc1": {"message": "Μπορείτε να τα συγκρίνετε για να κάνετε την καλύτερη επιλογή"}, "pTutorial_same_products_block1_desc2": {"message": "Κάντε κλικ στο \"Περισσότερα\" για \"Αναζήτηση με εικόνα\""}, "pTutorial_same_products_block1_title": {"message": "Ίδια προϊόντα"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Αποθέστε την εικόνα του προϊόντος εκεί και επιλέξτε μια κατηγορία"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Αναζήτηση με εικόνα"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Το θετικό ποσοστό σχολίων του πωλητή, τα αποτελέσματα σχολίων και το χρονικό διάστημα που ο πωλητής ήταν στην αγορά"}, "pTutorial_seller_analysis_block1_title": {"message": "Αξιολόγηση πωλητή"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Η βαθμολογία του πωλητή βασίζεται σε 3 ευρετήρια: στοιχείο όπως περιγράφεται, Ταχ<PERSON>τητα αποστολής επικοινωνίας"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Χρησιμοποιούμε 3 χρώματα και εικονίδια για να δείξουμε τα επίπεδα εμπιστοσύνης των πωλητών"}, "page_count": {"message": "Αριθμός σελίδων"}, "pai_chu": {"message": "Εξαιρείται"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Εξαιρέστε το Χονγκ Κονγκ-Περιορισμένο"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Εξαιρούνται οι περιορισμοί στην Ιαπωνία"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση περιορισμένης πρόσβασης στην Κορέα"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση Καζακστάν-Περιορισμένη"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Εξαιρέστε το Macau-Restricted"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση Περιορισμένης Ανατολικής Ευρώπης"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Εξαιρέστε τη Σαουδική Αραβία - Περιορισμένη"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση περιορισμένης πρόσβασης στην Ταϊβάν"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση των Η.Π.Α"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση περιορισμένης πρόσβασης στο Βιετνάμ"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Εξαίρεση περιορισμένων στοιχείων"}, "payable_price_formula": {"message": "Τιμή + Αποστολή + Έκπτωση"}, "pdd_pifa_to_retail_btn_txt": {"message": "Αγο<PERSON>ά στο λιανικό εμπόριο"}, "pdp_copy_fail": {"message": "Η αντιγραφή απέτυχε!"}, "pdp_copy_success": {"message": "Επιτυχής αντιγραφή!"}, "pdp_share_modal_subtitle": {"message": "Μοιραστείτε στιγμιότυπο οθόνης, θα δει την επιλογή σας."}, "pdp_share_modal_title": {"message": "Μοιραστείτε την επιλογή σας"}, "pdp_share_screenshot": {"message": "Κοινή χρήση στιγμιότυπου οθόνης"}, "pei_song": {"message": "Τρό<PERSON><PERSON> αποστολής"}, "pin_lei": {"message": "Κατηγορία"}, "pin_zhi_ti_yan": {"message": "Ποιότητα προϊόντος"}, "pin_zhi_ti_yan__desc": {"message": "Πο<PERSON><PERSON><PERSON><PERSON><PERSON> επιστροφής χρημάτων ποιότητας του καταστήματος του πωλητή"}, "pin_zhi_tui_kuan_lv": {"message": "Ποσοστ<PERSON> επιστροφής χρημάτων"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Ποσο<PERSON>τ<PERSON> παραγγ<PERSON><PERSON>ιών που έχουν επιστραφεί και επιστραφεί μόνο τις τελευταίες 30 ημέρες"}, "ping_fen": {"message": "Εκτίμηση"}, "ping_jun_fa_huo_su_du": {"message": "Μέση Ταχύτητα Αποστολής"}, "pkgInfo_hide": {"message": "Πληροφορίες Logistics: on/off"}, "pkgInfo_no_trace": {"message": "Δεν υπάρχουν πληροφορίες logistics"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> χονδρική"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "Σπίτι"}, "popup_go_to_platform": {"message": "Μεταβείτε στο $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Ψωνίζω για ..."}, "popup_track_package_btn_track": {"message": "ΠΙΣΤΑ"}, "popup_track_package_desc": {"message": "ΠΑΡΑΚΟΛΟΥΘΗΣΗ ΟΛΩΝ ΣΕ ΕΝΑ ΠΑΚΕΤΟ"}, "popup_track_package_search_placeholder": {"message": "Αριθμός εντοπισμού"}, "popup_translate_search_placeholder": {"message": "Μετάφραση και αναζήτηση στο $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Ιστορικό τιμών"}, "price_history_chart_tip_ae": {"message": "Συμβουλή: Ο αριθμός των παραγγελιών είναι ο αθροιστικός αριθμός παραγγελιών από την κυκλοφορία"}, "price_history_chart_tip_coupang": {"message": "Συμβουλή: Η Coupang θα διαγράψει τον αριθμό παραγγελιών για δόλιες παραγγελίες"}, "price_history_inm_1688_l1": {"message": "Εγκαταστήστε"}, "price_history_inm_1688_l2": {"message": "Ali<PERSON><PERSON> Shopping Assistant για το 1688"}, "price_history_panel_lowest_price": {"message": "ΧΑΜΗΛΟΤΕΡΗ ΤΙΜΗ:"}, "price_history_panel_tab_price_tracking": {"message": "Ιστορικό τιμών"}, "price_history_panel_tab_seller_analysis": {"message": "Ανάλυση πωλητή"}, "price_history_pro_modal_title": {"message": "Ιστορικό τιμών & ιστορικό παραγγελιών"}, "privacy_consent__btn_agree": {"message": "Επανεξετάστε τη συγκατάθεση συλλογής δεδομένων"}, "privacy_consent__btn_disable_all": {"message": "Δεν δέχομαι"}, "privacy_consent__btn_enable_all": {"message": "Ενεργοποίηση όλων"}, "privacy_consent__btn_uninstall": {"message": "Αφαιρώ"}, "privacy_consent__desc_privacy": {"message": "Λάβετε υπόψη ότι, χωρ<PERSON><PERSON> δεδομένα ή cookie, ορισμένες λειτουργίες θα απενεργοποιηθούν επειδή αυτές οι λειτουργίες χρειάζονται την εξήγηση των δεδομένων ή των cookie, αλλά μπορείτε ακόμα να χρησιμοποιήσετε τις άλλες λειτουργίες."}, "privacy_consent__desc_privacy_L1": {"message": "Δυστ<PERSON><PERSON><PERSON><PERSON>, χω<PERSON><PERSON><PERSON> δεδομένα ή cookie δεν θα λειτουργήσει επειδή χρειαζόμαστε την εξήγηση δεδομένων ή cookie."}, "privacy_consent__desc_privacy_L2": {"message": "Εάν δεν μας επιτρέπετε να συλλέγουμε αυτές τις πληροφορίες, καταργήστε τις."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, λαμβάνουμε τα δεδομένα νομίσματός σας μόνο στα cookies όταν πραγματοποιούμε αγορές στο διαδίκτυο για να δείξουμε το ιστορικό τιμών."}, "privacy_consent__item_cookies_title": {"message": "Απαιτούμενα Cookies"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Προ<PERSON>θέστε cookie στο πρόγραμμα περιήγησης για να αναγνωρίσετε ανώνυμα τον υπολογιστή ή τη συσκευή σας."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Προσθέστε λειτουργικ<PERSON> δεδομένα στο πρόσθετο για να λειτουργήσετε με τη λειτουργία."}, "privacy_consent__item_functional_title": {"message": "Λειτουργικά και Analytics Cookies"}, "privacy_consent__more_desc": {"message": "Λάβετε υπόψη ότι δεν κοινοποιούμε τα προσωπικά σας δεδομένα σε άλλες εταιρείες και καμία εταιρεία διαφημίσεων δεν συλλέγει δεδομένα μέσω της υπηρεσίας μας."}, "privacy_consent__options__btn__desc": {"message": "Για να χρησιμοποιήσετε όλες τις δυνατότητες, πρέπει να την ενεργοποιήσετε."}, "privacy_consent__options__btn__label": {"message": "Ενεργοποιήστε την"}, "privacy_consent__options__desc_L1": {"message": "Θα συλλέξουμε τα ακόλουθα δεδομένα που σας προσδιορίζουν προσωπικά:"}, "privacy_consent__options__desc_L2": {"message": "- cookie, λαμβάνουμε τα δεδομένα νομίσματός σας στα cookies μόνο όταν ψωνίζετε στο διαδίκτυο για να δείξουμε το ιστορικό τιμών."}, "privacy_consent__options__desc_L3": {"message": "- και προσθέστε cookie στο πρόγραμμα περιήγησης για να αναγνωρίσετε ανώνυμα τον υπολογιστή ή τη συσκευή σας."}, "privacy_consent__options__desc_L4": {"message": "- άλλα ανώνυμα δεδομένα καθιστούν αυτήν την επέκταση πιο βολική."}, "privacy_consent__options__desc_L5": {"message": "Λάβετε υπόψη ότι δεν κοινοποιούμε τα προσωπικά σας δεδομένα σε άλλες εταιρείες και καμία εταιρεία διαφημίσεων δεν συλλέγει δεδομένα μέσω της υπηρεσίας μας."}, "privacy_consent__privacy_preferences": {"message": "Προτιμή<PERSON><PERSON>ις απορρήτου"}, "privacy_consent__read_more": {"message": "Διαβάστε περισσότερα >>"}, "privacy_consent__title_privacy": {"message": "Μυστικότητα"}, "product_info": {"message": "Πληροφορίες προϊόντος"}, "product_recommend__name": {"message": "Ίδια προϊόντα"}, "product_research": {"message": "Έρευνα Προϊόντος"}, "product_sub__email_desc": {"message": "Email ειδοποίησης τιμής"}, "product_sub__email_edit": {"message": "επεξεργασία"}, "product_sub__email_not_verified": {"message": "Επαληθεύστε το email"}, "product_sub__email_required": {"message": "Δώστε email"}, "product_sub__form_countdown": {"message": "Αυτόματο κλείσιμο μετά από $seconds$ δευτερόλεπτα", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Η προσθήκη υπενθύμισης απέτυχε!"}, "product_sub__form_input_price": {"message": "τιμή εισροής"}, "product_sub__form_item_country": {"message": "έθνος"}, "product_sub__form_item_current_price": {"message": "Τρέχουσα τιμή"}, "product_sub__form_item_duration": {"message": "πίστα"}, "product_sub__form_item_higher_price": {"message": "Ή τιμή>"}, "product_sub__form_item_invalid_higher_price": {"message": "Η τιμή πρέπει να είναι μεγαλύτερη από $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Η τιμή πρέπει να είναι χαμηλότερη από $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Όταν η τιμή <"}, "product_sub__form_submit": {"message": "υποβάλλουν"}, "product_sub__form_success": {"message": "Κατάφερε να προσθέσει υπενθύμιση!"}, "product_sub__high_price_notify": {"message": "Ενημερώστε με για αυξήσεις τιμών"}, "product_sub__low_price_notify": {"message": "Ενημερώστε με για μειώσεις τιμών"}, "product_sub__modal_title": {"message": "Υπενθύμιση αλλαγής τιμής συνδρομής"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Επιχειρηματική περιοχή"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Παρακαλούμε επιλέξτε τουλάχιστον ένα προϊόν"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Επιλέξτε τουλάχιστον ένα πεδίο"}, "qu_deng_lu": {"message": "Συνδεθείτε"}, "quan_guo_yan_xuan": {"message": "Παγκόσμια Επιλογή"}, "recommendation_popup_banner_btn_install": {"message": "Εγκαταστήστε το"}, "recommendation_popup_banner_desc": {"message": "Εμφάνιση ιστορικού τιμών εντός 3/6 μηνών και ειδοποίηση πτώσης τιμών"}, "region__all": {"message": "Όλες οι περιφέρειες"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Ενεργοποιήστε το δεξί κλικ"}, "ren_zheng_gong_chang": {"message": "Πιστοποιημένο εργοστάσιο"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Χρόνια ως Πιστοποιημένος Προμηθευτής"}, "required_to_aliprice_login": {"message": "Πρέπει να συνδεθείτε στο AliPrice"}, "revenue_last30_days": {"message": "Ποσό πωλήσεων τις τελευταίες 30 ημέρες"}, "review_counts": {"message": "Αριθμός συλλεκτών"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισόδου"}, "sales_amount_last30_days": {"message": "Συνολικές πωλήσεις τις τελευταίες 30 ημέρες"}, "sales_last30_days": {"message": "Εκπτώσεις τις τελευταίες 30 ημέρες"}, "sbi_alibaba_cate__accessories": {"message": "αξεσ<PERSON>υ<PERSON>ρ"}, "sbi_alibaba_cate__aqfk": {"message": "Ασφάλεια"}, "sbi_alibaba_cate__bags_cases": {"message": "Τσάντες & Θήκες"}, "sbi_alibaba_cate__beauty": {"message": "Ομορφιά"}, "sbi_alibaba_cate__beverage": {"message": "Ποτό"}, "sbi_alibaba_cate__bgwh": {"message": "Κουλτούρα γραφείου"}, "sbi_alibaba_cate__bz": {"message": "Πακ<PERSON>το"}, "sbi_alibaba_cate__ccyj": {"message": "Μαγειρι<PERSON><PERSON> σκεύη"}, "sbi_alibaba_cate__clothes": {"message": "Ενδύματα"}, "sbi_alibaba_cate__cmgd": {"message": "Εκπομπή ΜΜΕ"}, "sbi_alibaba_cate__coat_jacket": {"message": "Παλτό & μπουφάν"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ηλεκτρονικά"}, "sbi_alibaba_cate__cryp": {"message": "Προϊόντα για ενήλικες"}, "sbi_alibaba_cate__csyp": {"message": "Επενδύσεις κρεβατιού"}, "sbi_alibaba_cate__cwyy": {"message": "Κηπουρική κατοικίδιων ζώων"}, "sbi_alibaba_cate__cysx": {"message": "Catering φρέσκο"}, "sbi_alibaba_cate__dgdq": {"message": "Ηλεκτρολόγος"}, "sbi_alibaba_cate__dl": {"message": "Ηθοποιία"}, "sbi_alibaba_cate__dress_suits": {"message": "Φόρεμα και κοστούμια"}, "sbi_alibaba_cate__dszm": {"message": "Φωτισμός"}, "sbi_alibaba_cate__dzqj": {"message": "Ηλεκτρονική συσκευή"}, "sbi_alibaba_cate__essb": {"message": "Μεταχει<PERSON>ισμένος <PERSON>ξοπλισμός"}, "sbi_alibaba_cate__food": {"message": "Τροφή"}, "sbi_alibaba_cate__fspj": {"message": "Ρούχα και Αξεσουάρ"}, "sbi_alibaba_cate__furniture": {"message": "Επιπλα"}, "sbi_alibaba_cate__fzpg": {"message": "Υφαντικό δέρμα"}, "sbi_alibaba_cate__ghjq": {"message": "Προσωπική φροντίδα"}, "sbi_alibaba_cate__gt": {"message": "Ατσάλι"}, "sbi_alibaba_cate__gyp": {"message": "Χειροτεχνία"}, "sbi_alibaba_cate__hb": {"message": "Φιλικό προς το περιβάλλον"}, "sbi_alibaba_cate__hfcz": {"message": "Μακ<PERSON><PERSON><PERSON><PERSON><PERSON> περιποίησης δέρματος"}, "sbi_alibaba_cate__hg": {"message": "Χημική βιομηχανία"}, "sbi_alibaba_cate__jg": {"message": "Επεξεργασία"}, "sbi_alibaba_cate__jianccai": {"message": "Οικοδομικά υλικά"}, "sbi_alibaba_cate__jichuang": {"message": "Μηχανικό εργαλείο"}, "sbi_alibaba_cate__jjry": {"message": "Οικιακή καθημερινή χρήση"}, "sbi_alibaba_cate__jtys": {"message": "Μεταφορά"}, "sbi_alibaba_cate__jxsb": {"message": "Εξοπλισμός"}, "sbi_alibaba_cate__jxwj": {"message": "Μηχανικό υλικό"}, "sbi_alibaba_cate__jydq": {"message": "Οικιακές συσκευές"}, "sbi_alibaba_cate__jzjc": {"message": "Οικοδομικά υλικά βελτίωσης σπιτιού"}, "sbi_alibaba_cate__jzjf": {"message": "Οικιακά κλωστοϋφαντουργικά προϊόντα"}, "sbi_alibaba_cate__mj": {"message": "Πετσέτα"}, "sbi_alibaba_cate__myyp": {"message": "Βρεφικά προϊόντα"}, "sbi_alibaba_cate__nanz": {"message": "Ανδρικά"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON>υν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ρουχισμός"}, "sbi_alibaba_cate__ny": {"message": "Ενέργεια"}, "sbi_alibaba_cate__others": {"message": "Οι υπολοιποι"}, "sbi_alibaba_cate__qcyp": {"message": "Αξε<PERSON><PERSON><PERSON><PERSON><PERSON> αυτοκινήτου"}, "sbi_alibaba_cate__qmpj": {"message": "Εξαρτήματα αυτοκινήτου"}, "sbi_alibaba_cate__shoes": {"message": "Παπούτσια"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON>η<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> υπολογιστής"}, "sbi_alibaba_cate__snqj": {"message": "Αποθήκευση και καθαρισμός"}, "sbi_alibaba_cate__spjs": {"message": "Φαγητο ΠΟΤΟ"}, "sbi_alibaba_cate__swfw": {"message": "Επιχειρηματικές υπηρεσίες"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Παιχνίδι"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Παντελόνι & φούστα"}, "sbi_alibaba_cate__txcp": {"message": "Προϊόντα επικοινωνίας"}, "sbi_alibaba_cate__tz": {"message": "Παιδικά ρούχα"}, "sbi_alibaba_cate__underwear": {"message": "Εσώρουχα"}, "sbi_alibaba_cate__wjgj": {"message": "Εργαλεία υλικού"}, "sbi_alibaba_cate__xgpi": {"message": "Δερμάτινες τσάντες"}, "sbi_alibaba_cate__xmhz": {"message": "συνεργασία για το έργο"}, "sbi_alibaba_cate__xs": {"message": "Κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "ΑΘΛΗΤΙΚΑ ΡΟΥΧΑ"}, "sbi_alibaba_cate__ydhw": {"message": "Υπαίθριο άθλημα"}, "sbi_alibaba_cate__yjkc": {"message": "Μεταλλουργικά Ορυκτά"}, "sbi_alibaba_cate__yqyb": {"message": "Ενοργάνιση"}, "sbi_alibaba_cate__ys": {"message": "Τυπώνω"}, "sbi_alibaba_cate__yyby": {"message": "Ιατρική φροντίδα"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Αριθμ<PERSON>ς αγοραστών τις τελευταίες 90 ημέρες"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Όγκος πωλήσεων τις τελευταίες 90 ημέρες"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Εκτιμώμενη τιμή"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Διε<PERSON><PERSON><PERSON>ς τέλος αποστολής"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Εξοδα αποστολής"}, "sbi_alibaba_cn_kj_gssj": {"message": "Εκτιμώμενη τιμή"}, "sbi_alibaba_cn_kj_lr": {"message": "Κ<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Κέρδος = εκτιμώμενη τιμή x περιθώριο κέρδους"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Μέση ταχύτητα παράδοσης"}, "sbi_alibaba_cn_kj_qtfy": {"message": "άλλο τέλος"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Άλλο κόστος = εκτιμώμενη τιμή x άλλη αναλογία κόστους"}, "sbi_alibaba_cn_kj_spjg": {"message": "Τιμή"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "Τοποθεσία"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Κομμάτια"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Κομμάτια"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Γραμμάριο"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Αγορα<PERSON>τ<PERSON>ς"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Κομμάτια"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Σκηνικά"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Μέρ<PERSON>ς"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON><PERSON>ς τιμή"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "τιμή"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Διατίθε<PERSON><PERSON><PERSON> προς πώληση"}, "sbi_aliprice_alibaba_cn__moren": {"message": "Προκαθορισμένο"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Σίγουρος"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Εκπτώσεις"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "έπιπλα"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "πρόχειρο φαγητό"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "μακιγι<PERSON>ζ"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Εσώρουχα"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "αξεσ<PERSON>υ<PERSON>ρ"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Εμφιαλωμένο ποτό"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Οι υπολοιποι"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Φούστα"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Σακάκι"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "ΗΛΕΚΤΡΟΝΙΚΑ ΕΙΔΗ"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Παιχνίδι"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Αποσκευές"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Πυθμένα"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "παπούτσι"}, "sbi_aliprice_cate__apparel": {"message": "Ενδύματα"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Αυτοκίνητα & Μοτοσικλέτες"}, "sbi_aliprice_cate__beauty_health": {"message": "ομορφιά και υγεία"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Κινητά τηλέφωνα και τηλεπικοινωνίες"}, "sbi_aliprice_cate__computer_office": {"message": "Υπολογιστής & γραφείο"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ηλεκτρονικά"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Εκπαίδευση & είδη γραφείου"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Ηλεκτρονι<PERSON><PERSON> εξαρτήματα & αναλώσιμα"}, "sbi_aliprice_cate__furniture": {"message": "Επιπλα"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Επεκτά<PERSON><PERSON>ις μαλλιών & περούκες"}, "sbi_aliprice_cate__home_garden": {"message": "σπίτι και κήπος"}, "sbi_aliprice_cate__home_improvement": {"message": "Βελτίωση σπιτιού"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Κοσμήματα & αξεσουάρ"}, "sbi_aliprice_cate__luggage_bags": {"message": "Αποσκευές & τσάντες"}, "sbi_aliprice_cate__mother_kids": {"message": "Μητέρα και παιδιά"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Καινοτομία και ειδική χρήση"}, "sbi_aliprice_cate__security_protection": {"message": "Ασφάλεια & Προστασία"}, "sbi_aliprice_cate__shoes": {"message": "Παπούτσια"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Σπορ και ψυχαγωγία"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Παιχνίδια και χόμπι"}, "sbi_aliprice_cate__watches": {"message": "Ρολόγια"}, "sbi_aliprice_cate__weddings_events": {"message": "Γάμοι & Εκδηλώσεις"}, "sbi_btn_capture_txt": {"message": "Πιάνω"}, "sbi_btn_source_now_txt": {"message": "Πηγή τώρα"}, "sbi_button__chat_with_me": {"message": "Συνομίλησε μαζί μου"}, "sbi_button__contact_supplier": {"message": "Επικοινωνία"}, "sbi_button__hide_on_this_site": {"message": "Να μην εμφανίζεται σε αυτόν τον ιστότοπο"}, "sbi_button__open_settings": {"message": "Διαμόρφωση αναζήτησης με εικόνα"}, "sbi_capture_shortcut_tip": {"message": "ή πατήστε το πλήκτρο \"Enter\" στο πληκτρολόγιο"}, "sbi_capturing_tip": {"message": "Σύλληψη"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 αστέρια"}, "sbi_crop_and_search": {"message": "Αναζήτηση"}, "sbi_crop_start": {"message": "Χρησιμοποιήστε στιγμιότυπο οθόνης"}, "sbi_err_captcha_action": {"message": "Επαληθεύω"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Χρειά<PERSON><PERSON>στε επαλήθευση, αν<PERSON><PERSON><PERSON><PERSON>τ<PERSON> μια φωτογραφία για επαλήθευση. (Προβολή $video_tutorial$ ή δοκιμάστε να διαγράψετε τα cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Ασυνήθιστη κίνηση, επαληθεύστε"}, "sbi_err_captcha_for_taobao": {"message": "Το Taobao σας ζητά να επαληθεύσετε, ανεβάστε με μη αυτόματο τρόπο μια εικόνα και πραγματοποιήστε αναζήτηση για να την επαληθεύσετε. Αυτό το σφάλμα οφείλεται στη νέα πολιτική επαλήθευσης \"TaoBao search by image\". Σας προτείνουμε ότι η καταγγελία επαληθεύεται πολύ συχνά στο Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "ανατροφοδότηση"}, "sbi_err_captcha_msg": {"message": "Το $platform$ απαιτεί να ανεβάσετε μια εικόνα για αναζήτηση ή να ολοκληρώσετε την επαλήθευση ασφαλείας για να καταργήσετε τους περιορισμούς αναζήτησης", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Ελέγξτε αν είναι η τελευτα<PERSON>α έκδοση"}, "sbi_err_cookie_btn_clear": {"message": "Διαγραφή των βοηθητικών αρχείων δεδομένων"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Διαγραφ<PERSON> cookies 1688; (Χρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> να συνδεθείτε ξανά)"}, "sbi_err_desperate_feature_pdd": {"message": "Η λειτουργία αναζήτησης εικόνων έχει μετακινηθεί στην επέκταση Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Πώς να βελτιώσετε το ποσοστ<PERSON> επιτυχίας της αναζήτησης εικόνων;"}, "sbi_err_img_undersize": {"message": "Εικόνα > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Συνδεθείτε $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Σύνδεση"}, "sbi_err_low_version": {"message": "Εγκαταστήστε την τελευτα<PERSON>α έκδοση ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Κατεβάστε"}, "sbi_err_need_help": {"message": "Χρει<PERSON><PERSON><PERSON>στε βοήθεια"}, "sbi_err_network": {"message": "Σφάλμα δικτύου, βεβα<PERSON>ωθείτε ότι μπορείτε να επισκεφτείτε τον ιστότοπο"}, "sbi_err_not_low_version": {"message": "Η τελευταία έκδοση έχει εγκατασταθεί ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Προσπάθη<PERSON><PERSON> ξανά"}, "sbi_err_try_again_action": {"message": "Προσπάθη<PERSON><PERSON> ξανά"}, "sbi_err_visit_and_try": {"message": "Δοκιμάστ<PERSON> ξανά ή επισκεφτείτε το $website$ για να προσπαθήσετε ξανά", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Επισκεφτείτε την αρχική σελίδα $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Η φόρτωση απέτυχε, ανανεώστε τη σελίδα και δοκιμάστε ξανά."}, "sbi_kuajing_filter_area": {"message": "Περιοχή"}, "sbi_kuajing_filter_au": {"message": "Αυστραλία"}, "sbi_kuajing_filter_btn_confirm": {"message": "Επιβεβαιώνω"}, "sbi_kuajing_filter_de": {"message": "Γερμανία"}, "sbi_kuajing_filter_destination_country": {"message": "Χώρα Προορισμού"}, "sbi_kuajing_filter_es": {"message": "Ισπανία"}, "sbi_kuajing_filter_estimate": {"message": "Εκτίμηση"}, "sbi_kuajing_filter_estimate_price": {"message": "Εκτιμώμενη τιμή"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Τύπος εκτιμώμενης τιμής = (τιμή εμπορευμάτων + διεθνές φορτίο εφοδιαστικής)/(1 - περιθώριο κέρδους - άλλ<PERSON> κόστος)"}, "sbi_kuajing_filter_fr": {"message": "Γαλλία"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Ει<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> λέξεις -κλειδι<PERSON> που ταιριάζουν με τον τίτλο"}, "sbi_kuajing_filter_logistics": {"message": "Πρότυπο εφοδιαστικής"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Ταχυδρομείο China Post Air"}, "sbi_kuajing_filter_logistics_discount": {"message": "Έκπτωση logistics"}, "sbi_kuajing_filter_logistics_epacket": {"message": "πακέτο"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Διεθνές φορτίο logistics = (βάρ<PERSON> x τιμή αποστολής + τέλος εγγραφής) x (1 - έκπτωση)"}, "sbi_kuajing_filter_others_fee": {"message": "Άλλο τέλος"}, "sbi_kuajing_filter_profit_percent": {"message": "Περιθώριο κέρδους"}, "sbi_kuajing_filter_prop": {"message": "Γνωρίσματα"}, "sbi_kuajing_filter_ru": {"message": "Ρωσία"}, "sbi_kuajing_filter_total": {"message": "Αντιστοιχίστε $count$ παρόμοια στοιχεία", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "ΗΝΩΜΕΝΟ ΒΑΣΙΛΕΙΟ"}, "sbi_kuajing_filter_usa": {"message": "Αμερική"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> χονδρική"}, "sbi_msg_no_result": {"message": "Δεν βρέθηκε αποτέλεσμα,συνδεθείτε στο $loginSite$ ή δοκιμάστε μια άλλη εικόνα", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Προσωρινά μη διαθέσιμο για τ<PERSON>, χρησιμοποιήστε το $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Πρόγραμμα περιήγησης Chrome και οι επεκτάσεις του"}, "sbi_msg_no_result_reinstall_l1": {"message": "Δεν βρέθηκαν αποτελέσματα, συνδεθείτε στο $loginSite$ ή δοκιμάστε άλλη εικόνα ή εγκαταστήστε ξανά το $latestExtUrl$ ", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Τελευτα<PERSON><PERSON> έκδοση", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Επιλεγμένη περιοχή"}, "sbi_shipping_": {"message": "Αποστολή αυθημερόν"}, "sbi_specify_category": {"message": "Προσδιορίστε την κατηγορία:"}, "sbi_start_crop": {"message": "Επιλέξτε περιοχή"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 oversea"}, "sbi_tutorial_btn_more": {"message": "Περισσότεροι τρόποι"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Βρείτε τα κουπόνια Taobao"}, "sbi_txt__empty_retry": {"message": "Λυπούμαστε, δεν βρέθηκαν αποτελέσματα, δοκιμάστε ξανά."}, "sbi_txt__min_order": {"message": "Ελάχ. Σειρά"}, "sbi_visiting": {"message": "Περιήγηση"}, "sbi_yiwugo__jiagexiangtan": {"message": "Επικοινωνήστε με τον πωλητή για την τιμή"}, "sbi_yiwugo__qigou": {"message": "$num$ Τεμάχια (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Αστέρια"}, "searchByImage_screenshot": {"message": "Στιγμιότυπο οθόνης με ένα κλικ"}, "searchByImage_search": {"message": "Αναζήτηση με ένα κλικ για τα ίδια στοιχεία"}, "searchByImage_size_type": {"message": "Το μέγεθος αρχείου δεν μπορεί να είναι μεγαλύτερο από $num$ MB, μόνο $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Ανάλυση εικόνας"}, "search_by_image_progress_searching": {"message": "Αναζήτηση προϊόντων"}, "search_by_image_progress_sending": {"message": "Αποστολή εικόνας"}, "search_by_image_response_rate": {"message": "Ποσο<PERSON>τ<PERSON> απόκρισης: $responseRate$ των αγοραστών που επικοινώνησαν με αυτόν τον προμηθευτή έλαβαν απάντηση εντός $ResponsInHour$ ώρες.", "placeholders": {"responseRate": {"content": "$1"}, "ResponsInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Αναζήτηση με Λέξη-κλειδί:"}, "select_country_language_modal_title_country": {"message": "Χώ<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Γλώσσα"}, "select_country_region_modal_title": {"message": "Επιλέξτε χώρα / περιοχή"}, "select_language_modal_title": {"message": "Επιλέξτε μια γλώσσα:"}, "select_shop": {"message": "Επιλέξτε κατάστημα"}, "sellers_count": {"message": "Αριθ<PERSON><PERSON><PERSON> πωλητών στην τρέχουσα σελίδα"}, "sellers_count_per_page": {"message": "Αριθ<PERSON><PERSON><PERSON> πωλητών στην τρέχουσα σελίδα"}, "service_score": {"message": "Ολοκληρωμένη βαθμολογία εξυπηρέτησης"}, "set_shortcut_keys": {"message": "Ορισμός πλήκτρων συντόμευσης"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αγορών"}, "setting_modal_options_position_title": {"message": "Θέση προσθήκης"}, "setting_modal_options_position_value_left": {"message": "Αριστερή γωνία"}, "setting_modal_options_position_value_right": {"message": "Σωστή γωνία"}, "setting_modal_options_theme_title": {"message": "Χρώμα θέματος"}, "setting_modal_options_theme_value_dark": {"message": "Σκοτάδι"}, "setting_modal_options_theme_value_light": {"message": "Φως"}, "setting_modal_title": {"message": "Ρυθμίσεις"}, "setting_options_country_title": {"message": "Χώρα / Περιφέρεια"}, "setting_options_hover_zoom_desc": {"message": "Ποντίκι για μεγέθυνση"}, "setting_options_hover_zoom_title": {"message": "Αιωρήστε το ζουμ"}, "setting_options_jd_coupon_desc": {"message": "Βρέθηκε κουπόνι στο JD.com"}, "setting_options_jd_coupon_title": {"message": "Κουπόνι JD.com"}, "setting_options_language_title": {"message": "Γλώσσα"}, "setting_options_price_drop_alert_desc": {"message": "Όταν η τιμή των προϊόντων στο My Favorite μειωθεί, θα λάβετε ειδοποίηση push."}, "setting_options_price_drop_alert_title": {"message": "Ειδοποίηση πτώσης τιμών"}, "setting_options_price_history_on_list_page_desc": {"message": "Εμφάνιση ιστορικού τιμών στη σελίδα αναζήτησης προϊόντων"}, "setting_options_price_history_on_list_page_title": {"message": "Ιστορικ<PERSON> τιμών (Σελίδ<PERSON> λίστας)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Εμφάνιση ιστορικού προϊόντος στη σελίδα λεπτομερειών προϊόντος"}, "setting_options_price_history_on_produt_page_title": {"message": "Ιστορικ<PERSON> τιμών (σελίδα λεπτομερειών)"}, "setting_options_sales_analysis_desc": {"message": "Υποστήριξη στατιστικών στοιχείων τιμής, όγκ<PERSON><PERSON> πωλήσεων, αριθμού πωλητών και αναλογίας πωλήσεων καταστήματος στη σελίδα λίστας προϊόντων $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Ανάλυση πωλήσεων"}, "setting_options_save_success_msg": {"message": "Επιτυχία"}, "setting_options_tacking_price_title": {"message": "Ειδοποίηση αλλαγής τιμής"}, "setting_options_value_off": {"message": "Μακριά από"}, "setting_options_value_on": {"message": "Επί"}, "setting_pkg_quick_view_desc": {"message": "Υποστήριξη: 1688 & Taobao"}, "setting_saved_message": {"message": "Οι αλλαγές αποθηκεύτηκαν με επιτυχία"}, "setting_section_enable_platform_title": {"message": "Εκτ<PERSON><PERSON> λειτουργίας"}, "setting_section_setting_title": {"message": "Ρυθμίσεις"}, "setting_section_shortcuts_title": {"message": "Συντομεύσεις"}, "settings_aliprice_agent__desc": {"message": "Εμφανίζεται στη σελίδα λεπτομερειών προϊόντος $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Buy-for-me"}, "settings_copy_link__desc": {"message": "Εμφάνιση στη σελίδα λεπτομερειών προϊόντος"}, "settings_copy_link__title": {"message": "Κουμπί αντιγραφής συνδέσμου"}, "settings_currency_desc__for_detail": {"message": "Υποστήριξη σελίδας λεπτομερειών προϊόντος 1688"}, "settings_currency_desc__for_list": {"message": "Αναζήτηση με εικόνα (συμπεριλάβετε το 1688/1688 στο εξωτερικό / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Επιλέξτε την τιμή"}, "settings_currency_desc_display_for_list": {"message": "Εμφανίζ<PERSON><PERSON><PERSON><PERSON> στην αναζήτηση εικόνων (συμπεριλαμβανομένου του 1688/1688 στο εξωτερικό/Taobao)"}, "settings_currency_rate_desc": {"message": "Ενημέρωση συναλλαγματικής ισοτιμίας από \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Τράπεζα της <PERSON>ς"}, "settings_download_images__desc": {"message": "Υποστήριξη για λήψη εικόνων από $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "κουμπί λήψης εικόνας"}, "settings_download_reviews__desc": {"message": "Εμφανίζεται στη σελίδα λεπτομερειών προϊόντος $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Λήψη εικόνων κριτικής"}, "settings_google_translate_desc": {"message": "Κάντε δεξί κλικ για λήψη της γραμμής μετάφρασης Google"}, "settings_google_translate_title": {"message": "μετάφραση ιστοσελίδας"}, "settings_historical_trend_desc": {"message": "Εμφάνιση στην κάτω δεξιά γωνία της εικόνας στη σελίδα λίστας προϊόντων"}, "settings_modal_btn_more": {"message": "Περισσότερες ρυθμίσεις"}, "settings_productInfo_desc": {"message": "Εμφανίστε πιο λεπτομερείς πληροφορίες προϊόντος στη σελίδα λίστας προϊόντων. Η ενεργοποίηση αυτού μπορεί να αυξήσει τη φόρτωση του υπολογιστή και να προκαλέσει καθυστέρηση σελίδας. Εάν επηρεάζει την απόδοση, συνιστάται να την απενεργοποιήσετε."}, "settings_product_recommend__desc": {"message": "Εμφανίζ<PERSON><PERSON><PERSON><PERSON> κάτω από την κύρια εικόνα στη σελίδα λεπτομερειών προϊόντος $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Συνιστώμενα προϊόντα"}, "settings_research_desc": {"message": "Ζητήστε πιο λεπτομερείς πληροφορίες στη σελίδα λίστας προϊόντων"}, "settings_sbi_add_to_list": {"message": "Προσθήκη στο $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Μικρογρα<PERSON><PERSON><PERSON> αποτελέσματος αναζήτησης εικόνων"}, "settings_sbi_remove_from_list": {"message": "Κατάργηση από το $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Προσθήκη στη λίστα αποκλεισμού"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Ρυθμίστε τη θέση εισόδου"}, "settings_search_by_image_blacklist_desc": {"message": "Να μην εμφανίζεται εικονίδιο σε ιστότοπους στη μαύρη λίστα."}, "settings_search_by_image_blacklist_title": {"message": "Λίστα των μπλοκαρισμένων"}, "settings_search_by_image_bottom_left": {"message": "Κάτω αριστερά"}, "settings_search_by_image_bottom_right": {"message": "Κάτω δεξιά"}, "settings_search_by_image_clear_blacklist": {"message": "Διαγρα<PERSON><PERSON> λίστας αποκλεισμού"}, "settings_search_by_image_detail_page_icon_title": {"message": "Ονυξ του αντίχειρος"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "Μεγα<PERSON>ύτ<PERSON><PERSON>ος"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Μικρ<PERSON>τερος"}, "settings_search_by_image_display_button_desc": {"message": "Ένα κλικ στο εικονίδιο για αναζήτηση με εικόνα"}, "settings_search_by_image_display_button_title": {"message": "Εικον<PERSON><PERSON><PERSON><PERSON> στις εικόνες"}, "settings_search_by_image_sourece_websites_desc": {"message": "Βρείτε το προϊόν προέλευσης σε αυτούς τους ιστότοπους"}, "settings_search_by_image_sourece_websites_title": {"message": "Αναζήτηση με αποτέλεσμα εικόνας"}, "settings_search_by_image_top_left": {"message": "Πάνω αριστερά"}, "settings_search_by_image_top_right": {"message": "Επάνω δεξιά"}, "settings_search_keyword_on_x__desc": {"message": "Αναζήτηση λέξεων στο $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Εμφάνιση του εικονιδίου $platform$ όταν επιλέγονται λέξεις", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Προσπαθήστε να βρείτε το ίδιο προϊόν σε αυτούς τους ιστότοπους (έως 5)"}, "settings_similar_products_title": {"message": "Βρείτε το ίδιο προϊόν"}, "settings_toolbar_expand_title": {"message": "Ελαχιστοποίηση προσθέτου"}, "settings_top_toolbar_desc": {"message": "Γρα<PERSON><PERSON><PERSON> αναζήτησης στο επάνω μέρος της σελίδας"}, "settings_top_toolbar_title": {"message": "Μπαρα αναζήτησης"}, "settings_translate_search_desc": {"message": "Μετάφραση στα κινέζικα και αναζήτηση"}, "settings_translate_search_title": {"message": "Πολυγλωσ<PERSON><PERSON><PERSON><PERSON> αναζήτηση"}, "settings_translator_contextmenu_title": {"message": "Λή<PERSON>η για μετάφραση"}, "settings_translator_title": {"message": "Μεταφράζω"}, "shai_xuan_dao_chu": {"message": "Φιλτράρισμα για εξαγωγή"}, "shai_xuan_zi_duan": {"message": "Φιλτράρισμα πεδίων"}, "shang_jia_shi_jian": {"message": "Σε χρόνο αποθήκευσης"}, "shang_pin_biao_ti": {"message": "τίτλος προϊόντος"}, "shang_pin_dui_bi": {"message": "Σύγκριση Προϊόντων"}, "shang_pin_lian_jie": {"message": "σύνδεσμος προϊόντος"}, "shang_pin_xin_xi": {"message": "Πληροφορίες προϊόντος"}, "share_modal__content": {"message": "Μοιράσου το με τους φίλους σου"}, "share_modal__disable_for_while": {"message": "Δεν θέλω να μοιραστώ τίποτα"}, "share_modal__title": {"message": "Σας αρέσει το $extensionName$;", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Παραμένων"}, "shi_fou_ke_ding_zhi": {"message": "Είναι προσαρμόσιμο;"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Πιστοποιη<PERSON><PERSON>νος Προμηθευτής"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Πιστοποιημένοι προμηθευτές"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Εμπορική ασφάλιση"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Εμπορικές εγγυήσεις"}, "shipping_fee": {"message": "Εξοδα αποστολής"}, "shop_followers": {"message": "Οι οπαδοί των καταστημάτων"}, "shou_qi": {"message": "Μείον"}, "similar_products_warn_max_platforms": {"message": "Μέγιστο έως 5"}, "sku_calc_price": {"message": "Υπολογιζόμενη Τιμή"}, "sku_calc_price_settings": {"message": "Ρυθμίσεις Υπολογιζόμενης Τιμής"}, "sku_formula": {"message": "Τύπος"}, "sku_formula_desc": {"message": "Περιγραφή Τύπου"}, "sku_formula_desc_text": {"message": "Υποστηρίζει σύνθετους μαθηματικούς τύπους, με την αρχική τιμή να αντιπροσωπεύεται από το A και το φορτίο να αντιπροσωπεύεται από το B\n\n<br/>\n\nΥποστηρίζει αγκύλες (), συν +, πλην -, πολλαπλασιασμό * και διαίρεση /\n\n<br/>\n\nΠαράδειγμα:\n\n<br/>\n\n1. Για να επιτευχθεί 1,2 φορές η αρχική τιμή και στη συνέχεια να προστεθεί το φορτίο, ο τύπος είναι: A*1,2+B\n\n<br/>\n\n2. Γι<PERSON> να επιτευχθεί η αρχική τιμή συν 1 γιουάν, στη συνέχεια να πολλαπλασιαστεί επί 1,2 φορές, ο τύπος είναι: (A+1)*1,2\n\n<br/>\n\n3. Γι<PERSON> να επιτευχθεί η αρχική τιμή συν 10 γιουάν, στη συνέχεια να πολλαπλασιαστεί επί 1,2 φορές και στη συνέχεια να αφαιρεθούν 3 γιουάν, ο τύπος είναι: (A+10)*1,2-3"}, "sku_in_stock": {"message": "Σε Απόθεμα"}, "sku_invalid_formula_format": {"message": "Μη έγκυρη μορφή τύπου"}, "sku_inventory": {"message": "Απόθεμα"}, "sku_link_copy_fail": {"message": "Αντιγράφηκε με επιτυχία, οι προδιαγραφές και τα χαρακτηριστικά sku δεν έχουν επιλεγεί"}, "sku_link_copy_success": {"message": "Αντιγράφηκε με επιτυχία, επιλέχθηκαν οι προδιαγραφές και τα χαρακτηριστικά του sku"}, "sku_list": {"message": "Λίστα SKU"}, "sku_min_qrder_qty": {"message": "Ελάχιστη Ποσότητα Παραγγελίας"}, "sku_name": {"message": "Όνομα SKU"}, "sku_no": {"message": "Αριθμός"}, "sku_original_price": {"message": "Αρχική Τιμή"}, "sku_price": {"message": "Τιμή SKU"}, "stop_track_time_label": {"message": "Προθεσμία παρακολούθησης:"}, "suo_zai_di_qu": {"message": "τοποθεσία"}, "tab_pkg_quick_view": {"message": "Logistics Monitor"}, "tab_product_details_price_history": {"message": "Ιστορικό τιμών"}, "tab_product_details_reviews": {"message": "Σχόλια φωτογραφιών"}, "tab_product_details_seller_analysis": {"message": "Ανάλυση πωλητή"}, "tab_product_details_similar_products": {"message": "Ίδια προϊόντα"}, "total_days_listed_per_product": {"message": "Άθροισμα ημερών αποθήκευσης ÷ Αριθμός προϊόντων"}, "total_items": {"message": "Συνολικός αριθμός προϊόντων"}, "total_price_per_product": {"message": "Άθροισμα τιμών ÷ Αριθμός προϊόντων"}, "total_rating_per_product": {"message": "Άθροισμα βαθμολογιών ÷ Αριθμός προϊόντων"}, "total_revenue": {"message": "Τα συνολικά έσοδα"}, "total_revenue40_items": {"message": "Συνολικά έσοδα από τα $amount$ προϊόντα στην τρέχουσα σελίδα", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Συνολικές πωλήσεις"}, "total_sales40_items": {"message": "Συνολικές πωλήσεις των $amount$ προϊόντων στην τρέχουσα σελίδα", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Παρακολούθηση για: 1 έτος"}, "track_for_3_months": {"message": "Παρακολούθηση για: 3 μήνες"}, "track_for_6_months": {"message": "Παρακολούθηση για: 6 μήνες"}, "tracking_price_email_add_btn": {"message": "Προσθήκη email"}, "tracking_price_email_edit_btn": {"message": "Επεξεργασία email"}, "tracking_price_email_intro": {"message": "Θα σας ειδοποιήσουμε μέσω email."}, "tracking_price_email_invalid": {"message": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON>τε ένα έγκυρο email"}, "tracking_price_email_verified_desc": {"message": "Τώρα μπορείτε να λαμβάνετε την ειδοποίηση πτώσης τιμών."}, "tracking_price_email_verified_title": {"message": "Επιβεβαιώθηκε με επιτυχία"}, "tracking_price_email_verify_desc_line1": {"message": "Στείλα<PERSON><PERSON> έναν σύνδεσμο επαλήθευσης στη διεύθυνση email σας,"}, "tracking_price_email_verify_desc_line2": {"message": "παρα<PERSON><PERSON><PERSON><PERSON> ελέγξτε τα εισερχόμενα του email σας."}, "tracking_price_email_verify_title": {"message": "Επαλήθευση email"}, "tracking_price_web_push_notification_intro": {"message": "Στην επιφάνεια εργασίας: Το AliPrice μπορεί να παρακολουθεί οποιοδήποτε προϊόν για εσάς και να σας στέλνει μια ειδοποίηση push Web μόλις αλλάξει η τιμή."}, "tracking_price_web_push_notification_title": {"message": "Ειδοποιήσεις προώθησης ιστού"}, "translate_im__login_required": {"message": "Μετα<PERSON>ράστηκε από την <PERSON>, συνδεθείτε στο $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Μετα<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON><PERSON> και αντιγρά<PERSON>ηκε στο πρόχειρο, αλλ<PERSON> λόγω του περιορισμού του <PERSON>, πρέπει να το επικολλήσετε χειροκίνητα!"}, "translate_im__send": {"message": "Μετάφραση & Αποστολή"}, "translate_search": {"message": "Μετάφραση και αναζήτηση"}, "translation_originals_translated": {"message": "Πρωτότυπο και κινέζικο"}, "translation_translated": {"message": "κινέζικα"}, "translator_btn_capture_txt": {"message": "Μεταφράζω"}, "translator_language_auto_detect": {"message": "Αυτόματη ανίχνευση"}, "translator_language_detected": {"message": "Εντοπίστηκε"}, "translator_language_search_placeholder": {"message": "Αναζήτηση γλώσσας"}, "try_again": {"message": "Προσπάθη<PERSON><PERSON> ξανά"}, "tu_pian_chi_cun": {"message": "Μέγ<PERSON><PERSON>ος εικόνας:"}, "tu_pian_lian_jie": {"message": "Σύνδεσμος εικόνας"}, "tui_huan_ti_yan": {"message": "Εμπειρία επιστροφής"}, "tui_huan_ti_yan__desc": {"message": "Αξιολογήστε τους δείκτες μετά την πώληση των πωλητών"}, "tutorial__show_all": {"message": "Όλα τα χαρακτηριστικά"}, "tutorial_ae_popup_title": {"message": "Καρ<PERSON>ιτσώστε την επέκταση, ανοίξτε το Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Ανάλυση κριτικής AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Υποστήριξη USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Αποστολή σε Κορέα/Ιαπωνία/Ηπειρωτική Κίνα"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Υποστηρίζει αγορές στο εξωτερικό"}, "tutorial_auto_apply_coupon_title": {"message": "Αυτόματη εφαρμογή κουπονιού"}, "tutorial_btn_example": {"message": "Παράδειγμα"}, "tutorial_btn_see_more": {"message": "Περισσότερες λειτουργίες"}, "tutorial_compare_products": {"message": "Συγκρίνετε προϊόντα"}, "tutorial_currency_convert_title": {"message": "Μετατροπή νομίσματος"}, "tutorial_export_shopping_cart": {"message": "Εξαγωγή ως <PERSON>V, Υποστήριξη Taobao και 1688"}, "tutorial_export_shopping_cart_title": {"message": "Καλάθι εξαγωγής"}, "tutorial_price_history_pro": {"message": "Εμφανίζεται στη σελίδα λεπτομερειών προϊόντος.\nΥποστήριξη Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Ιστορικ<PERSON> τιμών και παραγγελιών ολόκληρου του έτους"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Λήψη για αναζήτηση με εικόνα"}, "tutorial_translate_search": {"message": "Μετάφραση σε αναζήτηση"}, "tutorial_translate_search_and_package_tracking": {"message": "Αναζήτηση μετάφρασης και παρακολούθηση πακέτων"}, "unit_bao": {"message": "τεμ"}, "unit_ben": {"message": "τεμ"}, "unit_bi": {"message": "παραγγελίες"}, "unit_chuang": {"message": "τεμ"}, "unit_dai": {"message": "τεμ"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "τεμ"}, "unit_ge": {"message": "τεμ"}, "unit_he": {"message": "τεμ"}, "unit_jian": {"message": "τεμ"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "τεμ"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "τεμ"}, "unit_ti": {"message": "τεμ"}, "unit_tiao": {"message": "τεμ"}, "unit_xiang": {"message": "τεμ"}, "unit_zhang": {"message": "τεμ"}, "unit_zhi": {"message": "τεμ"}, "verify_contact_support": {"message": "Επικοινωνήστε με την Υποστήριξη"}, "verify_human_verification": {"message": "Ανθρώπινη επαλήθευση"}, "verify_unusual_access": {"message": "Εντοπίστηκε ασυνήθιστη πρόσβαση"}, "view_history_clean_all": {"message": "Καθαρ<PERSON>στε όλα"}, "view_history_clean_all_warring": {"message": "Καθα<PERSON>ι<PERSON><PERSON><PERSON>ς όλων των εγγραφών που προβλήθηκαν;"}, "view_history_clean_all_warring_title": {"message": "Προειδοποίηση"}, "view_history_viewd": {"message": "Προβολή"}, "website": {"message": "δικτυακ<PERSON>ς τόπος"}, "weight": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Δεν είναι δυνατή η λήψη των δεδομένων"}, "wu_liu_shi_xiao": {"message": "Έγκαιρη αποστολή"}, "wu_liu_shi_xiao__desc": {"message": "Το ποσοστό είσπραξης 48 ωρών και το ποσοστό εκπλήρωσης του καταστήματος του πωλητή"}, "xia_dan_jia": {"message": "Τελική τιμή"}, "xian_xuan_ze_product_attributes": {"message": "Επιλέξτε τα χαρακτηριστικά του προϊόντος"}, "xiao_liang": {"message": "Ογκος ΠΩΛΗΣΕΩΝ"}, "xiao_liang_zhan_bi": {"message": "Ποσοστό του όγκου πωλήσεων"}, "xiao_shi": {"message": "$num$ Ώρες", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Εσοδα"}, "xiao_shou_e_zhan_bi": {"message": "Ποσοστ<PERSON> εσόδων"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Επιλέξτε $amount$ εγγραφές", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Επιλογή"}, "yi_ding_zai_zuo_ce": {"message": "Καρφιτσωμένο"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Όλα τα προϊόντα φορτώθηκαν"}, "yi_nian_xiao_liang": {"message": "Ετήσιες <PERSON>ωλήσεις"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Ετή<PERSON><PERSON><PERSON>ί<PERSON>ιο <PERSON>ωλήσεων"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Μερίδιο Ετήσιου Κύκλου Εργασιών"}, "yi_shua_xin": {"message": "Ξεκούραστος"}, "yin_cang_xiang_tong_dian": {"message": "κρύβουν ομοιότητες"}, "you_xiao_liang": {"message": "Με Όγκο Πωλήσεων"}, "yu_ji_dao_da_shi_jian": {"message": "Εκτιμώμενος χρόνος άφιξης"}, "yuan_gong_ren_shu": {"message": "Αριθμός εργαζομένων"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yue_dai_xiao": {"message": "Η ναυτιλία πτώσης"}, "yue_dai_xiao__desc": {"message": "Dropshipping εκπτώσεις τις τελευταίες 30 ημέρες"}, "yue_dai_xiao_pai_xu__desc": {"message": "Οι πωλήσεις Dropshipping τις τελευταίες 30 ημέρες, ταξινομημένες από υψηλό σε χαμηλό"}, "yue_xiao_liang__desc": {"message": "Όγκος πωλήσεων τις τελευταίες 30 ημέρες"}, "zhan_kai": {"message": "Περισσότερο"}, "zhe_kou": {"message": "Εκπτωση"}, "zhi_chi_yi_jian_dai_fa": {"message": "Η ναυτιλία πτώσης"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Δω<PERSON><PERSON><PERSON><PERSON> αποστολή"}, "zhi_fu_ding_dan_shu": {"message": "Παραγγελίες επί πληρωμή"}, "zhi_fu_ding_dan_shu__desc": {"message": "Αριθμός παραγγελιών για αυτό το προϊόν (30 ημέρες)"}, "zhu_ce_xing_zhi": {"message": "Φύση εγγραφής"}, "zi_ding_yi_tiao_jian": {"message": "Προσαρμοσμένες συνθήκες"}, "zi_duan": {"message": "Πεδία"}, "zi_ti_xiao_liang": {"message": "Παραλλα<PERSON><PERSON>ουλήθηκε"}, "zong_he_fu_wu_fen": {"message": "Συνολική βαθμολογία"}, "zong_he_fu_wu_fen__desc": {"message": "Συνολική βαθμολογία της υπηρεσίας πωλητή"}, "zong_he_fu_wu_fen__short": {"message": "Εκτίμηση"}, "zong_he_ti_yan_fen": {"message": "Εκτίμηση"}, "zong_he_ti_yan_fen_3": {"message": "Κάτω από 4 αστέρια"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 αστέρια"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 αστέρια"}, "zong_he_ti_yan_fen_5": {"message": "5 αστέρια"}, "zong_ku_cun": {"message": "Συνολικό απόθεμα"}, "zong_xiao_liang": {"message": "Συνολικές πωλήσεις"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Ποσοστ<PERSON> απόκρισης 3 λεπτών τις τελευταίες 30 ημέρες"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Πο<PERSON><PERSON><PERSON><PERSON><PERSON> ανάκτησης 48 ωρών τις τελευταίες 30 ημέρες"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Ρυθμός απόδοσης 48 ωρών τις τελευταίες 30 ημέρες"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Ρεκόρ συναλλαγών (30 ημέρες)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Ρεκόρ συναλλαγών (30 ημέρες)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Ποσοστ<PERSON> αμφισβήτησης τις τελευταίες 30 ημέρες"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Ποσο<PERSON>τ<PERSON> επιστροφής χρημάτων ποιότητας τις τελευταίες 30 ημέρες"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Ο αριθμός των εντολών πληρωμής τις τελευταίες 30 ημέρες"}}