{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 نقطه فروش داغ فرامرزی"}, "1688_shi_li_ren_zheng": {"message": "گواهینامه قدرت 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "بیش از 1 سال"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "تحویل 24 ساعته"}, "24H_lan_shou_lv": {"message": "نرخ بسته بندی 24 ساعته"}, "30D_shang_xin": {"message": "ورودی های جدید ماهانه"}, "30d_sales": {"message": "$amount$ در 30 روز فروخته شد", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "پاسخ در عرض 3 دقیقه"}, "3Min_xiang_ying_lv__desc": {"message": "نسبت پاسخ‌های مؤثر Wangwang به پیام‌های درخواست خریدار در عرض 3 دقیقه در 30 روز گذشته"}, "48H": {"message": "48 ساعت"}, "48H_fa_huo": {"message": "تحویل در 48 ساعت"}, "48H_lan_shou_lv": {"message": "نرخ بسته بندی 48 ساعته"}, "48H_lan_shou_lv__desc": {"message": "نسبت تعداد سفارش های دریافتی ظرف 48 ساعت به تعداد کل سفارش ها"}, "48H_lv_yue_lv": {"message": "نرخ عملکرد 48 ساعته"}, "48H_lv_yue_lv__desc": {"message": "نسبت تعداد سفارش دریافتی یا تحویلی ظرف 48 ساعت به تعداد کل سفارشات"}, "72H": {"message": "72 ساعت"}, "7D_shang_xin": {"message": "تازه واردهای هفتگی"}, "7D_wu_li_you": {"message": "7 روز مراقبت رایگان"}, "ABS_title_text": {"message": "این فهرست شامل داستان برند است"}, "AC_title_text": {"message": "این فهرست دارای نشان انتخاب آمازون است"}, "A_title_text": {"message": "این فهرست دارای یک صفحه محتوای A+ است"}, "BS_title_text": {"message": "این فهرست به عنوان $num$ بهترین فروشنده در دسته $type$ رتبه بندی شده است", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (معامله مدت زمان محدود) به این معنی است که این فهرست بخشی از یک رویداد \"تبلیغات 7 روزه\" است."}, "NR_title_text": {"message": "این فهرست به عنوان $num$ نسخه جدید در دسته $type$ رتبه بندی شده است", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "این فهرست دارای یک تبلیغ ویدیویی است، نوعی تبلیغ PPC که معمولاً در وسط نتایج جستجو ظاهر می شود"}, "SB_title_text": {"message": "این فهرست دارای یک تبلیغ برند است، نوعی تبلیغات PPC که معمولاً در بالا یا پایین نتایج جستجو ظاهر می شود"}, "SP_title_text": {"message": "این فهرست دارای یک تبلیغ محصول حمایت شده است"}, "V_title_text": {"message": "این فهرست دارای یک معرفی ویدیویی است"}, "advanced_research": {"message": "تحقیقات پیشرفته"}, "agent_ds1688___my_order": {"message": "دستورات من"}, "agent_ds1688__add_to_cart": {"message": "<PERSON><PERSON><PERSON><PERSON> خارج از کشور"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON> خرید"}, "agent_ds1688__desc": {"message": "ارائه شده توسط 1688. خرید مستقیم از خارج از کشور، پرداخت به دلار و تحویل به انبار حمل و نقل شما در چین را پشتیبانی می کند."}, "agent_ds1688__freight": {"message": "ماشین حساب هزینه حمل و نقل"}, "agent_ds1688__help": {"message": "کمک"}, "agent_ds1688__packages": {"message": "بارنامه"}, "agent_ds1688__profile": {"message": "مرکز شخصی"}, "agent_ds1688__warehouse": {"message": "انبار من"}, "ai_comment_analysis_advantage": {"message": "طرفداران"}, "ai_comment_analysis_ai": {"message": "تجزیه و تحلیل بررسی هوش مصنوعی"}, "ai_comment_analysis_available": {"message": "در دسترس"}, "ai_comment_analysis_balance": {"message": "سکه ها کافی نیست، لطفا شارژ کنید"}, "ai_comment_analysis_behavior": {"message": "رفتار - اخلاق"}, "ai_comment_analysis_characteristic": {"message": "ویژگی های جمعیت"}, "ai_comment_analysis_comment": {"message": "محصول بررسی های کافی برای نتیجه گیری دقیق ندارد، لطفاً محصولی با بررسی های بیشتر انتخاب کنید."}, "ai_comment_analysis_consume": {"message": "مصرف تخمینی"}, "ai_comment_analysis_default": {"message": "بررسی های پیش فرض"}, "ai_comment_analysis_desire": {"message": "انتظارات مشتری"}, "ai_comment_analysis_disadvantage": {"message": "من<PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "تلاش های رایگان"}, "ai_comment_analysis_freeNum": {"message": "1 اعتبار رایگان استفاده خواهد شد"}, "ai_comment_analysis_go_recharge": {"message": "برو به بالا"}, "ai_comment_analysis_intelligence": {"message": "تحلیل بررسی هوشمند"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_motive": {"message": "انگیزه خرید"}, "ai_comment_analysis_network_error": {"message": "خطای شبکه، لطفا دوباره امتحان کنید"}, "ai_comment_analysis_normal": {"message": "بررسی عکس"}, "ai_comment_analysis_number_reviews": {"message": "تعداد نظرات: $num$، مصرف تخمینی: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "نظرات کلی"}, "ai_comment_analysis_percentage": {"message": "در<PERSON>د"}, "ai_comment_analysis_problem": {"message": "مشکلات پرداخت"}, "ai_comment_analysis_reanalysis": {"message": "دوباره تحلیل کنید"}, "ai_comment_analysis_reason": {"message": "دلیل"}, "ai_comment_analysis_recharge": {"message": "شارژ کنید"}, "ai_comment_analysis_recharged": {"message": "من شارژ کردم"}, "ai_comment_analysis_retry": {"message": "دوباره امتحان کنید"}, "ai_comment_analysis_scene": {"message": "سناریوی استفاده"}, "ai_comment_analysis_start": {"message": "شروع به تجزیه و تحلیل کنید"}, "ai_comment_analysis_subject": {"message": "موضوعات"}, "ai_comment_analysis_time": {"message": "زمان استفاده"}, "ai_comment_analysis_tool": {"message": "ابزار هوش مصنوعی"}, "ai_comment_analysis_user_portrait": {"message": "مشخصات کاربر"}, "ai_comment_analysis_welcome": {"message": "به تحلیل بررسی هوش مصنوعی خوش آمدید"}, "ai_comment_analysis_year": {"message": "نظرات سال گذشته"}, "ai_listing_Exclude_keywords": {"message": "کلمات کلیدی را حذف کنید"}, "ai_listing_Login_the_feature": {"message": "ورود برای ویژگی مورد نیاز است"}, "ai_listing_aI_generation": {"message": "نسل هوش مصنوعی"}, "ai_listing_add_automatic": {"message": "<PERSON>و<PERSON><PERSON>ار"}, "ai_listing_add_dictionary_new": {"message": "یک کتابخانه جدید ایجاد کنید"}, "ai_listing_add_enter_keywords": {"message": "کلیدواژه ها را وارد کنید"}, "ai_listing_add_inputkey_selling": {"message": "نقطه فروش را وارد کنید و برای تکمیل افزودن، $key$ را فشار دهید", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "از حد مجاز فراتر رفت، تا $amount$ امتیاز فروش", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "کلمات کلیدی را اضافه کنید"}, "ai_listing_add_manually": {"message": "به صورت دستی اضافه کنید"}, "ai_listing_add_selling": {"message": "نقاط فروش را اضافه کنید"}, "ai_listing_added_keywords": {"message": "کلمات کلیدی اضافه شده است"}, "ai_listing_added_successfully": {"message": "با موفقیت اضافه شد"}, "ai_listing_addexcluded_keywords": {"message": "کلمات کلیدی حذف شده را وارد کنید، Enter را فشار دهید تا افزودن به پایان برسد."}, "ai_listing_adding_selling": {"message": "امتیاز فروش اضافه شد"}, "ai_listing_addkeyword_enter": {"message": "کلمات مشخصه کلیدی را تایپ کنید و اینتر را فشار دهید تا افزودن به پایان برسد"}, "ai_listing_ai_description": {"message": "کتابخانه کلمات توصیف هوش مصنوعی"}, "ai_listing_ai_dictionary": {"message": "کتابخانه کلمات عنوان AI"}, "ai_listing_ai_title": {"message": "عنوان هوش مصنوعی"}, "ai_listing_aidescription_repeated": {"message": "نام کتابخانه کلمه توصیف هوش مصنوعی قابل تکرار نیست"}, "ai_listing_aititle_repeated": {"message": "نام کتابخانه کلمه عنوان هوش مصنوعی قابل تکرار نیست"}, "ai_listing_data_comes_from": {"message": "این داده ها از:"}, "ai_listing_deleted_successfully": {"message": "با موفقیت حذف شد"}, "ai_listing_dictionary_name": {"message": "نام کتابخانه"}, "ai_listing_edit_dictionary": {"message": "اصلاح کتابخانه..."}, "ai_listing_edit_word_library": {"message": "کلمه کتابخانه را ویرایش کنید"}, "ai_listing_enter_keywords": {"message": "کلمات کلیدی را وارد کنید و برای تکمیل افزودن، $key$ را فشار دهید", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "از حد مجاز فراتر رفته است، حداکثر $amount$ کلمات کلیدی", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "کتابخانه کلمه حذف شده است"}, "ai_listing_generate_characters": {"message": "شخصیت ها را تولید کنید"}, "ai_listing_generation_platform": {"message": "پلت فرم نسل"}, "ai_listing_help_optimize": {"message": "به من کمک کنید عنوان محصول را بهینه کنم، عنوان اصلی است"}, "ai_listing_include_selling": {"message": "سایر نقاط فروش شامل:"}, "ai_listing_included_keyword": {"message": "شام<PERSON> کلمات کلیدی"}, "ai_listing_included_keywords": {"message": "شام<PERSON> کلمات کلیدی"}, "ai_listing_input_selling": {"message": "نقطه فروش را وارد کنید"}, "ai_listing_input_selling_fit": {"message": "امتیاز فروش را برای مطابقت با عنوان وارد کنید"}, "ai_listing_input_selling_please": {"message": "لطفا نقاط فروش را وارد کنید"}, "ai_listing_intelligently_title": {"message": "برای ایجاد هوشمندانه عنوان، محتوای مورد نیاز را در بالا وارد کنید"}, "ai_listing_keyword_product_title": {"message": "عنوان محصول کلمه کلیدی"}, "ai_listing_keywords_repeated": {"message": "کلمات کلیدی قابل تکرار نیستند"}, "ai_listing_listed_selling_points": {"message": "شامل نقاط فروش"}, "ai_listing_long_title_1": {"message": "حاوی اطلاعات اولیه مانند نام تجاری، نوع محصول، ویژگی های محصول و غیره است."}, "ai_listing_long_title_2": {"message": "بر اساس عنوان محصول استاندارد، کلمات کلیدی مناسب برای سئو اضافه می شوند."}, "ai_listing_long_title_3": {"message": "علاوه بر نام تجاری، نوع محصول، ویژگی های محصول و کلمات کلیدی، کلمات کلیدی دم بلند نیز برای دستیابی به رتبه های بالاتر در جست و جوهای جستجوی بخش بندی شده خاص گنجانده شده است."}, "ai_listing_longtail_keyword_product_title": {"message": "عنوان محصول کلیدواژه دم بلند"}, "ai_listing_manually_enter": {"message": "به صورت دستی وارد ..."}, "ai_listing_network_not_working": {"message": "اینترنت در دسترس نیست، برای دسترسی به ChatGPT به VPN نیاز است"}, "ai_listing_new_dictionary": {"message": "ایجاد یک کتابخانه کلمه جدید ..."}, "ai_listing_new_generate": {"message": "تو<PERSON>ید می کنند"}, "ai_listing_optional_words": {"message": "کلمات اختیاری"}, "ai_listing_original_title": {"message": "عنوان اصلی"}, "ai_listing_other_keywords_included": {"message": "سایر کلمات کلیدی شامل:"}, "ai_listing_please_again": {"message": "لطفا دوباره تلاش کنید"}, "ai_listing_please_select": {"message": "عناوین زیر برای شما ایجاد شده است، لطفا انتخاب کنید:"}, "ai_listing_product_category": {"message": "رده محصولات"}, "ai_listing_product_category_is": {"message": "دسته محصول است"}, "ai_listing_product_category_to": {"message": "محصول متعلق به چه دسته ای است؟"}, "ai_listing_random_keywords": {"message": "کلمات کلیدی تصادفی $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "امتیاز فروش تصادفی $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "تصادفی از کلمه کتابخانه"}, "ai_listing_search_selling": {"message": "جستجو بر اساس نقطه فروش"}, "ai_listing_select_product_categories": {"message": "دسته بندی محصولات را به صورت خودکار انتخاب کنید."}, "ai_listing_select_product_selling_points": {"message": "نقاط فروش محصول را به صورت خودکار انتخاب کنید"}, "ai_listing_select_word_library": {"message": "کتابخانه کلمه را انتخاب کنید"}, "ai_listing_selling": {"message": "امتیاز فروش"}, "ai_listing_selling_ask": {"message": "چه الزامات دیگری برای امتیاز فروش برای عنوان وجود دارد؟"}, "ai_listing_selling_optional": {"message": "نقاط فروش اختیاری"}, "ai_listing_selling_repeat": {"message": "امتیازها قابل تکرار نیستند"}, "ai_listing_set_excluded": {"message": "به عنوان کتابخانه کلمه حذف شده تنظیم کنید"}, "ai_listing_set_include_selling_points": {"message": "شامل نقاط فروش"}, "ai_listing_set_included": {"message": "مجموعه به عنوان کتابخانه کلمه گنجانده شده است"}, "ai_listing_set_selling_dictionary": {"message": "تنظیم به عنوان کتابخانه نقطه فروش"}, "ai_listing_standard_product_title": {"message": "عنوان محصول استاندارد"}, "ai_listing_translated_title": {"message": "عنوان ترجمه شده"}, "ai_listing_visit_chatGPT": {"message": "از ChatGPT دیدن کنید"}, "ai_listing_what_other_keywords": {"message": "چه کلمات کلیدی دیگری برای عنوان مورد نیاز است؟"}, "aliprice_coupons_apply_again": {"message": "دوباره درخواست کنید"}, "aliprice_coupons_apply_coupons": {"message": "کوپن ها را اعمال کنید"}, "aliprice_coupons_apply_success": {"message": "کوپن پیدا شد: $amount$ را ذخیره کنید", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "کدهای تست برای بهترین معاملات..."}, "aliprice_coupons_applying_desc": {"message": "در حال بررسی: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "به پرداخت ادامه دهید"}, "aliprice_coupons_found_coupons": {"message": "کوپن‌های $amount$ پیدا کردیم", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "آماده تسویه حساب هستید؟ بیایید مطمئن شویم که بهترین قیمت را دریافت می کنید!"}, "aliprice_coupons_no_coupon_aviable": {"message": "آن کدها کار نکردند مهم نیست - شما در حال حاضر بهترین قیمت را دریافت می کنید."}, "aliprice_coupons_toolbar_btn": {"message": "دریافت کوپن"}, "aliww_translate": {"message": "مترجم چت Ali<PERSON>wang"}, "aliww_translate_supports": {"message": "پشتیبانی: 1688 و Taobao"}, "amazon_extended_keywords_Keywords": {"message": "کلمات کلیدی"}, "amazon_extended_keywords_copy_all": {"message": "همه را کپی کنید"}, "amazon_extended_keywords_more": {"message": "بیشتر"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "مرتب سازی بر اساس فروش تجمعی"}, "an_lei_xing_cha_kan": {"message": "تای<PERSON> کنید"}, "an_yue_dai_xiao_pai_xu": {"message": "رتبه بندی بر اساس فروش dropshipping"}, "apra_btn__cat_name": {"message": "تجزیه و تحلیل نظرات"}, "apra_chart__name": {"message": "درصد فروش محصولات بر اساس کشور"}, "apra_chart__update_at": {"message": "زمان به روزرسانی $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "آمار فروش کشورها"}, "auto_opening": {"message": "باز شدن خودکار در $num$ ثانیه", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "صفحات بعدی $autoPaging$", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "میانگین در روزهای نگهداری"}, "average_hui_fu_lv": {"message": "میانگین نرخ پاسخ"}, "average_ping_gong_ying_shang_deng_ji": {"message": "میانگین سطح تامین کننده"}, "average_price": {"message": "قیمت میانگین"}, "average_qi_ding_liang": {"message": "میانگین MOQ"}, "average_rating": {"message": "میانگین امتیاز"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "میانگین سالهای صدور گواهینامه"}, "average_revenue": {"message": "در<PERSON><PERSON><PERSON> متوسط"}, "average_revenue_per_product": {"message": "کل درآمد ÷ تعداد محصولات"}, "average_sales": {"message": "میانگین فروش"}, "average_sales_per_product": {"message": "کل فروش ÷ تعداد محصولات"}, "bao_han": {"message": "حا<PERSON><PERSON>"}, "bao_zheng_jin": {"message": "لبه"}, "bian_ti_shu": {"message": "تغییرات"}, "biao_ti": {"message": "عنوان"}, "blacklist_add_blacklist": {"message": "این فروشگاه را مسدود کنید"}, "blacklist_address_incorrect": {"message": "آدرس نادرست است لطفا آن را چک کنید."}, "blacklist_blacked_out": {"message": "فروشگاه مسدود شده است"}, "blacklist_blacklist": {"message": "لیست سیاه"}, "blacklist_no_records_yet": {"message": "هنوز هیچ رکوردی وجود ندارد!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "نام تجاری"}, "btn_aliprice_agent__daigou": {"message": "واسطه خرید"}, "btn_aliprice_agent__dropshipping": {"message": "دراپشیپینگ"}, "btn_have_a_try": {"message": "امت<PERSON><PERSON> کنید"}, "btn_refresh": {"message": "تازه کردن"}, "btn_try_it_now": {"message": "الآن امتحانش کن"}, "btn_txt_view_on_aliprice": {"message": "مشاهده در AliPrice"}, "bu_bao_han": {"message": "شامل نمی شود"}, "bulk_copy_links": {"message": "کپی انبوه لینک ها"}, "bulk_copy_products": {"message": "محصولات کپی انبوه"}, "cai_gou_zi_xun": {"message": "خدمات مشتری"}, "cai_gou_zi_xun__desc": {"message": "نرخ پاسخ سه دقیقه ای فروشنده"}, "can_ping_lei_xing": {"message": "تای<PERSON> کنید"}, "cao_zuo": {"message": "<PERSON><PERSON><PERSON>"}, "chan_pin_ID": {"message": "شناسه محصول"}, "chan_pin_e_wai_xin_xi": {"message": "اطلاعات اضافی محصول"}, "chan_pin_lian_jie": {"message": "لینک محصول"}, "cheng_li_shi_jian": {"message": "زمان تاسیس"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "روی کپی اطلاعات محصول کلیک کنید"}, "commmon_txt_expired": {"message": "منقضی شده"}, "common__date_range_12m": {"message": "1 سال"}, "common__date_range_1m": {"message": "1 ماه"}, "common__date_range_1w": {"message": "1 هفته"}, "common__date_range_2w": {"message": "2 هفته"}, "common__date_range_3m": {"message": "3 ماه"}, "common__date_range_3w": {"message": "3 هفته"}, "common__date_range_6m": {"message": "6 ماه"}, "common_btn_cancel": {"message": "لغو"}, "common_btn_close": {"message": "نزدیک"}, "common_btn_save": {"message": "صرفه جویی"}, "common_btn_setting": {"message": "برپایی"}, "common_email": {"message": "پست الکترونیک"}, "common_error_msg_no_data": {"message": "اطلاعاتی وجود ندارد"}, "common_error_msg_no_result": {"message": "با عرض پوزش ، هیچ نتیجه ای یافت نشد"}, "common_favorites": {"message": "موارد دلخواه"}, "common_feedback": {"message": "بازخورد"}, "common_help": {"message": "کمک"}, "common_loading": {"message": "بارگذاری"}, "common_login": {"message": "وارد شدن"}, "common_logout": {"message": "خروج"}, "common_no": {"message": "نه"}, "common_powered_by_aliprice": {"message": "طراحی شده توسط AliPrice.com"}, "common_setting": {"message": "تنظیمات"}, "common_sign_up": {"message": "ثبت نام"}, "common_system_upgrading_title": {"message": "ارتقا System سیستم"}, "common_system_upgrading_txt": {"message": "لطفا بعدا امتحان کنید"}, "common_txt__currency": {"message": "وا<PERSON><PERSON> پول"}, "common_txt__video_tutorial": {"message": "آموزش تصویری"}, "common_txt_ago_time": {"message": "$time$ روز پیش", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "همه"}, "common_txt_analysis": {"message": "تحلیل و بررسی"}, "common_txt_basically_used": {"message": "تقریبا هرگز استفاده نشده است"}, "common_txt_biaoti_link": {"message": "عنوان + لینک"}, "common_txt_biaoti_link_dian_pu": {"message": "عنوان + لینک + نام فروشگاه"}, "common_txt_blacklist": {"message": "فهرست مسدود شده"}, "common_txt_cancel": {"message": "لغو کنید"}, "common_txt_category": {"message": "دسته بندی"}, "common_txt_chakan": {"message": "بررسی"}, "common_txt_colors": {"message": "رنگ"}, "common_txt_confirm": {"message": "تا<PERSON><PERSON>د"}, "common_txt_copied": {"message": "کپی شد"}, "common_txt_copy": {"message": "کپی 🀄"}, "common_txt_copy_link": {"message": "لینک را کپی کنید"}, "common_txt_copy_title": {"message": "کپی عنوان"}, "common_txt_copy_title__link": {"message": "عنوان و لینک را کپی کنید"}, "common_txt_day": {"message": "آسمان"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "نام فروشگاه + لینک را کپی کنید"}, "common_txt_download": {"message": "د<PERSON><PERSON><PERSON>د"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> کنید"}, "common_txt_export_as_csv": {"message": "صادرات اکسل"}, "common_txt_export_as_txt": {"message": "صادرات Txt"}, "common_txt_fail": {"message": "شکست"}, "common_txt_format": {"message": "قالب"}, "common_txt_get": {"message": "دریا<PERSON>ت کنید"}, "common_txt_incert_selection": {"message": "انتخاب معکوس"}, "common_txt_install": {"message": "نصب"}, "common_txt_load_failed": {"message": "بارگیری ناموفق بود"}, "common_txt_month": {"message": "ماه"}, "common_txt_more": {"message": "بیشتر"}, "common_txt_new_unused": {"message": "کاملا نو، استفاده نشده"}, "common_txt_next": {"message": "بعد"}, "common_txt_no_limit": {"message": "نامحدود"}, "common_txt_no_noticeable": {"message": "هیچ خراش یا کثیفی قابل مشاهده نیست"}, "common_txt_on_sale": {"message": "مو<PERSON><PERSON><PERSON> است"}, "common_txt_opt_in_out": {"message": "روشن خاموش"}, "common_txt_order": {"message": "سفارش"}, "common_txt_others": {"message": "دیگران"}, "common_txt_overall_poor_condition": {"message": "وضعیت کلی بد"}, "common_txt_patterns": {"message": "الگوهای"}, "common_txt_platform": {"message": "بستر، زمینه"}, "common_txt_please_select": {"message": "لطفاً انتخاب کنید"}, "common_txt_prev": {"message": "قب<PERSON>ی"}, "common_txt_price": {"message": "قیمت"}, "common_txt_privacy_policy": {"message": "سیاست حفظ حریم خصوصی"}, "common_txt_product_condition": {"message": "وضعیت محصول"}, "common_txt_rating": {"message": "رت<PERSON><PERSON> بندی"}, "common_txt_ratings": {"message": "رتبه بندی ها"}, "common_txt_reload": {"message": "بارگذاری مجدد"}, "common_txt_reset": {"message": "بازنشانی کنید"}, "common_txt_review": {"message": "مرور"}, "common_txt_sale": {"message": "مو<PERSON><PERSON><PERSON> است"}, "common_txt_same": {"message": "یکسان"}, "common_txt_scratches_and_dirt": {"message": "با خط و خش و کثیفی"}, "common_txt_search_title": {"message": "عنوان جستجو"}, "common_txt_select_all": {"message": "انتخاب همه"}, "common_txt_selected": {"message": "انتخاب شد"}, "common_txt_share": {"message": "اشتراک گذاری"}, "common_txt_sold": {"message": "فروخته شده"}, "common_txt_sold_out": {"message": "فروخته شد"}, "common_txt_some_scratches": {"message": "مقداری خراش و خاک"}, "common_txt_sort_by": {"message": "مرتب سازی بر اساس"}, "common_txt_state": {"message": "وضعیت"}, "common_txt_success": {"message": "مو<PERSON><PERSON><PERSON>ت"}, "common_txt_sys_err": {"message": "خطای سیستم"}, "common_txt_today": {"message": "امروز"}, "common_txt_total": {"message": "همه"}, "common_txt_unselect_all": {"message": "انتخاب معکوس"}, "common_txt_upload_image": {"message": "آپلود تصویر"}, "common_txt_visit": {"message": "با<PERSON><PERSON><PERSON><PERSON> کنید"}, "common_txt_whitelist": {"message": "لیست سفید"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "سال"}, "common_yes": {"message": "آره"}, "compare_tool_btn_clear_all": {"message": "همه را پاک کن"}, "compare_tool_btn_compare": {"message": "مقای<PERSON>ه کنید"}, "compare_tool_btn_contact": {"message": "م<PERSON>ا<PERSON>ب"}, "compare_tool_tips_max_compared": {"message": "تا $maxComparedCount$ اضافه کنید", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "اعلان ها را پیکربندی کنید"}, "contact_us": {"message": "با ما تماس بگیرید"}, "context_menu_screenshot_search": {"message": "عکسبرداری برای جستجو بر اساس تصویر"}, "context_menus_aliprice_search_by_image": {"message": "تصویر را در <PERSON><PERSON><PERSON> جستجو کنید"}, "context_menus_goote_trans": {"message": "ترجمه صفحه/نمایش اصلی"}, "context_menus_search_by_image": {"message": "جستجو بر اساس تصویر در $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "گرفتن در $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "ضبط برای ترجمه"}, "converter_modal_amount_placeholder": {"message": "مقدار را اینجا وارد کنید"}, "converter_modal_btn_convert": {"message": "تبدیل"}, "converter_modal_exchange_rate_source": {"message": "داده‌ها از نرخ ارز خارجی $boc$ می‌آیند زمان به‌روزرسانی: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "تبدیل ارز"}, "converter_modal_search_placeholder": {"message": "جستجوی ارز"}, "copy_all_contact_us_notice": {"message": "این سایت در حال حاضر پشتیبانی نمی شود، لطفا با ما تماس بگیرید"}, "copy_product_info": {"message": "کپی اطلاعات محصول"}, "copy_suggest_search_kw": {"message": "کپی لیست های کشویی"}, "country__han_gou": {"message": "کره جنوبی"}, "country__ri_ben": {"message": "ژاپن"}, "country__yue_nan": {"message": "ویتنام"}, "currency_convert__custom": {"message": "نرخ ارز سفارشی"}, "currency_convert__sync_server": {"message": "همگام سازی سرور"}, "dang_ri_fa_huo": {"message": "حمل و نقل در همان روز"}, "dao_chu_quan_dian_shang_pin": {"message": "صادرات تمام محصولات فروشگاه"}, "dao_chu_wei_CSV": {"message": "صادرات"}, "dao_chu_zi_duan": {"message": "زمینه های صادرات"}, "delivery_address": {"message": "آدرس حمل و نقل"}, "delivery_company": {"message": "شرکت تحویل"}, "di_zhi": {"message": "نشانی"}, "dian_ji_cha_xun": {"message": "برای پرس و جو کلیک کنید"}, "dian_pu_ID": {"message": "شناسه فروشگاه"}, "dian_pu_di_zhi": {"message": "آدرس فروشگاه"}, "dian_pu_lian_jie": {"message": "لینک فروشگاه"}, "dian_pu_ming": {"message": "نام فروشگاه"}, "dian_pu_ming_cheng": {"message": "نام فروشگاه"}, "dian_pu_shang_pin_zong_hsu": {"message": "تعداد کل محصولات در فروشگاه: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "اطلاعات فروشگاه"}, "ding_zai_zuo_ce": {"message": "به سمت چپ میخکوب شده"}, "disable_old_version_tips_disable_btn_title": {"message": "نسخه قدیمی را غیرفعال کنید"}, "download_image__SKU_variant_images": {"message": "تصاویر نوع SKU"}, "download_image__assume": {"message": "به عنوان مثال ما 2 تصویر داریم product1.jpg و product2.gif.\n\nimg_{$no$} به img_01.jpg، img_02.gif تغییر نام خواهد داد.\n\n{$group$}_{$no$} به main_image_01.jpg، main_image_02.gif تغییر نام خواهد داد.", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "دان<PERSON>ود دسته ای"}, "download_image__combined_image": {"message": "تصویر جزئیات محصول ترکیبی"}, "download_image__continue_downloading": {"message": "دانلود را ادامه دهید"}, "download_image__description_images": {"message": "تصاویر توضیحات"}, "download_image__download_combined_image": {"message": "دانلود تصویر جزئیات محصول ترکیبی"}, "download_image__download_zip": {"message": "دانلود به صورت zip"}, "download_image__enlarge_check": {"message": "فقط از تصاویر JPEG، JPG، GIF و PNG پشتیبانی می کند، حداکثر اندازه یک تصویر: 1600 * 1600"}, "download_image__enlarge_image": {"message": "تصویر را بزرگ کنید"}, "download_image__export": {"message": "صادرات"}, "download_image__height": {"message": "ارتفاع"}, "download_image__ignore_videos": {"message": "ویدیو نادیده گرفته شده است، زیرا نمی توان آن را صادر کرد"}, "download_image__img_translate": {"message": "ترجمه تصویر"}, "download_image__main_image": {"message": "تصویر اصلی"}, "download_image__multi_folder": {"message": "چند پوشه"}, "download_image__name": {"message": "د<PERSON><PERSON><PERSON>د تصویر"}, "download_image__notice_content": {"message": "لطفاً در تنظیمات دانلود مرورگر خود گزینه \"Ask Where to save هر فایل را قبل از دانلود\" تیک نزنید!!! در غیر این صورت کادرهای گفتگوی زیادی وجود خواهد داشت."}, "download_image__notice_ignore": {"message": "دیگر این پیام را درخواست نکنید"}, "download_image__order_number": {"message": "شماره سریال {$no$}؛ نام گروه {$group$}؛ مهر زمانی {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "بررسی اجمالی"}, "download_image__prompt_download_zip": {"message": "تعداد تصاویر بسیار زیاد است، بهتر است آنها را در یک پوشه فشرده دانلود کنید."}, "download_image__rename": {"message": "تغییر نام دهید"}, "download_image__rule": {"message": "قوانین نامگذاری"}, "download_image__single_folder": {"message": "تک پوشه"}, "download_image__sku_image": {"message": "تصاویر SKU"}, "download_image__video": {"message": "ویدئو"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "دان<PERSON>ود تصویر نقد"}, "download_reviews__dropdown_title": {"message": "دان<PERSON>ود تصویر نقد"}, "download_reviews__export_csv": {"message": "صادرات CSV"}, "download_reviews__no_images": {"message": "کپی نمونه: 0 عکس برای دانلود"}, "download_reviews__no_reviews": {"message": "هیچ نقدی برای دانلود وجود ندارد!"}, "download_reviews__notice": {"message": "نکته:"}, "download_reviews__notice__chrome_settings": {"message": "مرورگر Chrome را طوری تنظیم کنید که قبل از دانلود هر فایل را بپرسد کجا ذخیره شود، روی «خاموش» تنظیم کنید."}, "download_reviews__notice__wait": {"message": "بسته به تعداد بازبینی‌ها، زمان انتظار ممکن است طولانی‌تر باشد"}, "download_reviews__pages_list__all": {"message": "همه"}, "download_reviews__pages_list__page": {"message": "صفحات $page$ قبلی", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "محدوده انتخاب"}, "export_shopping_cart__csv_filed__details_url": {"message": "لینک محصول"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "لینک Echo SKU"}, "export_shopping_cart__csv_filed__images": {"message": "لینک تصویر"}, "export_shopping_cart__csv_filed__quantity": {"message": "تعداد"}, "export_shopping_cart__csv_filed__sale_price": {"message": "قیمت"}, "export_shopping_cart__csv_filed__specs": {"message": "مشخصات فنی"}, "export_shopping_cart__csv_filed__store_name": {"message": "نام فروشگاه"}, "export_shopping_cart__csv_filed__store_url": {"message": "لینک فروشگاه"}, "export_shopping_cart__csv_filed__title": {"message": "نام محصول"}, "export_shopping_cart__export_btn": {"message": "صادرات"}, "export_shopping_cart__export_empty": {"message": "لطفا یک محصول را انتخاب کنید!"}, "fa_huo_shi_jian": {"message": "حمل دریایی"}, "favorite_add_email": {"message": "ایمیل اضافه کنید"}, "favorite_add_favorites": {"message": "به موارد دلخواه اضافه کنید"}, "favorite_added": {"message": "اضافه شد"}, "favorite_btn_add": {"message": "ه<PERSON><PERSON><PERSON><PERSON> افت قیمت"}, "favorite_btn_notify": {"message": "پیگیری قیمت"}, "favorite_cate_name_all": {"message": "همه محصولات"}, "favorite_current_price": {"message": "قیمت فعلی"}, "favorite_due_date": {"message": "سررسید"}, "favorite_enable_notification": {"message": "لطفاً اعلان ایمیل را فعال کنید"}, "favorite_expired": {"message": "منقضی شده است"}, "favorite_go_to_enable": {"message": "برای فعال کردن بروید"}, "favorite_msg_add_success": {"message": "به موارد دلخواه اضافه شد"}, "favorite_msg_del_success": {"message": "از موارد دلخواه حذف شد"}, "favorite_msg_failure": {"message": "شکست! صفحه را تازه کنید و دوباره امتحان کنید."}, "favorite_please_add_email": {"message": "لطفا ایمیل اضافه کنید"}, "favorite_price_drop": {"message": "پایین"}, "favorite_price_rise": {"message": "بالا"}, "favorite_price_untracked": {"message": "قیمت پیگیری نشده است"}, "favorite_saved_price": {"message": "قیمت ذخیره شده"}, "favorite_stop_tracking": {"message": "ردیابی را متوقف کنید"}, "favorite_sub_email_address": {"message": "آدرس ایمیل اشتراک"}, "favorite_tracking_period": {"message": "دوره پیگیری"}, "favorite_tracking_prices": {"message": "پیگیری قیمت ها"}, "favorite_verify_email": {"message": "آدرس ایمیل را تأیید کنید"}, "favorites_list_remove_prompt_msg": {"message": "آیا مطمئن هستید که آن را حذف می کنید؟"}, "favorites_update_button": {"message": "اکنون قیمت ها را به روز کنید"}, "fen_lei": {"message": "دسته بندی"}, "fen_xia_yan_xuan": {"message": "انتخاب توزیع کننده"}, "find_similar": {"message": "مشابه را پیدا کنید"}, "first_ali_price_date": {"message": "تاریخی که اولین بار توسط خزنده AliPrice ثبت شد"}, "fooview_coupons_modal_no_data": {"message": "بدون کوپن"}, "fooview_coupons_modal_title": {"message": "کوپن"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "قیمت <$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "یا قیمت > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "ضر<PERSON> الاجل"}, "fooview_favorites_error_msg_no_favorites": {"message": "برای دریافت هشدار افت قیمت محصولات موردعلاقه را اینجا اضافه کنید."}, "fooview_favorites_filter_latest": {"message": "آخرین"}, "fooview_favorites_filter_price_drop": {"message": "شکسته شدن قیمت"}, "fooview_favorites_filter_price_up": {"message": "افزا<PERSON><PERSON> قیمت"}, "fooview_favorites_modal_title": {"message": "علا<PERSON>ق من"}, "fooview_favorites_modal_title_title": {"message": "به AliPrice Favorite بروید"}, "fooview_favorites_track_price": {"message": "برای پیگیری قیمت"}, "fooview_price_history_app_price": {"message": "قیمت برنامه:"}, "fooview_price_history_title": {"message": "تاریخچه قیمت"}, "fooview_product_list_feedback": {"message": "بازخورد"}, "fooview_product_list_orders": {"message": "سفارشات"}, "fooview_product_list_price": {"message": "قیمت"}, "fooview_reviews_error_msg_no_review": {"message": "ما هیچ نظری برای این محصول پیدا نکردیم"}, "fooview_reviews_filter_buyer_reviews": {"message": "عکس خریداران"}, "fooview_reviews_modal_title": {"message": "بررسی ها"}, "fooview_same_product_choose_category": {"message": "دسته را انتخاب کنید"}, "fooview_same_product_filter_feedback": {"message": "بازخورد"}, "fooview_same_product_filter_orders": {"message": "سفارشات"}, "fooview_same_product_filter_price": {"message": "قیمت"}, "fooview_same_product_filter_rating": {"message": "رت<PERSON><PERSON> بندی"}, "fooview_same_product_modal_title": {"message": "همین محصول را پیدا کنید"}, "fooview_same_product_search_by_image": {"message": "جستجو بر اساس تصویر"}, "fooview_seller_analysis_modal_title": {"message": "تحلیل فروشنده"}, "for_12_months": {"message": "به مدت 1 سال"}, "for_12_months_list_pro": {"message": "12 ماه"}, "for_12_months_nei": {"message": "در عرض 12 ماه"}, "for_1_months": {"message": "1 ماه"}, "for_1_months_nei": {"message": "در عرض 1 ماه"}, "for_3_months": {"message": "به مدت 3 ماه"}, "for_3_months_nei": {"message": "در عرض 3 ماه"}, "for_6_months": {"message": "به مدت 6 ماه"}, "for_6_months_nei": {"message": "در عرض 6 ماه"}, "for_9_months": {"message": "9 ماه"}, "for_9_months_nei": {"message": "در عرض 9 ماه"}, "fu_gou_lv": {"message": "نرخ خرید مجدد"}, "gao_liang_bu_tong_dian": {"message": "برجسته کردن تفاوت ها"}, "gao_liang_guang_gao_chan_pin": {"message": "محصولات تبلیغاتی را برجسته کنید"}, "geng_duo_xin_xi": {"message": "اطلاعات بیشتر"}, "geng_xin_shi_jian": {"message": "زمان به روز رسانی"}, "get_store_products_fail_tip": {"message": "برای اطمینان از دسترسی عادی، روی تأیید کلیک کنید"}, "gong_x_kuan_shang_pin": {"message": "مجموعاً $amount$ محصول", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "تامین کننده"}, "gong_ying_shang_ID": {"message": "شناسه تامین کننده"}, "gong_ying_shang_deng_ji": {"message": "رتبه بندی تامین کننده"}, "gong_ying_shang_nian_zhan": {"message": "تامین کننده قدیمی تر است"}, "gong_ying_shang_xin_xi": {"message": "اطلاعات تأمینکننده"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "لینک صفحه اصلی تامین کننده"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "قیمت فروش تخمینی"}, "guan_jian_zi": {"message": "ک<PERSON><PERSON><PERSON> کلیدی"}, "guang_gao_chan_pin": {"message": "آگهی محصولات"}, "guang_gao_zhan_bi": {"message": "آگهی نسبت"}, "guo_ji_wu_liu_yun_fei": {"message": "هزینه حمل و نقل بین المللی"}, "guo_lv_tiao_jian": {"message": "فیلترها"}, "hao_ping_lv": {"message": "امت<PERSON><PERSON>ز مثبت"}, "highest_price": {"message": "بالا"}, "historical_trend": {"message": "روند تاریخی"}, "how_to_screenshot": {"message": "برای انتخاب ناحیه، دکمه سمت چپ ماوس را نگه دارید، برای خروج از اسکرین شات، روی دکمه سمت راست ماوس یا کلید Esc ضربه بزنید."}, "howt_it_works": {"message": "چگونه کار می کند"}, "hui_fu_lv": {"message": "نرخ پاسخ"}, "hui_tou_lv": {"message": "نرخ بازگشت"}, "inquire_freightFee": {"message": "استعلام حمل و نقل"}, "inquire_freightFee_Yuan": {"message": "حمل و نقل / یوان"}, "inquire_freightFee_province": {"message": "استان"}, "inquire_freightFee_the": {"message": "هزینه حمل و نقل $num$ است، به این معنی که حمل و نقل منطقه رایگان است.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "آگهی."}, "jia_ge": {"message": "قیمت"}, "jia_ge_dan_wei": {"message": "واحد"}, "jia_ge_qu_shi": {"message": "روندها"}, "jia_zai_n_ge_shang_pin": {"message": "بارگیری محصولات $num$", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "درصد حجم فروش در 30 روز گذشته"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "درصد درآمد در 30 روز گذشته"}, "jin_30d_xiao_liang": {"message": "حرا<PERSON>ی"}, "jin_30d_xiao_liang__desc": {"message": "مجموع فروش در 30 روز گذشته"}, "jin_30d_xiao_shou_e": {"message": "حجم معاملات"}, "jin_30d_xiao_shou_e__desc": {"message": "کل گردش مالی در 30 روز گذشته"}, "jin_90_tian_mai_jia_shu": {"message": "خریداران در 90 روز گذشته"}, "jin_90_tian_xiao_shou_liang": {"message": "فروش در 90 روز گذشته"}, "jing_xuan_huo_yuan": {"message": "منابع منتخب"}, "jing_ying_mo_shi": {"message": "مدل تجاری"}, "jing_ying_mo_shi__gong_chang": {"message": "سازنده"}, "jiu_fen_jie_jue": {"message": "ح<PERSON> اختلاف"}, "jiu_fen_jie_jue__desc": {"message": "حسابداری اختلافات حقوق فروشگاه فروشندگان"}, "jiu_fen_lv": {"message": "نرخ اختلاف"}, "jiu_fen_lv__desc": {"message": "نسبت سفارشات با شکایات تکمیل شده در 30 روز گذشته و مسئولیت آن بر عهده فروشنده یا هر دو طرف است"}, "kai_dian_ri_qi": {"message": "تاریخ افتتاحیه"}, "keywords": {"message": "کلید واژه ها"}, "kua_jin_Select_pan_huo": {"message": "انتخاب برون مرزی"}, "last15_days": {"message": "15 روز گذشته"}, "last180_days": {"message": "180 روز گذشته"}, "last30_days": {"message": "در 30 روز گذشته"}, "last360_days": {"message": "360 روز گذشته"}, "last45_days": {"message": "45 روز گذشته"}, "last60_days": {"message": "60 روز گذشته"}, "last7_days": {"message": "7 روز گذشته"}, "last90_days": {"message": "90 روز گذشته"}, "last_30d_sales": {"message": "فروش 30 روز گذشته"}, "lei_ji": {"message": "انباشته"}, "lei_ji_xiao_liang": {"message": "جمع"}, "lei_ji_xiao_liang__desc": {"message": "تمام فروش پس از محصول در قفسه"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "حجم فروش تجمعی در 30 روز گذشته، از بالا به پایین مرتب شده است"}, "lian_xi_fang_shi": {"message": "اطلاعات تماس"}, "list_time": {"message": "در تاریخ قفسه"}, "load_more": {"message": "بارگذاری بیشتر"}, "login_to_aliprice": {"message": "به AliPrice وارد شوید"}, "long_link": {"message": "لینک طولانی"}, "lowest_price": {"message": "کم"}, "mai_jia_shu": {"message": "فروشندگان"}, "mao_li_lv": {"message": "حاشیه ناخالص"}, "mobile_view__dkxbqy": {"message": "یک برگه جدید باز کنید"}, "mobile_view__sjdxq": {"message": "جزئیات در برنامه"}, "mobile_view__sjdxqy": {"message": "صفحه جزئیات در برنامه"}, "mobile_view__smck": {"message": "اسکن برای مشاهده"}, "mobile_view__smckms": {"message": "لطفاً از دوربین یا برنامه برای اسکن و مشاهده استفاده کنید"}, "modified_failed": {"message": "اصلاح نشد"}, "modified_successfully": {"message": "با موفقیت اصلاح شد"}, "nav_btn_favorites": {"message": "مجموعه های من"}, "nav_btn_package": {"message": "بسته بندی"}, "nav_btn_product_info": {"message": "درباره محصول"}, "nav_btn_viewed": {"message": "مشاهده شده"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "ه<PERSON>چ کدام"}, "normal_link": {"message": "لینک معمولی"}, "notice": {"message": "اشاره"}, "number_reviews": {"message": "بررسی ها"}, "only_show_num": {"message": "مجموع محصولات: $allnum$، پنهان: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "بدون علامت حذف کنید"}, "open": {"message": "باز کنید"}, "open_links": {"message": "پیوندها را باز کنید"}, "options_page_tab_check_links": {"message": "پیوندها را بررسی کنید"}, "options_page_tab_gernal": {"message": "عمومی"}, "options_page_tab_notifications": {"message": "اطلاعیه"}, "options_page_tab_others": {"message": "دیگران"}, "options_page_tab_sbi": {"message": "جستجو بر اساس تصویر"}, "options_page_tab_shortcuts": {"message": "میانبرها"}, "options_page_tab_shortcuts_title": {"message": "اندازه قلم برای میانبرها"}, "options_page_tab_similar_products": {"message": "محصولات مشابه"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "چندین پیوند محصول در شرف باز شدن است"}, "order_list_sku_show_title": {"message": "نمایش انواع انتخاب شده در پیوندهای مشترک"}, "orders_last30_days": {"message": "تعداد سفارشات در 30 روز گذشته"}, "pTutorial_favorites_block1_desc1": {"message": "محصولاتی که ردیابی کرده اید در اینجا لیست شده اند"}, "pTutorial_favorites_block1_title": {"message": "موارد دلخواه"}, "pTutorial_popup_block1_desc1": {"message": "برچسب سبز به معنای وجود محصولات افت قیمت است"}, "pTutorial_popup_block1_title": {"message": "میانبرها و موارد دلخواه"}, "pTutorial_price_history_block1_desc1": {"message": "روی \"پیگیری قیمت\" کلیک کنید ، محصولات را به موارد دلخواه اضافه کنید. با کاهش قیمت آنها ، اعلانی دریافت خواهید کرد"}, "pTutorial_price_history_block1_title": {"message": "پیگیری قیمت"}, "pTutorial_reviews_block1_desc1": {"message": "نظرات خریداران از Itao و عکس های واقعی از بازخورد AliExpress"}, "pTutorial_reviews_block1_title": {"message": "بررسی ها"}, "pTutorial_reviews_block2_desc1": {"message": "همیشه بررسی نظرات سایر خریداران مفید است"}, "pTutorial_same_products_block1_desc1": {"message": "می توانید آنها را با هم مقایسه کنید تا بهترین انتخاب را داشته باشید"}, "pTutorial_same_products_block1_desc2": {"message": "برای \"جستجو براساس تصویر\" روی \"بیشتر\" کلیک کنید"}, "pTutorial_same_products_block1_title": {"message": "محصولات مشابه"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "تصویر محصول را به آنجا رها کنید و یک دسته را انتخاب کنید"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "جستجو بر اساس تصویر"}, "pTutorial_seller_analysis_block1_desc1": {"message": "نرخ بازخورد مثبت فروشنده ، امتیازات بازخورد و مدت فروشنده در بازار"}, "pTutorial_seller_analysis_block1_title": {"message": "رده بندی فروشنده"}, "pTutorial_seller_analysis_block2_desc2": {"message": "رتبه بندی فروشنده بر اساس 3 شاخص است: مورد به شرح ، سرعت حمل و نقل ارتباطی"}, "pTutorial_seller_analysis_block3_desc3": {"message": "ما از 3 رنگ و نماد برای نشان دادن سطح اعتماد فروشندگان استفاده می کنیم"}, "page_count": {"message": "تعدادی از صفحات"}, "pai_chu": {"message": "مستثنی شده است"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "هنگ کنگ محدود را حذف کنید"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "محدود به ژاپن را حذف کنید"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "محدود به کره را حذف کنید"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "شامل قزاقستان محدود نمی شود"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "ماکائو محدود را حذف کنید"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "محدود به اروپای شرقی را حذف کنید"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "عربستان سعودی محدود را حذف کنید"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "محدود به تایوان را حذف کنید"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "شامل ایالات متحده محدود نمی شود"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "محدود به ویتنام را حذف کنید"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "موا<PERSON>د محدود را حذف کنید"}, "payable_price_formula": {"message": "قیمت + ارسال + تخفیف"}, "pdd_check_retail_btn_txt": {"message": "خرده فروشی را بررسی کنید"}, "pdd_pifa_to_retail_btn_txt": {"message": "خرده فروشی بخرید"}, "pdp_copy_fail": {"message": "کپی نشد!"}, "pdp_copy_success": {"message": "کپی با موفقیت انجام شد!"}, "pdp_share_modal_subtitle": {"message": "اسکرین شات را به اشتراک بگذارید، او انتخاب شما را خواهد دید."}, "pdp_share_modal_title": {"message": "انتخاب خود را به اشتراک بگذارید"}, "pdp_share_screenshot": {"message": "اسکرین شات را به اشتراک بگذارید"}, "pei_song": {"message": "روش ارسال"}, "pin_lei": {"message": "دسته بندی"}, "pin_zhi_ti_yan": {"message": "کی<PERSON><PERSON>ت محصول"}, "pin_zhi_ti_yan__desc": {"message": "نرخ بازپرداخت کیفی فروشگاه فروشنده"}, "pin_zhi_tui_kuan_lv": {"message": "نرخ بازپرداخت"}, "pin_zhi_tui_kuan_lv__desc": {"message": "نسبت سفارش هایی که فقط در 30 روز گذشته بازپرداخت و برگشت داده شده اند"}, "ping_fen": {"message": "رت<PERSON><PERSON> بندی"}, "ping_jun_fa_huo_su_du": {"message": "میانگین سرعت حمل و نقل"}, "pkgInfo_hide": {"message": "اطلاعات لجستیک: روشن/خاموش"}, "pkgInfo_no_trace": {"message": "بدون اطلاعات لجستیک"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "دی اچ گیت"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "ای بی"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "پیندودو"}, "platform_name__pinduoduo_pifa": {"message": "عمده فروشی <PERSON>o"}, "platform_name__shopee": {"message": "شو<PERSON>ی"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "تائوبائو"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "والمارت"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "خانه"}, "popup_go_to_platform": {"message": "به $name$ بروید", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "من برای ..."}, "popup_track_package_btn_track": {"message": "مسیر"}, "popup_track_package_desc": {"message": "ردیابی بسته بندی همه در یک"}, "popup_track_package_search_placeholder": {"message": "شماره پیگیری"}, "popup_translate_search_placeholder": {"message": "در $searchOn$ ترجمه و جستجو کنید", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "تاریخچه قیمت"}, "price_history_chart_tip_ae": {"message": "نکته: تعداد سفارش‌ها تعداد تجمعی سفارش‌ها از زمان راه‌اندازی است"}, "price_history_chart_tip_coupang": {"message": "نکته: <PERSON>upang تعداد سفارش‌های تقلبی را حذف می‌کند"}, "price_history_inm_1688_l1": {"message": "لطفا نصب کنید"}, "price_history_inm_1688_l2": {"message": "دستیا<PERSON> خرید Ali<PERSON> برای 1688"}, "price_history_panel_lowest_price": {"message": "کمترین قیمت:"}, "price_history_panel_tab_price_tracking": {"message": "تاریخچه قیمت"}, "price_history_panel_tab_seller_analysis": {"message": "تحلیل فروشنده"}, "price_history_pro_modal_title": {"message": "تاریخچه قیمت و تاریخ سفارش"}, "privacy_consent__btn_agree": {"message": "مجدداً از رضایت جمع آوری اطلاعات استفاده کنید"}, "privacy_consent__btn_disable_all": {"message": "قبول نکردن"}, "privacy_consent__btn_enable_all": {"message": "فعال کردن همه"}, "privacy_consent__btn_uninstall": {"message": "برداشتن"}, "privacy_consent__desc_privacy": {"message": "توجه داشته باشید که بدون داده یا کوکی ، برخی از توابع خاموش هستند زیرا این توابع نیاز به توضیح داده ها یا کوکی ها دارند ، اما شما هنوز هم می توانید از توابع دیگر استفاده کنید."}, "privacy_consent__desc_privacy_L1": {"message": "متأسفانه ، بدون داده یا کوکی کار نمی کند زیرا ما به توضیح داده ها یا کوکی ها نیاز داریم."}, "privacy_consent__desc_privacy_L2": {"message": "اگر اجازه نمی دهید این اطلاعات را جمع آوری کنیم ، لطفا آن را حذف کنید."}, "privacy_consent__item_cookies_desc": {"message": "کوکی ، ما فقط داده های ارزی شما را هنگام خرید آنلاین برای نشان دادن تاریخچه قیمت ، در کوکی ها دریافت می کنیم."}, "privacy_consent__item_cookies_title": {"message": "کوکی های مورد نیاز"}, "privacy_consent__item_functional_desc_L1": {"message": "1. کوکی ها را در مرورگر اضافه کنید تا به طور ناشناس رایانه یا دستگاه خود را شناسایی کنید."}, "privacy_consent__item_functional_desc_L2": {"message": "2. داده های عملکردی را به برنامه افزودنی اضافه کنید تا با عملکرد کار کند."}, "privacy_consent__item_functional_title": {"message": "کوکی های کاربردی و تحلیلی"}, "privacy_consent__more_desc": {"message": "لطفاً بدانید که ما اطلاعات شخصی شما را با شرکتهای دیگر به اشتراک نمی گذاریم و هیچ شرکت تبلیغاتی از طریق سرویس ما داده جمع نمی کند."}, "privacy_consent__options__btn__desc": {"message": "برای استفاده از همه ویژگی ها ، باید آن را روشن کنید."}, "privacy_consent__options__btn__label": {"message": "روشن کن"}, "privacy_consent__options__desc_L1": {"message": "ما داده های زیر را که شخصاً شما را شناسایی می کند جمع آوری خواهیم کرد:"}, "privacy_consent__options__desc_L2": {"message": "- کوکی ها ، ما فقط اطلاعات مربوط به ارز شما را در کوکی ها هنگام خرید آنلاین برای نشان دادن تاریخچه قیمت دریافت می کنیم."}, "privacy_consent__options__desc_L3": {"message": "- و کوکی ها را در مرورگر اضافه کنید تا به طور ناشناس رایانه یا دستگاه خود را شناسایی کنید."}, "privacy_consent__options__desc_L4": {"message": "- سایر داده های ناشناس این پسوند را راحت تر می کند."}, "privacy_consent__options__desc_L5": {"message": "لطفا توجه داشته باشید که ما اطلاعات شخصی شما را با شرکت های دیگر به اشتراک نمی گذاریم و هیچ شرکت تبلیغاتی از طریق خدمات ما داده ها را جمع نمی کند."}, "privacy_consent__privacy_preferences": {"message": "تنظیمات حریم خصوصی"}, "privacy_consent__read_more": {"message": "ادامه مطلب >>"}, "privacy_consent__title_privacy": {"message": "<PERSON>ری<PERSON> خصوصی"}, "product_info": {"message": "اطلاعات محصول"}, "product_recommend__name": {"message": "محصولات مشابه"}, "product_research": {"message": "تحقیق محصول"}, "product_sub__email_desc": {"message": "ایمیل هشدار قیمت"}, "product_sub__email_edit": {"message": "ویرایش کنید"}, "product_sub__email_not_verified": {"message": "لطفا ایمیل را تایید کنید"}, "product_sub__email_required": {"message": "لطفا ایمیل بدهید"}, "product_sub__form_countdown": {"message": "بسته شدن خودکار پس از $seconds$ ثانیه", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "یادآوری اضافه نشد!"}, "product_sub__form_input_price": {"message": "قیمت ورودی"}, "product_sub__form_item_country": {"message": "ملت"}, "product_sub__form_item_current_price": {"message": "قیمت فعلی"}, "product_sub__form_item_duration": {"message": "مسیر"}, "product_sub__form_item_higher_price": {"message": "یا قیمت>"}, "product_sub__form_item_invalid_higher_price": {"message": "قیمت باید بیشتر از $price$ باشد", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "قیمت باید کمتر از $price$ باشد", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "زمانی که قیمت <"}, "product_sub__form_submit": {"message": "ارسال"}, "product_sub__form_success": {"message": "در اضافه کردن یادآوری موفق شد!"}, "product_sub__high_price_notify": {"message": "من را از افزایش قیمت مطلع کنید"}, "product_sub__low_price_notify": {"message": "کاهش قیمت را به من اطلاع دهید"}, "product_sub__modal_title": {"message": "یادآوری تغییر قیمت اشتراک"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ و MOP"}, "qi_ye_mian_ji": {"message": "منطقه سازمانی"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "لط<PERSON>ا حدا<PERSON><PERSON> یک کالا را برگزینید"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "لط<PERSON>ا حدا<PERSON><PERSON> یک فیلد را انتخاب کنید"}, "qu_deng_lu": {"message": "وارد شوید"}, "quan_guo_yan_xuan": {"message": "انتخاب جهانی"}, "recommendation_popup_banner_btn_install": {"message": "نصب کنید"}, "recommendation_popup_banner_desc": {"message": "تاریخچه قیمت را ظرف 3/6 ماه و اعلان افت قیمت را نمایش دهید"}, "region__all": {"message": "همه مناطق"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "کلیک راست را فعال کنید"}, "ren_zheng_gong_chang": {"message": "کارخانه تایید شده"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "سالها به عنوان تامین کننده معتبر"}, "required_to_aliprice_login": {"message": "باید به AliPrice وارد شوید"}, "revenue_last30_days": {"message": "میزان فروش در 30 روز گذشته"}, "review_counts": {"message": "تعداد کلکسیونرها"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "دوره ورود"}, "sales_amount_last30_days": {"message": "مجموع فروش در 30 روز گذشته"}, "sales_last30_days": {"message": "فروش در 30 روز گذشته"}, "sbi_alibaba_cate__accessories": {"message": "تجهیزات جانبی"}, "sbi_alibaba_cate__aqfk": {"message": "امنیت"}, "sbi_alibaba_cate__bags_cases": {"message": "کیف و موارد"}, "sbi_alibaba_cate__beauty": {"message": "زیبا<PERSON>ی"}, "sbi_alibaba_cate__beverage": {"message": "نوشیدنی"}, "sbi_alibaba_cate__bgwh": {"message": "فرهنگ اداری"}, "sbi_alibaba_cate__bz": {"message": "بسته"}, "sbi_alibaba_cate__ccyj": {"message": "وسایل آشپزخانه"}, "sbi_alibaba_cate__clothes": {"message": "پوشاک"}, "sbi_alibaba_cate__cmgd": {"message": "پخش رسانه ای"}, "sbi_alibaba_cate__coat_jacket": {"message": "کت و ژاکت"}, "sbi_alibaba_cate__consumer_electronics": {"message": "لوازم الکترونیکی مصرفی"}, "sbi_alibaba_cate__cryp": {"message": "محصولات بزرگسالان"}, "sbi_alibaba_cate__csyp": {"message": "روتختی"}, "sbi_alibaba_cate__cwyy": {"message": "باغبانی حیوانات خانگی"}, "sbi_alibaba_cate__cysx": {"message": "پذیرایی تازه"}, "sbi_alibaba_cate__dgdq": {"message": "تکنسین برق"}, "sbi_alibaba_cate__dl": {"message": "بازیگری"}, "sbi_alibaba_cate__dress_suits": {"message": "لباس و لباس"}, "sbi_alibaba_cate__dszm": {"message": "نورپردازی"}, "sbi_alibaba_cate__dzqj": {"message": "دستگاه الکترونیکی"}, "sbi_alibaba_cate__essb": {"message": "تجهیزات مورد استفاده"}, "sbi_alibaba_cate__food": {"message": "غذا"}, "sbi_alibaba_cate__fspj": {"message": "لباس و لوازم جانبی"}, "sbi_alibaba_cate__furniture": {"message": "مب<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "چرم نساجی"}, "sbi_alibaba_cate__ghjq": {"message": "مراقبت شخصی"}, "sbi_alibaba_cate__gt": {"message": "فولاد"}, "sbi_alibaba_cate__gyp": {"message": "صنایع دستی"}, "sbi_alibaba_cate__hb": {"message": "دوستدار محیط زیست"}, "sbi_alibaba_cate__hfcz": {"message": "آرایش مراقبت از پوست"}, "sbi_alibaba_cate__hg": {"message": "صنایع شیمیایی"}, "sbi_alibaba_cate__jg": {"message": "در حال پردازش"}, "sbi_alibaba_cate__jianccai": {"message": "مصالح ساختمانی"}, "sbi_alibaba_cate__jichuang": {"message": "ما<PERSON>ین ابزار"}, "sbi_alibaba_cate__jjry": {"message": "استفاده روزانه خانگی"}, "sbi_alibaba_cate__jtys": {"message": "حمل و نقل"}, "sbi_alibaba_cate__jxsb": {"message": "تجهیزات"}, "sbi_alibaba_cate__jxwj": {"message": "سخت افزار مکانیکی"}, "sbi_alibaba_cate__jydq": {"message": "لوازم خانگی"}, "sbi_alibaba_cate__jzjc": {"message": "مصالح ساختمانی بهسازی خانه"}, "sbi_alibaba_cate__jzjf": {"message": "منسوجا<PERSON> خانگی"}, "sbi_alibaba_cate__mj": {"message": "حو<PERSON>ه"}, "sbi_alibaba_cate__myyp": {"message": "محصولات کودک"}, "sbi_alibaba_cate__nanz": {"message": "مردانه"}, "sbi_alibaba_cate__nvz": {"message": "لباس زنانه"}, "sbi_alibaba_cate__ny": {"message": "انرژی"}, "sbi_alibaba_cate__others": {"message": "دیگران"}, "sbi_alibaba_cate__qcyp": {"message": "لوازم جانبی خودرو"}, "sbi_alibaba_cate__qmpj": {"message": "قطعات خودرو"}, "sbi_alibaba_cate__shoes": {"message": "ک<PERSON>ش"}, "sbi_alibaba_cate__smdn": {"message": "کامپیوتر دیجیتال"}, "sbi_alibaba_cate__snqj": {"message": "نگهداری و نظافت"}, "sbi_alibaba_cate__spjs": {"message": "غذا نوشیدنی"}, "sbi_alibaba_cate__swfw": {"message": "خدما<PERSON> شغلی"}, "sbi_alibaba_cate__toys_hobbies": {"message": "اسباب بازی"}, "sbi_alibaba_cate__trousers_skirt": {"message": "شلوار و دامن"}, "sbi_alibaba_cate__txcp": {"message": "محصولات ارتباطی"}, "sbi_alibaba_cate__tz": {"message": "لباس بچه گانه"}, "sbi_alibaba_cate__underwear": {"message": "زیر شلواری"}, "sbi_alibaba_cate__wjgj": {"message": "ابزارهای سخت افزاری"}, "sbi_alibaba_cate__xgpi": {"message": "کیف های چرمی"}, "sbi_alibaba_cate__xmhz": {"message": "همکاری پروژه"}, "sbi_alibaba_cate__xs": {"message": "لاستیک"}, "sbi_alibaba_cate__ydfs": {"message": "لباس ورزشی"}, "sbi_alibaba_cate__ydhw": {"message": "ورزش در فضای باز"}, "sbi_alibaba_cate__yjkc": {"message": "مواد معدنی متالورژی"}, "sbi_alibaba_cate__yqyb": {"message": "ابزار دقیق"}, "sbi_alibaba_cate__ys": {"message": "چاپ"}, "sbi_alibaba_cate__yyby": {"message": "مراقبت پزشکی"}, "sbi_alibaba_cn_kj_90mjs": {"message": "تعداد خریداران در 90 روز گذشته"}, "sbi_alibaba_cn_kj_90xsl": {"message": "حجم فروش در 90 روز گذشته"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> قیمت"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "هزینه حمل و نقل بین المللی"}, "sbi_alibaba_cn_kj_gjyf": {"message": "هزینه ارسال"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> قیمت"}, "sbi_alibaba_cn_kj_lr": {"message": "سود"}, "sbi_alibaba_cn_kj_lrgs": {"message": "سود = قیمت تخمینی x حاشیه سود"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "سرعت متوسط تحویل"}, "sbi_alibaba_cn_kj_qtfy": {"message": "هزینه دیگر"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "هزینه دیگر = قیمت تخمینی x نسبت هزینه دیگر"}, "sbi_alibaba_cn_kj_spjg": {"message": "قیمت"}, "sbi_alibaba_cn_kj_spzl": {"message": "وزن"}, "sbi_alibaba_cn_kj_szd": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "قطعات"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "قطعات"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "گرام"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "خریداران"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "قطعات"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "مجموعه ها"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "روزها"}, "sbi_alibaba_cn_kj_zwbj": {"message": "بدون قیمت"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "قیمت"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "موجود برای فروش"}, "sbi_aliprice_alibaba_cn__moren": {"message": "پیش فرض"}, "sbi_aliprice_alibaba_cn__queding": {"message": "مطمئن"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "حرا<PERSON>ی"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "مب<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "خوراک مختصر"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "آرا<PERSON>ش"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "زیر شلواری"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "تجهیزات جانبی"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "نوشیدنی بطری شده"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "دیگران"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "دامن"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "ژاکت"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "الکترونیک"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "اسباب بازی"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "بار مسافر"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "پایین"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "ک<PERSON>ش"}, "sbi_aliprice_cate__apparel": {"message": "پوشاک"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "اتومبیل و موتور سیکلت"}, "sbi_aliprice_cate__beauty_health": {"message": "زیبایی و سلامت"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "تلفن های همراه و مخابرات"}, "sbi_aliprice_cate__computer_office": {"message": "رایانه و دفتر"}, "sbi_aliprice_cate__consumer_electronics": {"message": "لوازم الکترونیکی مصرفی"}, "sbi_aliprice_cate__education_office_supplies": {"message": "لوازم آموزشی و اداری"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "قطعات و لوازم الکترونیکی"}, "sbi_aliprice_cate__furniture": {"message": "مب<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "اکستنشن و کلاه گیس مو"}, "sbi_aliprice_cate__home_garden": {"message": "خانه و باغ"}, "sbi_aliprice_cate__home_improvement": {"message": "ب<PERSON><PERSON><PERSON><PERSON> منزل"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "جواهرات و لوازم جانبی"}, "sbi_aliprice_cate__luggage_bags": {"message": "چمدان و کیف"}, "sbi_aliprice_cate__mother_kids": {"message": "مادر و بچه ها"}, "sbi_aliprice_cate__novelty_special_use": {"message": "تازگی و استفاده خاص"}, "sbi_aliprice_cate__security_protection": {"message": "حفاظت از امنیت"}, "sbi_aliprice_cate__shoes": {"message": "ک<PERSON>ش"}, "sbi_aliprice_cate__sports_entertainment": {"message": "ورزش و سرگرمی"}, "sbi_aliprice_cate__toys_hobbies": {"message": "اسباب بازی ها و سرگرمی ها"}, "sbi_aliprice_cate__watches": {"message": "ساعت"}, "sbi_aliprice_cate__weddings_events": {"message": "عروسی و رویدادها"}, "sbi_btn_capture_txt": {"message": "گرفتن"}, "sbi_btn_source_now_txt": {"message": "اکنون منبع دهید"}, "sbi_button__chat_with_me": {"message": "با من چت کن"}, "sbi_button__contact_supplier": {"message": "م<PERSON>ا<PERSON>ب"}, "sbi_button__hide_on_this_site": {"message": "در این سایت نشان داده نشود"}, "sbi_button__open_settings": {"message": "پیکربندی جستجو براساس تصویر"}, "sbi_capture_shortcut_tip": {"message": "یا کلید \"Enter\" را از صفحه کلید فشار دهید"}, "sbi_capturing_tip": {"message": "گرفتن"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 ستاره"}, "sbi_crop_and_search": {"message": "جستجو کردن"}, "sbi_crop_start": {"message": "از عکس صفحه استفاده کنید"}, "sbi_err_captcha_action": {"message": "ت<PERSON><PERSON><PERSON><PERSON> کنید"}, "sbi_err_captcha_for_alibaba_cn": {"message": "به تأیید نیاز دارید، لطفاً برای تأیید یک عکس آپلود کنید. ($video_tutorial$ را مشاهده کنید یا سعی کنید کوکی ها را پاک کنید)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "ترافیک غیرمعمول ، لطفاً تأیید کنید"}, "sbi_err_captcha_for_taobao": {"message": "Taobao از شما درخواست تأیید می کند ، لطفاً تصویری را به صورت دستی بارگذاری کرده و برای تأیید آن جستجو کنید. این خطا به دلیل خط مشی جدید تأیید \"جستجوی TaoBao براساس تصویر\" است ، ما به شما پیشنهاد می کنیم که بررسی شکایت بیش از حد مکرر در Taobao $feedback$ انجام شود.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "بازخورد"}, "sbi_err_captcha_msg": {"message": "$platform$ از شما می‌خواهد که یک تصویر را برای جستجو آپلود کنید یا برای حذف محدودیت‌های جستجو، تأیید امنیتی را کامل کنید", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "بررسی کنید آخرین نسخه است یا خیر"}, "sbi_err_cookie_btn_clear": {"message": "پاک کردن کوکی ها"}, "sbi_err_cookie_for_alibaba_cn": {"message": "کوکی‌های 1688 را پاک کنید؟ (نیاز به ورود مجدد دارید)"}, "sbi_err_desperate_feature_pdd": {"message": "تابع جستجوی تصویر با پسوند تصویر به جستجوی Pinduoduo منتقل شده است."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "چگونه درصد موفقیت جستجوی تصویر را افزایش دهیم؟"}, "sbi_err_img_undersize": {"message": "تصویر > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "وارد شوید $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "وارد شدن"}, "sbi_err_low_version": {"message": "نصب آخرین نسخه ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "د<PERSON><PERSON><PERSON>د"}, "sbi_err_need_help": {"message": "به کمک نیاز دارید"}, "sbi_err_network": {"message": "خطای شبکه، مطمئن شوید که می توانید از وب سایت بازدید کنید"}, "sbi_err_not_low_version": {"message": "آخرین نسخه نصب شده است ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "دوباره امتحان کنید"}, "sbi_err_try_again_action": {"message": "دوباره امتحان کنید"}, "sbi_err_visit_and_try": {"message": "دوباره امتحان کنید یا از $website$ دیدن کنید تا دوباره امتحان کنید", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "از صفحه اصلی $siteName$ دیدن کنید", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "بارگیری ناموفق بود ، لطفاً صفحه را تازه کنید و دوباره امتحان کنید."}, "sbi_kuajing_filter_area": {"message": "حوزه"}, "sbi_kuajing_filter_au": {"message": "استرالیا"}, "sbi_kuajing_filter_btn_confirm": {"message": "تا<PERSON><PERSON>د"}, "sbi_kuajing_filter_de": {"message": "آلمان"}, "sbi_kuajing_filter_destination_country": {"message": "کشور مقصد"}, "sbi_kuajing_filter_es": {"message": "اسپانیا"}, "sbi_kuajing_filter_estimate": {"message": "تخمین زدن"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> قیمت"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "فرمول برآورد قیمت = (قیمت کالا + حمل و نقل بین المللی لجستیک)/(1 - حاشیه سود - نسبت هزینه دیگر)"}, "sbi_kuajing_filter_fr": {"message": "فرانسه"}, "sbi_kuajing_filter_kw_placeholder": {"message": "کلمات کلیدی را برای مطابقت با عنوان وارد کنید"}, "sbi_kuajing_filter_logistics": {"message": "قالب تدارکات"}, "sbi_kuajing_filter_logistics_china_post": {"message": "پست هوایی چین"}, "sbi_kuajing_filter_logistics_discount": {"message": "تخفیف لجستیک"}, "sbi_kuajing_filter_logistics_epacket": {"message": "کوله پشتی"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "حمل و نقل تدارکات بین المللی = (وزن x قیمت حمل و نقل + هزینه ثبت نام) x (1 - تخفیف)"}, "sbi_kuajing_filter_others_fee": {"message": "هزینه دیگر"}, "sbi_kuajing_filter_profit_percent": {"message": "حاشیه سود"}, "sbi_kuajing_filter_prop": {"message": "ویژگی های"}, "sbi_kuajing_filter_ru": {"message": "روسیه"}, "sbi_kuajing_filter_total": {"message": "$count$ موارد مشابه را مطابقت دهید", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "بریتانیا"}, "sbi_kuajing_filter_usa": {"message": "آمریکا"}, "sbi_login_punish_title__pdd_pifa": {"message": "عمده فروشی <PERSON>o"}, "sbi_msg_no_result": {"message": "هیچ نتیجه ای یافت نشد ، لطفاً به $loginSite$ وارد شوید یا تصویر دیگری را امتحان کنید", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "به طور موقت برای Safari در دسترس نیست ، لطفا از $supportPage$ استفاده کنید.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "مرورگر Chrome و برنامه های افزودنی آن"}, "sbi_msg_no_result_reinstall_l1": {"message": "هیچ نتیجه ای یافت نشد ، لطفاً به سیستم $loginSite$ وارد شوید یا تصویر دیگری را امتحان کنید ، یا آخرین نسخه را دوباره نصب کنید $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "آخرین نسخه", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "منطقه انتخاب شده"}, "sbi_shipping_": {"message": "حمل و نقل در همان روز"}, "sbi_specify_category": {"message": "دسته را مشخص کنید:"}, "sbi_start_crop": {"message": "منطقه را انتخاب کنید"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 خارج از کشور"}, "sbi_tutorial_btn_more": {"message": "راه های بیشتر"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "کوپن های Taobao را پیدا کنید"}, "sbi_txt__empty_retry": {"message": "متأسفیم ، نتیجه ای یافت نشد ، لطفاً دوباره امتحان کنید."}, "sbi_txt__min_order": {"message": "حداقل سفارش"}, "sbi_visiting": {"message": "در حال مرور"}, "sbi_yiwugo__jiagexiangtan": {"message": "برای اطلاع از قیمت با فروشنده تماس بگیرید"}, "sbi_yiwugo__qigou": {"message": "$num$ قطعه (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "ستاره ها"}, "searchByImage_screenshot": {"message": "اسکرین شات با یک کلیک"}, "searchByImage_search": {"message": "جستجوی یک کلیک برای موارد مشابه"}, "searchByImage_size_type": {"message": "اندازه فایل نمی تواند بزرگتر از $num$ مگابایت باشد، فقط $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "تجزیه و تحلیل تصویر"}, "search_by_image_progress_searching": {"message": "جستجوی محصولات"}, "search_by_image_progress_sending": {"message": "در حال ارسال تصویر"}, "search_by_image_response_rate": {"message": "میزان پاسخ: $responseRate$ از خریدارانی که با این تأمین کننده تماس گرفته اند ظرف ساعات $responseInHour$ پاسخی دریافت کرده اند.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "جستجو با کلیدواژه:"}, "select_country_language_modal_title_country": {"message": "کشور"}, "select_country_language_modal_title_language": {"message": "زبان"}, "select_country_region_modal_title": {"message": "یک کشور / منطقه را انتخاب کنید"}, "select_language_modal_title": {"message": "یک زبان را انتخاب کنید:"}, "select_shop": {"message": "فروشگاه را انتخاب کنید"}, "sellers_count": {"message": "تعداد فروشندگان در صفحه فعلی"}, "sellers_count_per_page": {"message": "تعداد فروشندگان در صفحه فعلی"}, "service_score": {"message": "رتبه بندی خدمات جامع"}, "set_shortcut_keys": {"message": "تنظیم کلیدهای میانبر"}, "setting_logo_title": {"message": "دست<PERSON><PERSON><PERSON> خرید"}, "setting_modal_options_position_title": {"message": "موقعیت پلاگین"}, "setting_modal_options_position_value_left": {"message": "گوشه سمت چپ"}, "setting_modal_options_position_value_right": {"message": "گوشه ی راست"}, "setting_modal_options_theme_title": {"message": "رنگ تم"}, "setting_modal_options_theme_value_dark": {"message": "تاریک"}, "setting_modal_options_theme_value_light": {"message": "سبک"}, "setting_modal_title": {"message": "تنظیمات"}, "setting_options_country_title": {"message": "کشور / منطقه"}, "setting_options_hover_zoom_desc": {"message": "برای بزرگنمایی موشواره را ببرید"}, "setting_options_hover_zoom_title": {"message": "بزرگنمایی شناور"}, "setting_options_jd_coupon_desc": {"message": "کوپن در JD.com پیدا شد"}, "setting_options_jd_coupon_title": {"message": "کوپن JD.com"}, "setting_options_language_title": {"message": "زبان"}, "setting_options_price_drop_alert_desc": {"message": "وقتی قیمت محصولات در موارد دلخواه من کاهش یابد ، اعلان فشار دریافت خواهید کرد."}, "setting_options_price_drop_alert_title": {"message": "ه<PERSON><PERSON><PERSON><PERSON> افت قیمت"}, "setting_options_price_history_on_list_page_desc": {"message": "تاریخچه قیمت را در صفحه جستجوی محصول نمایش دهید"}, "setting_options_price_history_on_list_page_title": {"message": "تاریخچه قیمت (صفحه لیست)"}, "setting_options_price_history_on_produt_page_desc": {"message": "تاریخچه محصول را در صفحه جزئیات محصول نمایش دهید"}, "setting_options_price_history_on_produt_page_title": {"message": "تاریخچه قیمت (صفحه جزئیات)"}, "setting_options_sales_analysis_desc": {"message": "پشتیبانی از آمار قیمت، حجم فروش، تعداد فروشندگان و نسبت فروش فروشگاه در صفحه فهرست محصول $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "تجزیه و تحلیل فروش"}, "setting_options_save_success_msg": {"message": "مو<PERSON><PERSON><PERSON>ت"}, "setting_options_tacking_price_title": {"message": "هشدا<PERSON> تغی<PERSON>ر قیمت"}, "setting_options_value_off": {"message": "خاموش"}, "setting_options_value_on": {"message": "بر"}, "setting_pkg_quick_view_desc": {"message": "پشتیبانی: 1688 و Taobao"}, "setting_saved_message": {"message": "تغییرات با موفقیت ذخیره شد"}, "setting_section_enable_platform_title": {"message": "روشن خاموش"}, "setting_section_setting_title": {"message": "تنظیمات"}, "setting_section_shortcuts_title": {"message": "میانبرها"}, "settings_aliprice_agent__desc": {"message": "در صفحه جزئیات محصول $platforms$ نمایش داده می شود", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "برای من بخر"}, "settings_copy_link__desc": {"message": "نمایش در صفحه جزئیات محصول"}, "settings_copy_link__title": {"message": "دکمه کپی و عنوان جستجو"}, "settings_currency_desc__for_detail": {"message": "صفحه جزئیات محصول 1688 را پشتیبانی کنید"}, "settings_currency_desc__for_list": {"message": "جستجو بر اساس تصویر (شامل 1688/1688 خارج از کشور / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "قیمت را انتخاب کنید"}, "settings_currency_desc_display_for_list": {"message": "نشان داده شده در جستجوی تصویر (از جمله 1688/1688 خارج از کشور/Taobao)"}, "settings_currency_rate_desc": {"message": "به روزرسانی نرخ ارز از \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "بانک چین"}, "settings_download_images__desc": {"message": "پشتیبانی از دانلود تصاویر از $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "دکمه دانلود تصویر"}, "settings_download_reviews__desc": {"message": "در صفحه جزئیات محصول $platforms$ نمایش داده می شود", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "دان<PERSON>ود تصاویر بررسی"}, "settings_google_translate_desc": {"message": "برای دریافت نوار ترجمه google کلیک راست کنید"}, "settings_google_translate_title": {"message": "ترجمه صفحه وب"}, "settings_historical_trend_desc": {"message": "در گوشه سمت راست پایین تصویر در صفحه لیست محصولات نمایش داده شود"}, "settings_modal_btn_more": {"message": "تنظیمات بیشتر"}, "settings_productInfo_desc": {"message": "اطلاعات دقیق تر محصول را در صفحه لیست محصولات نمایش دهید. فعال کردن این مورد ممکن است بار کامپیوتر را افزایش دهد و باعث تاخیر صفحه شود. اگر بر عملکرد تأثیر می گذارد، توصیه می شود آن را غیرفعال کنید."}, "settings_product_recommend__desc": {"message": "در زیر تصویر اصلی در صفحه جزئیات محصول $platforms$ نمایش داده می شود", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "محصولات توصیه شده"}, "settings_research_desc": {"message": "اطلاعات دقیق تر را در صفحه لیست محصولات جویا شوید"}, "settings_sbi_add_to_list": {"message": "به $listType$ اضافه کنید", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "تصویر کوچک نتیجه جستجوی تصویر"}, "settings_sbi_remove_from_list": {"message": "از $listType$ حذف کنید", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "افزودن به لیست مسدود کردن"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "موقعیت ورودی را تنظیم کنید"}, "settings_search_by_image_blacklist_desc": {"message": "در وب سایت های موجود در لیست سیاه نماد نشان داده نشود."}, "settings_search_by_image_blacklist_title": {"message": "لیست بلوک"}, "settings_search_by_image_bottom_left": {"message": "پایین سمت چپ"}, "settings_search_by_image_bottom_right": {"message": "پایین سمت راست"}, "settings_search_by_image_clear_blacklist": {"message": "پاک کردن لیست مسدود کردن"}, "settings_search_by_image_detail_page_icon_title": {"message": "بند انگشتی"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "بزرگتر"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "کوچکتر"}, "settings_search_by_image_display_button_desc": {"message": "با کلیک بر روی نماد جستجو بر اساس تصویر انجام می شود"}, "settings_search_by_image_display_button_title": {"message": "نماد روی تصاویر"}, "settings_search_by_image_sourece_websites_desc": {"message": "محصول اصلی را در این وب سایت ها پیدا کنید"}, "settings_search_by_image_sourece_websites_title": {"message": "جستجو بر اساس نتیجه تصویر"}, "settings_search_by_image_top_left": {"message": "بالا سمت چپ"}, "settings_search_by_image_top_right": {"message": "بالا سمت راست"}, "settings_search_keyword_on_x__desc": {"message": "کلمات را در $platform$ جستجو کنید", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "هنگام انتخاب کلمات، نماد $platform$ را نشان دهید", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "سعی کنید همان محصول را در آن وب سایت ها پیدا کنید (حداکثر تا 5)"}, "settings_similar_products_title": {"message": "همین محصول را پیدا کنید"}, "settings_toolbar_expand_title": {"message": "کاهش حداقل افزونه"}, "settings_top_toolbar_desc": {"message": "نوار جستجو در بالای صفحه"}, "settings_top_toolbar_title": {"message": "نوار جستجو"}, "settings_translate_search_desc": {"message": "به چینی ترجمه کنید و جستجو کنید"}, "settings_translate_search_title": {"message": "جستجوی چند زبانه"}, "settings_translator_contextmenu_title": {"message": "ضبط برای ترجمه"}, "settings_translator_title": {"message": "ترجمه کردن"}, "shai_xuan_dao_chu": {"message": "فیلتر برای صادرات"}, "shai_xuan_zi_duan": {"message": "فیلدها را فیلتر کنید"}, "shang_jia_shi_jian": {"message": "در زمان قفسه"}, "shang_pin_biao_ti": {"message": "عنوان محصول"}, "shang_pin_dui_bi": {"message": "مقایسه محصول"}, "shang_pin_lian_jie": {"message": "لینک محصول"}, "shang_pin_xin_xi": {"message": "اطلاعات محصول"}, "share_modal__content": {"message": "با دوستانتان به اشتراک بگذارید"}, "share_modal__disable_for_while": {"message": "من نمی خواهم چیزی را به اشتراک بگذارم"}, "share_modal__title": {"message": "آیا $extensionName$ را دوست دارید؟", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "باقی مانده است"}, "shi_fou_ke_ding_zhi": {"message": "آیا قابل تنظیم است؟"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "تامین کننده تایید شده"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "تامین کنندگان تایید شده"}, "shi_fou_you_mao_yi_dan_bao": {"message": "تضمین تجارت"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "ضمانت های تجاری"}, "shipping_fee": {"message": "هزینه ارسال"}, "shop_followers": {"message": "دن<PERSON>ال کنندگان خرید کنید"}, "shou_qi": {"message": "کمتر"}, "similar_products_warn_max_platforms": {"message": "حداکثر تا 5"}, "sku_calc_price": {"message": "قیمت محاسبه شده"}, "sku_calc_price_settings": {"message": "تنظیمات قیمت محاسبه شده"}, "sku_formula": {"message": "فرمول"}, "sku_formula_desc": {"message": "شرح فرمول"}, "sku_formula_desc_text": {"message": "از فرمول‌های ریاضی پیچیده پشتیبانی می‌کند، که قیمت اصلی با A و هزینه حمل و نقل با B نشان داده می‌شود.\n\n<br/>\n\nاز براکت ()، جمع +، منهای -، ضرب * و تقسیم پشتیبانی می‌کند.\n\n<br/>\n\nمثال:\n\n<br/>\n\n۱. برای رسیدن به ۱.۲ برابر قیمت اصلی و سپس اضافه کردن هزینه حمل و نقل، فرمول به این صورت است: A*۱.۲+B\n\n<br/>\n\n۲. برای رسیدن به قیمت اصلی به علاوه ۱ یوان، سپس در ۱.۲ برابر ضرب کنید، فرمول به این صورت است: (A+۱)*۱.۲\n\n<br/>\n\n۳. برای رسیدن به قیمت اصلی به علاوه ۱۰ یوان، سپس در ۱.۲ برابر ضرب کنید و سپس ۳ یوان از آن کم کنید، فرمول به این صورت است: (A+۱۰)*۱.۲-۳"}, "sku_in_stock": {"message": "موجود در انبار"}, "sku_invalid_formula_format": {"message": "فرمت فرمول نامعتبر است"}, "sku_inventory": {"message": "موجو<PERSON>ی"}, "sku_link_copy_fail": {"message": "با موفقیت کپی شد، مشخصات و ویژگی‌های sku انتخاب نشده‌اند"}, "sku_link_copy_success": {"message": "با موفقیت کپی شد، مشخصات و ویژگی‌های sku انتخاب شدند"}, "sku_list": {"message": "لیست SKU"}, "sku_min_qrder_qty": {"message": "حداقل مقدار سفارش"}, "sku_name": {"message": "نام SKU"}, "sku_no": {"message": "شماره"}, "sku_original_price": {"message": "قیمت اصلی"}, "sku_price": {"message": "قیمت SKU"}, "stop_track_time_label": {"message": "مهلت پیگیری:"}, "suo_zai_di_qu": {"message": "<PERSON><PERSON><PERSON>"}, "tab_pkg_quick_view": {"message": "مانیتور لجستیک"}, "tab_product_details_price_history": {"message": "تاریخچه قیمت"}, "tab_product_details_reviews": {"message": "بررسی عکس"}, "tab_product_details_seller_analysis": {"message": "تحلیل فروشنده"}, "tab_product_details_similar_products": {"message": "محصولات مشابه"}, "total_days_listed_per_product": {"message": "مجموع روزهای قفسه ÷ تعداد محصولات"}, "total_items": {"message": "تعداد کل محصولات"}, "total_price_per_product": {"message": "مجموع قیمت ها ÷ تعداد محصولات"}, "total_rating_per_product": {"message": "مجموع امتیازات ÷ تعداد محصولات"}, "total_revenue": {"message": "کل درآمد"}, "total_revenue40_items": {"message": "مجموع درآمد $amount$ محصول در صفحه فعلی", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "کل فروش"}, "total_sales40_items": {"message": "مجموع فروش $amount$ محصول در صفحه فعلی", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "پیگیری برای: 1 سال"}, "track_for_3_months": {"message": "پیگیری برای: 3 ماه"}, "track_for_6_months": {"message": "پیگیری برای: 6 ماه"}, "tracking_price_email_add_btn": {"message": "ایمیل اضافه کنید"}, "tracking_price_email_edit_btn": {"message": "ویرایش ایمیل"}, "tracking_price_email_intro": {"message": "ما از طریق ایمیل به شما اطلاع خواهیم داد."}, "tracking_price_email_invalid": {"message": "لطفا یک ایمیل معتبر وارد کنید"}, "tracking_price_email_verified_desc": {"message": "اکنون می توانید هشدار افت قیمت ما را دریافت کنید."}, "tracking_price_email_verified_title": {"message": "با موفقیت تأیید شد"}, "tracking_price_email_verify_desc_line1": {"message": "ما یک لینک تأیید به آدرس ایمیل شما ارسال کرده ایم ،"}, "tracking_price_email_verify_desc_line2": {"message": "لطفا صندوق ورودی ایمیل خود را بررسی کنید."}, "tracking_price_email_verify_title": {"message": "ت<PERSON><PERSON><PERSON>د ایمیل"}, "tracking_price_web_push_notification_intro": {"message": "در دسک تاپ: <PERSON><PERSON><PERSON> می تواند هر محصولی را برای شما کنترل کند و با تغییر قیمت ، یک هشدار از طریق فشار وب برای شما ارسال کند."}, "tracking_price_web_push_notification_title": {"message": "اعلان های فشار وب"}, "translate_im__login_required": {"message": "ترجمه شده توسط AliPrice، لطفاً وارد $loginUrl$ شوید", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "ترجمه شده و در کلیپ بورد کپی شده است، اما به دلیل محدودیت های Aliwangwang، باید آن را به صورت دستی Paste کنید!"}, "translate_im__send": {"message": "ترجمه و ارسال"}, "translate_search": {"message": "ترجمه و جستجو"}, "translation_originals_translated": {"message": "اصل و چینی"}, "translation_translated": {"message": "چینی ها"}, "translator_btn_capture_txt": {"message": "ترجمه کردن"}, "translator_language_auto_detect": {"message": "تشخیص خودکار"}, "translator_language_detected": {"message": "شناسایی شده"}, "translator_language_search_placeholder": {"message": "جستجوی زبان"}, "try_again": {"message": "دوباره امتحان کنید"}, "tu_pian_chi_cun": {"message": "اندازه تصویر:"}, "tu_pian_lian_jie": {"message": "لینک تصویر"}, "tui_huan_ti_yan": {"message": "تجربه بازگشت"}, "tui_huan_ti_yan__desc": {"message": "شاخص های پس از فروش فروشندگان را ارزیابی کنید"}, "tutorial__show_all": {"message": "همه ویژگی ها"}, "tutorial_ae_popup_title": {"message": "پسوند را پین کنید، Aliexpress را باز کنید"}, "tutorial_aliexpress_reviews_analysis": {"message": "تجزیه و تحلیل بررسی AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "پشتیبانی USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "حمل و نقل به کره / ژاپن / سرزمین اصلی چین"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 از خرید خارج از کشور پشتیبانی می کند"}, "tutorial_auto_apply_coupon_title": {"message": "اعمال خودکار کوپن"}, "tutorial_btn_end": {"message": "پایان"}, "tutorial_btn_example": {"message": "مثال"}, "tutorial_btn_see_more": {"message": "بیشتر"}, "tutorial_compare_products": {"message": "مقایسه محصولات"}, "tutorial_currency_convert_title": {"message": "تبدیل ارز"}, "tutorial_export_shopping_cart": {"message": "صادرات به عنوان CSV، پشتیبانی از Taobao و 1688"}, "tutorial_export_shopping_cart_title": {"message": "س<PERSON><PERSON> خر<PERSON>د صادراتی"}, "tutorial_price_history_pro": {"message": "در صفحه جزئیات محصول نمایش داده می شود.\nاز Shopee، Lazada، Amazon، Ebay پشتیبانی کنید"}, "tutorial_price_history_pro_title": {"message": "تاریخچه قیمت تمام سال و تاریخچه سفارش"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "عکسبرداری برای جستجو بر اساس تصویر"}, "tutorial_translate_search": {"message": "ترجمه به جستجو"}, "tutorial_translate_search_and_package_tracking": {"message": "جستجوی ترجمه و ردیابی بسته"}, "unit_bao": {"message": "<PERSON><PERSON><PERSON>"}, "unit_ben": {"message": "<PERSON><PERSON><PERSON>"}, "unit_bi": {"message": "سفارشات"}, "unit_chuang": {"message": "<PERSON><PERSON><PERSON>"}, "unit_dai": {"message": "<PERSON><PERSON><PERSON>"}, "unit_dui": {"message": "ج<PERSON>ت"}, "unit_fen": {"message": "<PERSON><PERSON><PERSON>"}, "unit_ge": {"message": "<PERSON><PERSON><PERSON>"}, "unit_he": {"message": "<PERSON><PERSON><PERSON>"}, "unit_jian": {"message": "<PERSON><PERSON><PERSON>"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "<PERSON><PERSON><PERSON>"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "ج<PERSON>ت"}, "unit_tai": {"message": "<PERSON><PERSON><PERSON>"}, "unit_ti": {"message": "<PERSON><PERSON><PERSON>"}, "unit_tiao": {"message": "<PERSON><PERSON><PERSON>"}, "unit_xiang": {"message": "<PERSON><PERSON><PERSON>"}, "unit_zhang": {"message": "<PERSON><PERSON><PERSON>"}, "unit_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "verify_contact_support": {"message": "با پشتیبانی تماس بگیرید"}, "verify_human_verification": {"message": "ت<PERSON><PERSON><PERSON><PERSON> انسانی"}, "verify_unusual_access": {"message": "دسترسی غیرمعمول شناسایی شد"}, "view_history_clean_all": {"message": "تمیز کردن همه"}, "view_history_clean_all_warring": {"message": "همه سوابق مشاهده شده پاک شود؟"}, "view_history_clean_all_warring_title": {"message": "هشدار"}, "view_history_viewd": {"message": "مشاهده شده"}, "website": {"message": "وب سایت"}, "weight": {"message": "وزن"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "دریافت داده ها ممکن نیست"}, "wu_liu_shi_xiao": {"message": "ارسال به موقع"}, "wu_liu_shi_xiao__desc": {"message": "نرخ جمع آوری 48 ساعته و نرخ تکمیل فروشگاه فروشنده"}, "xia_dan_jia": {"message": "قیمت نهایی"}, "xian_xuan_ze_product_attributes": {"message": "ویژگی های محصول را انتخاب کنید"}, "xiao_liang": {"message": "حجم فروش"}, "xiao_liang_zhan_bi": {"message": "درصد حجم فروش"}, "xiao_shi": {"message": "$num$ ساعت", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "در<PERSON><PERSON>د"}, "xiao_shou_e_zhan_bi": {"message": "در<PERSON><PERSON> درآمد"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ رکورد را انتخاب کنید", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "انتخاب"}, "yi_ding_zai_zuo_ce": {"message": "پین شده"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "همه محصولات بارگذاری شده است"}, "yi_nian_xiao_liang": {"message": "فروش سالانه"}, "yi_nian_xiao_liang_zhan_bi": {"message": "سهم فروش سالانه"}, "yi_nian_xiao_shou_e": {"message": "گردش مالی سالانه"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "سهم گردش مالی سالانه"}, "yi_shua_xin": {"message": "تازه شد"}, "yin_cang_xiang_tong_dian": {"message": "شباهت ها را پنهان کند"}, "you_xiao_liang": {"message": "با حجم فروش"}, "yu_ji_dao_da_shi_jian": {"message": "زمان تخمینی رسیدن"}, "yuan_gong_ren_shu": {"message": "تعداد کارکنان"}, "yue_cheng_jiao": {"message": "حجم ماهانه"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "فروش Dropshipping در 30 روز گذشته"}, "yue_dai_xiao_pai_xu__desc": {"message": "فروش Dropshipping در 30 روز گذشته، مرتب‌سازی شده از بالا به پایین"}, "yue_xiao_liang__desc": {"message": "حجم فروش در 30 روز گذشته"}, "zhan_kai": {"message": "بیشتر"}, "zhe_kou": {"message": "تخفیف"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "ارسال رایگان"}, "zhi_fu_ding_dan_shu": {"message": "سفارشات پرداخت شده"}, "zhi_fu_ding_dan_shu__desc": {"message": "تعداد سفارشات این محصول (30 روز)"}, "zhu_ce_xing_zhi": {"message": "ما<PERSON><PERSON><PERSON> ثبتی"}, "zi_ding_yi_tiao_jian": {"message": "شرایط سفارشی"}, "zi_duan": {"message": "فیلدها"}, "zi_ti_xiao_liang": {"message": "تنوع فروخته شد"}, "zong_he_fu_wu_fen": {"message": "امت<PERSON><PERSON>ز کلی"}, "zong_he_fu_wu_fen__desc": {"message": "رتبه بندی کلی خدمات فروشنده"}, "zong_he_fu_wu_fen__short": {"message": "رت<PERSON><PERSON> بندی"}, "zong_he_ti_yan_fen": {"message": "رت<PERSON><PERSON> بندی"}, "zong_he_ti_yan_fen_3": {"message": "زیر 4 ستاره"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 ستاره"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 ستاره"}, "zong_he_ti_yan_fen_5": {"message": "5 ستاره"}, "zong_ku_cun": {"message": "کل موجودی"}, "zong_xiao_liang": {"message": "کل فروش"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "نرخ پاسخ 3 دقیقه ای در 30 روز گذشته"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "نرخ بهبودی 48 ساعت در 30 روز گذشته"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "نرخ عملکرد 48 ساعت در 30 روز گذشته"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "سابقه معاملات (30 روز)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "سابقه معاملات (30 روز)"}, "zui_jin_30D_jiu_fen_lv": {"message": "نرخ اختلاف در 30 روز گذشته"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "نرخ بازپرداخت کیفیت در 30 روز گذشته"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "تعداد سفارش های پرداخت در 30 روز گذشته"}}