{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Tempat Jualan Panas merentas sempadan"}, "1688_shi_li_ren_zheng": {"message": "1688 pensijilan kekuatan"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Lebih dari 1 tahun"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Penghantaran dalam masa 24 jam"}, "24H_lan_shou_lv": {"message": "<PERSON><PERSON> 24 jam"}, "30D_shang_xin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "30d_sales": {"message": "Jualan bulanan:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON><PERSON><PERSON> balas dalam masa 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "<PERSON><PERSON><PERSON> respons berk<PERSON> terhadap mesej pertanyaan pembeli dalam masa 3 minit dalam 30 hari yang lalu"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Penghantaran dalam masa 48 jam"}, "48H_lan_shou_lv": {"message": "Kadar pembungkusan 48 jam"}, "48H_lan_shou_lv__desc": {"message": "Nisbah nombor pesanan yang diambil dalam masa 48 jam kepada jumlah pesanan"}, "48H_lv_yue_lv": {"message": "<PERSON><PERSON> prestasi 48 jam"}, "48H_lv_yue_lv__desc": {"message": "Nisbah nombor pesanan yang diambil atau dihantar dalam masa 48 jam kepada jumlah pesanan"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Ketibaan Baru <PERSON>"}, "7D_wu_li_you": {"message": "7 hari tanpa penjagaan"}, "ABS_title_text": {"message": "Penyenaraian ini termasuk cerita jenama"}, "AC_title_text": {"message": "Penyenaraian ini mempunyai lencana <PERSON>"}, "A_title_text": {"message": "Penyenaraian ini mempunyai halaman kandungan A+"}, "BS_title_text": {"message": "Penyenaraian ini disenaraikan sebagai Penjual Terbaik $num$ dalam kategori $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (<PERSON><PERSON><PERSON>) bermakna penyenaraian ini adalah sebahagian daripada acara \"promosi 7 hari\"."}, "NR_title_text": {"message": "Penyenaraian ini disenaraikan sebagai $num$ Keluaran Baharu dalam kategori $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Penyenaraian ini mempunyai iklan video, sejenis iklan PPC yang biasanya muncul di tengah-tengah hasil carian"}, "SB_title_text": {"message": "Penyenaraian ini mempunyai iklan jenama, sejenis iklan PPC yang biasanya muncul di bahagian atas atau bawah hasil carian"}, "SP_title_text": {"message": "Penyenaraian ini mempunyai iklan Produk Ta<PERSON>an"}, "V_title_text": {"message": "Penyenaraian ini mempunyai pengenalan video"}, "advanced_research": {"message": "Penyelidikan <PERSON>"}, "agent_ds1688___my_order": {"message": "a<PERSON>an saya"}, "agent_ds1688__add_to_cart": {"message": "Pembelian Luar <PERSON>"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON> beli-belah"}, "agent_ds1688__desc": {"message": "Disediakan oleh 1688. Ia menyokong pembelian terus dari luar negara, pembayaran dalam USD dan penghantaran ke gudang transit anda di China."}, "agent_ds1688__freight": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__help": {"message": "Tolong"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Pusat <PERSON>"}, "agent_ds1688__warehouse": {"message": "gudang saya"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_available": {"message": "Tersedia"}, "ai_comment_analysis_balance": {"message": "Syiling tidak men<PERSON>, sila tambah nilai"}, "ai_comment_analysis_behavior": {"message": "<PERSON>g<PERSON><PERSON> laku"}, "ai_comment_analysis_characteristic": {"message": "Ciri orang ramai"}, "ai_comment_analysis_comment": {"message": "Produk tidak mempunyai ulasan yang mencukupi untuk membuat kesimpulan yang tepat, sila pilih produk dengan lebih banyak ulasan."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_default": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_desire": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "ai_comment_analysis_disadvantage": {"message": "Keburukan"}, "ai_comment_analysis_free": {"message": "Per<PERSON><PERSON><PERSON> per<PERSON>a"}, "ai_comment_analysis_freeNum": {"message": "1 kredit percuma akan digunakan"}, "ai_comment_analysis_go_recharge": {"message": "<PERSON>gi ke tambah nilai"}, "ai_comment_analysis_intelligence": {"message": "<PERSON><PERSON><PERSON> se<PERSON>tar"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_motive": {"message": "<PERSON><PERSON><PERSON><PERSON>em<PERSON>"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON>, sila cuba lagi"}, "ai_comment_analysis_normal": {"message": "<PERSON>lasan foto"}, "ai_comment_analysis_number_reviews": {"message": "Bilangan Ulasan: $num$, Anggaran Penggunaan: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON> umum"}, "ai_comment_analysis_percentage": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_problem": {"message": "<PERSON><PERSON><PERSON> dengan pem<PERSON>"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON> semula"}, "ai_comment_analysis_reason": {"message": "Sebab"}, "ai_comment_analysis_recharge": {"message": "Tam<PERSON> nilai"}, "ai_comment_analysis_recharged": {"message": "<PERSON>a telah menambah nilai"}, "ai_comment_analysis_retry": {"message": "Cuba semula"}, "ai_comment_analysis_scene": {"message": "Senario <PERSON>"}, "ai_comment_analysis_start": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_tool": {"message": "alat AI"}, "ai_comment_analysis_user_portrait": {"message": "<PERSON>il <PERSON>"}, "ai_comment_analysis_welcome": {"message": "Selamat datang ke analisis semakan AI"}, "ai_comment_analysis_year": {"message": "<PERSON>men dari tahun lepas"}, "ai_listing_Exclude_keywords": {"message": "Tidak termasuk kata kunci"}, "ai_listing_Login_the_feature": {"message": "Log masuk diperlukan untuk ciri tersebut"}, "ai_listing_aI_generation": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_add_automatic": {"message": "Automatik"}, "ai_listing_add_dictionary_new": {"message": "<PERSON><PERSON><PERSON> per<PERSON> baharu"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON>n kata kunci"}, "ai_listing_add_inputkey_selling": {"message": "Masukkan titik jualan dan tekan $key$ untuk menyelesaikan penambahan", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Had me<PERSON><PERSON><PERSON>, se<PERSON>ga $amount$ mata jualan", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Tambah kata kunci"}, "ai_listing_add_manually": {"message": "Tambah secara manual"}, "ai_listing_add_selling": {"message": "Tambah mata jualan"}, "ai_listing_added_keywords": {"message": "Kata kunci ditambah"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON>kkan kata kunci yang dikecualikan, tekan enter untuk menyelesaikan penambahan."}, "ai_listing_adding_selling": {"message": "Menambah mata jualan"}, "ai_listing_addkeyword_enter": {"message": "Taipkan perkataan atribut utama dan tekan enter untuk menyelesaikan penambahan"}, "ai_listing_ai_description": {"message": "Perpustakaan perkataan perihalan AI"}, "ai_listing_ai_dictionary": {"message": "Perpustakaan perkataan tajuk AI"}, "ai_listing_ai_title": {"message": "Tajuk AI"}, "ai_listing_aidescription_repeated": {"message": "<PERSON><PERSON>an perkataan perihalan AI tidak boleh diulang"}, "ai_listing_aititle_repeated": {"message": "<PERSON><PERSON> perkataan tajuk AI tidak boleh diulang"}, "ai_listing_data_comes_from": {"message": "Data ini datang daripada:"}, "ai_listing_deleted_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_dictionary_name": {"message": "<PERSON><PERSON>"}, "ai_listing_edit_dictionary": {"message": "Ubah suai perpus<PERSON>an..."}, "ai_listing_edit_word_library": {"message": "<PERSON> per<PERSON><PERSON><PERSON><PERSON> per<PERSON>an"}, "ai_listing_enter_keywords": {"message": "Ma<PERSON>kkan kata kunci dan tekan $key$ untuk menyelesaikan penambahan", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Had telah me<PERSON>, maksim<PERSON> $amount$ kata kunci", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Pustaka perkataan dike<PERSON>alikan"}, "ai_listing_generate_characters": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_generation_platform": {"message": "Platform generasi"}, "ai_listing_help_optimize": {"message": "Bantu saya mengoptimumkan tajuk produk, tajuk asalnya"}, "ai_listing_include_selling": {"message": "<PERSON>itik jualan lain termasuk:"}, "ai_listing_included_keyword": {"message": "Kata kunci yang disertakan"}, "ai_listing_included_keywords": {"message": "Kata kunci yang disertakan"}, "ai_listing_input_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> titik jualan"}, "ai_listing_input_selling_fit": {"message": "<PERSON><PERSON><PERSON><PERSON> mata jualan untuk dipadankan dengan tajuk"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON> masukkan mata jualan"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON><PERSON><PERSON> kandungan yang diperlukan di atas untuk menjana tajuk dengan bijak"}, "ai_listing_keyword_product_title": {"message": "Tajuk produk kata kunci"}, "ai_listing_keywords_repeated": {"message": "Kata kunci tidak boleh diulang"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON><PERSON><PERSON> mata jualan"}, "ai_listing_long_title_1": {"message": "Mengandungi maklumat asas seperti nama jenama, jeni<PERSON> produk, ciri produk, dsb."}, "ai_listing_long_title_2": {"message": "Berdasarkan tajuk produk standard, kata kunci yang sesuai untuk SEO ditambah."}, "ai_listing_long_title_3": {"message": "Di samping mengandu<PERSON><PERSON> nama jenama, j<PERSON><PERSON> produk, ciri produk dan kata kunci, kata kunci berekor panjang juga disertakan untuk mencapai kedudukan yang lebih tinggi dalam pertanyaan carian terbahagi yang khusus."}, "ai_listing_longtail_keyword_product_title": {"message": "Tajuk produk kata kunci ekor panjang"}, "ai_listing_manually_enter": {"message": "Ma<PERSON>kka<PERSON> secara manual..."}, "ai_listing_network_not_working": {"message": "Internet tidak tersedia, VPN diperlukan untuk mengakses ChatGPT"}, "ai_listing_new_dictionary": {"message": "B<PERSON>t perpustakaan perkataan baharu..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "<PERSON><PERSON><PERSON> pilihan"}, "ai_listing_original_title": {"message": "<PERSON><PERSON><PERSON>al"}, "ai_listing_other_keywords_included": {"message": "<PERSON><PERSON> kunci lain termasuk:"}, "ai_listing_please_again": {"message": "Sila cuba lagi"}, "ai_listing_please_select": {"message": "Tajuk berikut telah dijana untuk anda, sila pilih:"}, "ai_listing_product_category": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_product_category_is": {"message": "<PERSON><PERSON><PERSON> produk ialah"}, "ai_listing_product_category_to": {"message": "Produk itu tergolong dalam kategori apa?"}, "ai_listing_random_keywords": {"message": "Kata kunci $amount$ rawak", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Mata jualan $amount$ rawak", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "<PERSON><PERSON> daripada per<PERSON>an per<PERSON>"}, "ai_listing_search_selling": {"message": "<PERSON>i mengikut titik jualan"}, "ai_listing_select_product_categories": {"message": "<PERSON><PERSON><PERSON> kategori produk secara automatik."}, "ai_listing_select_product_selling_points": {"message": "<PERSON><PERSON><PERSON> titik jualan produk secara automatik"}, "ai_listing_select_word_library": {"message": "<PERSON><PERSON><PERSON> per<PERSON>an"}, "ai_listing_selling": {"message": "<PERSON> jualan"}, "ai_listing_selling_ask": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>luan titik jualan lain yang ada untuk tajuk itu?"}, "ai_listing_selling_optional": {"message": "<PERSON> jualan pilihan"}, "ai_listing_selling_repeat": {"message": "<PERSON> tidak boleh digandakan"}, "ai_listing_set_excluded": {"message": "Tetapkan sebagai pustaka perkataan yang dikecualikan"}, "ai_listing_set_include_selling_points": {"message": "Ser<PERSON>kan mata jualan"}, "ai_listing_set_included": {"message": "Tetapkan sebagai perpustakaan perkataan yang disertakan"}, "ai_listing_set_selling_dictionary": {"message": "Tetapkan sebagai perpustakaan titik jualan"}, "ai_listing_standard_product_title": {"message": "Tajuk produk standard"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Lawati ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Apakah kata kunci lain yang diperlukan untuk tajuk?"}, "aliprice_coupons_apply_again": {"message": "<PERSON><PERSON>"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON> kupon"}, "aliprice_coupons_apply_success": {"message": "Ku<PERSON>n ditemui: Jimat $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "<PERSON><PERSON><PERSON> kod untuk tawaran terbaik..."}, "aliprice_coupons_applying_desc": {"message": "Mendaftar keluar: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Teruskan ke Checkout"}, "aliprice_coupons_found_coupons": {"message": "<PERSON><PERSON><PERSON>ui kupon $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Bersedia untuk checkout? <PERSON> pastikan anda mendapat harga terbaik!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Kod tersebut tidak berfungsi. Tiada masalah besar-anda sudah mendapat harga terbaik."}, "aliprice_coupons_toolbar_btn": {"message": "Dapatkan Kupon"}, "aliww_translate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "aliww_translate_supports": {"message": "Sokongan: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON> kunci"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON> semua"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "<PERSON><PERSON> men<PERSON>t jualan terk<PERSON>"}, "an_lei_xing_cha_kan": {"message": "<PERSON><PERSON>"}, "an_yue_dai_xiao_pai_xu": {"message": "<PERSON><PERSON><PERSON><PERSON> mengikut jualan dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON><PERSON>"}, "apra_chart__name": {"message": "Peratusan penjualan produk mengikut negara"}, "apra_chart__update_at": {"message": "Masa kemas kini $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statistik penjualan negara"}, "auto_opening": {"message": "Dibuka secara automatik dalam $num$ saat", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Halaman $autoPaging$ seterusnya", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "<PERSON><PERSON>ta pada hari simpanan"}, "average_hui_fu_lv": {"message": "<PERSON><PERSON> bala<PERSON> purata"}, "average_ping_gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON> pem<PERSON>al purata"}, "average_price": {"message": "<PERSON>rga purata"}, "average_qi_ding_liang": {"message": "Purata MOQ"}, "average_rating": {"message": "<PERSON><PERSON><PERSON> purata"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON><PERSON><PERSON> ta<PERSON>"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON> hasil"}, "average_revenue_per_product": {"message": "<PERSON><PERSON><PERSON> has<PERSON> ÷ Bilangan produk"}, "average_sales": {"message": "<PERSON><PERSON><PERSON> jualan"}, "average_sales_per_product": {"message": "<PERSON><PERSON><PERSON> jualan ÷ Bilangan produk"}, "bao_han": {"message": "Mengandungi"}, "bao_zheng_jin": {"message": "<PERSON><PERSON>"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "Tajuk"}, "blacklist_add_blacklist": {"message": "Sekat kedai ini"}, "blacklist_address_incorrect": {"message": "Alamatnya tidak betul. Sila periksa."}, "blacklist_blacked_out": {"message": "Kedai telah disekat"}, "blacklist_blacklist": {"message": "<PERSON><PERSON><PERSON> hitam"}, "blacklist_no_records_yet": {"message": "Belum ada rekod!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Jenama"}, "btn_aliprice_agent__daigou": {"message": "Pengantara pembelian"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Selamat mencuba"}, "btn_refresh": {"message": "Segarkan"}, "btn_try_it_now": {"message": "<PERSON><PERSON>"}, "btn_txt_view_on_aliprice": {"message": "<PERSON><PERSON> di AliPrice"}, "bu_bao_han": {"message": "Tidak mengandungi"}, "bulk_copy_links": {"message": "<PERSON><PERSON><PERSON>"}, "bulk_copy_products": {"message": "Produk Salinan P<PERSON>l"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cai_gou_zi_xun__desc": {"message": "<PERSON><PERSON> tindak balas tiga minit penjual"}, "can_ping_lei_xing": {"message": "taip"}, "cao_zuo": {"message": "Operasi"}, "chan_pin_ID": {"message": "ID produk"}, "chan_pin_e_wai_xin_xi": {"message": "Maklumat tambahan produk"}, "chan_pin_lian_jie": {"message": "Pautan <PERSON>"}, "cheng_li_shi_jian": {"message": "<PERSON><PERSON>"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klik Salin maklumat produk"}, "commmon_txt_expired": {"message": "<PERSON><PERSON> tempoh"}, "common__date_range_12m": {"message": "1 tahun"}, "common__date_range_1m": {"message": "1 bulan"}, "common__date_range_1w": {"message": "1 minggu"}, "common__date_range_2w": {"message": "2 minggu"}, "common__date_range_3m": {"message": "3 bulan"}, "common__date_range_3w": {"message": "3 minggu"}, "common__date_range_6m": {"message": "6 bulan"}, "common_btn_cancel": {"message": "<PERSON><PERSON>"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "Jimat"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_email": {"message": "E-mel"}, "common_error_msg_no_data": {"message": "Tiada data"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON>, tiada hasil dijumpai."}, "common_favorites": {"message": "Kegemaran"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON> balas"}, "common_help": {"message": "<PERSON><PERSON><PERSON>"}, "common_loading": {"message": "Memuatkan"}, "common_login": {"message": "Log masuk"}, "common_logout": {"message": "Log keluar"}, "common_no": {"message": "Tidak"}, "common_powered_by_aliprice": {"message": "Dikuasakan oleh AliPrice.com"}, "common_setting": {"message": "Menetapkan"}, "common_sign_up": {"message": "Mendaftar"}, "common_system_upgrading_title": {"message": "Peningkatan sistem"}, "common_system_upgrading_txt": {"message": "Sila cuba kemudian"}, "common_txt__currency": {"message": "<PERSON>"}, "common_txt__video_tutorial": {"message": "Tutorial video"}, "common_txt_ago_time": {"message": "$time$ hari yang lalu", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "semua"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> tidak pernah digunakan"}, "common_txt_biaoti_link": {"message": "Tajuk+Pautan"}, "common_txt_biaoti_link_dian_pu": {"message": "Tajuk+Pautan+<PERSON><PERSON>"}, "common_txt_blacklist": {"message": "<PERSON><PERSON><PERSON> sekatan"}, "common_txt_cancel": {"message": "<PERSON><PERSON>"}, "common_txt_category": {"message": "kate<PERSON>i"}, "common_txt_chakan": {"message": "Semak"}, "common_txt_colors": {"message": "warna"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Di<PERSON>in"}, "common_txt_copy": {"message": "Salinan"}, "common_txt_copy_link": {"message": "<PERSON><PERSON>"}, "common_txt_copy_title": {"message": "<PERSON><PERSON> ta<PERSON>"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON> tajuk dan pautan"}, "common_txt_day": {"message": "langit"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Padam"}, "common_txt_dian_pu_link": {"message": "<PERSON>in nama kedai + pautan"}, "common_txt_download": {"message": "muat turun"}, "common_txt_downloaded": {"message": "<PERSON>at turun"}, "common_txt_export_as_csv": {"message": "Eksport Excel"}, "common_txt_export_as_txt": {"message": "Eksport Txt"}, "common_txt_fail": {"message": "Gaga<PERSON>"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "dapatkan"}, "common_txt_incert_selection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pem<PERSON>"}, "common_txt_install": {"message": "<PERSON><PERSON>"}, "common_txt_load_failed": {"message": "<PERSON><PERSON>"}, "common_txt_month": {"message": "bulan"}, "common_txt_more": {"message": "Lebih banyak lagi"}, "common_txt_new_unused": {"message": "Baru, belum digunakan"}, "common_txt_next": {"message": "Seterusnya"}, "common_txt_no_limit": {"message": "Tidak terhad"}, "common_txt_no_noticeable": {"message": "Tiada calar atau kotoran yang kelihatan"}, "common_txt_on_sale": {"message": "Tersedia"}, "common_txt_opt_in_out": {"message": "Hidup/Mati"}, "common_txt_order": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_others": {"message": "<PERSON> lain"}, "common_txt_overall_poor_condition": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang buruk"}, "common_txt_patterns": {"message": "corak"}, "common_txt_platform": {"message": "Platform"}, "common_txt_please_select": {"message": "<PERSON><PERSON> pilih"}, "common_txt_prev": {"message": "Sebelumnya"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "<PERSON><PERSON>"}, "common_txt_product_condition": {"message": "Status Produk"}, "common_txt_rating": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "Tam<PERSON> nilai"}, "common_txt_reset": {"message": "Tetapkan semula"}, "common_txt_review": {"message": "<PERSON><PERSON>"}, "common_txt_sale": {"message": "Tersedia"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "<PERSON>gan calar dan kotoran"}, "common_txt_search_title": {"message": "<PERSON><PERSON>k <PERSON>ian"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON> se<PERSON>a"}, "common_txt_selected": {"message": "dipilih"}, "common_txt_share": {"message": "Berkongsi"}, "common_txt_sold": {"message": "dijual"}, "common_txt_sold_out": {"message": "habis dijual"}, "common_txt_some_scratches": {"message": "Beberapa calar dan kotoran"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_state": {"message": "Status"}, "common_txt_success": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sys_err": {"message": "ralat sistem"}, "common_txt_today": {"message": "<PERSON> ini"}, "common_txt_total": {"message": "semua"}, "common_txt_unselect_all": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_upload_image": {"message": "<PERSON>at naik imej"}, "common_txt_whitelist": {"message": "<PERSON><PERSON><PERSON>ih"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "Ya"}, "compare_tool_btn_clear_all": {"message": "Kosongkan semua"}, "compare_tool_btn_compare": {"message": "Bandingkan"}, "compare_tool_btn_contact": {"message": "Hubung<PERSON>"}, "compare_tool_tips_max_compared": {"message": "Tambah hingga $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Konfigurasikan Pemberitahuan"}, "contact_us": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "context_menu_screenshot_search": {"message": "Tangkap untuk mencari mengikut imej"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON>i Imej di AliPrice"}, "context_menus_goote_trans": {"message": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON> asal"}, "context_menus_search_by_image": {"message": "Cari mengikut gambar di $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Tangkap ke $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Tangkap untuk menterjemah"}, "converter_modal_amount_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> jumlah di sini"}, "converter_modal_btn_convert": {"message": "<PERSON><PERSON>"}, "converter_modal_exchange_rate_source": {"message": "Data datang daripada $boc$ kadar pertukaran asing <PERSON><PERSON> kemas kini: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "penukaran mata wang"}, "converter_modal_search_placeholder": {"message": "mata wang carian"}, "copy_all_contact_us_notice": {"message": "Tapak ini tidak disokong pada masa ini, sila hubungi kami"}, "copy_product_info": {"message": "<PERSON><PERSON> mak<PERSON>at produk"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON>"}, "country__han_gou": {"message": "Korea Selatan"}, "country__ri_ben": {"message": "Je<PERSON>n"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "<PERSON><PERSON> tersuai"}, "currency_convert__sync_server": {"message": "<PERSON><PERSON><PERSON><PERSON> pelayan"}, "dang_ri_fa_huo": {"message": "Penghan<PERSON>n hari yang sama"}, "dao_chu_quan_dian_shang_pin": {"message": "Eksport Semua Produk Kedai"}, "dao_chu_wei_CSV": {"message": "Eksport"}, "dao_chu_zi_duan": {"message": "Eksport Medan"}, "delivery_address": {"message": "<PERSON><PERSON><PERSON>"}, "delivery_company": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "Klik untuk bertanya"}, "dian_pu_ID": {"message": "ID kedai"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_pu_lian_jie": {"message": "Pautan <PERSON>"}, "dian_pu_ming": {"message": "<PERSON><PERSON>"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON>"}, "dian_pu_shang_pin_zong_hsu": {"message": "<PERSON><PERSON><PERSON> B<PERSON>ngan Produk dalam Kedai: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Menyimpan maklumat"}, "ding_zai_zuo_ce": {"message": "dipaku ke kiri"}, "disable_old_version_tips_disable_btn_title": {"message": "Lumpuhkan versi lama"}, "download_image__SKU_variant_images": {"message": "<PERSON><PERSON>j varian SKU"}, "download_image__assume": {"message": "<PERSON><PERSON><PERSON> conto<PERSON>, kami memp<PERSON><PERSON> 2 imej, product1.jpg dan product2.gif.\nimg_{$no$} akan dinamakan semula kepada img_01.jpg, img_02.gif;\n{$group$}_{$no$} akan dinamakan semula kepada main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "<PERSON><PERSON>"}, "download_image__combined_image": {"message": "<PERSON><PERSON><PERSON> perincian produk gabungan"}, "download_image__continue_downloading": {"message": "Teruskan memuat turun"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__download_combined_image": {"message": "<PERSON><PERSON> turun imej perincian produk gabungan"}, "download_image__download_zip": {"message": "Muat turun zip"}, "download_image__enlarge_check": {"message": "<PERSON>ya menyokong imej JPEG, JPG, GIF dan P<PERSON>, saiz maksimum satu imej: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__export": {"message": "Eksport"}, "download_image__height": {"message": "Ketinggian"}, "download_image__ignore_videos": {"message": "Video telah di<PERSON>, kerana ia tidak boleh dieksport"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "<PERSON><PERSON> u<PERSON>a"}, "download_image__multi_folder": {"message": "Berbilang folder"}, "download_image__name": {"message": "muat turun imej"}, "download_image__notice_content": {"message": "<PERSON><PERSON> jangan tan<PERSON> \"Tanya tempat untuk menyimpan setiap fail sebelum memuat turun\" dalam tetapan muat turun penyemak imbas anda!!! <PERSON><PERSON> tidak, akan ada banyak kotak dialog."}, "download_image__notice_ignore": {"message": "<PERSON>an gesa untuk mesej ini lagi"}, "download_image__order_number": {"message": "{$no$} nombor siri; {$group$} nama kumpulan; {$date$} cap masa", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__prompt_download_zip": {"message": "Terdapat terlalu banyak imej, lebih baik anda memuat turunnya sebagai folder zip."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON> semula"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__single_folder": {"message": "Folder tunggal"}, "download_image__sku_image": {"message": "Imej SKU"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "<PERSON><PERSON>"}, "download_reviews__download_images": {"message": "<PERSON><PERSON> turun imej <PERSON>n"}, "download_reviews__dropdown_title": {"message": "<PERSON><PERSON> turun imej <PERSON>n"}, "download_reviews__export_csv": {"message": "eksport CSV"}, "download_reviews__no_images": {"message": "0 gambar tersedia untuk dimuat turun"}, "download_reviews__no_reviews": {"message": "Tiada ulasan untuk dimuat turun!"}, "download_reviews__notice": {"message": "Petua:"}, "download_reviews__notice__chrome_settings": {"message": "Tetapkan penyemak imbas Chrome untuk bertanya tempat untuk menyimpan setiap fail sebelum memuat turun, tetapkan kepada \"Mati\""}, "download_reviews__notice__wait": {"message": "<PERSON><PERSON><PERSON> pada bilangan ul<PERSON>n, masa <PERSON> mungkin lebih lama"}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__pages_list__page": {"message": "Halaman $page$ sebelumnya", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Julat pilihan"}, "export_shopping_cart__csv_filed__details_url": {"message": "Pautan produk"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Pautan SKU gema"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "harga"}, "export_shopping_cart__csv_filed__specs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__title": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__export_btn": {"message": "Eksport"}, "export_shopping_cart__export_empty": {"message": "Sila pilih produk!"}, "fa_huo_shi_jian": {"message": "penghantaran"}, "favorite_add_email": {"message": "Tambah E-mel"}, "favorite_add_favorites": {"message": "Tambahkan pada Kegemaran"}, "favorite_added": {"message": "Ditambah"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "favorite_btn_notify": {"message": "<PERSON><PERSON><PERSON> harga"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON><PERSON> produk"}, "favorite_current_price": {"message": "<PERSON><PERSON> semasa"}, "favorite_due_date": {"message": "tarikh tamat tempoh"}, "favorite_enable_notification": {"message": "<PERSON>la dayakan pemberitahuan e-mel"}, "favorite_expired": {"message": "<PERSON><PERSON> tempoh"}, "favorite_go_to_enable": {"message": "<PERSON>gi untuk membolehkan"}, "favorite_msg_add_success": {"message": "Ditambah ke kegemaran"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON><PERSON><PERSON> dari kegemaran"}, "favorite_msg_failure": {"message": "Gagal! <PERSON>at semula <PERSON> dan cuba lagi."}, "favorite_please_add_email": {"message": "Sila tambah E-mel"}, "favorite_price_drop": {"message": "<PERSON>wa<PERSON>"}, "favorite_price_rise": {"message": "<PERSON><PERSON>"}, "favorite_price_untracked": {"message": "<PERSON><PERSON> tidak di<PERSON>i"}, "favorite_saved_price": {"message": "<PERSON><PERSON> yang disimpan"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_sub_email_address": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON>mel lang<PERSON>an"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_tracking_prices": {"message": "<PERSON><PERSON><PERSON> harga"}, "favorite_verify_email": {"message": "<PERSON><PERSON><PERSON> alamat e-mel"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON><PERSON>h anda pasti menghapusnya?"}, "favorites_update_button": {"message": "<PERSON><PERSON> kini harga sekarang"}, "fen_lei": {"message": "kate<PERSON>i"}, "fen_xia_yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "find_similar": {"message": "<PERSON><PERSON>"}, "first_ali_price_date": {"message": "<PERSON><PERSON><PERSON> mula-mula ditang<PERSON>p o<PERSON>h per<PERSON>k AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Tiada kupon"}, "fooview_coupons_modal_title": {"message": "kupon"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Harga < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "atau harga > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_error_msg_no_favorites": {"message": "Tambahkan produk kegemaran di sini untuk menerima amaran penurunan harga."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "potongan harga"}, "fooview_favorites_filter_price_up": {"message": "kena<PERSON>n harga"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON> saya"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON> ke AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "Untuk men<PERSON> ha<PERSON>ya"}, "fooview_price_history_app_price": {"message": "<PERSON>rga APP:"}, "fooview_price_history_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON> balas"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON>mi tidak menemui sebarang ulasan untuk produk ini."}, "fooview_reviews_filter_buyer_reviews": {"message": "Foto pembeli"}, "fooview_reviews_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>i"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON> balas"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Penarafan"}, "fooview_same_product_modal_title": {"message": "<PERSON>i produk yang sama"}, "fooview_same_product_search_by_image": {"message": "<PERSON>i mengikut gambar"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "for_12_months": {"message": "Selama 1 tahun"}, "for_12_months_list_pro": {"message": "12 bulan"}, "for_12_months_nei": {"message": "<PERSON><PERSON> tempoh 12 bulan"}, "for_1_months": {"message": "1 bulan"}, "for_1_months_nei": {"message": "Dalam masa 1 bulan"}, "for_3_months": {"message": "Selama 3 bulan"}, "for_3_months_nei": {"message": "<PERSON>am masa 3 bulan"}, "for_6_months": {"message": "Selama 6 bulan"}, "for_6_months_nei": {"message": "<PERSON><PERSON> masa 6 bulan"}, "for_9_months": {"message": "9 bulan"}, "for_9_months_nei": {"message": "<PERSON><PERSON> masa 9 bulan"}, "fu_gou_lv": {"message": "<PERSON><PERSON> belian semula"}, "gao_liang_bu_tong_dian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON>an"}, "gao_liang_guang_gao_chan_pin": {"message": "Serlahkan Produk Iklan"}, "geng_duo_xin_xi": {"message": "Maklumat lan<PERSON>t"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON> kemas kini"}, "get_store_products_fail_tip": {"message": "Klik OK untuk pergi ke pengesahan untuk memastikan akses biasa"}, "gong_x_kuan_shang_pin": {"message": "Sejumlah $amount$ produk", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "ID <PERSON>al"}, "gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON>"}, "gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih tua"}, "gong_ying_shang_xin_xi": {"message": "mak<PERSON><PERSON> pembekal"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "<PERSON><PERSON><PERSON>"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON>"}, "guan_jian_zi": {"message": "<PERSON><PERSON> kunci"}, "guang_gao_chan_pin": {"message": "Iklan produk"}, "guang_gao_zhan_bi": {"message": "Iklan nisbah"}, "guo_ji_wu_liu_yun_fei": {"message": "<PERSON><PERSON>"}, "guo_lv_tiao_jian": {"message": "Penap<PERSON>"}, "hao_ping_lv": {"message": "<PERSON><PERSON><PERSON> positif"}, "highest_price": {"message": "tinggi"}, "historical_trend": {"message": "<PERSON><PERSON>"}, "how_to_screenshot": {"message": "Tekan dan tahan butang kiri tetikus untuk memilih kawasan, ketik butang kanan tetikus atau kekunci Esc untuk keluar dari tangkapan skrin"}, "howt_it_works": {"message": "Bagaimana ia berfungsi"}, "hui_fu_lv": {"message": "<PERSON><PERSON>"}, "hui_tou_lv": {"message": "kadar pu<PERSON>an"}, "inquire_freightFee": {"message": "Siasatan Pengangkutan"}, "inquire_freightFee_Yuan": {"message": "Pengangkutan/Yuan"}, "inquire_freightFee_province": {"message": "Wilayah"}, "inquire_freightFee_the": {"message": "Pengangkutan ialah $num$ , yang bermaksud bahawa rantau ini mempunyai penghantaran percuma.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Iklan"}, "jia_ge": {"message": "harga"}, "jia_ge_dan_wei": {"message": "Unit"}, "jia_ge_qu_shi": {"message": "Trend"}, "jia_zai_n_ge_shang_pin": {"message": "Muatkan $num$ Produk", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Peratusan volum jualan dalam 30 hari yang lalu"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON> hasil dalam 30 hari yang lalu"}, "jin_30d_xiao_liang": {"message": "Jualan"}, "jin_30d_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> j<PERSON>an da<PERSON> 30 hari yang lalu"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "<PERSON><PERSON><PERSON> dalam 30 hari yang lalu"}, "jin_90_tian_mai_jia_shu": {"message": "<PERSON><PERSON>beli dalam 90 <PERSON>"}, "jin_90_tian_xiao_shou_liang": {"message": "Jualan dalam 90 Hari <PERSON>"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON> terpilih"}, "jing_ying_mo_shi": {"message": "<PERSON>"}, "jing_ying_mo_shi__gong_chang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> pertikaian hak kedai penjual"}, "jiu_fen_lv": {"message": "<PERSON><PERSON>"}, "jiu_fen_lv__desc": {"message": "Perkadaran pesanan dengan aduan diselesaikan dalam tempoh 30 hari yang lalu dan dinilai sebagai tanggungjawab penjual atau kedua-dua pihak"}, "kai_dian_ri_qi": {"message": "<PERSON><PERSON><PERSON>"}, "keywords": {"message": "<PERSON><PERSON> kunci"}, "kua_jin_Select_pan_huo": {"message": "<PERSON><PERSON><PERSON> rentas sempadan"}, "last15_days": {"message": "15 hari lepas"}, "last180_days": {"message": "180 hari lepas"}, "last30_days": {"message": "Dalam 30 hari lepas"}, "last360_days": {"message": "360 hari lepas"}, "last45_days": {"message": "45 hari lepas"}, "last60_days": {"message": "60 hari lepas"}, "last7_days": {"message": "7 hari lepas"}, "last90_days": {"message": "90 hari lepas"}, "last_30d_sales": {"message": "Jualan 30 hari lepas"}, "lei_ji": {"message": "Kumulatif"}, "lei_ji_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> jualan selepas produk di rak"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON> jualan te<PERSON> dalam 30 hari yang lalu, diisih dari tinggi ke rendah"}, "lian_xi_fang_shi": {"message": "Maklumat per<PERSON>bungan"}, "list_time": {"message": "Pada tarikh rak"}, "load_more": {"message": "Muatkan <PERSON>gi"}, "login_to_aliprice": {"message": "Log masuk ke AliPrice"}, "long_link": {"message": "<PERSON><PERSON><PERSON>"}, "lowest_price": {"message": "rendah"}, "mai_jia_shu": {"message": "pen<PERSON>al"}, "mao_li_lv": {"message": "<PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "<PERSON><PERSON>"}, "mobile_view__sjdxq": {"message": "<PERSON><PERSON><PERSON> da<PERSON> Apl"}, "mobile_view__sjdxqy": {"message": "<PERSON><PERSON> da<PERSON> Apl"}, "mobile_view__smck": {"message": "<PERSON><PERSON><PERSON> un<PERSON>"}, "mobile_view__smckms": {"message": "<PERSON>la gunakan kamera atau apl untuk mengimbas dan melihat"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gagal"}, "modified_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_btn_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_btn_package": {"message": "<PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Mengenai produk"}, "nav_btn_viewed": {"message": "<PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "tiada"}, "normal_link": {"message": "<PERSON><PERSON><PERSON>"}, "notice": {"message": "petunjuk"}, "number_reviews": {"message": "<PERSON><PERSON><PERSON>"}, "only_show_num": {"message": "Jumlah produk: $allnum$, Tersembunyi: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "<PERSON><PERSON>"}, "open": {"message": "<PERSON><PERSON>"}, "open_links": {"message": "<PERSON><PERSON> p<PERSON>an"}, "options_page_tab_check_links": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_gernal": {"message": "Am"}, "options_page_tab_notifications": {"message": "Pemberitahuan"}, "options_page_tab_others": {"message": "<PERSON> lain"}, "options_page_tab_sbi": {"message": "<PERSON>i mengikut gambar"}, "options_page_tab_shortcuts": {"message": "Jalan pintas"}, "options_page_tab_shortcuts_title": {"message": "Saiz fon untuk jalan pintas"}, "options_page_tab_similar_products": {"message": "<PERSON>duk yang sama"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Berbilang pautan produk akan dibuka"}, "order_list_sku_show_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> varian yang dipilih dalam pautan kongsi"}, "orders_last30_days": {"message": "Bilangan pesanan dalam 30 hari terakhir"}, "pTutorial_favorites_block1_desc1": {"message": "<PERSON><PERSON><PERSON> yang anda jejak disenaraikan di sini"}, "pTutorial_favorites_block1_title": {"message": "Kegemaran"}, "pTutorial_popup_block1_desc1": {"message": "Label hijau bermaksud terdapat produk yang turun harga"}, "pTutorial_popup_block1_title": {"message": "<PERSON><PERSON><PERSON> dan <PERSON>"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\", tambahkan produk ke Kegemaran. <PERSON><PERSON><PERSON> harga mereka turun, anda akan menerima pember<PERSON>n"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON><PERSON> harga"}, "pTutorial_reviews_block1_desc1": {"message": "<PERSON><PERSON><PERSON> pembeli dari <PERSON>ao dan gambar sebenar dari maklum balas AliExpress"}, "pTutorial_reviews_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_reviews_block2_desc1": {"message": "Sangat berguna untuk memeriksa ulasan dari pembeli lain"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON>a boleh membandingkannya untuk membuat pilihan terbaik"}, "pTutorial_same_products_block1_desc2": {"message": "K<PERSON> 'La<PERSON>' untuk \"Cari mengikut gambar\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON>duk yang sama"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Jatuhkan gambar produk di sana dan pilih kategori"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON>i mengikut gambar"}, "pTutorial_seller_analysis_block1_desc1": {"message": "<PERSON><PERSON> maklum balas positif penjual, skor maklum balas dan berapa lama penjual berada di pasaran"}, "pTutorial_seller_analysis_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Peringkat penjual berdasarkan 3 indeks: item se<PERSON>i yang <PERSON>, <PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Kami menggunakan 3 warna dan ikon untuk menunjukkan tahap kepercayaan penjual"}, "page_count": {"message": "Bilangan muka surat"}, "pai_chu": {"message": "Dikecualikan"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Kecualikan Hong Kong-Terhad"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Kecualikan <PERSON>-Terhad"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Kecualikan Korea-Restricted"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kecualikan Kazakhstan-Terhad"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Kecualikan Macau-Restricted"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Kecualikan Eropah Timur-Terhad"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Kecualikan Arab Saudi-Terhad"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Kecualikan Taiwan-Restricted"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Kecualikan A.S.-Terhad"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Kecualikan Vietnam-Restricted"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Kecualikan Item Terhad"}, "payable_price_formula": {"message": "Harga + Penghantaran + Diskaun"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON><PERSON> runcit"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON> secara runcit"}, "pdp_copy_fail": {"message": "Salinan gagal!"}, "pdp_copy_success": {"message": "Penyalinan berjaya!"}, "pdp_share_modal_subtitle": {"message": "<PERSON><PERSON> tan<PERSON> s<PERSON>, dia akan melihat chioce anda."}, "pdp_share_modal_title": {"message": "<PERSON><PERSON> pilihan anda"}, "pdp_share_screenshot": {"message": "<PERSON><PERSON> tang<PERSON>pan skrin"}, "pei_song": {"message": "Penghantaran"}, "pin_lei": {"message": "kate<PERSON>i"}, "pin_zhi_ti_yan": {"message": "<PERSON><PERSON><PERSON> produk"}, "pin_zhi_ti_yan__desc": {"message": "<PERSON><PERSON> bayaran balik kualiti kedai penjual"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON> bayaran balik"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Bahagian pesanan yang hanya dikembalikan dan dikembalikan dalam tempoh 30 hari yang lalu"}, "ping_fen": {"message": "<PERSON><PERSON><PERSON>"}, "ping_jun_fa_huo_su_du": {"message": "<PERSON><PERSON><PERSON>"}, "pkgInfo_hide": {"message": "Maklumat logistik: hidup/mati"}, "pkgInfo_no_trace": {"message": "Tiada maklumat logistik"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Pergi ke $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON>a me<PERSON> ..."}, "popup_track_package_btn_track": {"message": "LATIHAN"}, "popup_track_package_desc": {"message": "TRACKING PAKEJ SEMUA DALAM SATU"}, "popup_track_package_search_placeholder": {"message": "Nombor pengesanan"}, "popup_translate_search_placeholder": {"message": "Te<PERSON><PERSON><PERSON> dan cari di $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_chart_tip_ae": {"message": "Petua: Bilangan pesanan ialah bilangan terkumpul pesanan sejak dilancarkan"}, "price_history_chart_tip_coupang": {"message": "Petua: <PERSON><PERSON><PERSON> akan memadamkan kiraan pesanan pesanan penipuan"}, "price_history_inm_1688_l1": {"message": "<PERSON>la pasang"}, "price_history_inm_1688_l2": {"message": "Pembantu Belanja AliPrice untuk 1688"}, "price_history_panel_lowest_price": {"message": "Harga <PERSON>:"}, "price_history_panel_tab_price_tracking": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_pro_modal_title": {"message": "<PERSON><PERSON><PERSON> harga & <PERSON><PERSON><PERSON> pesanan"}, "privacy_consent__btn_agree": {"message": "<PERSON><PERSON> semula per<PERSON>an pengumpulan data"}, "privacy_consent__btn_disable_all": {"message": "Tidak menerima"}, "privacy_consent__btn_enable_all": {"message": "<PERSON><PERSON><PERSON>"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON> bahawa, tanpa data atau kuki beberapa fungsi akan dimatikan kerana fungsi tersebut memerlukan penjelasan data atau kuki, tetapi anda masih dapat menggunakan fungsi lainnya."}, "privacy_consent__desc_privacy_L1": {"message": "Sayang<PERSON>, tanpa data atau kuki tidak akan berfungsi kerana kita memerlukan penjelasan mengenai data atau kuki."}, "privacy_consent__desc_privacy_L2": {"message": "Se<PERSON>ranya anda tidak membenarkan kami mengumpulkan maklumat ini, hapus maklumat tersebut."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, kami hanya mendapatkan data mata wang anda dalam kuki semasa membeli-belah dalam talian untuk menunjukkan sejarah harga."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON> yang dip<PERSON>an"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Tambahkan kuki dalam penyemak imbas untuk mengenal pasti komputer atau peranti anda tanpa nama."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Tambahkan data fungsional sebagai tambahan untuk berfungsi dengan fungsi."}, "privacy_consent__item_functional_title": {"message": "<PERSON><PERSON> dan <PERSON>"}, "privacy_consent__more_desc": {"message": "Harap maklum bahawa kami tidak berkongsi data peribadi anda dengan syarikat lain dan tidak ada syarikat iklan yang mengumpulkan data melalui perkhidmatan kami."}, "privacy_consent__options__btn__desc": {"message": "Untuk mengg<PERSON>kan semua ciri, <PERSON><PERSON> per<PERSON> mengh<PERSON>."}, "privacy_consent__options__btn__label": {"message": "Hidupkan<PERSON>"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON> akan mengumpulkan data berikut yang mengenal pasti anda secara peribadi:"}, "privacy_consent__options__desc_L2": {"message": "- kuki, kami hanya mendapatkan data mata wang Anda dalam kuki semasa Anda membeli-belah dalam talian untuk menunjukkan sejarah harga."}, "privacy_consent__options__desc_L3": {"message": "- dan tambahkan kuki dalam penyemak imbas untuk mengenal pasti komputer atau peranti Anda tanpa nama."}, "privacy_consent__options__desc_L4": {"message": "- data tanpa nama lain menjadikan pelanjutan ini lebih senang."}, "privacy_consent__options__desc_L5": {"message": "Hara<PERSON> maklum bahawa kami tidak membagikan data peribadi anda dengan syarikat lain dan tidak ada syarikat iklan yang mengumpulkan data melalui perkhidmatan kami."}, "privacy_consent__privacy_preferences": {"message": "<PERSON><PERSON><PERSON> privasi"}, "privacy_consent__read_more": {"message": "Baca lebih lanjut >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_info": {"message": "Maklumat Produk"}, "product_recommend__name": {"message": "<PERSON>duk yang sama"}, "product_research": {"message": "Penyelidikan Produk"}, "product_sub__email_desc": {"message": "E-mel mak<PERSON>an harga"}, "product_sub__email_edit": {"message": "edit"}, "product_sub__email_not_verified": {"message": "Sila sahkan e-mel"}, "product_sub__email_required": {"message": "Sila berikan e-mel"}, "product_sub__form_countdown": {"message": "Tutup automatik selepas $seconds$ saat", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Gagal menambah per<PERSON>tan!"}, "product_sub__form_input_price": {"message": "harga input"}, "product_sub__form_item_country": {"message": "bang<PERSON>"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON> semasa"}, "product_sub__form_item_duration": {"message": "trek"}, "product_sub__form_item_higher_price": {"message": "Atau harga>"}, "product_sub__form_item_invalid_higher_price": {"message": "Harga mestilah lebih besar daripada $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Harga mestilah lebih rendah daripada $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON><PERSON> harga <"}, "product_sub__form_submit": {"message": "Hantar"}, "product_sub__form_success": {"message": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>bah per<PERSON>!"}, "product_sub__high_price_notify": {"message": "<PERSON><PERSON><PERSON> saya tentang kenaikan harga"}, "product_sub__low_price_notify": {"message": "<PERSON><PERSON><PERSON> saya tentang pengurangan harga"}, "product_sub__modal_title": {"message": "<PERSON><PERSON><PERSON> per<PERSON>han harga langganan"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "<PERSON><PERSON><PERSON>"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON>la pilih sekurang-kura<PERSON>nya satu produk"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON>la pilih sekurang-kurangnya satu medan"}, "qu_deng_lu": {"message": "Log masuk"}, "quan_guo_yan_xuan": {"message": "Pilihan Global"}, "recommendation_popup_banner_btn_install": {"message": "<PERSON><PERSON>"}, "recommendation_popup_banner_desc": {"message": "<PERSON><PERSON><PERSON> sejarah harga dalam 3/6 bulan, dan pember<PERSON><PERSON>n penurunan harga"}, "region__all": {"message": "<PERSON><PERSON><PERSON> wilayah"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "<PERSON><PERSON><PERSON> klik kanan"}, "ren_zheng_gong_chang": {"message": "<PERSON><PERSON>"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON> se<PERSON>"}, "required_to_aliprice_login": {"message": "Perlu log masuk ke AliPrice"}, "revenue_last30_days": {"message": "<PERSON><PERSON><PERSON> j<PERSON>an da<PERSON> 30 hari terakhir"}, "review_counts": {"message": "Bilangan pengumpul"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Tempoh kemasukan"}, "sales_amount_last30_days": {"message": "<PERSON><PERSON><PERSON> j<PERSON>an da<PERSON> 30 hari yang lalu"}, "sales_last30_days": {"message": "Jualan dalam 30 hari yang lalu"}, "sbi_alibaba_cate__accessories": {"message": "Aks<PERSON>ori"}, "sbi_alibaba_cate__aqfk": {"message": "Keselamatan"}, "sbi_alibaba_cate__bags_cases": {"message": "Beg & Sarung"}, "sbi_alibaba_cate__beauty": {"message": "Kecantikan"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Budaya pejabat"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Perala<PERSON> dapur"}, "sbi_alibaba_cate__clothes": {"message": "Pakaia<PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Penyiaran Media"}, "sbi_alibaba_cate__coat_jacket": {"message": "Mantel & Jaket"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Elektronik Pengguna"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__csyp": {"message": "<PERSON><PERSON><PERSON><PERSON> katil"}, "sbi_alibaba_cate__cwyy": {"message": "Be<PERSON><PERSON><PERSON> haiwan peli<PERSON>an"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON> segar"}, "sbi_alibaba_cate__dgdq": {"message": "Juruelektrik"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Pakaian & Pakaian"}, "sbi_alibaba_cate__dszm": {"message": "Pencahayaan"}, "sbi_alibaba_cate__dzqj": {"message": "Peranti elektronik"}, "sbi_alibaba_cate__essb": {"message": "Peralatan Terpakai"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Pakaian & Aksesori"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "<PERSON><PERSON><PERSON><PERSON> diri"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Kraftangan"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON> alam"}, "sbi_alibaba_cate__hfcz": {"message": "Solekan penja<PERSON>an kulit"}, "sbi_alibaba_cate__hg": {"message": "Industri kimia"}, "sbi_alibaba_cate__jg": {"message": "Memproses"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON>at mesin"}, "sbi_alibaba_cate__jjry": {"message": "kegunaan harian isi rumah"}, "sbi_alibaba_cate__jtys": {"message": "Pengangkutan"}, "sbi_alibaba_cate__jxsb": {"message": "perala<PERSON>"}, "sbi_alibaba_cate__jxwj": {"message": "<PERSON><PERSON><PERSON><PERSON> mekanikal"}, "sbi_alibaba_cate__jydq": {"message": "<PERSON><PERSON><PERSON> rumah"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON><PERSON> binaan pembaikan rumah"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__mj": {"message": "tuala"}, "sbi_alibaba_cate__myyp": {"message": "Produk Bayi"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON> wanita"}, "sbi_alibaba_cate__ny": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__others": {"message": "<PERSON> lain"}, "sbi_alibaba_cate__qcyp": {"message": "Aksesori Auto"}, "sbi_alibaba_cate__qmpj": {"message": "Bahagian-bahagian auto"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Komputer digital"}, "sbi_alibaba_cate__snqj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan pem<PERSON>an"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON>kh<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Seluar & Skirt"}, "sbi_alibaba_cate__txcp": {"message": "Produk komunikasi"}, "sbi_alibaba_cate__tz": {"message": "Pakaian kanak-kanak"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Alat perkakasan"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON>g kulit"}, "sbi_alibaba_cate__xmhz": {"message": "k<PERSON><PERSON><PERSON> projek"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "<PERSON><PERSON><PERSON> sukan"}, "sbi_alibaba_cate__ydhw": {"message": "<PERSON><PERSON> luar"}, "sbi_alibaba_cate__yjkc": {"message": "Mineral Metalurgi"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentasi"}, "sbi_alibaba_cate__ys": {"message": "Cetak"}, "sbi_alibaba_cate__yyby": {"message": "Rawatan perubatan"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Bilangan pembeli dalam 90 hari terakhir"}, "sbi_alibaba_cn_kj_90xsl": {"message": "<PERSON><PERSON><PERSON> j<PERSON>an da<PERSON> 90 hari terakhir"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON> harga"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Bayaran penghantaran antarabangsa"}, "sbi_alibaba_cn_kj_gjyf": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON> harga"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Keuntungan = anggaran harga x margin keuntungan"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "<PERSON><PERSON><PERSON><PERSON> purata"}, "sbi_alibaba_cn_kj_qtfy": {"message": "bayaran lain"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Kos lain = anggaran harga x nisbah kos lain"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Set"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON>pa harga"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "harga"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Terdapat untuk dijual"}, "sbi_aliprice_alibaba_cn__moren": {"message": "lalai"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Pasti"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Jualan"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "perabot"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "makanan ringan"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "alat solek"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Aks<PERSON>ori"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Minuman botol"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON> lain"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Skirt"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagasi"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bahagian bawah"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "kasut"}, "sbi_aliprice_cate__apparel": {"message": "Pakaia<PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Kereta & Motosikal"}, "sbi_aliprice_cate__beauty_health": {"message": "Kecantikan & Kesihatan"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Telefon bimbit & Telekomunikasi"}, "sbi_aliprice_cate__computer_office": {"message": "Komputer & Pejabat"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Elektronik Pengguna"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Pendidikan & Bekalan <PERSON>"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Komponen & Bekalan Elektronik"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Sambungan & Rambut palsu"}, "sbi_aliprice_cate__home_garden": {"message": "Rumah & Taman"}, "sbi_aliprice_cate__home_improvement": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rumah"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Barang Kemas & Aksesori"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagasi & Beg"}, "sbi_aliprice_cate__mother_kids": {"message": "Ibu & Anak"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Kebaharuan & Penggunaan Khas"}, "sbi_aliprice_cate__security_protection": {"message": "Keselamatan & Perlindungan"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sukan & Hiburan"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Mainan & Hobi"}, "sbi_aliprice_cate__watches": {"message": "<PERSON> tangan"}, "sbi_aliprice_cate__weddings_events": {"message": "Perkahwinan & Acara"}, "sbi_btn_capture_txt": {"message": "Menangkap"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON> se<PERSON>ng"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON><PERSON> den<PERSON> saya"}, "sbi_button__contact_supplier": {"message": "Hubung<PERSON>"}, "sbi_button__hide_on_this_site": {"message": "<PERSON><PERSON> tun<PERSON>n di laman web ini"}, "sbi_button__open_settings": {"message": "Konfigurasikan carian mengikut gambar"}, "sbi_capture_shortcut_tip": {"message": "atau tekan kekunci \"Enter\" pada papan kekunci"}, "sbi_capturing_tip": {"message": "Menangkap"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 Bintang"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON>"}, "sbi_crop_start": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON> pen<PERSON>, sila muat naik gambar untuk pengesahan. (Lihat $video_tutorial$ atau cuba kosongkan kuki)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "<PERSON><PERSON><PERSON><PERSON> yang tidak <PERSON>a, sila sahkan"}, "sbi_err_captcha_for_taobao": {"message": "Taobao meminta anda men<PERSON>, sila muat naik gambar secara manual dan cari untuk mengesahkannya. Kesalahan ini disebabkan oleh dasar pengesahan baru \"Pencarian TaoBao dengan gambar\", kami mencadangkan agar aduan mengesahkan terlalu kerap di Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "mak<PERSON> balas"}, "sbi_err_captcha_msg": {"message": "$platform$ memer<PERSON>an anda memuat naik imej untuk mencari atau melengkapkan pengesahan keselamatan untuk mengalih keluar sekatan carian", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON> sama ada versi terbaru"}, "sbi_err_cookie_btn_clear": {"message": "Kosongkan Kuki"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Kosongkan kuki 1688? (Perlu log masuk semula)"}, "sbi_err_desperate_feature_pdd": {"message": "<PERSON><PERSON>i carian imej telah dialihkan ke Carian Pinduoduo oleh sambungan Imej."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Bagaimana untuk meningkatkan kadar kejayaan carian imej?"}, "sbi_err_img_undersize": {"message": "Imej > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Log masuk $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Log masuk"}, "sbi_err_low_version": {"message": "Pasang versi terkini ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON>at turun"}, "sbi_err_need_help": {"message": "<PERSON><PERSON>"}, "sbi_err_network": {"message": "<PERSON><PERSON>, pastikan anda boleh melawati tapak web"}, "sbi_err_not_low_version": {"message": "Versi terbaru telah dipasang ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Cuba lagi"}, "sbi_err_try_again_action": {"message": "Cuba lagi"}, "sbi_err_visit_and_try": {"message": "Cuba lagi, atau lawati $website$ untuk mencuba semula", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Lawati Laman Utama $siteUrl$ daripada $siteName$", "placeholders": {"siteName": {"content": "$1"}, "siteUrl": {"content": "$2"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON><PERSON><PERSON> gagal, muat semula halaman dan cuba lagi."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "Negara Destinasi"}, "sbi_kuajing_filter_es": {"message": "Sepanyol"}, "sbi_kuajing_filter_estimate": {"message": "Anggarkan"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON> harga"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Formula anggaran harga = (harga komoditi + penghantaran logistik antarabangsa) / (1 - margin keuntungan - nisbah kos lain)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Masukkan kata kunci agar sesuai dengan tajuk"}, "sbi_kuajing_filter_logistics": {"message": "Templat logistik"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "Diskaun logistik"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Pengangkutan logistik antarabangsa = (berat x harga penghantaran + yuran pendaftaran) x (1 - diskaun)"}, "sbi_kuajing_filter_others_fee": {"message": "Bayaran lain"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON><PERSON> k<PERSON>"}, "sbi_kuajing_filter_prop": {"message": "Atribut"}, "sbi_kuajing_filter_ru": {"message": "Rusia"}, "sbi_kuajing_filter_total": {"message": "Padankan item serupa $count$", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "U.K."}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON> tidak diju<PERSON>ai,sila masuk ke $loginSite$ atau cuba gambar lain", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Untuk sementara tidak tersedia untuk Safari, sila gunakan $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Penyemak imbas Chrome dan pelanju<PERSON>nya"}, "sbi_msg_no_result_reinstall_l1": {"message": "Tidak ada hasil yang dijumpai, sila log masuk ke $loginSite$ atau cuba gambar lain, atau pasang semula versi $latestExtUrl$ terkini", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "<PERSON><PERSON><PERSON> te<PERSON>", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_shipping_": {"message": "Penghan<PERSON>n hari yang sama"}, "sbi_specify_category": {"message": "Nyatakan kategori:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 di luar negara"}, "sbi_tutorial_btn_more": {"message": "Penggunaan 2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON><PERSON>"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON>, tidak ada hasil yang di<PERSON>, sila cuba lagi."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON>"}, "sbi_visiting": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_yiwugo__jiagexiangtan": {"message": "Hubungi penjual untuk mengetahui ha<PERSON>ya"}, "sbi_yiwugo__qigou": {"message": "$num$ Potongan (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Bintang"}, "searchByImage_screenshot": {"message": "Tangkapan skrin satu klik"}, "searchByImage_search": {"message": "Carian satu klik untuk item yang sama"}, "searchByImage_size_type": {"message": "Saiz fail tidak boleh lebih besar daripada $num$ MB, $type$ sahaja", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Menganalis<PERSON> gambar"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON> produk"}, "search_by_image_progress_sending": {"message": "Menghantar gambar"}, "search_by_image_response_rate": {"message": "Kadar Respons: $responseRate$ pembeli yang menghubungi pembekal ini mendapat sambutan dalam masa $responseInHour$ jam.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON>i mengikut kata kunci:"}, "select_country_language_modal_title_country": {"message": "Negara"}, "select_country_language_modal_title_language": {"message": "Bahasa"}, "select_country_region_modal_title": {"message": "<PERSON><PERSON><PERSON> negara/rantau"}, "select_language_modal_title": {"message": "<PERSON><PERSON><PERSON> bahasa:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON>"}, "sellers_count": {"message": "Bilangan penjual pada halaman semasa"}, "sellers_count_per_page": {"message": "Bilangan penjual pada halaman semasa"}, "service_score": {"message": "<PERSON><PERSON><PERSON> per<PERSON>n yang komp<PERSON><PERSON>"}, "set_shortcut_keys": {"message": "Tetapkan kekunci pintasan"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON><PERSON> membe<PERSON>-belah"}, "setting_modal_options_position_title": {"message": "Kedudukan pemalam"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON>t kiri"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON> kanan"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> tema"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "Tetapan"}, "setting_options_country_title": {"message": "Negara / Wilayah"}, "setting_options_hover_zoom_desc": {"message": "Arahkan tetikus untuk mengezum masuk"}, "setting_options_hover_zoom_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_jd_coupon_desc": {"message": "Dijumpai kupon di JD.com"}, "setting_options_jd_coupon_title": {"message": "Kupon JD.com"}, "setting_options_language_title": {"message": "Bahasa"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> harga produk di <PERSON>uru<PERSON>, anda akan menerima pember<PERSON>huan tolak."}, "setting_options_price_drop_alert_title": {"message": "<PERSON><PERSON><PERSON><PERSON> harga"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON><PERSON><PERSON> sejarah harga di halaman carian produk"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>)"}, "setting_options_price_history_on_produt_page_desc": {"message": "<PERSON><PERSON><PERSON> sejarah produk di halaman perincian produk"}, "setting_options_price_history_on_produt_page_title": {"message": "<PERSON><PERSON><PERSON> harga (halaman butiran)"}, "setting_options_sales_analysis_desc": {"message": "Ondersteuningsstatistieken van prijs, verkoopvolume, aantal verkopers en winkelverkoopratio op de $platforms$ productlijstpagina", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_tacking_price_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_value_off": {"message": "<PERSON><PERSON>"}, "setting_options_value_on": {"message": "Dihidup<PERSON>"}, "setting_pkg_quick_view_desc": {"message": "Sokongan: 1688 & Taobao"}, "setting_saved_message": {"message": "<PERSON><PERSON><PERSON> ber<PERSON>a disimpan"}, "setting_section_enable_platform_title": {"message": "Tidak aktif"}, "setting_section_setting_title": {"message": "Tetapan"}, "setting_section_shortcuts_title": {"message": "Jalan pintas"}, "settings_aliprice_agent__desc": {"message": "Dipaparkan pada halaman butiran produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>a"}, "settings_copy_link__desc": {"message": "<PERSON><PERSON>n pada halaman butiran produk"}, "settings_copy_link__title": {"message": "<PERSON><PERSON> salin dan tajuk <PERSON>"}, "settings_currency_desc__for_detail": {"message": "Sokong halaman perincian produk 1688"}, "settings_currency_desc__for_list": {"message": "<PERSON>i mengikut gambar (termasuk 1688/1688 di luar negara / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON>"}, "settings_currency_desc_display_for_list": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dalam carian imej (termasuk 1688/1688 di luar negara/Taobao)"}, "settings_currency_rate_desc": {"message": "<PERSON><PERSON> tukar dikemas kini dari \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank Negara China"}, "settings_download_images__desc": {"message": "Ondersteuning voor het downloaden van afbeeldingen van $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "butang muat turun imej"}, "settings_download_reviews__desc": {"message": "Dipaparkan pada halaman butiran produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "<PERSON><PERSON> turun imej <PERSON>n"}, "settings_google_translate_desc": {"message": "Klik kanan untuk mendapatkan bar terjemahan google"}, "settings_google_translate_title": {"message": "ter<PERSON><PERSON>an halaman web"}, "settings_historical_trend_desc": {"message": "<PERSON>rkan di sudut kanan bawah imej pada halaman senarai produk"}, "settings_modal_btn_more": {"message": "Lebih banyak tetapan"}, "settings_productInfo_desc": {"message": "Paparkan maklumat produk yang lebih terperinci pada halaman senarai produk. Mendayakan ini boleh meningkatkan beban komputer dan menyebabkan lag halaman. Jika ia menjejaskan prestasi, disyorkan untuk melumpuhkannya."}, "settings_product_recommend__desc": {"message": "Dipaparkan di bawah imej utama pada halaman butiran produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produk Disyorkan"}, "settings_research_desc": {"message": "<PERSON> maklumat lebih terperinci pada halaman senarai produk"}, "settings_sbi_add_to_list": {"message": "Tambahkan ke dalam $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Gambar kecil hasil carian gambar"}, "settings_sbi_remove_from_list": {"message": "<PERSON>h keluar dari $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Tambahkan ke senarai blok"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Laraskan kedudukan masuk"}, "settings_search_by_image_blacklist_desc": {"message": "<PERSON><PERSON> tunjukkan ikon di laman web dalam senarai hitam."}, "settings_search_by_image_blacklist_title": {"message": "<PERSON><PERSON><PERSON> sekatan"}, "settings_search_by_image_bottom_left": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_bottom_right": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_clear_blacklist": {"message": "Kosongkan senarai blok"}, "settings_search_by_image_detail_page_icon_title": {"message": "Gambar kecil"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON> besar"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON><PERSON> kecil"}, "settings_search_by_image_display_button_desc": {"message": "Satu klik pada ikon untuk mencari mengikut gambar"}, "settings_search_by_image_display_button_title": {"message": "Ikon pada gambar"}, "settings_search_by_image_sourece_websites_desc": {"message": "Cari produk sumber di laman web ini"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON> mengikut hasil gambar"}, "settings_search_by_image_top_left": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON>"}, "settings_search_keyword_on_x__desc": {"message": "Cari per<PERSON>an di $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Tunjukkan ikon $platform$ apabila perkataan dipilih", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Cuba cari produk yang sama di laman web tersebut (maks<PERSON>um hingga 5)"}, "settings_similar_products_title": {"message": "<PERSON>i produk yang sama"}, "settings_toolbar_expand_title": {"message": "<PERSON><PERSON><PERSON>"}, "settings_top_toolbar_desc": {"message": "Bar Cari di bahagian atas halaman"}, "settings_top_toolbar_title": {"message": "Bar Carian"}, "settings_translate_search_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> ke dalam bahasa <PERSON>ina dan cari"}, "settings_translate_search_title": {"message": "<PERSON><PERSON> berbilang bahasa"}, "settings_translator_contextmenu_title": {"message": "Tangkap untuk menterjemah"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Tapis untuk Eksport"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON> medan"}, "shang_jia_shi_jian": {"message": "Pada masa simpanan"}, "shang_pin_biao_ti": {"message": "tajuk produk"}, "shang_pin_dui_bi": {"message": "Perbandingan Produk"}, "shang_pin_lian_jie": {"message": "pautan produk"}, "shang_pin_xin_xi": {"message": "Maklumat produk"}, "share_modal__content": {"message": "Berkongsi dengan rakan anda"}, "share_modal__disable_for_while": {"message": "<PERSON>a tidak mahu be<PERSON> apa-apa"}, "share_modal__title": {"message": "Adakah anda suka $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "yang tinggal"}, "shi_fou_ke_ding_zhi": {"message": "<PERSON><PERSON>h ia boleh disesuaikan?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Jaminan <PERSON>"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Jaminan per<PERSON>"}, "shipping_fee": {"message": "<PERSON><PERSON>"}, "shop_followers": {"message": "Pengi<PERSON><PERSON>ai"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Maksimum hingga 5"}, "sku_calc_price": {"message": "<PERSON><PERSON>"}, "sku_calc_price_settings": {"message": "<PERSON><PERSON><PERSON>"}, "sku_formula": {"message": "Formula"}, "sku_formula_desc": {"message": "Penerangan Formula"}, "sku_formula_desc_text": {"message": "Menyokong formula matematik yang kompleks, dengan harga asal diwakili oleh A dan tambang diwakili oleh B \n\n<br/> \n\nMenyokong kurungan (), tambah +, tolak -, pendaraban * dan pembahagian / \n\n<br/> \n\nContoh: \n\n<br/> \n\n1. Untuk mencapai 1.2 kali ganda harga asal dan kemudian menambah tambang, formulanya ialah: A*1.2+B \n\n<br/> \n\n2. Untuk mencapai harga asal tambah 1 yuan, kemudian darab dengan 1.2 kali ganda, formulanya ialah: (A+1)*1.2 \n\n<br/> \n\n3. Untuk mencapai harga asal ditambah 10 yuan, kemudian darab dengan 1.2 kali, dan kemudian tolak 3 yuan, formulanya ialah: (A+10)*1.2-3"}, "sku_in_stock": {"message": "<PERSON><PERSON>"}, "sku_invalid_formula_format": {"message": "Format formula tidak sah"}, "sku_inventory": {"message": "Inventori"}, "sku_link_copy_fail": {"message": "<PERSON><PERSON><PERSON><PERSON>, spesif<PERSON><PERSON> dan atribut sku tidak dipilih"}, "sku_link_copy_success": {"message": "<PERSON><PERSON><PERSON><PERSON>, spesif<PERSON><PERSON> dan atribut sku dipilih"}, "sku_list": {"message": "Senarai SKU"}, "sku_min_qrder_qty": {"message": "<PERSON><PERSON><PERSON>"}, "sku_name": {"message": "Nama SKU"}, "sku_no": {"message": "Tidak."}, "sku_original_price": {"message": "<PERSON><PERSON>"}, "sku_price": {"message": "Harga SKU"}, "stop_track_time_label": {"message": "<PERSON><PERSON><PERSON>:"}, "suo_zai_di_qu": {"message": "lokasi"}, "tab_pkg_quick_view": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_price_history": {"message": "<PERSON><PERSON><PERSON> harga"}, "tab_product_details_reviews": {"message": "Ulasan gambar"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON>duk yang sama"}, "total_days_listed_per_product": {"message": "<PERSON><PERSON><PERSON> pada hari simpanan ÷ Bilangan produk"}, "total_items": {"message": "<PERSON><PERSON><PERSON> bi<PERSON>an produk"}, "total_price_per_product": {"message": "<PERSON><PERSON><PERSON> ha<PERSON> B<PERSON>ngan produk"}, "total_rating_per_product": {"message": "<PERSON><PERSON><PERSON> pen<PERSON>ian ÷ Bilangan produk"}, "total_revenue": {"message": "<PERSON><PERSON><PERSON>"}, "total_revenue40_items": {"message": "<PERSON><PERSON><PERSON> hasil daripada $amount$ produk pada halaman semasa", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON><PERSON>"}, "total_sales40_items": {"message": "<PERSON><PERSON><PERSON> jualan $amount$ produk pada halaman semasa", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "<PERSON><PERSON><PERSON> se<PERSON>: 1 tahun"}, "track_for_3_months": {"message": "<PERSON><PERSON><PERSON>: 3 bulan"}, "track_for_6_months": {"message": "<PERSON><PERSON><PERSON>: 6 bulan"}, "tracking_price_email_add_btn": {"message": "Tambahkan e-mel"}, "tracking_price_email_edit_btn": {"message": "Edit e-mel"}, "tracking_price_email_intro": {"message": "<PERSON><PERSON> akan member<PERSON><PERSON> anda melalui e-mel."}, "tracking_price_email_invalid": {"message": "Sila berikan e-mel yang sah"}, "tracking_price_email_verified_desc": {"message": "<PERSON>a kini boleh menerima mak<PERSON>an penurunan harga kami."}, "tracking_price_email_verified_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tracking_price_email_verify_desc_line1": {"message": "<PERSON><PERSON> telah men<PERSON>tar pautan pengesahan ke alamat e-mel anda,"}, "tracking_price_email_verify_desc_line2": {"message": "sila semak peti masuk e-mel anda."}, "tracking_price_email_verify_title": {"message": "Mengesahkan E-mel"}, "tracking_price_web_push_notification_intro": {"message": "Di Desktop: <PERSON><PERSON><PERSON> dapat memantau sebarang produk untuk anda dan mengirimkan Pemberitahuan Tolak Web setelah harga berubah."}, "tracking_price_web_push_notification_title": {"message": "Pemberitahuan Tolak Web"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON><PERSON>, sila log masuk ke $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Di<PERSON><PERSON><PERSON> dan disalin ke papan keratan, tetapi kerana keterbat<PERSON>n <PERSON>, anda perlu menampalnya secara manual!"}, "translate_im__send": {"message": "Terjemah & Hantar"}, "translate_search": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan cari"}, "translation_originals_translated": {"message": "<PERSON><PERSON> dan <PERSON>"}, "translation_translated": {"message": "cina"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Pengesanan automatik"}, "translator_language_detected": {"message": "<PERSON><PERSON><PERSON>"}, "translator_language_search_placeholder": {"message": "Bahasa carian"}, "try_again": {"message": "Cuba lagi"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON>:"}, "tu_pian_lian_jie": {"message": "<PERSON><PERSON><PERSON>"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tui_huan_ti_yan__desc": {"message": "<PERSON><PERSON> selepas jualan penjual"}, "tutorial__show_all": {"message": "<PERSON><PERSON><PERSON> ciri"}, "tutorial_ae_popup_title": {"message": "Semat<PERSON> sambung<PERSON>, buka Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Sokong USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Penghantaran ke Korea/Jepun/Tanah Besar China"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Menyokong Pembelian Luar Negara"}, "tutorial_auto_apply_coupon_title": {"message": "Auto memohon kupon"}, "tutorial_btn_end": {"message": "tamat"}, "tutorial_btn_example": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_btn_have_a_try": {"message": "Ok, cuba"}, "tutorial_btn_next": {"message": "Seterusnya"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Bandingkan Produk"}, "tutorial_currency_convert_title": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_export_shopping_cart": {"message": "Eksport sebagai CSV, Sokong Taobao dan 1688"}, "tutorial_export_shopping_cart_title": {"message": "Troli eksport"}, "tutorial_price_history_pro": {"message": "Dipaparkan pada halaman butiran produk.\nSokong Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Sepanjang tahun <PERSON> harga & sejar<PERSON> pesanan"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Tangkap untuk mencari mengikut imej"}, "tutorial_translate_search": {"message": "<PERSON><PERSON><PERSON><PERSON> un<PERSON>k mencari"}, "tutorial_translate_search_and_package_tracking": {"message": "<PERSON><PERSON> te<PERSON>an dan penje<PERSON>an pakej"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "pesanan"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pcs"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "Hubungi <PERSON>"}, "verify_human_verification": {"message": "Pengesahan Manusia"}, "verify_unusual_access": {"message": "<PERSON><PERSON><PERSON> luar biasa di<PERSON>an"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON><PERSON> semua rekod yang dilihat?"}, "view_history_clean_all_warring_title": {"message": "<PERSON><PERSON>"}, "view_history_viewd": {"message": "<PERSON><PERSON><PERSON>"}, "website": {"message": "laman web"}, "weight": {"message": "<PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Tidak dapat mendapatkan data"}, "wu_liu_shi_xiao": {"message": "Penghantaran tepat pada masanya"}, "wu_liu_shi_xiao__desc": {"message": "<PERSON><PERSON> kutipan 48 jam dan kadar pemenuhan kedai penjual"}, "xia_dan_jia": {"message": "<PERSON><PERSON> a<PERSON><PERSON>"}, "xian_xuan_ze_product_attributes": {"message": "Pilih atribut produk"}, "xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "xiao_liang_zhan_bi": {"message": "Peratusan volum jualan"}, "xiao_shi": {"message": "$num$ Jam", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON> hasil"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Pilih $amount$ rekod", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "Disemat"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Bahagian <PERSON><PERSON>"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Bahagian Pusing G<PERSON>"}, "yi_shua_xin": {"message": "Disegar<PERSON> semula"}, "yin_cang_xiang_tong_dian": {"message": "menyembunyikan persa<PERSON>an"}, "you_xiao_liang": {"message": "<PERSON><PERSON>"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON> masa keti<PERSON>an"}, "yuan_gong_ren_shu": {"message": "Bilangan Pekerja"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON><PERSON>"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Jualan dropshipping dalam 30 hari terakhir"}, "yue_dai_xiao_pai_xu__desc": {"message": "Jualan dropshipping dalam 30 hari te<PERSON>hir, diisih dari tinggi ke rendah"}, "yue_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> jualan dalam tempoh 30 hari yang lalu"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Penghantaran percuma"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "Bilangan pesanan untuk produk ini (30 hari)"}, "zhu_ce_xing_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "zi_duan": {"message": "Padang"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_fu_wu_fen": {"message": "Kedudukan k<PERSON>luruhan"}, "zong_he_fu_wu_fen__desc": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>han perkhidmatan penjual"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen_3": {"message": "Bawah 4 Bintang"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 Bintang"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 Bintang"}, "zong_he_ti_yan_fen_5": {"message": "5 Bintang"}, "zong_ku_cun": {"message": "<PERSON><PERSON><PERSON>"}, "zong_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "<PERSON><PERSON> respons 3 minit dalam 30 hari yang lalu"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "<PERSON><PERSON> 48J dalam 30 hari yang lalu"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "<PERSON><PERSON> prestasi 48H dalam 30 hari yang lalu"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "<PERSON><PERSON><PERSON>(30 hari)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "<PERSON><PERSON><PERSON>(30 hari)"}, "zui_jin_30D_jiu_fen_lv": {"message": "<PERSON><PERSON> dalam 30 hari yang lalu"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON> bayaran balik berkualiti dalam 30 hari yang lalu"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Bilangan pesanan pembayaran dalam 30 hari terakhir"}}