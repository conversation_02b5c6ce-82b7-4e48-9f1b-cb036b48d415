{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Ponto de venda transfronteiriço mais vendido"}, "1688_shi_li_ren_zheng": {"message": "1688 certificação de força"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Mais de 1 ano"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Entrega em 24 horas"}, "24H_lan_shou_lv": {"message": "Taxa de embalagem 24 horas"}, "30D_shang_xin": {"message": "Novidades Mensais"}, "30d_sales": {"message": "$amount$ vendido em 30 dias", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Resposta em 3 minutos."}, "3Min_xiang_ying_lv__desc": {"message": "A proporção de respostas efetivas de Wangwang às mensagens de consulta do comprador em até 3 minutos nos últimos 30 dias"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Entrega em 48 horas"}, "48H_lan_shou_lv": {"message": "Taxa de embalagem de 48 horas"}, "48H_lan_shou_lv__desc": {"message": "Proporção entre o número do pedido retirado em 48 horas e o número total de pedidos"}, "48H_lv_yue_lv": {"message": "Taxa de desempenho de 48 horas"}, "48H_lv_yue_lv__desc": {"message": "Proporção entre o número do pedido coletado ou entregue em 48 horas e o número total de pedidos"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Novidades Semanais"}, "7D_wu_li_you": {"message": "7 dias sem cuidados"}, "ABS_title_text": {"message": "Este anúncio inclui uma história de marca"}, "AC_title_text": {"message": "Este anúncio tem o selo Amazon's Choice"}, "A_title_text": {"message": "Este anúncio tem uma página de conteúdo A+"}, "BS_title_text": {"message": "Este anúncio é classificado como $num$ Best Seller na categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) significa que este anúncio faz parte de um evento de \"promoção de 7 dias\""}, "NR_title_text": {"message": "Este anúncio é classificado como $num$ New Release na categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Este anúncio tem um anúncio em vídeo, um tipo de anúncio PPC que geralmente aparece no meio dos resultados de pesquisa"}, "SB_title_text": {"message": "Este anúncio tem um anúncio de marca, um tipo de anúncio PPC que geralmente aparece na parte superior ou inferior dos resultados de pesquisa"}, "SP_title_text": {"message": "Este anúncio tem um anúncio de produto patrocinado"}, "V_title_text": {"message": "Este anúncio tem uma introdução em vídeo"}, "advanced_research": {"message": "Pesquisa Avançada"}, "agent_ds1688___my_order": {"message": "minhas ordens"}, "agent_ds1688__add_to_cart": {"message": "Compra no exterior"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON>pra<PERSON>"}, "agent_ds1688__desc": {"message": "Fornecido pela 1688. Suporta compra direta no exterior, pagamento em dólares americanos e entrega em seu armazém de trânsito na China."}, "agent_ds1688__freight": {"message": "Calculadora de custos de envio"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Centro Pessoal"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Prós"}, "ai_comment_analysis_ai": {"message": "Análise de revisão de IA"}, "ai_comment_analysis_available": {"message": "Disponível"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON> insuficient<PERSON>, recarre<PERSON>"}, "ai_comment_analysis_behavior": {"message": "Comportamento"}, "ai_comment_analysis_characteristic": {"message": "Características da multidão"}, "ai_comment_analysis_comment": {"message": "O produto não tem avaliações suficientes para tirar conclusões precisas, selecione um produto com mais avaliações."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON> estimado"}, "ai_comment_analysis_default": {"message": "Avaliações padrão"}, "ai_comment_analysis_desire": {"message": "Expectativas do cliente"}, "ai_comment_analysis_disadvantage": {"message": "Contras"}, "ai_comment_analysis_free": {"message": "Tentativas gratuitas"}, "ai_comment_analysis_freeNum": {"message": "1 crédito gr<PERSON><PERSON> será usado"}, "ai_comment_analysis_go_recharge": {"message": "Ir para recarga"}, "ai_comment_analysis_intelligence": {"message": "Análise de revisão inteligente"}, "ai_comment_analysis_location": {"message": "Localização"}, "ai_comment_analysis_motive": {"message": "Motivação de compra"}, "ai_comment_analysis_network_error": {"message": "Erro de rede, tente novamente"}, "ai_comment_analysis_normal": {"message": "Avaliações de fotos"}, "ai_comment_analysis_number_reviews": {"message": "Número de avaliações: $num$, Consumo estimado: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Comentários gerais"}, "ai_comment_analysis_percentage": {"message": "Porcentagem"}, "ai_comment_analysis_problem": {"message": "Problemas com o pagamento"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "Motivo"}, "ai_comment_analysis_recharge": {"message": "Recarga"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON> recar<PERSON>i"}, "ai_comment_analysis_retry": {"message": "Tentar novamente"}, "ai_comment_analysis_scene": {"message": "Ce<PERSON>rio de uso"}, "ai_comment_analysis_start": {"message": "Começar a analisar"}, "ai_comment_analysis_subject": {"message": "Tópicos"}, "ai_comment_analysis_time": {"message": "Tempo de uso"}, "ai_comment_analysis_tool": {"message": "Ferramenta de IA"}, "ai_comment_analysis_user_portrait": {"message": "Perfil do usuário"}, "ai_comment_analysis_welcome": {"message": "Bem-vindo à análise de revisão de IA"}, "ai_comment_analysis_year": {"message": "Comentários do ano passado"}, "ai_listing_Exclude_keywords": {"message": "Excluir palavras-chave"}, "ai_listing_Login_the_feature": {"message": "O login é necessário para o recurso"}, "ai_listing_aI_generation": {"message": "Geração de IA"}, "ai_listing_add_automatic": {"message": "Automático"}, "ai_listing_add_dictionary_new": {"message": "Crie uma nova biblioteca"}, "ai_listing_add_enter_keywords": {"message": "Digite palav<PERSON>-chave"}, "ai_listing_add_inputkey_selling": {"message": "Insira um ponto de venda e pressione $key$ para finalizar a adição", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limite excedido, até $amount$ pontos de venda", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Adicionar p<PERSON>-chave"}, "ai_listing_add_manually": {"message": "Adicionar manualmente"}, "ai_listing_add_selling": {"message": "Adicione pontos de venda"}, "ai_listing_added_keywords": {"message": "Palavras-chave adicionadas"}, "ai_listing_added_successfully": {"message": "Adicionado com sucesso"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON><PERSON> as palavras-chave excluídas e pressione Enter para finalizar a adição."}, "ai_listing_adding_selling": {"message": "Pontos de venda adicionados"}, "ai_listing_addkeyword_enter": {"message": "<PERSON><PERSON>e as palavras do atributo-chave e pressione Enter para terminar de adicionar"}, "ai_listing_ai_description": {"message": "Biblioteca de palavras de descrição de IA"}, "ai_listing_ai_dictionary": {"message": "Biblioteca de palavras de título AI"}, "ai_listing_ai_title": {"message": "Título de IA"}, "ai_listing_aidescription_repeated": {"message": "O nome da biblioteca de palavras de descrição da IA ​​não pode ser repetido"}, "ai_listing_aititle_repeated": {"message": "O nome da biblioteca de palavras do título AI não pode ser repetido"}, "ai_listing_data_comes_from": {"message": "Esses dados vêm de:"}, "ai_listing_deleted_successfully": {"message": "Apagado com sucesso"}, "ai_listing_dictionary_name": {"message": "Nome da biblioteca"}, "ai_listing_edit_dictionary": {"message": "Modificar biblioteca..."}, "ai_listing_edit_word_library": {"message": "Edite a biblioteca de palavras"}, "ai_listing_enter_keywords": {"message": "Insira palavras-chave e pressione $key$ para terminar de adicionar", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "O limite foi excedido, máximo de $amount$ palavras-chave", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Biblioteca de palavras excluídas"}, "ai_listing_generate_characters": {"message": "Gerar personagens"}, "ai_listing_generation_platform": {"message": "Plataforma de geração"}, "ai_listing_help_optimize": {"message": "Ajude-me a otimizar o título do produto, o título original é"}, "ai_listing_include_selling": {"message": "Outros pontos de venda incluídos:"}, "ai_listing_included_keyword": {"message": "Palavras-chave incluídas"}, "ai_listing_included_keywords": {"message": "Palavras-chave incluídas"}, "ai_listing_input_selling": {"message": "Insira um ponto de venda"}, "ai_listing_input_selling_fit": {"message": "Insira pontos de venda que correspondam ao título"}, "ai_listing_input_selling_please": {"message": "Por favor insira os pontos de venda"}, "ai_listing_intelligently_title": {"message": "Insira o conteúdo necessário acima para gerar o título de forma inteligente"}, "ai_listing_keyword_product_title": {"message": "Título do produto com palavra-chave"}, "ai_listing_keywords_repeated": {"message": "Palavras-chave não podem ser repetidas"}, "ai_listing_listed_selling_points": {"message": "Pontos de venda incluídos"}, "ai_listing_long_title_1": {"message": "Contém informações básicas, como nome da marca, tipo de produto, características do produto, etc."}, "ai_listing_long_title_2": {"message": "Com base no título padrão do produto, são adicionadas palavras-chave que conduzem ao SEO."}, "ai_listing_long_title_3": {"message": "Além de conter o nome da marca, tipo de produto, características do produto e palavras-chave, palavras-chave de cauda longa também são incluídas para obter classificações mais altas em consultas de pesquisa segmentadas e específicas."}, "ai_listing_longtail_keyword_product_title": {"message": "Título do produto com palavra-chave de cauda longa"}, "ai_listing_manually_enter": {"message": "Insira manualmente..."}, "ai_listing_network_not_working": {"message": "A Internet não está disponível, é necessária VPN para acessar o ChatGPT"}, "ai_listing_new_dictionary": {"message": "Crie uma nova biblioteca de palavras..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Palavras opcionais"}, "ai_listing_original_title": {"message": "<PERSON><PERSON><PERSON><PERSON> original"}, "ai_listing_other_keywords_included": {"message": "Outras palavras-chave incluídas:"}, "ai_listing_please_again": {"message": "Por favor, tente novamente"}, "ai_listing_please_select": {"message": "Os se<PERSON>tes títulos foram gerados para você, selecione:"}, "ai_listing_product_category": {"message": "Categoria de Produto"}, "ai_listing_product_category_is": {"message": "A categoria do produto é"}, "ai_listing_product_category_to": {"message": "A que categoria o produto pertence?"}, "ai_listing_random_keywords": {"message": "Palavras-chave $amount$ aleatórias", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Pontos de venda aleatórios de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomize a partir da biblioteca de palavras"}, "ai_listing_search_selling": {"message": "Pesquise por ponto de venda"}, "ai_listing_select_product_categories": {"message": "Selecione automaticamente categorias de produtos."}, "ai_listing_select_product_selling_points": {"message": "Selecione automaticamente pontos de venda de produtos"}, "ai_listing_select_word_library": {"message": "Selecione a biblioteca de palavras"}, "ai_listing_selling": {"message": "Pontos de venda"}, "ai_listing_selling_ask": {"message": "Que outros requisitos de argumento de venda existem para o título?"}, "ai_listing_selling_optional": {"message": "Pontos de venda opcionais"}, "ai_listing_selling_repeat": {"message": "Os pontos não podem ser duplicados"}, "ai_listing_set_excluded": {"message": "Definir como biblioteca de palavras excluídas"}, "ai_listing_set_include_selling_points": {"message": "Incluir pontos de venda"}, "ai_listing_set_included": {"message": "Definir como biblioteca de palavras incluída"}, "ai_listing_set_selling_dictionary": {"message": "Definir como biblioteca de pontos de venda"}, "ai_listing_standard_product_title": {"message": "Título do produto padrão"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Visite ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Que outras palavras-chave são necessárias para o título?"}, "aliprice_coupons_apply_again": {"message": "Aplicar novamente"}, "aliprice_coupons_apply_coupons": {"message": "Aplicar cupons"}, "aliprice_coupons_apply_success": {"message": "Cupom encontrado: economize $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "<PERSON>ando c<PERSON>s para as melhores ofertas..."}, "aliprice_coupons_applying_desc": {"message": "Check-out: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continuar para Check-out"}, "aliprice_coupons_found_coupons": {"message": "Encontramos cupons de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Pronto para finalizar a compra? Vamos garantir que você obtenha o melhor preço!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Esses códigos não funcionaram. Nada demais - você já está obtendo o melhor preço."}, "aliprice_coupons_toolbar_btn": {"message": "Obter cupons"}, "aliww_translate": {"message": "Tradutor de bate-pap<PERSON>"}, "aliww_translate_supports": {"message": "Suporte: 1688 e Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Palavras-chave"}, "amazon_extended_keywords_copy_all": {"message": "Copiar tudo"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Classificar por vendas acumuladas"}, "an_lei_xing_cha_kan": {"message": "Visualizar por tipo"}, "an_yue_dai_xiao_pai_xu": {"message": "Classificação por vendas de dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "apra_chart__name": {"message": "Porcentagem de vendas de produtos por país"}, "apra_chart__update_at": {"message": "Atualizar hora $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Estatísticas de vendas de países"}, "auto_opening": {"message": "Abrindo automaticamente em $num$ segundos", "placeholders": {"num": {"content": "$1"}}}, "average_days_listed": {"message": "Média em dias de prateleira"}, "average_hui_fu_lv": {"message": "Taxa média de resposta"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Nível médio do fornecedor"}, "average_price": {"message": "Preço médio"}, "average_qi_ding_liang": {"message": "Quantidade mínima média"}, "average_rating": {"message": "Classificação média"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Média de anos de certificação"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "average_revenue_per_product": {"message": "Receita total ÷ Número de produtos"}, "average_sales": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "average_sales_per_product": {"message": "Vendas totais ÷ Número de produtos"}, "bao_han": {"message": "<PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "Margem"}, "bian_ti_shu": {"message": "Variações"}, "biao_ti": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "blacklist_add_blacklist": {"message": "Bloquear esta loja"}, "blacklist_address_incorrect": {"message": "O endereço está incorreto. Por favor verifique isto."}, "blacklist_blacked_out": {"message": "A loja foi bloqueada"}, "blacklist_blacklist": {"message": "Lista negra"}, "blacklist_no_records_yet": {"message": "Ainda não há registro!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Comprador intermediário"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Tente agora"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "Tente agora"}, "btn_txt_view_on_aliprice": {"message": "Ver no AliPrice"}, "bu_bao_han": {"message": "Não contém"}, "bulk_copy_links": {"message": "Links de cópia em massa"}, "bulk_copy_products": {"message": "Produtos de cópia em massa"}, "cai_gou_zi_xun": {"message": "Atendimento ao Cliente"}, "cai_gou_zi_xun__desc": {"message": "Taxa de resposta de três minutos do vendedor"}, "can_ping_lei_xing": {"message": "Tipo"}, "cao_zuo": {"message": "Operação"}, "chan_pin_ID": {"message": "ID do produto"}, "chan_pin_e_wai_xin_xi": {"message": "Informações extras do produto"}, "chan_pin_lian_jie": {"message": "Link do produto"}, "cheng_li_shi_jian": {"message": "Hora de estabelecimento"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Clique em Copiar informações do produto"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 ano"}, "common__date_range_1m": {"message": "1 mês"}, "common__date_range_1w": {"message": "1 semana"}, "common__date_range_2w": {"message": "2 semanas"}, "common__date_range_3m": {"message": "3 meses"}, "common__date_range_3w": {"message": "3 semanas"}, "common__date_range_6m": {"message": "6 meses"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON>"}, "common_btn_setting": {"message": "Configuração"}, "common_email": {"message": "O email"}, "common_error_msg_no_data": {"message": "Sem dados"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON><PERSON>, nenhum resultado encontrado."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "Comentários"}, "common_help": {"message": "Socorro"}, "common_loading": {"message": "Carregando"}, "common_login": {"message": "Entrar"}, "common_logout": {"message": "<PERSON><PERSON>"}, "common_no": {"message": "Não"}, "common_powered_by_aliprice": {"message": "Desenvolvido por AliPrice.com"}, "common_setting": {"message": "Configuração"}, "common_sign_up": {"message": "Inscrever-se"}, "common_system_upgrading_title": {"message": "Atualização do sistema"}, "common_system_upgrading_txt": {"message": "Por favor tente mais tarde"}, "common_txt__currency": {"message": "<PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Ví<PERSON><PERSON> tutorial"}, "common_txt_ago_time": {"message": "$time$ dias atrás", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "todos"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Quase nunca usado"}, "common_txt_biaoti_link": {"message": "Título+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Título+Link+<PERSON><PERSON>"}, "common_txt_blacklist": {"message": "Blocklist"}, "common_txt_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_category": {"message": "Categoria"}, "common_txt_chakan": {"message": "Verificar"}, "common_txt_colors": {"message": "cores"}, "common_txt_confirm": {"message": "confirme"}, "common_txt_copied": {"message": "Copiado"}, "common_txt_copy": {"message": "cópia de"}, "common_txt_copy_link": {"message": "Link de cópia"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy_title__link": {"message": "Copie o título e o link"}, "common_txt_day": {"message": "céu"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Excluir"}, "common_txt_dian_pu_link": {"message": "Copiar nome da loja + link"}, "common_txt_download": {"message": "download"}, "common_txt_downloaded": {"message": "Download"}, "common_txt_export_as_csv": {"message": "Exportar Excel"}, "common_txt_export_as_txt": {"message": "Exportar Txt"}, "common_txt_fail": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_format": {"message": "Formato"}, "common_txt_get": {"message": "pegar"}, "common_txt_incert_selection": {"message": "Seleção invertida"}, "common_txt_install": {"message": "Instalar"}, "common_txt_load_failed": {"message": "Falha ao carregar"}, "common_txt_month": {"message": "mês"}, "common_txt_more": {"message": "<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Novo, sem uso"}, "common_txt_next": {"message": "Próximo"}, "common_txt_no_limit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_noticeable": {"message": "Se<PERSON> a<PERSON>nh<PERSON> ou sujeira visíveis"}, "common_txt_on_sale": {"message": "Disponível"}, "common_txt_opt_in_out": {"message": "Ligado desligado"}, "common_txt_order": {"message": "Pedido"}, "common_txt_others": {"message": "Outras"}, "common_txt_overall_poor_condition": {"message": "Condição geral ruim"}, "common_txt_patterns": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_platform": {"message": "Plataformas"}, "common_txt_please_select": {"message": "Por favor, selecione"}, "common_txt_prev": {"message": "Anterior"}, "common_txt_price": {"message": "Preço"}, "common_txt_privacy_policy": {"message": "Política de Privacidade"}, "common_txt_product_condition": {"message": "Status do produto"}, "common_txt_rating": {"message": "Avaliação"}, "common_txt_ratings": {"message": "Classificações"}, "common_txt_reload": {"message": "recarregar"}, "common_txt_reset": {"message": "Redefinir"}, "common_txt_retail": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_review": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sale": {"message": "Disponível"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Com arranhões e sujeira"}, "common_txt_search_title": {"message": "<PERSON><PERSON><PERSON><PERSON> de p<PERSON>quisa"}, "common_txt_select_all": {"message": "Selecionar tudo"}, "common_txt_selected": {"message": "selecionado"}, "common_txt_share": {"message": "Comp<PERSON><PERSON><PERSON>"}, "common_txt_sold": {"message": "vendido"}, "common_txt_sold_out": {"message": "vendido"}, "common_txt_some_scratches": {"message": "Alguns arranhões e sujeira"}, "common_txt_sort_by": {"message": "Ordenar por"}, "common_txt_state": {"message": "Estado"}, "common_txt_success": {"message": "Sucesso"}, "common_txt_sys_err": {"message": "erro no sistema"}, "common_txt_today": {"message": "Hoje"}, "common_txt_total": {"message": "todo"}, "common_txt_unselect_all": {"message": "Seleção invertida"}, "common_txt_upload_image": {"message": "Enviar Imagem"}, "common_txt_visit": {"message": "Visita"}, "common_txt_whitelist": {"message": "Whitelist"}, "common_txt_wholesale": {"message": "Atacado"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "sim"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON> tudo"}, "compare_tool_btn_compare": {"message": "Comparar"}, "compare_tool_btn_contact": {"message": "Contato"}, "compare_tool_tips_max_compared": {"message": "Добавить до $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configurar notificações"}, "contact_us": {"message": "Contate-Nos"}, "context_menu_screenshot_search": {"message": "Pesquisa de captura de tela para o mesmo estilo"}, "context_menus_aliprice_search_by_image": {"message": "Procurar imagem no AliPrice"}, "context_menus_goote_trans": {"message": "Traduzir <PERSON>/Mostrar original"}, "context_menus_search_by_image": {"message": "Pesquisa por imagem no $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Captura para $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Capture para traduzir"}, "converter_modal_amount_placeholder": {"message": "Insira o valor aqui"}, "converter_modal_btn_convert": {"message": "converter"}, "converter_modal_exchange_rate_source": {"message": "Os dados vêm da taxa de câmbio $boc$ Tempo de atualização: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "conversão de moeda"}, "converter_modal_search_placeholder": {"message": "pesquisar moeda"}, "copy_all_contact_us_notice": {"message": "Este site não é suportado neste momento, entre em contato conosco"}, "copy_product_info": {"message": "Copiar informações do produto"}, "copy_suggest_search_kw": {"message": "Copiar listas suspensas"}, "country__han_gou": {"message": "Coréia do Sul"}, "country__ri_ben": {"message": "Japão"}, "country__yue_nan": {"message": "Vietnã"}, "currency_convert__custom": {"message": "Taxa de câmbio personalizada"}, "currency_convert__sync_server": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> servidor"}, "dang_ri_fa_huo": {"message": "Envio no mesmo dia"}, "dao_chu_quan_dian_shang_pin": {"message": "Exportar todos os produtos da loja"}, "dao_chu_wei_CSV": {"message": "Exportar"}, "dao_chu_zi_duan": {"message": "Exportar campos"}, "delivery_address": {"message": "Endereço para envio"}, "delivery_company": {"message": "Empresa de entrega"}, "di_zhi": {"message": "endereço"}, "dian_ji_cha_xun": {"message": "Clique para consultar"}, "dian_pu_ID": {"message": "ID da loja"}, "dian_pu_di_zhi": {"message": "Endereço da loja"}, "dian_pu_lian_jie": {"message": "<PERSON> loja"}, "dian_pu_ming": {"message": "Nome da loja"}, "dian_pu_ming_cheng": {"message": "Nome da loja"}, "dian_pu_shang_pin_zong_hsu": {"message": "Número total de produtos na loja: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Guardar informação"}, "ding_zai_zuo_ce": {"message": "pregado à esquerda"}, "download_image__SKU_variant_images": {"message": "Imagens de variantes de SKU"}, "download_image__assume": {"message": "Por exemplo, temos 2 imagens, product1.jpg e product2.gif.\nimg_{$no$} será renomeado para img_01.jpg, img_02.gif;\n{$group$}_{$no$} será renomeado para main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Download em lote"}, "download_image__combined_image": {"message": "Imagem combinada de detalhes do produto"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON><PERSON><PERSON> baixando"}, "download_image__description_images": {"message": "Imagens de descrição"}, "download_image__download_combined_image": {"message": "Baixe a imagem combinada de detalhes do produto"}, "download_image__download_zip": {"message": "Baixar zip"}, "download_image__enlarge_check": {"message": "Suporta apenas imagens JPEG, JPG, GIF e PNG, tamanho máximo de uma única imagem: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Aumentar imagem"}, "download_image__export": {"message": "Exportar links"}, "download_image__height": {"message": "Altura"}, "download_image__ignore_videos": {"message": "O vídeo foi ignorado, pois não pode ser exportado"}, "download_image__img_translate": {"message": "Traduto<PERSON> de <PERSON>m"}, "download_image__main_image": {"message": "imagem principal"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON><PERSON> pastas"}, "download_image__name": {"message": "baixar imagem"}, "download_image__notice_content": {"message": "Por favor, não marque \"Perguntar onde salvar cada arquivo antes de baixar\" nas configurações de download do seu navegador!!! <PERSON><PERSON><PERSON> contr<PERSON>, haverá muitas caixas de diálogo."}, "download_image__notice_ignore": {"message": "Não solicitar esta mensagem novamente"}, "download_image__order_number": {"message": "{$no$} número de série; {$group$} nome do grupo; {$date$} carimbo de data/hora", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Visão geral"}, "download_image__prompt_download_zip": {"message": "<PERSON><PERSON> muitas imagens, é melhor baixá-las como uma pasta zip."}, "download_image__rename": {"message": "Renomear"}, "download_image__rule": {"message": "Regras de nomenclatura"}, "download_image__single_folder": {"message": "Pasta única"}, "download_image__sku_image": {"message": "Imagens de SKU"}, "download_image__video": {"message": "vídeo"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Baixe a imagem da avaliação"}, "download_reviews__dropdown_title": {"message": "Baixe a imagem da avaliação"}, "download_reviews__export_csv": {"message": "exportar CSV"}, "download_reviews__no_images": {"message": "0 imagens disponíveis para download"}, "download_reviews__no_reviews": {"message": "Nenhuma revisão para baixar!"}, "download_reviews__notice": {"message": "Dica:"}, "download_reviews__notice__chrome_settings": {"message": "Defina o navegador Chrome para perguntar onde salvar cada arquivo antes de fazer o download, defina como \"Desativado\""}, "download_reviews__notice__wait": {"message": "Dependendo do número de revisões, o tempo de espera pode ser maior"}, "download_reviews__pages_list__all": {"message": "Todos"}, "download_reviews__pages_list__page": {"message": "Páginas anteriores de $page$", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Faixa de seleção"}, "export_shopping_cart__csv_filed__details_url": {"message": "link do produto"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Link SKU de eco"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON> da <PERSON>m"}, "export_shopping_cart__csv_filed__quantity": {"message": "Quantidade"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Preço"}, "export_shopping_cart__csv_filed__specs": {"message": "Especificações"}, "export_shopping_cart__csv_filed__store_name": {"message": "Nome da loja"}, "export_shopping_cart__csv_filed__store_url": {"message": "link da loja"}, "export_shopping_cart__csv_filed__title": {"message": "Nome do Produto"}, "export_shopping_cart__export_btn": {"message": "Exportar"}, "export_shopping_cart__export_empty": {"message": "Selecione um produto!"}, "fa_huo_shi_jian": {"message": "<PERSON><PERSON>"}, "favorite_add_email": {"message": "Adicionar e-mail"}, "favorite_add_favorites": {"message": "Adicionar aos favoritos"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Alerta de queda do preço."}, "favorite_btn_notify": {"message": "Preço da trilha"}, "favorite_cate_name_all": {"message": "Todos os produtos"}, "favorite_current_price": {"message": "Preço atual"}, "favorite_due_date": {"message": "Data de vencimento"}, "favorite_enable_notification": {"message": "Ative a notificação por e-mail."}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "Ative a notificação por e-mail."}, "favorite_msg_add_success": {"message": "Adicionar aos favoritos"}, "favorite_msg_del_success": {"message": "Excluir de favoritos"}, "favorite_msg_failure": {"message": "Falhou! Atualize Página e tente novamente."}, "favorite_please_add_email": {"message": "Por favor, adicione e-mail"}, "favorite_price_drop": {"message": "Para baixo"}, "favorite_price_rise": {"message": "Para cima"}, "favorite_price_untracked": {"message": "Preço não rastreado"}, "favorite_saved_price": {"message": "Preço salvo"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON>"}, "favorite_sub_email_address": {"message": "Endereço de e-mail de assinatura"}, "favorite_tracking_period": {"message": "Per<PERSON><PERSON>"}, "favorite_tracking_prices": {"message": "Preços de rastreamento"}, "favorite_verify_email": {"message": "Verifique o endereço de e-mail"}, "favorites_list_remove_prompt_msg": {"message": "Você tem certeza que quer apagar isso?"}, "favorites_update_button": {"message": "Atualizar preços agora"}, "fen_lei": {"message": "Categoria"}, "fen_xia_yan_xuan": {"message": "Escolha do Distribuidor"}, "find_similar": {"message": "Encontrar <PERSON><PERSON><PERSON><PERSON>"}, "first_ali_price_date": {"message": "A data em que foi capturada pela primeira vez pelo rastreador AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Sem cupons"}, "fooview_coupons_modal_title": {"message": "Cupons"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Preço <$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "ou preço> $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Prazo final"}, "fooview_favorites_error_msg_no_favorites": {"message": "Adicione produtos favoritos aqui para receber alerta de queda de preço."}, "fooview_favorites_filter_latest": {"message": "Últimas"}, "fooview_favorites_filter_price_drop": {"message": "BAIXA"}, "fooview_favorites_filter_price_up": {"message": "ACIMA"}, "fooview_favorites_modal_title": {"message": "Meus favoritos"}, "fooview_favorites_modal_title_title": {"message": "<PERSON>r <PERSON> AliPrice <PERSON>"}, "fooview_favorites_track_price": {"message": "Para acompanhar o preço"}, "fooview_price_history_app_price": {"message": "Preço da APP :"}, "fooview_price_history_title": {"message": "Rastreamento de preço"}, "fooview_product_list_feedback": {"message": "Ponto"}, "fooview_product_list_orders": {"message": "Ordem"}, "fooview_product_list_price": {"message": "preço"}, "fooview_reviews_error_msg_no_review": {"message": "Nós não encontrar qualquer comentário sobre este produto."}, "fooview_reviews_filter_buyer_reviews": {"message": "Fotos dos compradores"}, "fooview_reviews_modal_title": {"message": "<PERSON>er"}, "fooview_same_product_choose_category": {"message": "Escolher categoria"}, "fooview_same_product_filter_feedback": {"message": "Ponto"}, "fooview_same_product_filter_orders": {"message": "Ordem"}, "fooview_same_product_filter_price": {"message": "preço"}, "fooview_same_product_filter_rating": {"message": "Avaliação"}, "fooview_same_product_modal_title": {"message": "Encontrar o mesmo produto"}, "fooview_same_product_search_by_image": {"message": "Pesquisa por esta imagem"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>"}, "for_12_months": {"message": "Por 1 ano"}, "for_12_months_list_pro": {"message": "12 meses"}, "for_12_months_nei": {"message": "Dentro de 12 meses"}, "for_1_months": {"message": "1 mês"}, "for_1_months_nei": {"message": "<PERSON><PERSON> de 1 mês"}, "for_3_months": {"message": "Por 3 meses"}, "for_3_months_nei": {"message": "Dentro de 3 meses"}, "for_6_months": {"message": "Por 6 meses"}, "for_6_months_nei": {"message": "Dentro de 6 meses"}, "for_9_months": {"message": "9 meses"}, "for_9_months_nei": {"message": "Dentro de 9 meses"}, "fu_gou_lv": {"message": "Taxa de recompra"}, "gao_liang_bu_tong_dian": {"message": "destacar as diferen<PERSON><PERSON>"}, "gao_liang_guang_gao_chan_pin": {"message": "Destaque Produtos de Anúncios"}, "geng_duo_xin_xi": {"message": "Mais informaç<PERSON>"}, "geng_xin_shi_jian": {"message": "Hora da atualização"}, "get_store_products_fail_tip": {"message": "Clique em OK para ir para a verificação e garantir acesso normal"}, "gong_x_kuan_shang_pin": {"message": "Um total de $amount$ produtos", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Fornecedor"}, "gong_ying_shang_ID": {"message": "Identificação do Fornecedor"}, "gong_ying_shang_deng_ji": {"message": "Classificação do fornecedor"}, "gong_ying_shang_nian_zhan": {"message": "O fornecedor é mais antigo"}, "gong_ying_shang_xin_xi": {"message": "informação do fornecedor"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link da página inicial do fornecedor"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Preço de venda estimado"}, "guan_jian_zi": {"message": "Palavra-chave"}, "guang_gao_chan_pin": {"message": "Produ<PERSON>"}, "guang_gao_zhan_bi": {"message": "Proporção de anúncios"}, "guo_ji_wu_liu_yun_fei": {"message": "Taxa de envio internacional"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "Avaliação positiva"}, "highest_price": {"message": "Alto"}, "historical_trend": {"message": "Tendência histórica"}, "how_to_screenshot": {"message": "Mantenha pressionado o botão esquerdo do mouse para selecionar a área, toque no botão direito do mouse ou na tecla Esc para sair da captura de tela"}, "howt_it_works": {"message": "Como funciona"}, "hui_fu_lv": {"message": "Taxa de resposta"}, "hui_tou_lv": {"message": "taxa de retorno"}, "inquire_freightFee": {"message": "Consulta de frete"}, "inquire_freightFee_Yuan": {"message": "Frete/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON>v<PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "O frete é $num$, o que significa que a região tem frete grátis.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON>."}, "jia_ge": {"message": "Preço"}, "jia_ge_dan_wei": {"message": "Unidade"}, "jia_ge_qu_shi": {"message": "Tendência"}, "jia_zai_n_ge_shang_pin": {"message": "Carregar $num$ produtos", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Porcentagem do volume de vendas nos últimos 30 dias"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Porcentagem da receita nos últimos 30 dias"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Vendas totais nos últimos 30 dias"}, "jin_30d_xiao_shou_e": {"message": "Volume de negócios"}, "jin_30d_xiao_shou_e__desc": {"message": "Faturamento total nos últimos 30 dias"}, "jin_90_tian_mai_jia_shu": {"message": "Compradores nos últimos 90 dias"}, "jin_90_tian_xiao_shou_liang": {"message": "Vendas nos últimos 90 dias"}, "jing_xuan_huo_yuan": {"message": "Fontes selecionadas"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON>"}, "jing_ying_mo_shi__gong_chang": {"message": "Fabricante"}, "jiu_fen_jie_jue": {"message": "Resolução de disputas"}, "jiu_fen_jie_jue__desc": {"message": "Contabilização de disputas de direitos de loja dos vendedores"}, "jiu_fen_lv": {"message": "Taxa de disputa"}, "jiu_fen_lv__desc": {"message": "Proporção de pedidos com reclamações concluídas nos últimos 30 dias e consideradas de responsabilidade do vendedor ou de ambas as partes"}, "kai_dian_ri_qi": {"message": "Data de abertura"}, "keywords": {"message": "Palavras-chave"}, "kua_jin_Select_pan_huo": {"message": "Seleção Transfronteiriça"}, "last15_days": {"message": "Últimos 15 dias"}, "last180_days": {"message": "Últimos 180 dias"}, "last30_days": {"message": "Nos últimos 30 dias"}, "last360_days": {"message": "Últimos 360 dias"}, "last45_days": {"message": "Últimos 45 dias"}, "last60_days": {"message": "Últimos 60 dias"}, "last7_days": {"message": "Últimos 7 dias"}, "last90_days": {"message": "Últimos 90 dias"}, "last_30d_sales": {"message": "Vendas dos últimos 30 dias"}, "lei_ji": {"message": "Cumulativo"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "<PERSON><PERSON> as vendas após o produto na prateleira"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volume de vendas acumulado nos últimos 30 dias, classificado do maior para o menor"}, "lian_xi_fang_shi": {"message": "Informações de contato"}, "list_time": {"message": "Data de validade"}, "load_more": {"message": "<PERSON><PERSON><PERSON> mais"}, "login_to_aliprice": {"message": "Faça login no AliPrice"}, "long_link": {"message": "<PERSON>"}, "lowest_price": {"message": "Baixo"}, "mai_jia_shu": {"message": "Vendedores"}, "mao_li_lv": {"message": "Margem"}, "mobile_view__dkxbqy": {"message": "<PERSON><PERSON>r uma nova guia"}, "mobile_view__sjdxq": {"message": "Detalhes no aplicativo"}, "mobile_view__sjdxqy": {"message": "Página de detalhes no aplicativo"}, "mobile_view__smck": {"message": "Digitalizar para visualizar"}, "mobile_view__smckms": {"message": "Use a câmera ou o aplicativo para digitalizar e visualizar"}, "modified_failed": {"message": "Falha na modificação"}, "modified_successfully": {"message": "Modificado com sucesso"}, "nav_btn_favorites": {"message": "Minhas coleções"}, "nav_btn_package": {"message": "pacote"}, "nav_btn_product_info": {"message": "Sobre o produto"}, "nav_btn_viewed": {"message": "Visto"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON><PERSON>"}, "normal_link": {"message": "Link normal"}, "notice": {"message": "dica"}, "number_reviews": {"message": "Avaliações"}, "only_show_num": {"message": "Total de produtos: $allnum$, Oculto: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Remover Desmarcado"}, "open": {"message": "Abrir"}, "open_links": {"message": "Abrir links"}, "options_page_tab_check_links": {"message": "Verifique os links"}, "options_page_tab_gernal": {"message": "G<PERSON>"}, "options_page_tab_notifications": {"message": "Notificações"}, "options_page_tab_others": {"message": "Outras"}, "options_page_tab_sbi": {"message": "Pesquisa por esta imagem"}, "options_page_tab_shortcuts": {"message": "Atalhos"}, "options_page_tab_shortcuts_title": {"message": "<PERSON><PERSON><PERSON> da fonte para atalhos"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON>"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Vários links de produtos estão prestes a abrir"}, "order_list_sku_show_title": {"message": "Mostrar variantes selecionadas nos links compartilhados"}, "orders_last30_days": {"message": "Número de pedidos nos últimos 30 dias"}, "pTutorial_favorites_block1_desc1": {"message": "Os produtos que você acompanhou estão listados aqui"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Um rótulo verde significa que há produtos com queda de preço"}, "pTutorial_popup_block1_title": {"message": "Atalhos e Favoritos"}, "pTutorial_price_history_block1_desc1": {"message": "Clique em \"Acompanhar preço\", adicione produtos aos Favoritos. Quando os preços caírem, você receberá notificações"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_reviews_block1_desc1": {"message": "Comentários de compradores de Itao e fotos reais do feedback do AliExpress"}, "pTutorial_reviews_block1_title": {"message": "<PERSON>er"}, "pTutorial_reviews_block2_desc1": {"message": "É sempre útil verificar comentários de outros compradores"}, "pTutorial_same_products_block1_desc1": {"message": "Você pode compará-los para fazer a melhor escolha"}, "pTutorial_same_products_block1_desc2": {"message": "Clique em '<PERSON><PERSON> 'para\" Pesquisar por imagem \""}, "pTutorial_same_products_block1_title": {"message": "Mesmos produtos"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Largue a imagem do produto e escolha uma categoria"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Pesquisar por imagem"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Taxa de feedback positivo do vendedor, pontuação do feedback e há quanto tempo o vendedor está no mercado"}, "pTutorial_seller_analysis_block1_title": {"message": "Avaliação do vendedor"}, "pTutorial_seller_analysis_block2_desc2": {"message": "A classificação do vendedor é baseada em 3 índices: item como descrito, velocidade de envio da comunicação"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Usamos 3 cores e ícones para indicar os níveis de confiança dos vendedores"}, "page_count": {"message": "Número de páginas"}, "pai_chu": {"message": "Excluído"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições de Hong Kong"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições do Japão"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições da Coreia"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições do Cazaquistão"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições de Macau"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições da Europa Oriental"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições da Arábia Saudita"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições de Taiwan"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições dos EUA"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Excluir Restrições do Vietnã"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Excluir Itens Restritos"}, "payable_price_formula": {"message": "Preço + Envio + Desconto"}, "pdd_check_retail_btn_txt": {"message": "Verifique o varejo"}, "pdd_pifa_to_retail_btn_txt": {"message": "Compre no varejo"}, "pdp_copy_fail": {"message": "A cópia falhou!"}, "pdp_copy_success": {"message": "Cópia realizada com sucesso!"}, "pdp_share_modal_subtitle": {"message": "Compartilhe a captura de tela, ele / ela verá seu chioce."}, "pdp_share_modal_title": {"message": "Compartilhe sua escolha"}, "pdp_share_screenshot": {"message": "Compartilhar captura de tela"}, "pei_song": {"message": "<PERSON><PERSON>"}, "pin_lei": {"message": "Categoria"}, "pin_zhi_ti_yan": {"message": "Qualidade do produto"}, "pin_zhi_ti_yan__desc": {"message": "Taxa de reembolso de qualidade da loja do vendedor"}, "pin_zhi_tui_kuan_lv": {"message": "Taxa de reembolso"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proporção de pedidos que foram reembolsados e devolvidos apenas nos últimos 30 dias"}, "ping_fen": {"message": "Avaliação"}, "ping_jun_fa_huo_su_du": {"message": "Velocidade média de envio"}, "pkgInfo_hide": {"message": "Informações de logística: ligar/desligar"}, "pkgInfo_no_trace": {"message": "Sem informações de logística"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "homepage"}, "popup_go_to_platform": {"message": "Ir para $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Estou procurando por..."}, "popup_track_package_btn_track": {"message": "Rastreamento"}, "popup_track_package_desc": {"message": "RASTREAMENTO COMPLETO DE ENCOMENDAS"}, "popup_track_package_search_placeholder": {"message": "Numero de rastreio"}, "popup_translate_search_placeholder": {"message": "Traduza e pesquise em $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Rastreamento de preço"}, "price_history_chart_tip_ae": {"message": "Dica: o número de pedidos é o número acumulado de pedidos desde o lançamento"}, "price_history_chart_tip_coupang": {"message": "Dica: Coupang excluirá a contagem de pedidos fraudulentos"}, "price_history_inm_1688_l1": {"message": "Por favor instale"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant para 1688"}, "price_history_panel_lowest_price": {"message": "O preço mais baixo: "}, "price_history_panel_tab_price_tracking": {"message": "Rastreamento de preço"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>"}, "price_history_pro_modal_title": {"message": "Histórico de preços e histórico de pedidos"}, "privacy_consent__btn_agree": {"message": "Revisar o consentimento da coleta de dados"}, "privacy_consent__btn_disable_all": {"message": "Não aceite"}, "privacy_consent__btn_enable_all": {"message": "Habilitar todos"}, "privacy_consent__btn_uninstall": {"message": "Remover"}, "privacy_consent__desc_privacy": {"message": "Observe que, sem dados ou cookies, algumas funções estarão desligadas porque essas funções precisam da explicação de dados ou cookies, mas você ainda pode usar as outras funções."}, "privacy_consent__desc_privacy_L1": {"message": "Infelizmente, sem dados ou cookies, não funcionará porque precisamos da explicação de dados ou cookies."}, "privacy_consent__desc_privacy_L2": {"message": "Se você não nos permitir coletar essas informações, remova-as."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, só obtemos os dados da sua moeda em cookies quando fazemos compras online para mostrar o histórico de preços."}, "privacy_consent__item_cookies_title": {"message": "Cookies Requeridos"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Adicione cookies no navegador para identificar anonimamente seu computador ou dispositivo."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Adicione dados funcionais no add-on para trabalhar com a função."}, "privacy_consent__item_functional_title": {"message": "Cookies funcionais e analíticos"}, "privacy_consent__more_desc": {"message": "Saiba que não compartilhamos seus dados pessoais com outras empresas e nenhuma empresa de publicidade coleta dados por meio de nosso serviço."}, "privacy_consent__options__btn__desc": {"message": "Para usar todos os recursos, você precisa ligá-lo."}, "privacy_consent__options__btn__label": {"message": "Ligue"}, "privacy_consent__options__desc_L1": {"message": "Coletaremos os seguintes dados que o identificam pessoalmente:"}, "privacy_consent__options__desc_L2": {"message": "- <PERSON><PERSON>, só obtemos seus dados de moeda em Cookies quando você faz compras on-line para mostrar o histórico de preços."}, "privacy_consent__options__desc_L3": {"message": "- e adicione Cookies no navegador para identificar anonimamente o seu computador ou dispositivo."}, "privacy_consent__options__desc_L4": {"message": "- outros dados anônimos tornam essa extensão mais conveniente."}, "privacy_consent__options__desc_L5": {"message": "Observe que não compartilhamos seus dados pessoais com outras empresas e nenhuma empresa de publicidade coleta dados por meio de nosso serviço."}, "privacy_consent__privacy_preferences": {"message": "Preferências de privacidade"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON> mais >>"}, "privacy_consent__title_privacy": {"message": "Privacidade"}, "product_info": {"message": "Informações do produto"}, "product_recommend__name": {"message": "<PERSON><PERSON>"}, "product_research": {"message": "Pesquisa de Produto"}, "product_sub__email_desc": {"message": "Email para Alerta de Preço"}, "product_sub__email_edit": {"message": "<PERSON><PERSON>"}, "product_sub__email_not_verified": {"message": "Por favor, verifique o e-mail"}, "product_sub__email_required": {"message": "Por favor, forneça um e-mail"}, "product_sub__form_countdown": {"message": "Fechamento automático após $seconds$ segundos", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Falha ao adicionar lembre<PERSON>!"}, "product_sub__form_input_price": {"message": "Preço de entrada"}, "product_sub__form_item_country": {"message": "<PERSON><PERSON>"}, "product_sub__form_item_current_price": {"message": "Preço atual"}, "product_sub__form_item_duration": {"message": "Faixa para"}, "product_sub__form_item_higher_price": {"message": "ou preço >"}, "product_sub__form_item_invalid_higher_price": {"message": "O preço deve ser maior que $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "O preço deve ser inferior a $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Quando o preço <"}, "product_sub__form_submit": {"message": "Enviar"}, "product_sub__form_success": {"message": "Adicionar lembrete de sucesso!"}, "product_sub__high_price_notify": {"message": "Notifique-me sobre aumentos de preços"}, "product_sub__low_price_notify": {"message": "Notifique-me sobre reduções de preços"}, "product_sub__modal_title": {"message": "Lembrete de mudança de preço de assinatura"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "Quantidade mínima"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ e MOP"}, "qi_ye_mian_ji": {"message": "área empresarial"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "por favor selecione pelo menos um produto"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Selecione pelo menos um campo"}, "qu_deng_lu": {"message": "Entrar"}, "quan_guo_yan_xuan": {"message": "Escolha Global"}, "recommendation_popup_banner_btn_install": {"message": "Instale-o"}, "recommendation_popup_banner_desc": {"message": "Exibir histórico de preços em 3/6 meses e notificação de queda de preços"}, "region__all": {"message": "<PERSON><PERSON> as regi<PERSON><PERSON>"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Ativar clique direito"}, "ren_zheng_gong_chang": {"message": "Fábrica Certificada"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Anos como Fornecedor Certificado"}, "required_to_aliprice_login": {"message": "Precisa entrar no <PERSON>Price"}, "revenue_last30_days": {"message": "Valor das vendas nos últimos 30 dias"}, "review_counts": {"message": "Número de colecionadores"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "período de entrada"}, "sales_amount_last30_days": {"message": "Total de vendas nos últimos 30 dias"}, "sales_last30_days": {"message": "Vendas nos últimos 30 dias"}, "sbi_alibaba_cate__accessories": {"message": "Acessórios"}, "sbi_alibaba_cate__aqfk": {"message": "Segurança"}, "sbi_alibaba_cate__bags_cases": {"message": "Sacos e estojos"}, "sbi_alibaba_cate__beauty": {"message": "Beleza"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Cultura do escritório"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__clothes": {"message": "roupa"}, "sbi_alibaba_cate__cmgd": {"message": "Media Broadcasting"}, "sbi_alibaba_cate__coat_jacket": {"message": "Casaco e jaqueta"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Eletrônicos de consumo"}, "sbi_alibaba_cate__cryp": {"message": "Produtos para adultos"}, "sbi_alibaba_cate__csyp": {"message": "forros de cama"}, "sbi_alibaba_cate__cwyy": {"message": "jardinagem para animais de estimação"}, "sbi_alibaba_cate__cysx": {"message": "Catering fresco"}, "sbi_alibaba_cate__dgdq": {"message": "Eletricista"}, "sbi_alibaba_cate__dl": {"message": "Atuando"}, "sbi_alibaba_cate__dress_suits": {"message": "Vestidos e Ternos"}, "sbi_alibaba_cate__dszm": {"message": "Iluminação"}, "sbi_alibaba_cate__dzqj": {"message": "<PERSON><PERSON><PERSON><PERSON>rô<PERSON>"}, "sbi_alibaba_cate__essb": {"message": "Equipamento usado"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "roupas e acessórios"}, "sbi_alibaba_cate__furniture": {"message": "Mobília"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON> t<PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "Cuidado pessoal"}, "sbi_alibaba_cate__gt": {"message": "Aço"}, "sbi_alibaba_cate__gyp": {"message": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>"}, "sbi_alibaba_cate__hb": {"message": "amigo do ambiente"}, "sbi_alibaba_cate__hfcz": {"message": "Maquiagem para cuidados com a pele"}, "sbi_alibaba_cate__hg": {"message": "Indústria química"}, "sbi_alibaba_cate__jg": {"message": "Em processamento"}, "sbi_alibaba_cate__jianccai": {"message": "Materiais de construção"}, "sbi_alibaba_cate__jichuang": {"message": "máquina-ferramenta"}, "sbi_alibaba_cate__jjry": {"message": "Uso doméstico <PERSON>"}, "sbi_alibaba_cate__jtys": {"message": "Transporte"}, "sbi_alibaba_cate__jxsb": {"message": "Equipamento"}, "sbi_alibaba_cate__jxwj": {"message": "Hardware mecânico"}, "sbi_alibaba_cate__jydq": {"message": "Electrodomésticos"}, "sbi_alibaba_cate__jzjc": {"message": "Materiais de construção para reforma da casa"}, "sbi_alibaba_cate__jzjf": {"message": "Têxteis do lar"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nanz": {"message": "masculino"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "Energia"}, "sbi_alibaba_cate__others": {"message": "Outras"}, "sbi_alibaba_cate__qcyp": {"message": "Acessórios automotivos"}, "sbi_alibaba_cate__qmpj": {"message": "Autopeças"}, "sbi_alibaba_cate__shoes": {"message": "Sapato"}, "sbi_alibaba_cate__smdn": {"message": "Computador digital"}, "sbi_alibaba_cate__snqj": {"message": "Armazenamento e limpeza"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON> bebida"}, "sbi_alibaba_cate__swfw": {"message": "Serviços prestados às empresas"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Calças e saia"}, "sbi_alibaba_cate__txcp": {"message": "produtos de comunicação"}, "sbi_alibaba_cate__tz": {"message": "<PERSON><PERSON><PERSON> infantis"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "ferramentas de hardware"}, "sbi_alibaba_cate__xgpi": {"message": "Bolsas de couro"}, "sbi_alibaba_cate__xmhz": {"message": "cooperação de projeto"}, "sbi_alibaba_cate__xs": {"message": "Borracha"}, "sbi_alibaba_cate__ydfs": {"message": "Roupa de esporte"}, "sbi_alibaba_cate__ydhw": {"message": "Desporto ao ar-livre"}, "sbi_alibaba_cate__yjkc": {"message": "Minerais Metalúrgicos"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentação"}, "sbi_alibaba_cate__ys": {"message": "Imprimir"}, "sbi_alibaba_cate__yyby": {"message": "Cuidados médicos"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Número de compradores nos últimos 90 dias"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volume de vendas nos últimos 90 dias"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Preço estimado"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Taxa de envio internacional"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Taxa de envio"}, "sbi_alibaba_cn_kj_gssj": {"message": "Preço estimado"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Lucro = preço estimado x margem de lucro"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Velocidade média de entrega"}, "sbi_alibaba_cn_kj_qtfy": {"message": "outra taxa"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Outro custo = preço estimado x outra relação de custo"}, "sbi_alibaba_cn_kj_spjg": {"message": "Preço"}, "sbi_alibaba_cn_kj_spzl": {"message": "Peso"}, "sbi_alibaba_cn_kj_szd": {"message": "Localização"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Peças"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Peças"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Grama"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Compradores"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Peças"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Jogos"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "preço"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Disponível para venda"}, "sbi_aliprice_alibaba_cn__moren": {"message": "predefinição"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Certo"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "mobiliário"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "lanche"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "maquiagens"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "R<PERSON><PERSON> de baixo"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Acessórios"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Outros"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Eletrônicos"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagagem"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Partes inferiores"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "sapato"}, "sbi_aliprice_cate__apparel": {"message": "Vestu<PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automóveis e Motocicletas"}, "sbi_aliprice_cate__beauty_health": {"message": "beleza e saúde"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Telefones celulares e telecomunicações"}, "sbi_aliprice_cate__computer_office": {"message": "Computador e escritório"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Eletrônicos de consumo"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Educação e material de escritório"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Componentes e suprimentos eletrônicos"}, "sbi_aliprice_cate__furniture": {"message": "Mobília"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Extensões de cabelo e perucas"}, "sbi_aliprice_cate__home_garden": {"message": "Casa e jardim"}, "sbi_aliprice_cate__home_improvement": {"message": "Mel<PERSON><PERSON> da casa"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Joias e acessórios"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagagens e bolsas"}, "sbi_aliprice_cate__mother_kids": {"message": "Mãe e filhos"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Novidade e uso especial"}, "sbi_aliprice_cate__security_protection": {"message": "Proteção de segurança"}, "sbi_aliprice_cate__shoes": {"message": "sapatos"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Esportes e Entretenimento"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Brinquedos e Hobbies"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Casamentos e eventos"}, "sbi_btn_capture_txt": {"message": "Capturar"}, "sbi_btn_source_now_txt": {"message": "Fonte agora"}, "sbi_button__chat_with_me": {"message": "Conversar comigo"}, "sbi_button__contact_supplier": {"message": "Contato"}, "sbi_button__hide_on_this_site": {"message": "Não mostrar neste site"}, "sbi_button__open_settings": {"message": "Configurar pesquisa por imagem"}, "sbi_capture_shortcut_tip": {"message": "ou pressione a tecla \"Enter\" no teclado"}, "sbi_capturing_tip": {"message": "Capturando"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 estrelas"}, "sbi_crop_and_search": {"message": "Procurar"}, "sbi_crop_start": {"message": "Use a captura de tela"}, "sbi_err_captcha_action": {"message": "Verificar"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Precisa de verificação, faça upload de uma foto para verificar. (Veja $video_tutorial$ ou tente limpar os cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Trân<PERSON><PERSON> incomum, verifique"}, "sbi_err_captcha_for_taobao": {"message": "O Taobao solicita que você verifique, carregue manualmente uma imagem e pesquise para verificá-la. Este erro é devido à nova política de verificação do \"TaoBao search by image\", sugerimos que essa reclamação verifique muito frequente no Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "comentários"}, "sbi_err_captcha_msg": {"message": "$platform$ exige que você carregue uma imagem para pesquisar ou conclua a verificação de segurança para remover restrições de pesquisa", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Verifique se é a versão mais recente"}, "sbi_err_cookie_btn_clear": {"message": "Limpar Cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Tentar limpar os cookies de 1688? (É necessário fazer login novamente)"}, "sbi_err_desperate_feature_pdd": {"message": "A função de pesquisa de imagens foi movida para a extensão Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Como melhorar a taxa de sucesso da pesquisa de imagens?"}, "sbi_err_img_undersize": {"message": "Imagem > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Faça login $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Conecte-se"}, "sbi_err_low_version": {"message": "Instale a versão mais recente ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Download"}, "sbi_err_need_help": {"message": "Precisa de ajuda"}, "sbi_err_network": {"message": "Erro de rede, certifique-se de visitar o site"}, "sbi_err_not_low_version": {"message": "A versão mais recente foi instalada ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Tente novamente"}, "sbi_err_try_again_action": {"message": "Tente novamente"}, "sbi_err_visit_and_try": {"message": "Tente novamente ou visite $website$ para tentar novamente", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Visite a página inicial de $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "O carregamento falhou, atualize a página e tente novamente."}, "sbi_kuajing_filter_area": {"message": "Á<PERSON>"}, "sbi_kuajing_filter_au": {"message": "Austrália"}, "sbi_kuajing_filter_btn_confirm": {"message": "confirme"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "País <PERSON>"}, "sbi_kuajing_filter_es": {"message": "Espanha"}, "sbi_kuajing_filter_estimate": {"message": "Estimativa"}, "sbi_kuajing_filter_estimate_price": {"message": "Preço estimado"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Fórmula de preço estimado = (preço da mercadoria + frete logístico internacional) / (1 - margem de lucro - outra relação de custo)"}, "sbi_kuajing_filter_fr": {"message": "França"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Insira palavras-chave para corresponder ao título"}, "sbi_kuajing_filter_logistics": {"message": "Modelo de logística"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Correio Aéreo da China"}, "sbi_kuajing_filter_logistics_discount": {"message": "Desconto logistico"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Frete de logística internacional = (peso x preço de envio + taxa de registro) x (1 - desconto)"}, "sbi_kuajing_filter_others_fee": {"message": "Outra taxa"}, "sbi_kuajing_filter_profit_percent": {"message": "Margem de lucro"}, "sbi_kuajing_filter_prop": {"message": "Atributos"}, "sbi_kuajing_filter_ru": {"message": "Rússia"}, "sbi_kuajing_filter_total": {"message": "Combine $count$ itens semelhantes", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "REINO UNIDO"}, "sbi_kuajing_filter_usa": {"message": "América"}, "sbi_login_punish_title__pdd_pifa": {"message": "Atacado <PERSON>"}, "sbi_msg_no_result": {"message": "Nenhum resultado encontrado,faça login em $loginSite$ ou tente outra foto", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Temporariamente indisponível para Safari, use $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Navegador Chrome e suas extensões"}, "sbi_msg_no_result_reinstall_l1": {"message": "Nenhum resultado encontrado, faça login em $loginSite$ ou tente outra imagem, ou reinstale $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Última versão", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Área selecionada"}, "sbi_shipping_": {"message": "Envio no mesmo dia"}, "sbi_specify_category": {"message": "Especifique a categoria:"}, "sbi_start_crop": {"message": "Selecione a área"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 no exterior"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON>"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Encontre cupons Taobao"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>, nenhum resultado encontrado, por favor tente novamente."}, "sbi_txt__min_order": {"message": "Min. ordem"}, "sbi_visiting": {"message": "Navegando"}, "sbi_yiwugo__jiagexiangtan": {"message": "Contate o vendedor para saber o preço"}, "sbi_yiwugo__qigou": {"message": "$num$ Peças (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Estrelas"}, "searchByImage_screenshot": {"message": "Captura de tela com um clique"}, "searchByImage_search": {"message": "Pesquisa com um clique para os mesmos itens"}, "searchByImage_size_type": {"message": "O tamanho do arquivo não pode ser maior que $num$ MB, somente $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "search_by_image_progress_searching": {"message": "Pes<PERSON><PERSON> produtos"}, "search_by_image_progress_sending": {"message": "Enviando imagem"}, "search_by_image_response_rate": {"message": "Taxa de resposta: $responseRate$ dos compradores que entraram em contato com este fornecedor receberam uma resposta dentro de $responseInHour$ horas.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Pesquisar por palavra-chave:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Língua"}, "select_country_region_modal_title": {"message": "Selecione um país/região"}, "select_language_modal_title": {"message": "Selecione um idioma:"}, "select_shop": {"message": "Selecionar loja"}, "sellers_count": {"message": "Número de vendedores na página atual"}, "sellers_count_per_page": {"message": "Número de vendedores na página atual"}, "service_score": {"message": "Avaliação abrangente do serviço"}, "set_shortcut_keys": {"message": "Definir teclas de atalho"}, "setting_logo_title": {"message": "Assistente de compras"}, "setting_modal_options_position_title": {"message": "Localização"}, "setting_modal_options_position_value_left": {"message": "À esquerda"}, "setting_modal_options_position_value_right": {"message": "À direita"}, "setting_modal_options_theme_title": {"message": "Cor do tema"}, "setting_modal_options_theme_value_dark": {"message": "Escuro"}, "setting_modal_options_theme_value_light": {"message": "Luz"}, "setting_modal_title": {"message": "Configurações"}, "setting_options_country_title": {"message": "País/Região"}, "setting_options_hover_zoom_desc": {"message": "Passe o mouse em cima para dar zoom"}, "setting_options_hover_zoom_title": {"message": "Passe o mouse e zoom"}, "setting_options_jd_coupon_desc": {"message": "Cupom encontrado em JD.com"}, "setting_options_jd_coupon_title": {"message": "Cupom JD.com"}, "setting_options_language_title": {"message": "Idioma"}, "setting_options_price_drop_alert_desc": {"message": "Assim que o preço das mercadorias em\" My Favorite \"cair, você receberá uma notificação."}, "setting_options_price_drop_alert_title": {"message": "Acompanhe o preço"}, "setting_options_price_history_on_list_page_desc": {"message": "Exibir histórico de preços na página de pesquisa de produtos"}, "setting_options_price_history_on_list_page_title": {"message": "Histórico de preços (página de lista)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Exibir o histórico do produto na página de pesquisa do produto"}, "setting_options_price_history_on_produt_page_title": {"message": "Histó<PERSON><PERSON> de preços (página de detalhes)"}, "setting_options_sales_analysis_desc": {"message": "Estatísticas de suporte de preço, volume de vendas, número de vendedores e taxa de vendas da loja na página de lista de produtos $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "Sucesso"}, "setting_options_tacking_price_title": {"message": "Alerta de alteração de preço"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Suporte: 1688 e Taobao"}, "setting_saved_message": {"message": "Alterações salvas com sucesso"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "Configurações"}, "setting_section_shortcuts_title": {"message": "Atalhos :"}, "settings_aliprice_agent__desc": {"message": "Exibido na página de detalhes do produto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Compre para mim"}, "settings_copy_link__desc": {"message": "Exibir na página de detalhes do produto"}, "settings_copy_link__title": {"message": "Botão Copiar e título de pesquisa"}, "settings_currency_desc__for_detail": {"message": "Suporte página de detalhes do produto 1688"}, "settings_currency_desc__for_list": {"message": "Pesquisa por imagem (inclui 1688/1688 no exterior / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Selecione o preço"}, "settings_currency_desc_display_for_list": {"message": "Mostrado na pesquisa de imagens (incluindo 1688/1688 no exterior/Taobao)"}, "settings_currency_rate_desc": {"message": "Atualização da taxa de câmbio de \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Banco da China"}, "settings_download_images__desc": {"message": "Suporte para baixar imagens de $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "botão baixar imagem"}, "settings_download_reviews__desc": {"message": "Exibido na página de detalhes do produto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Baixar imagens de avaliação"}, "settings_google_translate_desc": {"message": "Clique com o botão direito para obter a barra de tradução do google"}, "settings_google_translate_title": {"message": "tradução de páginas da web"}, "settings_historical_trend_desc": {"message": "Exibir no canto inferior direito da imagem na página da lista de produtos"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON> configu<PERSON>"}, "settings_productInfo_desc": {"message": "Exibir informações mais detalhadas sobre o produto na página de lista de produtos. Habilitar isso pode aumentar a carga do computador e causar atraso na página. Se afetar o desempenho, é recomendável desabilitar."}, "settings_product_recommend__desc": {"message": "Exibido abaixo da imagem principal na página de detalhes do produto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "<PERSON><PERSON><PERSON> recomendados"}, "settings_research_desc": {"message": "Consultar informações mais detalhadas na página de lista de produtos"}, "settings_sbi_add_to_list": {"message": "Adicione ao $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatura do resultado da pesquisa de imagens"}, "settings_sbi_remove_from_list": {"message": "Remova de $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Adicionar à lista negra"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Ajustar a posição de entrada"}, "settings_search_by_image_blacklist_desc": {"message": "Não mostrar o ícone em sites da lista negra."}, "settings_search_by_image_blacklist_title": {"message": "Lista negra"}, "settings_search_by_image_bottom_left": {"message": "Inferior Esquerdo"}, "settings_search_by_image_bottom_right": {"message": "Inferior Direito"}, "settings_search_by_image_clear_blacklist": {"message": "Limpar lista negra"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatura"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Um clique no ícone para pesquisar por imagem"}, "settings_search_by_image_display_button_title": {"message": "Ícone nas imagens"}, "settings_search_by_image_sourece_websites_desc": {"message": "Encontre o produto de origem nesses sites"}, "settings_search_by_image_sourece_websites_title": {"message": "Resultado da pesquisa por imagem"}, "settings_search_by_image_top_left": {"message": "Superior Esquerdo"}, "settings_search_by_image_top_right": {"message": "Superior Direito"}, "settings_search_keyword_on_x__desc": {"message": "Pesquisar palavras em $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Mostrar o ícone $platform$ quando as palavras selecionadas", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Tente encontrar o mesmo produto nesses sites (máx. 5)"}, "settings_similar_products_title": {"message": "Encontre o mesmo produto"}, "settings_toolbar_expand_title": {"message": "Plug-in minimizar"}, "settings_top_toolbar_desc": {"message": "Barra de pesquisa no topo da página"}, "settings_top_toolbar_title": {"message": "Barra de pesquisa"}, "settings_translate_search_desc": {"message": "Traduza para chinês e pesquise"}, "settings_translate_search_title": {"message": "Pesquisa multilíngue"}, "settings_translator_contextmenu_title": {"message": "Capture para traduzir"}, "settings_translator_title": {"message": "Traduzir"}, "shai_xuan_dao_chu": {"message": "Filtrar para exportar"}, "shai_xuan_zi_duan": {"message": "Campos de filtro"}, "shang_jia_shi_jian": {"message": "Tempo de prateleira"}, "shang_pin_biao_ti": {"message": "título do produto"}, "shang_pin_dui_bi": {"message": "Comparação de Produto"}, "shang_pin_lian_jie": {"message": "link do produto"}, "shang_pin_xin_xi": {"message": "Informação do produto"}, "share_modal__content": {"message": "Compartilhe com os seus amigos"}, "share_modal__disable_for_while": {"message": "Eu não quero compartilhar nada"}, "share_modal__title": {"message": "Você gosta de $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Restante"}, "shi_fou_ke_ding_zhi": {"message": "É personalizável?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Fornecedor certificado"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Fornecedores certificados"}, "shi_fou_you_mao_yi_dan_bao": {"message": "<PERSON><PERSON><PERSON>"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Taxa de envio"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON> loja"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Máximo a 5"}, "sku_calc_price": {"message": "Preço Calculado"}, "sku_calc_price_settings": {"message": "Configurações de Preço Calculado"}, "sku_formula": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_formula_desc": {"message": "Descrição da Fórmula"}, "sku_formula_desc_text": {"message": "Suporta fórmulas matemáticas complexas, com o preço original representado por A e o frete representado por B.\n\n<br/>\n\nSuporta colchetes (), mais +, menos -, multiplicação * e divisão /.\n\n<br/>\n\nExemplo:\n\n<br/>\n\n1. Para obter 1,2 vezes o preço original e adicionar o frete, a fórmula é: A*1,2+B.\n\n<br/>\n\n2. Para obter o preço original mais 1 yuan e multiplicar por 1,2 vezes, a fórmula é: (A+1)*1,2.\n\n<br/>\n\n3. Para obter o preço original mais 10 yuans, multiplicar por 1,2 vezes e subtrair 3 yuans, a fórmula é: (A+10)*1,2-3."}, "sku_in_stock": {"message": "Em Estoque"}, "sku_invalid_formula_format": {"message": "Formato de fórmula inválido"}, "sku_inventory": {"message": "Estoque"}, "sku_link_copy_fail": {"message": "Copiado com sucesso, especificações e atributos de SKU não selecionados"}, "sku_link_copy_success": {"message": "Copiado com sucesso, especificações de SKU e atributos selecionados"}, "sku_list": {"message": "Lista de SKUs"}, "sku_min_qrder_qty": {"message": "Quantidade Mínima do Pedido"}, "sku_name": {"message": "Nome do SKU"}, "sku_no": {"message": "Nº"}, "sku_original_price": {"message": "Preço Original"}, "sku_price": {"message": "Preço do SKU"}, "stop_track_time_label": {"message": "Prazo de rastreamento:"}, "suo_zai_di_qu": {"message": "localização"}, "tab_pkg_quick_view": {"message": "Monitor de Logística"}, "tab_product_details_price_history": {"message": "História"}, "tab_product_details_reviews": {"message": "Avaliações"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON>"}, "total_days_listed_per_product": {"message": "Soma dos dias de prateleira ÷ Número de produtos"}, "total_items": {"message": "Número total de produtos"}, "total_price_per_product": {"message": "Soma dos preços ÷ Quantidade de produtos"}, "total_rating_per_product": {"message": "Soma das avaliações ÷ Número de produtos"}, "total_revenue": {"message": "Rendimento total"}, "total_revenue40_items": {"message": "Receita total dos $amount$ produtos na página atual", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON><PERSON> to<PERSON>s"}, "total_sales40_items": {"message": "Vendas totais dos $amount$ produtos da página atual", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Faixa para: 1 ano"}, "track_for_3_months": {"message": "Faixa por: 3 meses"}, "track_for_6_months": {"message": "Faixa por: 6 meses"}, "tracking_price_email_add_btn": {"message": "Adicionar email"}, "tracking_price_email_edit_btn": {"message": "Editar email"}, "tracking_price_email_intro": {"message": "Nós o notificaremos por e-mail."}, "tracking_price_email_invalid": {"message": "Por favor forneça um email válido"}, "tracking_price_email_verified_desc": {"message": "Agora você pode receber nosso alerta de queda de preço."}, "tracking_price_email_verified_title": {"message": "Verificado com sucesso"}, "tracking_price_email_verify_desc_line1": {"message": "Enviámos um link de verificação para o seu endereço de email,"}, "tracking_price_email_verify_desc_line2": {"message": "verifique sua caixa de entrada de e-mail."}, "tracking_price_email_verify_title": {"message": "Verificar e-mail"}, "tracking_price_web_push_notification_intro": {"message": "Na área de trabalho: AliPrice pode monitorar qualquer produto e enviar uma notificação por push da Web assim que o preço mudar."}, "tracking_price_web_push_notification_title": {"message": "Notificações por push da Web"}, "translate_im__login_required": {"message": "Traduzido por AliPrice, faça login em $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Traduzido e copiado para a área de transferência, mas devido à limitação do Aliwangwang, é necessário colá-lo manualmente!"}, "translate_im__send": {"message": "Traduzir e enviar"}, "translate_search": {"message": "Traduzir e pesquisar"}, "translation_originals_translated": {"message": "Original e Chinês"}, "translation_translated": {"message": "chinês"}, "translator_btn_capture_txt": {"message": "Traduzir"}, "translator_language_auto_detect": {"message": "Detecção automática"}, "translator_language_detected": {"message": "Detectou"}, "translator_language_search_placeholder": {"message": "Idioma de pesquisa"}, "try_again": {"message": "Tente novamente"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON>m:"}, "tu_pian_lian_jie": {"message": "<PERSON> da <PERSON>m"}, "tui_huan_ti_yan": {"message": "Experiência de retorno"}, "tui_huan_ti_yan__desc": {"message": "Avalie os indicadores de pós-venda dos vendedores"}, "tutorial__show_all": {"message": "Todos os recursos"}, "tutorial_ae_popup_title": {"message": "Fixe a extensão, abra o Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Análise de revisão do AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Suporte USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Envio para Coreia/Japão/China Continental"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 apoia compras no exterior"}, "tutorial_auto_apply_coupon_title": {"message": "Cupom de aplicação automática"}, "tutorial_btn_end": {"message": "Fim"}, "tutorial_btn_example": {"message": "Exemplo"}, "tutorial_btn_have_a_try": {"message": "Ok, tem uma chance"}, "tutorial_btn_next": {"message": "Próxima"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Compare com o mesmo estilo"}, "tutorial_currency_convert_title": {"message": "conversão de taxa de câmbio"}, "tutorial_export_shopping_cart": {"message": "Exportar CSV, suporte Taobao e 1688"}, "tutorial_export_shopping_cart_title": {"message": "carrinho de exportação"}, "tutorial_price_history_pro": {"message": "Exibido na página de detalhes do produto.\nSuporte Shopee, Lazada, Amazon e Ebay"}, "tutorial_price_history_pro_title": {"message": "Histórico de preços e histórico de pedidos do ano inteiro"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Pesquisa de captura de tela para o mesmo estilo"}, "tutorial_translate_search": {"message": "Traduzir para pesquisar"}, "tutorial_translate_search_and_package_tracking": {"message": "Pesquisa de tradução e rastreamento de pacotes"}, "unit_bao": {"message": "peças"}, "unit_ben": {"message": "peças"}, "unit_bi": {"message": "pedidos"}, "unit_chuang": {"message": "peças"}, "unit_dai": {"message": "peças"}, "unit_dui": {"message": "par"}, "unit_fen": {"message": "peças"}, "unit_ge": {"message": "peças"}, "unit_he": {"message": "peças"}, "unit_jian": {"message": "peças"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "peças"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "par"}, "unit_tai": {"message": "peças"}, "unit_ti": {"message": "peças"}, "unit_tiao": {"message": "peças"}, "unit_xiang": {"message": "peças"}, "unit_zhang": {"message": "peças"}, "unit_zhi": {"message": "peças"}, "verify_contact_support": {"message": "Entre em contato com o suporte"}, "verify_human_verification": {"message": "Verificação humana"}, "verify_unusual_access": {"message": "Acesso incomum detectado"}, "view_history_clean_all": {"message": "<PERSON><PERSON> tudo"}, "view_history_clean_all_warring": {"message": "Limpar todos os registros visualizados?"}, "view_history_clean_all_warring_title": {"message": "Atenção"}, "view_history_viewd": {"message": "Visto"}, "website": {"message": "site"}, "weight": {"message": "Peso"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Não foi possível obter os dados"}, "wu_liu_shi_xiao": {"message": "<PERSON>vio dentro do prazo"}, "wu_liu_shi_xiao__desc": {"message": "A taxa de coleta em 48 horas e a taxa de atendimento da loja do vendedor"}, "xia_dan_jia": {"message": "Preço final"}, "xian_xuan_ze_product_attributes": {"message": "Selecione os atributos do produto"}, "xiao_liang": {"message": "Volume de vendas"}, "xiao_liang_zhan_bi": {"message": "Porcentagem do volume de vendas"}, "xiao_shi": {"message": "$num$ horas", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "Porcentagem da receita"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Selecione $amount$ registros", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Escolha"}, "yi_ding_zai_zuo_ce": {"message": "Fixado"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Todos os produtos carregados"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Participação nas Vendas Anuais"}, "yi_nian_xiao_shou_e": {"message": "Faturamento Anual"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Participação no Faturamento Anual"}, "yi_shua_xin": {"message": "Atualizado"}, "yin_cang_xiang_tong_dian": {"message": "esconder semelhanças"}, "you_xiao_liang": {"message": "Com volume de vendas"}, "yu_ji_dao_da_shi_jian": {"message": "Hora estimada de chegada"}, "yuan_gong_ren_shu": {"message": "<PERSON>ú<PERSON><PERSON>"}, "yue_cheng_jiao": {"message": "Volume mensal"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Vendas de dropshipping nos últimos 30 dias"}, "yue_dai_xiao_pai_xu__desc": {"message": "Vendas de dropshipping nos últimos 30 dias, classificadas da maior para a menor"}, "yue_xiao_liang__desc": {"message": "Volume de vendas nos últimos 30 dias"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "Desconto"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Frete gr<PERSON><PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "Pedidos pagos"}, "zhi_fu_ding_dan_shu__desc": {"message": "Número de pedidos deste produto (30 dias)"}, "zhu_ce_xing_zhi": {"message": "Natureza do registro"}, "zi_ding_yi_tiao_jian": {"message": "Condições personalizadas"}, "zi_duan": {"message": "Campos"}, "zi_ti_xiao_liang": {"message": "Variação Vendida"}, "zong_he_fu_wu_fen": {"message": "Avaliação geral"}, "zong_he_fu_wu_fen__desc": {"message": "Avaliação geral do serviço do vendedor"}, "zong_he_fu_wu_fen__short": {"message": "Avaliação"}, "zong_he_ti_yan_fen": {"message": "Avaliação"}, "zong_he_ti_yan_fen_3": {"message": "Abaixo de 4 estrelas"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 estrelas"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 estrelas"}, "zong_he_ti_yan_fen_5": {"message": "5 estrelas"}, "zong_ku_cun": {"message": "Estoque total"}, "zong_xiao_liang": {"message": "<PERSON><PERSON><PERSON> to<PERSON>s"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Taxa de resposta em 3 minutos nos últimos 30 dias"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Taxa de recuperação de 48H nos últimos 30 dias"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Taxa de desempenho de 48 horas nos últimos 30 dias"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Registro de negociação (30 dias)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Registro de negociação (30 dias)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Taxa de disputa nos últimos 30 dias"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Taxa de reembolso de qualidade nos últimos 30 dias"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "O número de ordens de pagamento nos últimos 30 dias"}}