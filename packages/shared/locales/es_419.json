{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Punto de venta transfronterizo en auge"}, "1688_shi_li_ren_zheng": {"message": "Certificación de fuerza 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Más de 1 año"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Entrega en 24 horas"}, "24H_lan_shou_lv": {"message": "Tarifa de embalaje 24 horas"}, "30D_shang_xin": {"message": "Novedades mensuales"}, "30d_sales": {"message": "$amount$ vendido en 30 días", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Respuesta en 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "La proporción de respuestas efectivas de Wangwang a los mensajes de consulta de los compradores en 3 minutos en los últimos 30 días."}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Entrega en 48 horas"}, "48H_lan_shou_lv": {"message": "Tarifa de embalaje de 48 horas"}, "48H_lan_shou_lv__desc": {"message": "Relación entre el número de pedido recogido dentro de las 48 horas y el número total de pedidos"}, "48H_lv_yue_lv": {"message": "Tasa de rendimiento de 48 horas"}, "48H_lv_yue_lv__desc": {"message": "Relación entre el número de pedido recogido o entregado dentro de las 48 horas y el número total de pedidos"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Novedades semanales"}, "7D_wu_li_you": {"message": "7 días sin cuidados"}, "ABS_title_text": {"message": "Este listado incluye una historia de marca"}, "AC_title_text": {"message": "Este listado tiene la insignia Amazon's Choice"}, "A_title_text": {"message": "Este listado tiene una página de contenido A+"}, "BS_title_text": {"message": "Este listado está clasificado como el $num$ más vendido en la categoría $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) significa que este listado es parte de un evento de \"promoción de 7 días\""}, "NR_title_text": {"message": "Este listado está clasificado como el $num$ nuevo lanzamiento en la categoría $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Este listado tiene un anuncio de video, un tipo de anuncio PPC que suele aparecer en el medio de los resultados de búsqueda"}, "SB_title_text": {"message": "Este listado tiene un anuncio de marca, un tipo de anuncio PPC que suele aparecer en la parte superior o inferior de los resultados de búsqueda"}, "SP_title_text": {"message": "Este listado tiene un anuncio de producto patrocinado"}, "V_title_text": {"message": "Este listado tiene una introducción en video"}, "advanced_research": {"message": "Investigación avanzada"}, "agent_ds1688___my_order": {"message": "Mis ordenes"}, "agent_ds1688__add_to_cart": {"message": "Compra en el extranjero"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON> compra"}, "agent_ds1688__desc": {"message": "Proporcionado por 1688. Admite compras directas desde el extranjero, pagos en USD y entrega a su almacén de tránsito en China."}, "agent_ds1688__freight": {"message": "Calculadora de costos de envío"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Hoja de ruta"}, "agent_ds1688__profile": {"message": "Centro personal"}, "agent_ds1688__warehouse": {"message": "Mi almacen"}, "ai_comment_analysis_advantage": {"message": "Pros"}, "ai_comment_analysis_ai": {"message": "Análisis de reseñas de IA"}, "ai_comment_analysis_available": {"message": "Disponibles"}, "ai_comment_analysis_balance": {"message": "No hay suficientes monedas, por favor, recargue"}, "ai_comment_analysis_behavior": {"message": "Comportamiento"}, "ai_comment_analysis_characteristic": {"message": "Características de la multitud"}, "ai_comment_analysis_comment": {"message": "El producto no tiene suficientes reseñas para sacar conclusiones precisas, por favor, seleccione un producto con más reseñas."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON> estimado"}, "ai_comment_analysis_default": {"message": "Reseñas predeterminadas"}, "ai_comment_analysis_desire": {"message": "Expectativas del cliente"}, "ai_comment_analysis_disadvantage": {"message": "Contras"}, "ai_comment_analysis_free": {"message": "Intentos gratis"}, "ai_comment_analysis_freeNum": {"message": "Se utilizará 1 crédito gratuito"}, "ai_comment_analysis_go_recharge": {"message": "Ir a recarga"}, "ai_comment_analysis_intelligence": {"message": "Análisis de reseñas inteligente"}, "ai_comment_analysis_location": {"message": "Ubicación"}, "ai_comment_analysis_motive": {"message": "Motivación de compra"}, "ai_comment_analysis_network_error": {"message": "Error de red, por favor, inténtelo de nuevo"}, "ai_comment_analysis_normal": {"message": "Reseñas con fotos"}, "ai_comment_analysis_number_reviews": {"message": "Número de reseñas: $num$, Consumo estimado: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Comentarios generales"}, "ai_comment_analysis_percentage": {"message": "Po<PERSON>entaj<PERSON>"}, "ai_comment_analysis_problem": {"message": "Problemas con el pago"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "Motivo"}, "ai_comment_analysis_recharge": {"message": "Recarga"}, "ai_comment_analysis_recharged": {"message": "<PERSON> recargado"}, "ai_comment_analysis_retry": {"message": "Reintentar"}, "ai_comment_analysis_scene": {"message": "Escenario de uso"}, "ai_comment_analysis_start": {"message": "Comenzar a analizar"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "Tiempo de uso"}, "ai_comment_analysis_tool": {"message": "Herramienta de IA"}, "ai_comment_analysis_user_portrait": {"message": "Perfil de usuario"}, "ai_comment_analysis_welcome": {"message": "Bienvenido al análisis de reseñas de IA"}, "ai_comment_analysis_year": {"message": "Comentarios del año pasado"}, "ai_listing_Exclude_keywords": {"message": "Excluir palabras clave"}, "ai_listing_Login_the_feature": {"message": "Es necesario iniciar sesión para la función."}, "ai_listing_aI_generation": {"message": "generación de IA"}, "ai_listing_add_automatic": {"message": "Automático"}, "ai_listing_add_dictionary_new": {"message": "Crear una nueva biblioteca"}, "ai_listing_add_enter_keywords": {"message": "Ingrese palabras claves"}, "ai_listing_add_inputkey_selling": {"message": "Ingresa un punto de venta y presiona $key$ para terminar de agregar", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Límite excedido, hasta $amount$ puntos de venta", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Agregar palabras clave"}, "ai_listing_add_manually": {"message": "Agregar manualmente"}, "ai_listing_add_selling": {"message": "Agregar puntos de venta"}, "ai_listing_added_keywords": {"message": "Palabras clave agregadas"}, "ai_listing_added_successfully": {"message": "Agregado exitosamente"}, "ai_listing_addexcluded_keywords": {"message": "Ingrese las palabras clave excluidas, presione Intro para terminar de agregar."}, "ai_listing_adding_selling": {"message": "Puntos de venta añadidos"}, "ai_listing_addkeyword_enter": {"message": "Escriba las palabras del atributo clave y presione Intro para terminar de agregar"}, "ai_listing_ai_description": {"message": "Biblioteca de palabras de descripción de IA"}, "ai_listing_ai_dictionary": {"message": "Biblioteca de palabras de títulos de IA"}, "ai_listing_ai_title": {"message": "título de IA"}, "ai_listing_aidescription_repeated": {"message": "El nombre de la biblioteca de palabras de descripción de AI no se puede repetir"}, "ai_listing_aititle_repeated": {"message": "El nombre de la biblioteca de palabras del título AI no se puede repetir"}, "ai_listing_data_comes_from": {"message": "Estos datos provienen de:"}, "ai_listing_deleted_successfully": {"message": "Bo<PERSON>do exitosamente"}, "ai_listing_dictionary_name": {"message": "Nombre de la biblioteca"}, "ai_listing_edit_dictionary": {"message": "Modificar biblioteca..."}, "ai_listing_edit_word_library": {"message": "Editar la biblioteca de palabras"}, "ai_listing_enter_keywords": {"message": "Ingrese palabras clave y presione $key$ para terminar de agregar", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Se ha excedido el límite, máximo $amount$ palabras clave", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Biblioteca de palabras excluidas"}, "ai_listing_generate_characters": {"message": "<PERSON><PERSON>"}, "ai_listing_generation_platform": {"message": "Plataforma de generación"}, "ai_listing_help_optimize": {"message": "Ayúdame a optimizar el título del producto, el título original es"}, "ai_listing_include_selling": {"message": "Otros puntos de venta incluyeron:"}, "ai_listing_included_keyword": {"message": "Palabras clave incluidas"}, "ai_listing_included_keywords": {"message": "Palabras clave incluidas"}, "ai_listing_input_selling": {"message": "Ingrese un punto de venta"}, "ai_listing_input_selling_fit": {"message": "Ingrese los puntos de venta que coincidan con el título"}, "ai_listing_input_selling_please": {"message": "Por favor ingrese puntos de venta"}, "ai_listing_intelligently_title": {"message": "Ingrese el contenido requerido arriba para generar el título de manera inteligente"}, "ai_listing_keyword_product_title": {"message": "Título del producto con palabra clave"}, "ai_listing_keywords_repeated": {"message": "Las palabras clave no se pueden repetir"}, "ai_listing_listed_selling_points": {"message": "Puntos de venta incluidos"}, "ai_listing_long_title_1": {"message": "Contiene información básica como marca, tipo de producto, características del producto, etc."}, "ai_listing_long_title_2": {"message": "A partir del título estándar del producto se añaden palabras clave que favorecen el SEO."}, "ai_listing_long_title_3": {"message": "Además de contener el nombre de la marca, el tipo de producto, las características del producto y las palabras clave, también se incluyen palabras clave de cola larga para lograr clasificaciones más altas en consultas de búsqueda segmentadas específicas."}, "ai_listing_longtail_keyword_product_title": {"message": "Título del producto con palabras clave de cola larga"}, "ai_listing_manually_enter": {"message": "Ingrese manualmente..."}, "ai_listing_network_not_working": {"message": "Internet no está disponible, se requiere VPN para acceder a ChatGPT"}, "ai_listing_new_dictionary": {"message": "Crea una nueva biblioteca de palabras..."}, "ai_listing_new_generate": {"message": "Generar"}, "ai_listing_optional_words": {"message": "Palabras opcionales"}, "ai_listing_original_title": {"message": "Titulo original"}, "ai_listing_other_keywords_included": {"message": "Otras palabras clave incluidas:"}, "ai_listing_please_again": {"message": "Inténtalo de nuevo"}, "ai_listing_please_select": {"message": "Los siguientes títulos han sido generados para usted, seleccione:"}, "ai_listing_product_category": {"message": "Categoria de producto"}, "ai_listing_product_category_is": {"message": "La categoría del producto es"}, "ai_listing_product_category_to": {"message": "¿A qué categoría pertenece el producto?"}, "ai_listing_random_keywords": {"message": "Palabras clave aleatorias de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Puntos de venta aleatorios de $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Aleatorizar de la biblioteca de palabras"}, "ai_listing_search_selling": {"message": "Buscar por punto de venta"}, "ai_listing_select_product_categories": {"message": "Seleccione automáticamente categorías de productos."}, "ai_listing_select_product_selling_points": {"message": "Seleccionar automáticamente los puntos de venta de productos."}, "ai_listing_select_word_library": {"message": "Seleccionar biblioteca de palabras"}, "ai_listing_selling": {"message": "Puntos de venta"}, "ai_listing_selling_ask": {"message": "¿Qué otros requisitos de venta existen para el título?"}, "ai_listing_selling_optional": {"message": "Puntos de venta opcionales"}, "ai_listing_selling_repeat": {"message": "Los puntos no se pueden duplicar"}, "ai_listing_set_excluded": {"message": "Establecer como biblioteca de palabras excluida"}, "ai_listing_set_include_selling_points": {"message": "Incluir puntos de venta"}, "ai_listing_set_included": {"message": "Establecer como biblioteca de palabras incluida"}, "ai_listing_set_selling_dictionary": {"message": "Establecer como biblioteca de puntos de venta"}, "ai_listing_standard_product_title": {"message": "Título de producto estándar"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON><PERSON><PERSON> traduc<PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Επισκεφτείτε το ChatGPT"}, "ai_listing_what_other_keywords": {"message": "¿Qué otras palabras clave se requieren para el título?"}, "aliprice_coupons_apply_again": {"message": "Aplicar de nuevo"}, "aliprice_coupons_apply_coupons": {"message": "Obtienes el mejor precio!"}, "aliprice_coupons_apply_success": {"message": "Cupón encontrado: Ahorre $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Probando códigos para las mejores ofertas..."}, "aliprice_coupons_applying_desc": {"message": "Echando un vistazo: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continuar a la comprobación"}, "aliprice_coupons_found_coupons": {"message": "Encontramos $amount$ cupones", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "¿Listo para pagar? asegurémonos"}, "aliprice_coupons_no_coupon_aviable": {"message": "Esos códigos no funcionaron.\nNo hay problema, ya estás\nobteniendo el mejor precio."}, "aliprice_coupons_toolbar_btn": {"message": "Obtener cupones"}, "aliww_translate": {"message": "Μετα<PERSON>ραστής συνομιλ<PERSON><PERSON><PERSON>"}, "aliww_translate_supports": {"message": "Υποστήριξη: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Palabras clave"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON> todo"}, "amazon_extended_keywords_more": {"message": "Más"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Ordenar por ventas acumuladas"}, "an_lei_xing_cha_kan": {"message": "Tipo"}, "an_yue_dai_xiao_pai_xu": {"message": "Ranking por ventas dropshipping"}, "apra_btn__cat_name": {"message": "Análisis de reseñas"}, "apra_chart__name": {"message": "Porcentaje de ventas de productos por país"}, "apra_chart__update_at": {"message": "Hora de actualización $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Estadísticas de ventas de países"}, "auto_opening": {"message": "Se abrirán automáticamente en $num$ segundos", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Siguiente $autoPaging$ paginas", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Promedio en días de estantería"}, "average_hui_fu_lv": {"message": "Tasa de respuesta promedio"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Nivel medio de proveedores"}, "average_price": {"message": "<PERSON><PERSON> promedio"}, "average_qi_ding_liang": {"message": "MOQ promedio"}, "average_rating": {"message": "Puntuación media"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Años promedio de certificación"}, "average_revenue": {"message": "Ingresos promedio"}, "average_revenue_per_product": {"message": "Ingresos totales ÷ Número de productos"}, "average_sales": {"message": "Ventas promedio"}, "average_sales_per_product": {"message": "Ventas totales ÷ Número de productos"}, "bao_han": {"message": "Sisaldab"}, "bao_zheng_jin": {"message": "Margen"}, "bian_ti_shu": {"message": "Variaciones"}, "biao_ti": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "blacklist_add_blacklist": {"message": "Bloquear esta tienda"}, "blacklist_address_incorrect": {"message": "La dirección es incorrecta. Compruébelo por favor."}, "blacklist_blacked_out": {"message": "La tienda ha sido bloqueada"}, "blacklist_blacklist": {"message": "Lista negra"}, "blacklist_no_records_yet": {"message": "No hay registro todavía!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "intermediario de compras"}, "btn_aliprice_agent__dropshipping": {"message": "Envío directo"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON><PERSON> ahora"}, "btn_refresh": {"message": "Actualizar"}, "btn_try_it_now": {"message": "<PERSON><PERSON><PERSON><PERSON> ahora"}, "btn_txt_view_on_aliprice": {"message": "Ver en AliPrice"}, "bu_bao_han": {"message": "<PERSON>i sisalda"}, "bulk_copy_links": {"message": "Copia masiva de enlaces"}, "bulk_copy_products": {"message": "Copia masiva de productos"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON> al Cliente"}, "cai_gou_zi_xun__desc": {"message": "Tasa de respuesta de tres minutos del vendedor"}, "can_ping_lei_xing": {"message": "Tipo"}, "cao_zuo": {"message": "Operatsioon"}, "chan_pin_ID": {"message": "Toote ID"}, "chan_pin_e_wai_xin_xi": {"message": "Información adicional del producto"}, "chan_pin_lian_jie": {"message": "Toote link"}, "cheng_li_shi_jian": {"message": "tiempo de establecimiento"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Haga clic en Copiar información del producto"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 año"}, "common__date_range_1m": {"message": "1 mes"}, "common__date_range_1w": {"message": "1 semana"}, "common__date_range_2w": {"message": "2 semanas"}, "common__date_range_3m": {"message": "3 meses"}, "common__date_range_3w": {"message": "3 semanas"}, "common__date_range_6m": {"message": "6 meses"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "Guardar"}, "common_btn_setting": {"message": "Configuración"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "Sin datos"}, "common_error_msg_no_result": {"message": "<PERSON> sentimo<PERSON>, no result found."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "Realimentación"}, "common_help": {"message": "<PERSON><PERSON><PERSON>"}, "common_loading": {"message": "Cargando"}, "common_login": {"message": "In<PERSON><PERSON>"}, "common_logout": {"message": "<PERSON><PERSON><PERSON>"}, "common_no": {"message": "No"}, "common_powered_by_aliprice": {"message": "Desarrollado por AliPrice.com"}, "common_setting": {"message": "<PERSON><PERSON><PERSON>"}, "common_sign_up": {"message": "Regístrate"}, "common_system_upgrading_title": {"message": "Actualización del sistema"}, "common_system_upgrading_txt": {"message": "Por favor intente más tarde"}, "common_txt__currency": {"message": "Moneda"}, "common_txt__video_tutorial": {"message": "Vídeotutorial"}, "common_txt_ago_time": {"message": "$time$ hace días", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "todo"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Casi nunca usado"}, "common_txt_biaoti_link": {"message": "Título + Enlace"}, "common_txt_biaoti_link_dian_pu": {"message": "Título + enlace + nombre de la tienda"}, "common_txt_blacklist": {"message": "Lista de bloqueos"}, "common_txt_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_category": {"message": "Categoría"}, "common_txt_chakan": {"message": "Controlar"}, "common_txt_colors": {"message": "colores"}, "common_txt_confirm": {"message": "Confirmar"}, "common_txt_copied": {"message": "Copiado"}, "common_txt_copy": {"message": "Copiar"}, "common_txt_copy_link": {"message": "Copiar link"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON><PERSON> t<PERSON> y enlace"}, "common_txt_day": {"message": "cielo"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Eliminar"}, "common_txt_dian_pu_link": {"message": "Copiar nombre de la tienda + enlace"}, "common_txt_download": {"message": "<PERSON>car<PERSON>"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Exportar Excel"}, "common_txt_export_as_txt": {"message": "Exportar texto"}, "common_txt_fail": {"message": "<PERSON>ar"}, "common_txt_format": {"message": "Formato"}, "common_txt_get": {"message": "conseguir"}, "common_txt_incert_selection": {"message": "Invertir selección"}, "common_txt_install": {"message": "Instalar en pc"}, "common_txt_load_failed": {"message": "Falló al cargar"}, "common_txt_month": {"message": "mes"}, "common_txt_more": {"message": "Más"}, "common_txt_new_unused": {"message": "A estrenar, sin uso"}, "common_txt_next": {"message": "próximo"}, "common_txt_no_limit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_noticeable": {"message": "Sin arañazos ni suciedad visibles."}, "common_txt_on_sale": {"message": "Disponible"}, "common_txt_opt_in_out": {"message": "Encendido <PERSON>"}, "common_txt_order": {"message": "Pedido"}, "common_txt_others": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "En general mal estado"}, "common_txt_patterns": {"message": "patrones"}, "common_txt_platform": {"message": "Plataformas"}, "common_txt_please_select": {"message": "Por favor seleccione"}, "common_txt_prev": {"message": "Anterior"}, "common_txt_price": {"message": "Precio"}, "common_txt_privacy_policy": {"message": "Política de privacidad"}, "common_txt_product_condition": {"message": "Estado del producto"}, "common_txt_rating": {"message": "Clasificación"}, "common_txt_ratings": {"message": "Calificaciones"}, "common_txt_reload": {"message": "Recargar"}, "common_txt_reset": {"message": "Reiniciar"}, "common_txt_retail": {"message": "Venta minorista"}, "common_txt_review": {"message": "revisión"}, "common_txt_sale": {"message": "Disponible"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Con arañazos y suciedad"}, "common_txt_search_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_select_all": {"message": "Vali kõik"}, "common_txt_selected": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_share": {"message": "Compartir"}, "common_txt_sold": {"message": "vendido"}, "common_txt_sold_out": {"message": "<PERSON>tado"}, "common_txt_some_scratches": {"message": "Algunos rasguños y suciedad"}, "common_txt_sort_by": {"message": "Ordenar por"}, "common_txt_state": {"message": "Estado"}, "common_txt_success": {"message": "Éxito"}, "common_txt_sys_err": {"message": "error del sistema"}, "common_txt_today": {"message": "Hoy"}, "common_txt_total": {"message": "todos"}, "common_txt_unselect_all": {"message": "Valiku <PERSON>ö<PERSON>"}, "common_txt_upload_image": {"message": "<PERSON><PERSON> imagen"}, "common_txt_visit": {"message": "Visita"}, "common_txt_whitelist": {"message": "Lista blanca"}, "common_txt_wholesale": {"message": "<PERSON><PERSON><PERSON> al por mayor"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "si"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON> todo"}, "compare_tool_btn_compare": {"message": "Comparar"}, "compare_tool_btn_contact": {"message": "Contacto"}, "compare_tool_tips_max_compared": {"message": "Suma hasta $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configurar notificaciones"}, "contact_us": {"message": "Cont<PERSON><PERSON><PERSON>s"}, "context_menu_screenshot_search": {"message": "Capturar para buscar por imagen"}, "context_menus_aliprice_search_by_image": {"message": "La búsqueda de imágenes en AliPrice"}, "context_menus_goote_trans": {"message": "<PERSON><PERSON><PERSON><PERSON>/Mostrar original"}, "context_menus_search_by_image": {"message": "Búsqueda por imagen en $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Captura a $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Capturar para traducir"}, "converter_modal_amount_placeholder": {"message": "Ingrese la cantidad aquí"}, "converter_modal_btn_convert": {"message": "convertir"}, "converter_modal_exchange_rate_source": {"message": "Los datos provienen de la tasa de cambio de divisas de $boc$ Hora de actualización: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Conversión de Moneda"}, "converter_modal_search_placeholder": {"message": "moneda de búsqueda"}, "copy_all_contact_us_notice": {"message": "Este sitio no cuenta con soporte en este momento, por favor contáctenos"}, "copy_product_info": {"message": "Copiar información del producto"}, "copy_suggest_search_kw": {"message": "Copiar listas desplegables"}, "country__han_gou": {"message": "Corea del Sur"}, "country__ri_ben": {"message": "Japón"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Tipo de cambio personalizado"}, "currency_convert__sync_server": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> servidor"}, "dang_ri_fa_huo": {"message": "Envío el mismo día"}, "dao_chu_quan_dian_shang_pin": {"message": "Exportar todos los productos de la tienda"}, "dao_chu_wei_CSV": {"message": "Exportar"}, "dao_chu_zi_duan": {"message": "Ekspordi väljad"}, "delivery_address": {"message": "Dirección de envío"}, "delivery_company": {"message": "Compañía de entrega"}, "di_zhi": {"message": "DIRECCIÓN"}, "dian_ji_cha_xun": {"message": "Haga clic para consultar"}, "dian_pu_ID": {"message": "Kaupluse ID"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON> aadress"}, "dian_pu_lian_jie": {"message": "Poe link"}, "dian_pu_ming": {"message": "<PERSON><PERSON><PERSON><PERSON> nimi"}, "dian_pu_ming_cheng": {"message": "Nombre de la tienda"}, "dian_pu_shang_pin_zong_hsu": {"message": "Número total de productos en la tienda: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Almacenar información"}, "ding_zai_zuo_ce": {"message": "clavado a la izquierda"}, "download_image__SKU_variant_images": {"message": "Imágenes de variantes de SKU"}, "download_image__assume": {"message": "<PERSON><PERSON> e<PERSON><PERSON><PERSON>, tenemos 2 imágenes, product1.jpg y product2.gif.\nimg_{$no$} se renombrará a img_01.jpg, img_02.gif;\n{$group$}_{$no$} se renombrará a main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Descar<PERSON> por lotes"}, "download_image__combined_image": {"message": "Imagen de detalle del producto combinado"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__description_images": {"message": "Imágenes de descripción"}, "download_image__download_combined_image": {"message": "Descargar imagen de detalle del producto combinado"}, "download_image__download_zip": {"message": "Descargar zip"}, "download_image__enlarge_check": {"message": "Solo admite imágenes JPEG, JPG, GIF y PNG. Tamaño máximo de una sola imagen: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Ampliar imagen"}, "download_image__export": {"message": "Exportar"}, "download_image__height": {"message": "Alto"}, "download_image__ignore_videos": {"message": "El video ha sido ignorado, ya que no se puede exportar."}, "download_image__img_translate": {"message": "Traducir imagen"}, "download_image__main_image": {"message": "imagen principal"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "download_image__name": {"message": "descargar imagen"}, "download_image__notice_content": {"message": "¡No marque \"Preguntar dónde guardar cada archivo antes de descargarlo\" en la configuración de descarga de su navegador! De lo contrario, aparecerán muchos cuadros de diálogo."}, "download_image__notice_ignore": {"message": "No volver a solicitar este mensaje"}, "download_image__order_number": {"message": "{$no$} número de serie; {$group$} nombre de grupo; {$date$} marca de tiempo", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Visión de conjunto"}, "download_image__prompt_download_zip": {"message": "Hay demasiadas imágenes, es mejor que las descargues como una carpeta zip."}, "download_image__rename": {"message": "Cambiar nombre"}, "download_image__rule": {"message": "Reglas de nombres"}, "download_image__single_folder": {"message": "Carpeta única"}, "download_image__sku_image": {"message": "SKU imágenes"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "<PERSON><PERSON>"}, "download_reviews__download_images": {"message": "<PERSON><PERSON><PERSON> imagen de reseña"}, "download_reviews__dropdown_title": {"message": "<PERSON><PERSON><PERSON> imagen de reseña"}, "download_reviews__export_csv": {"message": "exportar CSV"}, "download_reviews__no_images": {"message": "Copia de muestra: 0 imágenes para descargar"}, "download_reviews__no_reviews": {"message": "¡Ninguna revisión para descargar!"}, "download_reviews__notice": {"message": "Consejo:"}, "download_reviews__notice__chrome_settings": {"message": "Configure el navegador Chrome para preguntar dónde guardar cada archivo antes de descargarlo, configure en \"Desactivado\""}, "download_reviews__notice__wait": {"message": "Dependiendo de la cantidad de revisiones, el tiempo de espera puede ser más largo"}, "download_reviews__pages_list__all": {"message": "Todo"}, "download_reviews__pages_list__page": {"message": "Páginas $page$ anteriores", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON> de se<PERSON>cción"}, "export_shopping_cart__csv_filed__details_url": {"message": "Enlace del producto"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Enlace SKU de eco"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON> imágen"}, "export_shopping_cart__csv_filed__quantity": {"message": "Cantidad"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Precio"}, "export_shopping_cart__csv_filed__specs": {"message": "Especificaciones"}, "export_shopping_cart__csv_filed__store_name": {"message": "Nombre de la tienda"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON><PERSON> de la tienda"}, "export_shopping_cart__csv_filed__title": {"message": "Nombre del producto"}, "export_shopping_cart__export_btn": {"message": "Exportar"}, "export_shopping_cart__export_empty": {"message": "¡Por favor seleccione un producto!"}, "fa_huo_shi_jian": {"message": "Envío"}, "favorite_add_email": {"message": "Agregar correo electrónico"}, "favorite_add_favorites": {"message": "Agregar a favoritos"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Alerta de baja de precios."}, "favorite_btn_notify": {"message": "Precio de la pista"}, "favorite_cate_name_all": {"message": "Todos los productos"}, "favorite_current_price": {"message": "Precio actual"}, "favorite_due_date": {"message": "<PERSON><PERSON>nc<PERSON>o"}, "favorite_enable_notification": {"message": "Habilitar notificaciones por correo electrónico"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "Ir a habilitar"}, "favorite_msg_add_success": {"message": "Añadir a favoritos"}, "favorite_msg_del_success": {"message": "Eliminar de favoritos"}, "favorite_msg_failure": {"message": "Fallar! Actualización de la página y vuelve a intentarlo."}, "favorite_please_add_email": {"message": "Agregar correo electrónico"}, "favorite_price_drop": {"message": "Abajo"}, "favorite_price_rise": {"message": "Arriba"}, "favorite_price_untracked": {"message": "Precio sin seguimiento"}, "favorite_saved_price": {"message": "<PERSON><PERSON> guardado"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON> de hacer se<PERSON>o"}, "favorite_sub_email_address": {"message": "Dirección de correo electrónico de suscripción"}, "favorite_tracking_period": {"message": "Período <PERSON>gu<PERSON>o"}, "favorite_tracking_prices": {"message": "Seguimiento de precios"}, "favorite_verify_email": {"message": "Verificar dirección de correo electrónico"}, "favorites_list_remove_prompt_msg": {"message": "¿Estás seguro de eliminarlo?"}, "favorites_update_button": {"message": "Actualizar precios ahora"}, "fen_lei": {"message": "Categoría"}, "fen_xia_yan_xuan": {"message": "Elección del distribuidor"}, "find_similar": {"message": "Buscar similar"}, "first_ali_price_date": {"message": "La fecha en que fue capturado por primera vez por el rastreador AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Sin cupones"}, "fooview_coupons_modal_title": {"message": "Cupones"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Precio < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "o precio> $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON> lí<PERSON>"}, "fooview_favorites_error_msg_no_favorites": {"message": "Añada los productos favoritos aquí para recibir la alerta de precio."}, "fooview_favorites_filter_latest": {"message": "Las últimas"}, "fooview_favorites_filter_price_drop": {"message": "ABAJO"}, "fooview_favorites_filter_price_up": {"message": "ARRIBA"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, "fooview_favorites_track_price": {"message": "Para rastrear el precio"}, "fooview_price_history_app_price": {"message": "APP Precio :"}, "fooview_price_history_title": {"message": "Control de precios"}, "fooview_product_list_feedback": {"message": "Retroalimentacón"}, "fooview_product_list_orders": {"message": "Orden"}, "fooview_product_list_price": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "No hemos encontrado ninguna revisión acerca de este producto."}, "fooview_reviews_filter_buyer_reviews": {"message": "Fotos de compradores"}, "fooview_reviews_modal_title": {"message": "Comentarios"}, "fooview_same_product_choose_category": {"message": "Elegir categoría"}, "fooview_same_product_filter_feedback": {"message": "Retroalimentacón"}, "fooview_same_product_filter_orders": {"message": "Orden"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Clasificación"}, "fooview_same_product_modal_title": {"message": "Busca el mismo producto"}, "fooview_same_product_search_by_image": {"message": "Búsqueda por imagen"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "for_12_months": {"message": "Por 1 año"}, "for_12_months_list_pro": {"message": "12 meses"}, "for_12_months_nei": {"message": "En un plazo de 12 meses"}, "for_1_months": {"message": "1 mes"}, "for_1_months_nei": {"message": "En un plazo de 1 mes"}, "for_3_months": {"message": "Por 3 meses"}, "for_3_months_nei": {"message": "En un plazo de 3 meses"}, "for_6_months": {"message": "Por 6 meses"}, "for_6_months_nei": {"message": "En un plazo de 6 meses"}, "for_9_months": {"message": "9 meses"}, "for_9_months_nei": {"message": "En un plazo de 9 meses"}, "fu_gou_lv": {"message": "Tasa de recompra"}, "gao_liang_bu_tong_dian": {"message": "resaltar las diferencias"}, "gao_liang_guang_gao_chan_pin": {"message": "Destacar productos publicitarios"}, "geng_duo_xin_xi": {"message": "Más información"}, "geng_xin_shi_jian": {"message": "Hora de actualización"}, "get_store_products_fail_tip": {"message": "Haga clic en Aceptar para ir a la verificación y garantizar el acceso normal."}, "gong_x_kuan_shang_pin": {"message": "Un total de $amount$ productos", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Tarnija"}, "gong_ying_shang_ID": {"message": "Tarnija ID"}, "gong_ying_shang_deng_ji": {"message": "Tarnija reiting"}, "gong_ying_shang_nian_zhan": {"message": "El proveedor es mayor"}, "gong_ying_shang_xin_xi": {"message": "información del proveedor"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Tarnija kodulehe link"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Eeldatav müügihind"}, "guan_jian_zi": {"message": "Märksõna"}, "guang_gao_chan_pin": {"message": "Productos de anuncios"}, "guang_gao_zhan_bi": {"message": "Proporción de anuncios"}, "guo_ji_wu_liu_yun_fei": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "Calificación positiva"}, "highest_price": {"message": "Alto"}, "historical_trend": {"message": "Tendencia histórica"}, "how_to_screenshot": {"message": "Mantenga presionado el botón izquierdo del mouse para seleccionar el área, toque el botón derecho del mouse o la tecla Esc para salir de la captura de pantalla."}, "howt_it_works": {"message": "Cómo funciona"}, "hui_fu_lv": {"message": "Reageerimismäär"}, "hui_tou_lv": {"message": "tasa de retorno"}, "inquire_freightFee": {"message": "Consulta de flete"}, "inquire_freightFee_Yuan": {"message": "Flete/Yuan"}, "inquire_freightFee_province": {"message": "Provincia"}, "inquire_freightFee_the": {"message": "El flete es de $num$, lo que significa que la región tiene envío gratuito.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON>."}, "jia_ge": {"message": "Hind"}, "jia_ge_dan_wei": {"message": "Unidad"}, "jia_ge_qu_shi": {"message": "Tendencias"}, "jia_zai_n_ge_shang_pin": {"message": "Cargar $num$ productos", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Porcentaje del volumen de ventas en los últimos 30 días"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Porcentaje de ingresos en los últimos 30 días"}, "jin_30d_xiao_liang": {"message": "Ventas"}, "jin_30d_xiao_liang__desc": {"message": "Ventas totales en los últimos 30 días."}, "jin_30d_xiao_shou_e": {"message": "Rotación"}, "jin_30d_xiao_shou_e__desc": {"message": "Facturación total en los últimos 30 días"}, "jin_90_tian_mai_jia_shu": {"message": "Ostjad viimase 90 päeva jooksul"}, "jin_90_tian_xiao_shou_liang": {"message": "Müük viimase 90 päeva jooksul"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON>s se<PERSON>"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON> de nego<PERSON>"}, "jing_ying_mo_shi__gong_chang": {"message": "Fabricante"}, "jiu_fen_jie_jue": {"message": "Resolución de conflictos"}, "jiu_fen_jie_jue__desc": {"message": "Contabilidad de las disputas sobre derechos de tienda de los vendedores"}, "jiu_fen_lv": {"message": "Tasa de disputa"}, "jiu_fen_lv__desc": {"message": "Proporción de pedidos con quejas completadas en los últimos 30 días y considerados responsabilidad del vendedor o de ambas partes"}, "kai_dian_ri_qi": {"message": "Fecha de apertura"}, "keywords": {"message": "Palabras clave"}, "kua_jin_Select_pan_huo": {"message": "Selección transfronteriza"}, "last15_days": {"message": "Últimos 15 días"}, "last180_days": {"message": "Últimos 180 días"}, "last30_days": {"message": "En los últimos 30 días"}, "last360_days": {"message": "Últimos 360 días"}, "last45_days": {"message": "Últimos 45 días"}, "last60_days": {"message": "Últimos 60 días"}, "last7_days": {"message": "Últimos 7 días"}, "last90_days": {"message": "Últimos 90 días"}, "last_30d_sales": {"message": "Ventas de los últimos 30 días"}, "lei_ji": {"message": "Acumulativo"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "Todas las ventas después del producto en el estante."}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volumen de ventas acumulado en los últimos 30 días, ordenado de mayor a menor"}, "lian_xi_fang_shi": {"message": "Información del contacto"}, "list_time": {"message": "<PERSON><PERSON>nc<PERSON>o"}, "load_more": {"message": "<PERSON>gar más"}, "login_to_aliprice": {"message": "Inicie sesión en AliPrice"}, "long_link": {"message": "<PERSON>lace largo"}, "lowest_price": {"message": "<PERSON><PERSON>"}, "mai_jia_shu": {"message": "Vendedores"}, "mao_li_lv": {"message": "<PERSON>gen bruto"}, "mobile_view__dkxbqy": {"message": "Abrir una nueva pestaña"}, "mobile_view__sjdxq": {"message": "Detalles en la aplicación"}, "mobile_view__sjdxqy": {"message": "Página de detalles en la aplicación"}, "mobile_view__smck": {"message": "Escanear para ver"}, "mobile_view__smckms": {"message": "Utilice la cámara o la aplicación para escanear y ver"}, "modified_failed": {"message": "La modificación falló"}, "modified_successfully": {"message": "Modificado exitosamente"}, "nav_btn_favorites": {"message": "Mis colecciones"}, "nav_btn_package": {"message": "paquete"}, "nav_btn_product_info": {"message": "Sobre el producto"}, "nav_btn_viewed": {"message": "Visto"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON><PERSON>"}, "normal_link": {"message": "Enlace normal"}, "notice": {"message": "insinuación"}, "number_reviews": {"message": "Reseñas"}, "only_show_num": {"message": "Total de productos: $allnum$, Oculto: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Eliminar sin marcar"}, "open": {"message": "Abrir"}, "open_links": {"message": "<PERSON><PERSON><PERSON> enlaces"}, "options_page_tab_check_links": {"message": "Comprobar enlaces"}, "options_page_tab_gernal": {"message": "General"}, "options_page_tab_notifications": {"message": "Notificaciones"}, "options_page_tab_others": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "Búsqueda por imagen"}, "options_page_tab_shortcuts": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_shortcuts_title": {"message": "Tamaño de fuente para accesos directos"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON>"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Varios enlaces de productos están a punto de abrirse"}, "order_list_sku_show_title": {"message": "Mostrar variantes seleccionadas en los enlaces compartidos"}, "orders_last30_days": {"message": "Número de pedidos en los últimos 30 días"}, "pTutorial_favorites_block1_desc1": {"message": "Los productos que rastreó se enumeran aquí"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Una etiqueta verde significa que hay productos con precios reducidos"}, "pTutorial_popup_block1_title": {"message": "Atajos y favoritos"}, "pTutorial_price_history_block1_desc1": {"message": "Haga clic en \"Seguir precio\", agregue productos a Favoritos. Una vez que sus precios bajen, recibirás notificaciones"}, "pTutorial_price_history_block1_title": {"message": "Precio de la pista"}, "pTutorial_reviews_block1_desc1": {"message": "Comentarios de los compradores de Itao y fotos reales de los comentarios de AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Comentarios"}, "pTutorial_reviews_block2_desc1": {"message": "Siempre es útil consultar las opiniones de otros compradores"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON><PERSON>es compararlos para hacer la mejor elección"}, "pTutorial_same_products_block1_desc2": {"message": "Haga clic en \"Más\" para \"Buscar por imagen\""}, "pTutorial_same_products_block1_title": {"message": "Mismos productos"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Coloque la imagen del producto allí y elija una categoría"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Buscar por imagen"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Tasa de retroalimentación positiva del vendedor, puntajes de retroalimentación y cuánto tiempo ha estado en el mercado"}, "pTutorial_seller_analysis_block1_title": {"message": "Calificación del vendedor"}, "pTutorial_seller_analysis_block2_desc2": {"message": "La calificación del vendedor se basa en 3 índices: artículo como se describe, velocidad de envío de comunicación"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Utilizamos 3 colores e íconos para indicar los niveles de confianza de los vendedores"}, "page_count": {"message": "Número de páginas"}, "pai_chu": {"message": "excluido"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Excluir Hong Kong: restringido"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Excluir Japón: restringido"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Excluir Corea: restringido"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Excluir <PERSON>: restringido"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Excluir <PERSON>: restringido"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Excluir Europa del Este: restringido"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Excluir Arabia Saudita: restringido"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Excluir <PERSON>: restringido"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Excluir EE. UU.: restringido"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Excluir Vietnam: restringido"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Excluir artículos restringidos"}, "payable_price_formula": {"message": "Precio + Envío + Descuento"}, "pdd_check_retail_btn_txt": {"message": "Consultar minorista"}, "pdd_pifa_to_retail_btn_txt": {"message": "Comprar al por menor"}, "pdp_copy_fail": {"message": "Copia fallida"}, "pdp_copy_success": {"message": "¡Copia realizada correctamente!"}, "pdp_share_modal_subtitle": {"message": "Comparte captura de pantalla, verá tu elección."}, "pdp_share_modal_title": {"message": "Comparte tu elección"}, "pdp_share_screenshot": {"message": "Compartir captura de pantalla"}, "pei_song": {"message": "M<PERSON><PERSON><PERSON>"}, "pin_lei": {"message": "Categoría"}, "pin_zhi_ti_yan": {"message": "Calidad del producto"}, "pin_zhi_ti_yan__desc": {"message": "Tasa de reembolso de calidad de la tienda del vendedor."}, "pin_zhi_tui_kuan_lv": {"message": "Tasa de reembolso"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proporción de pedidos que solo han sido reembolsados y devueltos en los últimos 30 días"}, "ping_fen": {"message": "Clasificación"}, "ping_jun_fa_huo_su_du": {"message": "Keskmine saatmiskiirus"}, "pkgInfo_hide": {"message": "Información de logística: encendido/apagado"}, "pkgInfo_no_trace": {"message": "Sin información de logística"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "venta al por mayor"}, "platform_name__shopee": {"message": "comprar"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "página de inicio"}, "popup_go_to_platform": {"message": "Ir a $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Estoy haciendo compras para..."}, "popup_track_package_btn_track": {"message": "Segu<PERSON><PERSON><PERSON>"}, "popup_track_package_desc": {"message": "SEGUIMIENTO DE PAQUETES TODO-EN-UNO"}, "popup_track_package_search_placeholder": {"message": "El número de rastreo"}, "popup_translate_search_placeholder": {"message": "Traducir y buscar en $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Control de precios"}, "price_history_chart_tip_ae": {"message": "Consejo: el número de pedidos es el número acumulado de pedidos desde el lanzamiento."}, "price_history_chart_tip_coupang": {"message": "Consejo: Coupang eliminará el recuento de pedidos fraudulentos"}, "price_history_inm_1688_l1": {"message": "Por favor instalar"}, "price_history_inm_1688_l2": {"message": "Asistente de compras AliPrice para <PERSON>"}, "price_history_panel_lowest_price": {"message": "El precio más bajo: "}, "price_history_panel_tab_price_tracking": {"message": "Control de precios"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "price_history_pro_modal_title": {"message": "Historial de precios e historial de pedidos"}, "privacy_consent__btn_agree": {"message": "Revisar el consentimiento de recopilación de datos"}, "privacy_consent__btn_disable_all": {"message": "No aceptar"}, "privacy_consent__btn_enable_all": {"message": "Activar todo"}, "privacy_consent__btn_uninstall": {"message": "Eliminar"}, "privacy_consent__desc_privacy": {"message": "Tenga en cuenta que, sin datos o cookies, algunas funciones estarán desactivadas porque esas funciones necesitan la explicación de datos o cookies, pero aún puede usar las otras funciones."}, "privacy_consent__desc_privacy_L1": {"message": "Desafortunadamente, sin datos o cookies no funcionará porque necesitamos la explicación de los datos o las cookies."}, "privacy_consent__desc_privacy_L2": {"message": "Si no nos permite recopilar esta información, elimínela."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, solo obtenemos sus datos de moneda en cookies cuando compramos en línea para mostrar el historial de precios."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON> requeridas"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Agregue cookies en el navegador para identificar de forma anónima su computadora o dispositivo."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Agregue datos funcionales en el complemento para trabajar con la función."}, "privacy_consent__item_functional_title": {"message": "Cookies funcionales y analíticas"}, "privacy_consent__more_desc": {"message": "Tenga en cuenta que no compartimos sus datos personales con otras empresas y que ninguna empresa de publicidad recopila datos a través de nuestro servicio."}, "privacy_consent__options__btn__desc": {"message": "Para usar todas las funciones, debe activarlo."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__options__desc_L1": {"message": "Recopilaremos los siguientes datos que lo identifican personalmente:"}, "privacy_consent__options__desc_L2": {"message": "- Cook<PERSON>, solo obtenemos sus datos de moneda en Cookies cuando realiza compras en línea para mostrar el historial de precios."}, "privacy_consent__options__desc_L3": {"message": "- y agregue Cookies en el navegador para identificar anónimamente su computadora o dispositivo."}, "privacy_consent__options__desc_L4": {"message": "- otros datos anónimos hacen que esta extensión sea más conveniente."}, "privacy_consent__options__desc_L5": {"message": "Tenga en cuenta que no compartimos sus datos personales con otras compañías y ninguna compañía de publicidad recopila datos a través de nuestro servicio."}, "privacy_consent__privacy_preferences": {"message": "Preferencias de privacidad"}, "privacy_consent__read_more": {"message": "<PERSON>r más >>"}, "privacy_consent__title_privacy": {"message": "Intimidad"}, "product_info": {"message": "Información del producto"}, "product_recommend__name": {"message": "<PERSON><PERSON>"}, "product_research": {"message": "Investigación de productos"}, "product_sub__email_desc": {"message": "Correo electrónico de alerta de precio"}, "product_sub__email_edit": {"message": "editar"}, "product_sub__email_not_verified": {"message": "Verifique el correo electrónico"}, "product_sub__email_required": {"message": "Por favor proporcione un correo electrónico"}, "product_sub__form_countdown": {"message": "Cierre automático después de $seconds$ segundos", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "No se pudo agregar el recordatorio."}, "product_sub__form_input_price": {"message": "precio de entrada"}, "product_sub__form_item_country": {"message": "nación"}, "product_sub__form_item_current_price": {"message": "Precio actual"}, "product_sub__form_item_duration": {"message": "pista"}, "product_sub__form_item_higher_price": {"message": "O precio>"}, "product_sub__form_item_invalid_higher_price": {"message": "El precio debe ser superior a $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "El precio debe ser inferior a $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Cuando el precio <"}, "product_sub__form_submit": {"message": "Enviar"}, "product_sub__form_success": {"message": "¡Tuvo éxito en agregar un recordatorio!"}, "product_sub__high_price_notify": {"message": "Notificarme de aumentos de precios"}, "product_sub__low_price_notify": {"message": "Notificarme de reducciones de precio"}, "product_sub__modal_title": {"message": "Recordatorio de cambio de precio de suscripción"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ y MOP"}, "qi_ye_mian_ji": {"message": "área empresarial"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Valige vähemalt üks toode"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Valige vähemalt üks väli"}, "qu_deng_lu": {"message": "In<PERSON><PERSON>"}, "quan_guo_yan_xuan": {"message": "Elección global"}, "recommendation_popup_banner_btn_install": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "recommendation_popup_banner_desc": {"message": "Mostrar historial de precios dentro de 3/6 meses y notificación de caída de precio"}, "region__all": {"message": "Todas las regiones"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Habilitar clic derecho"}, "ren_zheng_gong_chang": {"message": "Fábrica certificada"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarnija<PERSON>"}, "required_to_aliprice_login": {"message": "Necesito iniciar sesión en AliPrice"}, "revenue_last30_days": {"message": "Monto de ventas en los últimos 30 días"}, "review_counts": {"message": "Número de coleccionistas"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Período de entrada"}, "sales_amount_last30_days": {"message": "Ventas totales en los últimos 30 días."}, "sales_last30_days": {"message": "Ventas en los últimos 30 días."}, "sbi_alibaba_cate__accessories": {"message": "Accesorios"}, "sbi_alibaba_cate__aqfk": {"message": "Seguridad"}, "sbi_alibaba_cate__bags_cases": {"message": "Bolsas y Estuches"}, "sbi_alibaba_cate__beauty": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Cultura de oficina"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Bater<PERSON> de <PERSON>a"}, "sbi_alibaba_cate__clothes": {"message": "Vestir"}, "sbi_alibaba_cate__cmgd": {"message": "Medios de difusión"}, "sbi_alibaba_cate__coat_jacket": {"message": "Chaqueta de abrigo"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Electrónica de consumo"}, "sbi_alibaba_cate__cryp": {"message": "Productos para adultos"}, "sbi_alibaba_cate__csyp": {"message": "Ropa de cama"}, "sbi_alibaba_cate__cwyy": {"message": "Jardinería de mascotas"}, "sbi_alibaba_cate__cysx": {"message": "catering fresco"}, "sbi_alibaba_cate__dgdq": {"message": "Electricista"}, "sbi_alibaba_cate__dl": {"message": "Interino"}, "sbi_alibaba_cate__dress_suits": {"message": "Vestido y trajes"}, "sbi_alibaba_cate__dszm": {"message": "Encendiendo"}, "sbi_alibaba_cate__dzqj": {"message": "Dispositivo electronico"}, "sbi_alibaba_cate__essb": {"message": "Equipo usado"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Ropa y Accesorios"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "cuero textil"}, "sbi_alibaba_cate__ghjq": {"message": "Cuidado personal"}, "sbi_alibaba_cate__gt": {"message": "Acero"}, "sbi_alibaba_cate__gyp": {"message": "Artesanía"}, "sbi_alibaba_cate__hb": {"message": "Amigable con el medio ambiente"}, "sbi_alibaba_cate__hfcz": {"message": "maquillaje para el cuidado de la piel"}, "sbi_alibaba_cate__hg": {"message": "Industria química"}, "sbi_alibaba_cate__jg": {"message": "Procesando"}, "sbi_alibaba_cate__jianccai": {"message": "Materiales de construcción"}, "sbi_alibaba_cate__jichuang": {"message": "Herramienta de máquina"}, "sbi_alibaba_cate__jjry": {"message": "Uso doméstico diario"}, "sbi_alibaba_cate__jtys": {"message": "Transportación"}, "sbi_alibaba_cate__jxsb": {"message": "Equipo"}, "sbi_alibaba_cate__jxwj": {"message": "Hardware mecánico"}, "sbi_alibaba_cate__jydq": {"message": "Electrodomésticos"}, "sbi_alibaba_cate__jzjc": {"message": "Materiales de construcción para mejoras en el hogar"}, "sbi_alibaba_cate__jzjf": {"message": "Casa de textiles"}, "sbi_alibaba_cate__mj": {"message": "Toalla"}, "sbi_alibaba_cate__myyp": {"message": "Productos para bebés"}, "sbi_alibaba_cate__nanz": {"message": "de los hombres"}, "sbi_alibaba_cate__nvz": {"message": "Rop<PERSON> de mujer"}, "sbi_alibaba_cate__ny": {"message": "Energía"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Accesorios de auto"}, "sbi_alibaba_cate__qmpj": {"message": "Autopartes"}, "sbi_alibaba_cate__shoes": {"message": "Zapatos"}, "sbi_alibaba_cate__smdn": {"message": "Ordenador digital"}, "sbi_alibaba_cate__snqj": {"message": "Almacenamiento y limpieza"}, "sbi_alibaba_cate__spjs": {"message": "bebida de comida"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Juguete"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Pantalones y faldas"}, "sbi_alibaba_cate__txcp": {"message": "Productos de comunicación"}, "sbi_alibaba_cate__tz": {"message": "Ropa de ninos"}, "sbi_alibaba_cate__underwear": {"message": "Ropa interior"}, "sbi_alibaba_cate__wjgj": {"message": "herramientas de ferreteria"}, "sbi_alibaba_cate__xgpi": {"message": "Bolsas de cuero"}, "sbi_alibaba_cate__xmhz": {"message": "cooperación en proyectos"}, "sbi_alibaba_cate__xs": {"message": "C<PERSON>cho"}, "sbi_alibaba_cate__ydfs": {"message": "Ropa de deporte"}, "sbi_alibaba_cate__ydhw": {"message": "Deporte al aire libre"}, "sbi_alibaba_cate__yjkc": {"message": "Minerales metalúrgicos"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentación"}, "sbi_alibaba_cate__ys": {"message": "Imprimir"}, "sbi_alibaba_cate__yyby": {"message": "Atención médica"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Número de compradores en los últimos 90 días"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volumen de ventas en los últimos 90 días"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON> estimado"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Tarifa de envío internacional"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Gastos de envío"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON> estimado"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Beneficio = precio estimado x margen de beneficio"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Velocidad de entrega promedio"}, "sbi_alibaba_cn_kj_qtfy": {"message": "otra tarifa"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Otro costo = precio estimado x otra relación de costo"}, "sbi_alibaba_cn_kj_spjg": {"message": "Precio"}, "sbi_alibaba_cn_kj_spzl": {"message": "Peso"}, "sbi_alibaba_cn_kj_szd": {"message": "Localización"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Pie<PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Pie<PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Compradores"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Pie<PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Conjuntos"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Sin precio"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "precio"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Disponible para venta"}, "sbi_aliprice_alibaba_cn__moren": {"message": "defecto"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Ventas"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "muebles"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "bocadillo"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Ropa interior"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accesorios"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON> embotellada"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Electrónica"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Juguete"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Equipaje"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Fondos"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "zapato"}, "sbi_aliprice_cate__apparel": {"message": "Vestir"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "automoviles y motocicletas"}, "sbi_aliprice_cate__beauty_health": {"message": "<PERSON><PERSON> y <PERSON>za"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Teléfonos móviles y telecomunicaciones"}, "sbi_aliprice_cate__computer_office": {"message": "Computadora y oficina"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Electrónica de consumo"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Suministros de oficina y educación"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Componentes y suministros electrónicos"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Extensiones de cabello y pelucas"}, "sbi_aliprice_cate__home_garden": {"message": "Hogar & Jardín"}, "sbi_aliprice_cate__home_improvement": {"message": "Mejoras para el hogar"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Joyas y accesorios"}, "sbi_aliprice_cate__luggage_bags": {"message": "Equipaje y bolsos"}, "sbi_aliprice_cate__mother_kids": {"message": "Madre e hijos"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Novedad y uso especial"}, "sbi_aliprice_cate__security_protection": {"message": "Protección de seguridad"}, "sbi_aliprice_cate__shoes": {"message": "Zapatos"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Deportes y Entretenimiento"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Juguetes y pasatiempos"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Bodas y eventos"}, "sbi_btn_capture_txt": {"message": "Capturar"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON> ahora"}, "sbi_button__chat_with_me": {"message": "Chatea conmigo"}, "sbi_button__contact_supplier": {"message": "Contacto"}, "sbi_button__hide_on_this_site": {"message": "No mostrar en este sitio"}, "sbi_button__open_settings": {"message": "Configurar b<PERSON><PERSON><PERSON> por imagen"}, "sbi_capture_shortcut_tip": {"message": "o presione la tecla \"Enter\" en el teclado"}, "sbi_capturing_tip": {"message": "Capturar"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 estrellas"}, "sbi_crop_and_search": {"message": "Buscar"}, "sbi_crop_start": {"message": "Usar captura de pantalla"}, "sbi_err_captcha_action": {"message": "Verificar"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Necesita verificación, cargue una imagen para verificar. (Ver $video_tutorial$ o intentar borrar las cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Tráfico inusual, verifique"}, "sbi_err_captcha_for_taobao": {"message": "Taobao le solicita que verifique, cargue manualmente una imagen y busque para verificarla. Este error se debe a la nueva política de verificación \"Búsqueda de TaoBao por imagen\", le sugerimos que verifique la queja con demasiada frecuencia en Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "realimentación"}, "sbi_err_captcha_msg": {"message": "$platform$ requiere que cargues una imagen para realizar la búsqueda o completar la verificación de seguridad para eliminar las restricciones de búsqueda.", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Comprueba si es la última versión"}, "sbi_err_cookie_btn_clear": {"message": "Eliminar cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "¿Intentar borrar las cookies de 1688? (Necesita iniciar sesión nuevamente)"}, "sbi_err_desperate_feature_pdd": {"message": "La función de búsqueda de imágenes se ha trasladado a Pinduoduo Search by Image extensión."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "¿Cómo mejorar la tasa de éxito de la búsqueda de imágenes?"}, "sbi_err_img_undersize": {"message": "Imagen > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Inicie sesión $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "In<PERSON><PERSON>"}, "sbi_err_low_version": {"message": "Instale la última versión ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_network": {"message": "<PERSON><PERSON>r de red, aseg<PERSON>rate de poder visitar el sitio web"}, "sbi_err_not_low_version": {"message": "Se instaló la última versión ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Intentar otra vez"}, "sbi_err_try_again_action": {"message": "Intentar otra vez"}, "sbi_err_visit_and_try": {"message": "Inténtalo de nuevo o visita el $website$ para volver a intentarlo.", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Visite la página de inicio de $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON><PERSON> al cargar, actualice la página e intente nuevamente."}, "sbi_kuajing_filter_area": {"message": "Zona"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "Confirmar"}, "sbi_kuajing_filter_de": {"message": "Alemania"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON> de destino"}, "sbi_kuajing_filter_es": {"message": "España"}, "sbi_kuajing_filter_estimate": {"message": "Estimar"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON> estimado"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Fórmula de precio estimado = (precio de los productos básicos + flete logístico internacional) / (1 - margen de beneficio - relación de otros costos)"}, "sbi_kuajing_filter_fr": {"message": "Francia"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Ingrese palabras clave para que coincidan con el título"}, "sbi_kuajing_filter_logistics": {"message": "Plantilla de logística"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Correo aéreo de China"}, "sbi_kuajing_filter_logistics_discount": {"message": "Descuento logístico"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Flete logístico internacional = (peso x precio de envío + tarifa de registro) x (1 - descuento)"}, "sbi_kuajing_filter_others_fee": {"message": "Otra tarifa"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON><PERSON> de beneficio"}, "sbi_kuajing_filter_prop": {"message": "Atributos"}, "sbi_kuajing_filter_ru": {"message": "Rusia"}, "sbi_kuajing_filter_total": {"message": "Coincidir $count$ artículos similares", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "REINO UNIDO"}, "sbi_kuajing_filter_usa": {"message": "America"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> al por mayor"}, "sbi_msg_no_result": {"message": "No se encontraron resultados,inicie sesión en $loginSite$ o pruebe con otra imagen", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Temporalmente no disponible para Safari, utilice $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Navegador Chrome y sus extensiones"}, "sbi_msg_no_result_reinstall_l1": {"message": "No se encontraron resultados, inicie sesión en $loginSite$ o pruebe con otra imagen, o reinstale $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Ultima versión", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Buscar por área seleccionada"}, "sbi_shipping_": {"message": "Envío el mismo día"}, "sbi_specify_category": {"message": "Especificar categoría:"}, "sbi_start_crop": {"message": "Seleccionar zona"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 de ultramar"}, "sbi_tutorial_btn_more": {"message": "Más uso"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Encuentra cupones de Taobao"}, "sbi_txt__empty_retry": {"message": "Lo sentimos, no se han encontrado resultados, inténtelo de nuevo."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON> orden"}, "sbi_visiting": {"message": "Ho<PERSON>ada"}, "sbi_yiwugo__jiagexiangtan": {"message": "Póngase en contacto con el vendedor para conocer el precio"}, "sbi_yiwugo__qigou": {"message": "$num$ Piezas (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Estrellas"}, "searchByImage_screenshot": {"message": "Captura de pantalla con un clic"}, "searchByImage_search": {"message": "Búsqueda con un clic de los mismos artículos"}, "searchByImage_size_type": {"message": "El tamaño del archivo no puede ser mayor que $num$ MB, $type$ solamente", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON> imagen"}, "search_by_image_progress_searching": {"message": "Busca productos"}, "search_by_image_progress_sending": {"message": "Enviando imagen"}, "search_by_image_response_rate": {"message": "Tasa de respuesta: $responseRate$ de los compradores que se contactaron con este proveedor recibieron una respuesta dentro de $responseInHour$ horas.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Buscar por palabra clave:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Idioma"}, "select_country_region_modal_title": {"message": "Elige un país o una región"}, "select_language_modal_title": {"message": "Selecciona un idioma:"}, "select_shop": {"message": "Se<PERSON><PERSON><PERSON><PERSON> tienda"}, "sellers_count": {"message": "Número de vendedores en la página actual"}, "sellers_count_per_page": {"message": "Número de vendedores en la página actual"}, "service_score": {"message": "Calificación de servicio integral"}, "set_shortcut_keys": {"message": "Establecer teclas de acceso directo"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON> de compras"}, "setting_modal_options_position_title": {"message": "Posición de plug-in"}, "setting_modal_options_position_value_left": {"message": "A la izquierda"}, "setting_modal_options_position_value_right": {"message": "A la derecha"}, "setting_modal_options_theme_title": {"message": "Tema de color"}, "setting_modal_options_theme_value_dark": {"message": "oscuro"}, "setting_modal_options_theme_value_light": {"message": "Luz"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "País/Región"}, "setting_options_hover_zoom_desc": {"message": "Sitúa el cursor encima para hacer zoom"}, "setting_options_hover_zoom_title": {"message": "Despla<PERSON><PERSON> y hacer zoom"}, "setting_options_jd_coupon_desc": {"message": "Cupón encontrado en JD.com"}, "setting_options_jd_coupon_title": {"message": "Cupón de JD.com"}, "setting_options_language_title": {"message": "Idioma"}, "setting_options_price_drop_alert_desc": {"message": "Recibirás una notificación oportuna tan pronto como bajen los precios de productos en ¨Mis Favoritos¨"}, "setting_options_price_drop_alert_title": {"message": "Alerta de caída de precios"}, "setting_options_price_history_on_list_page_desc": {"message": "Se muestra el historial de precios en la página de búsqueda de productos"}, "setting_options_price_history_on_list_page_title": {"message": "Historial de precios (página de lista)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Se muestra el historial de productos en la página detallada de productos"}, "setting_options_price_history_on_produt_page_title": {"message": "Historial de precios (página de detalles)"}, "setting_options_sales_analysis_desc": {"message": "Admite estadísticas de precio, volumen de ventas, número de vendedores y proporción de ventas en tiendas en la página de lista de productos $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "Éxito"}, "setting_options_tacking_price_title": {"message": "Alerta de cambio de precio"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Soporte: 1688 y Taobao"}, "setting_saved_message": {"message": "Los cambios se guardaron exitosamente"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "Atajos :"}, "settings_aliprice_agent__desc": {"message": "Se muestra en la página de detalles del producto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Compra para mí"}, "settings_copy_link__desc": {"message": "Mostrar en la página de detalles del producto"}, "settings_copy_link__title": {"message": "Botón Copiar y Título de búsqueda"}, "settings_currency_desc__for_detail": {"message": "Soporte página de detalles del producto 1688"}, "settings_currency_desc__for_list": {"message": "Búsqueda por imagen (incluye 1688/1688 en el extranjero / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Seleccione el precio"}, "settings_currency_desc_display_for_list": {"message": "Se muestra en la búsqueda de imágenes (incluido 1688/1688 en el extranjero/Taobao)"}, "settings_currency_rate_desc": {"message": "Actualización del tipo de cambio desde \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Banco de China"}, "settings_download_images__desc": {"message": "Soporte para descargar imágenes de $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "boton descargar imagen"}, "settings_download_reviews__desc": {"message": "Se muestra en la página de detalles del producto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Descargar imágenes de revisión"}, "settings_google_translate_desc": {"message": "Haga clic derecho para obtener la barra de traducción de Google"}, "settings_google_translate_title": {"message": "traducción de páginas web"}, "settings_historical_trend_desc": {"message": "Mostrar en la esquina inferior derecha de la imagen en la página de lista de productos"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON>"}, "settings_productInfo_desc": {"message": "Mostrar información más detallada del producto en la página de lista de productos. Habilitar esta opción puede aumentar la carga del equipo y provocar retrasos en la página. Si afecta el rendimiento, se recomienda deshabilitarla."}, "settings_product_recommend__desc": {"message": "Se muestra debajo de la imagen principal en la página de detalles del producto $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Productos recomendados"}, "settings_research_desc": {"message": "Consultar información más detallada en la página de lista de productos"}, "settings_sbi_add_to_list": {"message": "Agregar a $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatura del resultado de la búsqueda de imágenes"}, "settings_sbi_remove_from_list": {"message": "Eliminar de $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Añadir a la lista negra"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Ajustar la posición de entrada"}, "settings_search_by_image_blacklist_desc": {"message": "No mostrar el icono en los sitios web de la lista negra."}, "settings_search_by_image_blacklist_title": {"message": "Lista negra"}, "settings_search_by_image_bottom_left": {"message": "Abajo a la izquierda"}, "settings_search_by_image_bottom_right": {"message": "Abajo a la derecha"}, "settings_search_by_image_clear_blacklist": {"message": "Borrar lista negra"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatura"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "Más grande"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Un clic en el icono para buscar por imagen"}, "settings_search_by_image_display_button_title": {"message": "Icono de imágenes"}, "settings_search_by_image_sourece_websites_desc": {"message": "Encuentre el producto de origen en estos sitios web"}, "settings_search_by_image_sourece_websites_title": {"message": "Buscar por resultado de imagen"}, "settings_search_by_image_top_left": {"message": "Arriba a la izquierda"}, "settings_search_by_image_top_right": {"message": "Arriba a la derecha"}, "settings_search_keyword_on_x__desc": {"message": "Buscar palabras en $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Mostrar el icono $platform$ cuando se seleccionen palabras", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Intente encontrar el mismo producto en esos sitios web (máximo 5)"}, "settings_similar_products_title": {"message": "Encuentra el mismo producto"}, "settings_toolbar_expand_title": {"message": "<PERSON><PERSON><PERSON>"}, "settings_top_toolbar_desc": {"message": "Barra de búsqueda en la parte superior de la página"}, "settings_top_toolbar_title": {"message": "Barra de búsqueda"}, "settings_translate_search_desc": {"message": "Traducir al chino y buscar"}, "settings_translate_search_title": {"message": "Búsqueda multilingüe"}, "settings_translator_contextmenu_title": {"message": "Capturar para traducir"}, "settings_translator_title": {"message": "Traducir"}, "shai_xuan_dao_chu": {"message": "Filtreerige ekspordiks"}, "shai_xuan_zi_duan": {"message": "Campos de filtro"}, "shang_jia_shi_jian": {"message": "En tiempo de espera"}, "shang_pin_biao_ti": {"message": "Titulo del producto"}, "shang_pin_dui_bi": {"message": "Comparación de productos"}, "shang_pin_lian_jie": {"message": "enlace del producto"}, "shang_pin_xin_xi": {"message": "Información del producto"}, "share_modal__content": {"message": "compartir con tus amigos"}, "share_modal__disable_for_while": {"message": "No quiero compartir nada"}, "share_modal__title": {"message": "¿Te gusta $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Disponibilidad"}, "shi_fou_ke_ding_zhi": {"message": "¿Es personalizable?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Sertifitseeritud tarnija"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Proveedores certificados"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Ka<PERSON>nduse tagamine"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> comercia<PERSON>"}, "shipping_fee": {"message": "Gastos de envío"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON> de la tienda"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Máximo a 5"}, "sku_calc_price": {"message": "Precio calculado"}, "sku_calc_price_settings": {"message": "Configuración del precio calculado"}, "sku_formula": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_formula_desc": {"message": "Descripción de la fórmula"}, "sku_formula_desc_text": {"message": "Admite fórmulas matemáticas complejas, representando el precio original con A y el flete con B.\n\n<br/>\n\nAdmite corchetes (), más +, menos -, multiplicación * y división /.\n\n<br/>\n\nEjemplo:\n\n<br/>\n\n1. Para multiplicar 1,2 veces el precio original y añadir el flete, la fórmula es: A*1,2+B\n\n<br/>\n\n2. Para multiplicar 1 yuan por el precio original y luego multiplicar por 1,2, la fórmula es: (A+1)*1,2\n\n<br/>\n\n3. Para multiplicar 10 yuan por el precio original y luego multiplicar por 1,2 y restar 3 yuanes, la fórmula es: (A+10)*1,2-3"}, "sku_in_stock": {"message": "En stock"}, "sku_invalid_formula_format": {"message": "Formato de fórmula no válido"}, "sku_inventory": {"message": "Inventario"}, "sku_link_copy_fail": {"message": "Copiado correctamente, las especificaciones y atributos del sku no están seleccionados"}, "sku_link_copy_success": {"message": "Copiado correctamente, especificaciones y atributos de sku seleccionados"}, "sku_list": {"message": "Lista de SKU"}, "sku_min_qrder_qty": {"message": "Cantidad mínima de pedido"}, "sku_name": {"message": "Nombre del SKU"}, "sku_no": {"message": "N.º"}, "sku_original_price": {"message": "Precio original"}, "sku_price": {"message": "Precio del SKU"}, "stop_track_time_label": {"message": "Fecha límite de seguimiento:"}, "suo_zai_di_qu": {"message": "ubicación"}, "tab_pkg_quick_view": {"message": "Monitor de Logística"}, "tab_product_details_price_history": {"message": "Historia"}, "tab_product_details_reviews": {"message": "Reseñas"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON>"}, "total_days_listed_per_product": {"message": "Suma de días en estantería ÷ Número de productos"}, "total_items": {"message": "Número total de productos"}, "total_price_per_product": {"message": "Suma de precios ÷ Número de productos"}, "total_rating_per_product": {"message": "Suma de calificaciones ÷ Número de productos"}, "total_revenue": {"message": "Los ingresos totales"}, "total_revenue40_items": {"message": "Ingresos totales de los $amount$ productos en la página actual", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Ventas totales"}, "total_sales40_items": {"message": "Ventas totales de los $amount$ productos de la página actual.", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Seguir para: 1 año"}, "track_for_3_months": {"message": "Seguir para: 3 meses"}, "track_for_6_months": {"message": "Seguir para: 6 meses"}, "tracking_price_email_add_btn": {"message": "Agregar correo electrónico"}, "tracking_price_email_edit_btn": {"message": "Editar correo electrónico"}, "tracking_price_email_intro": {"message": "Le notificaremos por correo electrónico."}, "tracking_price_email_invalid": {"message": "Por favor proporcione un correo electrónico válido"}, "tracking_price_email_verified_desc": {"message": "Ahora puede recibir nuestra alerta de caída de precios."}, "tracking_price_email_verified_title": {"message": "Verificado con éxito"}, "tracking_price_email_verify_desc_line1": {"message": "Hemos enviado un enlace de verificación a su dirección de correo electrónico,"}, "tracking_price_email_verify_desc_line2": {"message": "por favor revise su bandeja de entrada de correo electrónico."}, "tracking_price_email_verify_title": {"message": "Verificar correo electrónico"}, "tracking_price_web_push_notification_intro": {"message": "En el escritorio: AliPrice puede monitorear cualquier producto por usted y enviarle una Notificación Push Web una vez que el precio cambie."}, "tracking_price_web_push_notification_title": {"message": "Notificaciones web push"}, "translate_im__login_required": {"message": "Traducido por AliPrice, inicie sesión en $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Traducido y copiado al portapapeles, pero debido a la limitación de Aliwangwang, ¡debes pegarlo manualmente!"}, "translate_im__send": {"message": "Traducir y enviar"}, "translate_search": {"message": "Traducir y buscar"}, "translation_originals_translated": {"message": "Original y chino"}, "translation_translated": {"message": "Chino"}, "translator_btn_capture_txt": {"message": "Traducir"}, "translator_language_auto_detect": {"message": "Detección automática"}, "translator_language_detected": {"message": "Detectado"}, "translator_language_search_placeholder": {"message": "Idioma de búsqueda"}, "try_again": {"message": "Inténtalo de nuevo"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON> de la imagen:"}, "tu_pian_lian_jie": {"message": "Pildi link"}, "tui_huan_ti_yan": {"message": "Experiencia de regreso"}, "tui_huan_ti_yan__desc": {"message": "Evaluar los indicadores posventa de los vendedores."}, "tutorial__show_all": {"message": "Todas las características"}, "tutorial_ae_popup_title": {"message": "Καρ<PERSON>ιτσώστε την επέκταση, ανοίξτε το Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Análisis de revisión de AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Soporte USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Envío a Corea/Japón/China continental"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Υποστηρίζει αγορές στο εξωτερικό"}, "tutorial_auto_apply_coupon_title": {"message": "Αυτόματη εφαρμογή κουπονιού"}, "tutorial_btn_end": {"message": "Fin"}, "tutorial_btn_example": {"message": "Ejemplo"}, "tutorial_btn_have_a_try": {"message": "<PERSON> p<PERSON>o"}, "tutorial_btn_next": {"message": "Siguient<PERSON>"}, "tutorial_btn_see_more": {"message": "Más"}, "tutorial_compare_products": {"message": "Comparar productos"}, "tutorial_currency_convert_title": {"message": "Conversión de Moneda"}, "tutorial_export_shopping_cart": {"message": "Exportar como CSV, Soporte Taobao y 1688"}, "tutorial_export_shopping_cart_title": {"message": "Carrito de exportación"}, "tutorial_price_history_pro": {"message": "Se muestra en la página de detalles del producto.\nSoporte Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Todo el año Historial de precios e historial de pedidos"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Capturar para buscar por imagen"}, "tutorial_translate_search": {"message": "Traducir para buscar"}, "tutorial_translate_search_and_package_tracking": {"message": "Búsqueda de traducción y seguimiento de paquetes"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "pedidos"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "piezas"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "Contacto con el servicio de asistencia"}, "verify_human_verification": {"message": "Verificación humana"}, "verify_unusual_access": {"message": "Se detectó un acceso inusual"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> todo"}, "view_history_clean_all_warring": {"message": "¿Limpiar todos los registros vistos?"}, "view_history_clean_all_warring_title": {"message": "Advertencia"}, "view_history_viewd": {"message": "Visto"}, "website": {"message": "sitio web"}, "weight": {"message": "Peso"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "No se pueden obtener los datos"}, "wu_liu_shi_xiao": {"message": "Envío a tiempo"}, "wu_liu_shi_xiao__desc": {"message": "La tasa de cobro en 48 horas y la tasa de cumplimiento de la tienda del vendedor."}, "xia_dan_jia": {"message": "Precio final"}, "xian_xuan_ze_product_attributes": {"message": "Seleccione los atributos del producto"}, "xiao_liang": {"message": "Müügimaht"}, "xiao_liang_zhan_bi": {"message": "Porcentaje del volumen de ventas"}, "xiao_shi": {"message": "$num$ horas", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Ingresos"}, "xiao_shou_e_zhan_bi": {"message": "Porcentaje de ingresos"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Seleccionar $amount$ registros", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Elección"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Todos los productos cargados"}, "yi_nian_xiao_liang": {"message": "Ventas anuales"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Participación en ventas anuales"}, "yi_nian_xiao_shou_e": {"message": "Facturación anual"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Participación en facturación anual"}, "yi_shua_xin": {"message": "renovado"}, "yin_cang_xiang_tong_dian": {"message": "ocultar similitudes"}, "you_xiao_liang": {"message": "Müügimahuga"}, "yu_ji_dao_da_shi_jian": {"message": "Hora estimada de llegada"}, "yuan_gong_ren_shu": {"message": "Número de empleados"}, "yue_cheng_jiao": {"message": "Volumen mensual"}, "yue_dai_xiao": {"message": "Envío directo"}, "yue_dai_xiao__desc": {"message": "Ventas de dropshipping en los últimos 30 días"}, "yue_dai_xiao_pai_xu__desc": {"message": "Ventas de dropshipping en los últimos 30 días, ordenadas de mayor a menor"}, "yue_xiao_liang__desc": {"message": "Volumen de ventas en los últimos 30 días"}, "zhan_kai": {"message": "Más"}, "zhe_kou": {"message": "Descuento"}, "zhi_chi_yi_jian_dai_fa": {"message": "Envío directo"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON> gratis"}, "zhi_fu_ding_dan_shu": {"message": "Pedidos pagados"}, "zhi_fu_ding_dan_shu__desc": {"message": "Número de pedidos de este producto (30 días)"}, "zhu_ce_xing_zhi": {"message": "Naturaleza del registro"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "zi_duan": {"message": "Campos"}, "zi_ti_xiao_liang": {"message": "Variación vendida"}, "zong_he_fu_wu_fen": {"message": "Calificación general"}, "zong_he_fu_wu_fen__desc": {"message": "Calificación general del servicio del vendedor."}, "zong_he_fu_wu_fen__short": {"message": "Clasificación"}, "zong_he_ti_yan_fen": {"message": "Clasificación"}, "zong_he_ti_yan_fen_3": {"message": "Por debajo de 4 estrellas"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 estrellas"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 estrellas"}, "zong_he_ti_yan_fen_5": {"message": "5 estrellas"}, "zong_ku_cun": {"message": "Inventario total"}, "zong_xiao_liang": {"message": "Ventas totales"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Tasa de respuesta de 3 minutos en los últimos 30 días"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Tasa de recuperación de 48H en los últimos 30 días"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Tasa de rendimiento de 48H en los últimos 30 días"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Registro comercial (30 días)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Registro comercial (30 días)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Tasa de disputa en los últimos 30 días"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Tasa de reembolso de calidad en los últimos 30 días"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "El número de órdenes de pago en los últimos 30 días"}}