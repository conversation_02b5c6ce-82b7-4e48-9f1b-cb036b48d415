{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 क्रॉस-बॉर्डर हॉट सेलिंग स्पॉट"}, "1688_shi_li_ren_zheng": {"message": "1688 सामर्थ्य प्रमाणपत्र"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "1 वर्षापेक्षा जास्त"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24 तासांच्या आत वितरण"}, "24H_lan_shou_lv": {"message": "24-ता<PERSON> पॅकेजिंग दर"}, "30D_shang_xin": {"message": "मासिक नवीन आगमन"}, "30d_sales": {"message": "$amount$ 30 दिवसांत विकले", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3 मिनिटांच्या आत प्रतिसाद."}, "3Min_xiang_ying_lv__desc": {"message": "माग<PERSON><PERSON> 30 दिवसांत 3 मिनिटांत खरेदीदार चौकशी संदेशांना वांगवांगच्या प्रभावी प्रतिसादांचे प्रमाण"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "48 तासांच्या आत वितरण"}, "48H_lan_shou_lv": {"message": "48-ता<PERSON> पॅकेजिंग दर"}, "48H_lan_shou_lv__desc": {"message": "48 तासांच्या आत पिकअप केलेल्या ऑर्डर क्रमांकाचे एकूण ऑर्डरच्या संख्येचे गुणोत्तर"}, "48H_lv_yue_lv": {"message": "48-ता<PERSON> कामगिरी दर"}, "48H_lv_yue_lv__desc": {"message": "48 तासांच्या आत घेतलेल्या किंवा वितरित केलेल्या ऑर्डर क्रमांकाचे एकूण ऑर्डरच्या संख्येचे गुणोत्तर"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "साप्ताहिक नवीन आगमन"}, "7D_wu_li_you": {"message": "7 दिवस मोफत काळजी"}, "ABS_title_text": {"message": "या सूचीमध्ये ब्रँड कथा समाविष्ट आहे"}, "AC_title_text": {"message": "या सूचीमध्ये Amazon चा चॉईस बॅज आहे"}, "A_title_text": {"message": "या सूचीमध्ये A+ सामग्री पृष्ठ आहे"}, "BS_title_text": {"message": "ही सूची $type$ श्रेणीमध्ये $num$ बेस्ट सेलर म्हणून रँक केली आहे", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (मर्यादित वेळेची डील) म्हणजे ही सूची \"7-दिवसांच्या जाहिराती\" कार्यक्रमाचा भाग आहे"}, "NR_title_text": {"message": "या सूचीला $type$ श्रेणीमध्ये $num$ नवीन रिलीज म्हणून रँक केले आहे", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "या सूचीमध्ये एक व्हिडिओ जाहिरात आहे, एक प्रकारची PPC जाहिरात जी सहसा शोध परिणामांच्या मध्यभागी दिसते"}, "SB_title_text": {"message": "या सूचीमध्ये एक ब्रँड जाहिरात आहे, एक प्रकारची PPC जाहिरात जी सहसा शोध परिणामांच्या शीर्षस्थानी किंवा तळाशी दिसते"}, "SP_title_text": {"message": "या सूचीमध्ये प्रायोजित उत्पादन जाहिरात आहे"}, "V_title_text": {"message": "या सूचीमध्ये व्हिडिओ परिचय आहे"}, "advanced_research": {"message": "प्रगत संशोधन"}, "agent_ds1688___my_order": {"message": "माझे आदेश"}, "agent_ds1688__add_to_cart": {"message": "परदेशात खरेदी"}, "agent_ds1688__cart": {"message": "खरेदी कार्ट"}, "agent_ds1688__desc": {"message": "1688 द्वारे प्रदान केले गेले. हे परदेशातून थेट खरेदी, USD मध्ये पेमेंट आणि चीनमधील तुमच्या ट्रान्झिट वेअरहाऊसमध्ये वितरणास समर्थन देते."}, "agent_ds1688__freight": {"message": "शिपिंग खर्च कॅल्क्युलेटर"}, "agent_ds1688__help": {"message": "मद<PERSON> करा"}, "agent_ds1688__packages": {"message": "वेबिल"}, "agent_ds1688__profile": {"message": "वैयक्तिक केंद्र"}, "agent_ds1688__warehouse": {"message": "माझे कोठार"}, "ai_comment_analysis_advantage": {"message": "साधक"}, "ai_comment_analysis_ai": {"message": "एआय पुनरावलोकन विश्लेषण"}, "ai_comment_analysis_available": {"message": "उपलब्ध"}, "ai_comment_analysis_balance": {"message": "अपुरी नाणी, कृपया टॉप अप करा"}, "ai_comment_analysis_behavior": {"message": "वागणूक"}, "ai_comment_analysis_characteristic": {"message": "गर्दीची वैशिष्ट्ये"}, "ai_comment_analysis_comment": {"message": "अचूक निष्कर्ष काढण्यासाठी उत्पादनाकडे पुरेशी पुनरावलोकने नाहीत, कृपया अधिक पुनरावलोकनांसह उत्पादन निवडा."}, "ai_comment_analysis_consume": {"message": "अंदाजे वापर"}, "ai_comment_analysis_default": {"message": "डीफॉल्ट पुनरावलोकने"}, "ai_comment_analysis_desire": {"message": "ग्राहकांच्या अपेक्षा"}, "ai_comment_analysis_disadvantage": {"message": "बा<PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "मोफत प्रयत्न"}, "ai_comment_analysis_freeNum": {"message": "1 मोफत क्रेडिट वापरले जाईल"}, "ai_comment_analysis_go_recharge": {"message": "टॉप अप वर जा"}, "ai_comment_analysis_intelligence": {"message": "बुद्धिमान पुनरावलोकन विश्लेषण"}, "ai_comment_analysis_location": {"message": "स्थान"}, "ai_comment_analysis_motive": {"message": "खरेदी प्रेरणा"}, "ai_comment_analysis_network_error": {"message": "नेटवर्क एरर, कृपया पुन्हा प्रयत्न करा"}, "ai_comment_analysis_normal": {"message": "फोटो पुनरावलोकने"}, "ai_comment_analysis_number_reviews": {"message": "पुनरावलोकनांची संख्या: $num$, अंदाजे वापर: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "सामान्य टिप्पण्या"}, "ai_comment_analysis_percentage": {"message": "टक्केवारी"}, "ai_comment_analysis_problem": {"message": "पेमेंटमध्ये समस्या"}, "ai_comment_analysis_reanalysis": {"message": "पुन्हा विश्लेषण करा"}, "ai_comment_analysis_reason": {"message": "का<PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "टॉप अप"}, "ai_comment_analysis_recharged": {"message": "मी टॉप अप केले आहे"}, "ai_comment_analysis_retry": {"message": "पुन्हा प्रयत्न करा"}, "ai_comment_analysis_scene": {"message": "वापर परिस्थिती"}, "ai_comment_analysis_start": {"message": "विश्लेषण सुरू करा"}, "ai_comment_analysis_subject": {"message": "विषय"}, "ai_comment_analysis_time": {"message": "वापरण्याची वेळ"}, "ai_comment_analysis_tool": {"message": "एआय साधन"}, "ai_comment_analysis_user_portrait": {"message": "वापरकर्ता प्रोफाइल"}, "ai_comment_analysis_welcome": {"message": "AI पुनरावलोकन विश्लेषणामध्ये आपले स्वागत आहे"}, "ai_comment_analysis_year": {"message": "मागील वर्षातील टिप्पण्या"}, "ai_listing_Exclude_keywords": {"message": "कीवर्ड वगळून"}, "ai_listing_Login_the_feature": {"message": "वैशिष्ट्यासाठी लॉगिन आवश्यक आहे"}, "ai_listing_aI_generation": {"message": "एआय पिढी"}, "ai_listing_add_automatic": {"message": "स्वयंचलित"}, "ai_listing_add_dictionary_new": {"message": "नवीन लायब्ररी तयार करा"}, "ai_listing_add_enter_keywords": {"message": "कीवर्ड प्रविष्ट करा"}, "ai_listing_add_inputkey_selling": {"message": "विक्री बिंदू प्रविष्ट करा आणि जोडणे पूर्ण करण्यासाठी $key$ दाबा", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "मर्यादा ओलांडली, $amount$ विक्री पॉइंटपर्यंत", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "कीवर्ड जोडा"}, "ai_listing_add_manually": {"message": "व्यक्तिचलितपणे जोडा"}, "ai_listing_add_selling": {"message": "विक्री गुण जोडा"}, "ai_listing_added_keywords": {"message": "कीवर्ड जोडले"}, "ai_listing_added_successfully": {"message": "यशस्वीरित्या जोडले"}, "ai_listing_addexcluded_keywords": {"message": "वगळलेले कीवर्ड एंटर करा, जोडणे पूर्ण करण्यासाठी एंटर दाबा."}, "ai_listing_adding_selling": {"message": "विक्री गुण जोडले"}, "ai_listing_addkeyword_enter": {"message": "मुख्य विशेषता शब्द टाइप करा आणि जोडणे पूर्ण करण्यासाठी एंटर दाबा"}, "ai_listing_ai_description": {"message": "एआय वर्णन शब्द लायब्ररी"}, "ai_listing_ai_dictionary": {"message": "एआय शीर्षक शब्द लायब्ररी"}, "ai_listing_ai_title": {"message": "AI शीर्षक"}, "ai_listing_aidescription_repeated": {"message": "AI वर्णन शब्द लायब्ररी नावाची पुनरावृत्ती होऊ शकत नाही"}, "ai_listing_aititle_repeated": {"message": "AI शीर्षक शब्द लायब्ररी नावाची पुनरावृत्ती होऊ शकत नाही"}, "ai_listing_data_comes_from": {"message": "हा डेटा येथून येतो:"}, "ai_listing_deleted_successfully": {"message": "यशस्वीरित्या हटवले"}, "ai_listing_dictionary_name": {"message": "लायब्ररीचे नाव"}, "ai_listing_edit_dictionary": {"message": "लायब्ररी सुधारित करा..."}, "ai_listing_edit_word_library": {"message": "लायब्र<PERSON>ी शब्द संपादित करा"}, "ai_listing_enter_keywords": {"message": "कीवर्ड एंटर करा आणि जोडणे पूर्ण करण्यासाठी $key$ दाबा", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "मर्यादा ओलांडली आहे, कमाल $amount$ कीवर्ड", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "वगळलेले शब्द लायब्ररी"}, "ai_listing_generate_characters": {"message": "वर्ण तयार करा"}, "ai_listing_generation_platform": {"message": "जनरेशन प्लॅटफॉर्म"}, "ai_listing_help_optimize": {"message": "उत्पादन शीर्षक ऑप्टिमाइझ करण्यात मला मदत करा, मूळ शीर्षक आहे"}, "ai_listing_include_selling": {"message": "इतर विक्री बिंदूंचा समावेश आहे:"}, "ai_listing_included_keyword": {"message": "समाविष्ट कीवर्ड"}, "ai_listing_included_keywords": {"message": "समाविष्ट कीवर्ड"}, "ai_listing_input_selling": {"message": "विक्री बिंदू प्रविष्ट करा"}, "ai_listing_input_selling_fit": {"message": "शीर्षकाशी जुळण्यासाठी विक्री गुण प्रविष्ट करा"}, "ai_listing_input_selling_please": {"message": "कृपया विक्री बिंदू प्रविष्ट करा"}, "ai_listing_intelligently_title": {"message": "हुशारीने शीर्षक तयार करण्यासाठी वरील आवश्यक सामग्री प्रविष्ट करा"}, "ai_listing_keyword_product_title": {"message": "कीवर्ड उत्पादन शीर्षक"}, "ai_listing_keywords_repeated": {"message": "कीवर्डची पुनरावृत्ती होऊ शकत नाही"}, "ai_listing_listed_selling_points": {"message": "विक्री गुण समाविष्ट"}, "ai_listing_long_title_1": {"message": "ब्रँड नाव, उत्पादनाचा प्रकार, उत्पादन वैशिष्ट्ये इ. यासारखी मूलभूत माहिती समाविष्ट आहे."}, "ai_listing_long_title_2": {"message": "मानक उत्पादन शीर्षकाच्या आधारावर, SEO साठी अनुकूल कीवर्ड जोडले जातात."}, "ai_listing_long_title_3": {"message": "ब्रँडचे नाव, उत्पादन प्रकार, उत्पादन वैशिष्ट्ये आणि कीवर्ड समाविष्ट करण्याव्यतिरिक्त, विशिष्ट, खंडित शोध क्वेरींमध्ये उच्च रँकिंग मिळविण्यासाठी लाँग-टेल कीवर्ड देखील समाविष्ट केले जातात."}, "ai_listing_longtail_keyword_product_title": {"message": "लाँग-टेल कीवर्ड उत्पादन शीर्षक"}, "ai_listing_manually_enter": {"message": "व्यक्तिचलितपणे प्रविष्ट करा..."}, "ai_listing_network_not_working": {"message": "इंटरनेट उपलब्ध नाही, ChatGPT मध्ये प्रवेश करण्यासाठी VPN आवश्यक आहे"}, "ai_listing_new_dictionary": {"message": "नवीन शब्द लायब्ररी तयार करा..."}, "ai_listing_new_generate": {"message": "उत्पन्न करा"}, "ai_listing_optional_words": {"message": "पर्यायी शब्द"}, "ai_listing_original_title": {"message": "मूळ शीर्षक"}, "ai_listing_other_keywords_included": {"message": "इतर कीवर्ड समाविष्ट आहेत:"}, "ai_listing_please_again": {"message": "कृपया पुन्हा प्रयत्न करा"}, "ai_listing_please_select": {"message": "तुमच्यासाठी खालील शीर्षके तयार केली गेली आहेत, कृपया निवडा:"}, "ai_listing_product_category": {"message": "उत्पादन वर्ग"}, "ai_listing_product_category_is": {"message": "उत्पादन श्रेणी आहे"}, "ai_listing_product_category_to": {"message": "उत्पादन कोणत्या श्रेणीशी संबंधित आहे?"}, "ai_listing_random_keywords": {"message": "यादृच्छिक $amount$ कीवर्ड", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "यादृच्छिक $amount$ विक्री गुण", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "लायब्ररी शब्दावरून यादृच्छिक करा"}, "ai_listing_search_selling": {"message": "विक्री बिंदूद्वारे शोधा"}, "ai_listing_select_product_categories": {"message": "उत्पादन श्रेणी स्वयंचलितपणे निवडा."}, "ai_listing_select_product_selling_points": {"message": "आपोआप उत्पादन विक्री बिंदू निवडा"}, "ai_listing_select_word_library": {"message": "शब्द लायब्ररी निवडा"}, "ai_listing_selling": {"message": "विक्री गुण"}, "ai_listing_selling_ask": {"message": "शीर्षकासाठी इतर कोणत्या विक्री बिंदू आवश्यकता आहेत?"}, "ai_listing_selling_optional": {"message": "पर्यायी विक्री गुण"}, "ai_listing_selling_repeat": {"message": "पॉइंट्स डुप्लिकेट केले जाऊ शकत नाहीत"}, "ai_listing_set_excluded": {"message": "वगळलेले शब्द लायब्ररी म्हणून सेट करा"}, "ai_listing_set_include_selling_points": {"message": "विक्री गुण समाविष्ट करा"}, "ai_listing_set_included": {"message": "समाविष्ट शब्द लायब्ररी म्हणून सेट करा"}, "ai_listing_set_selling_dictionary": {"message": "सेलिंग पॉइंट लायब्ररी म्हणून सेट करा"}, "ai_listing_standard_product_title": {"message": "मानक उत्पादन शीर्षक"}, "ai_listing_translated_title": {"message": "भाषांतरित शीर्षक"}, "ai_listing_visit_chatGPT": {"message": "ChatGPT ला भेट द्या"}, "ai_listing_what_other_keywords": {"message": "शीर्षकासाठी इतर कोणते कीवर्ड आवश्यक आहेत?"}, "aliprice_coupons_apply_again": {"message": "पुन्हा अर्ज करा"}, "aliprice_coupons_apply_coupons": {"message": "कूपन लागू करा"}, "aliprice_coupons_apply_success": {"message": "कूपन सापडले: $amount$ जतन करा", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "सर्वोत्तम सौद्यांसाठी चाचणी कोड..."}, "aliprice_coupons_applying_desc": {"message": "चेक आउट करत आहे: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "चेकआउट करणे सुरू ठेवा"}, "aliprice_coupons_found_coupons": {"message": "आम्हाला $amount$ कूपन सापडले", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "चेकआउट करण्यासाठी तयार आहात? तुम्हाला सर्वोत्तम किंमत मिळेल याची खात्री करूया!"}, "aliprice_coupons_no_coupon_aviable": {"message": "ते कोड काम करत नाहीत. कोणतीही मोठी गोष्ट नाही—तुम्हाला आधीपासूनच सर्वोत्तम किंमत मिळत आहे."}, "aliprice_coupons_toolbar_btn": {"message": "कूपन मिळवा"}, "aliww_translate": {"message": "अलीवांगवांग गप्पा अनुवादक"}, "aliww_translate_supports": {"message": "समर्थन: 1688 आणि Taobao"}, "amazon_extended_keywords_Keywords": {"message": "कीवर्ड"}, "amazon_extended_keywords_copy_all": {"message": "सर्व कॉपी करा"}, "amazon_extended_keywords_more": {"message": "अधिक"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "संचयी विक्रीनुसार क्रमवारी लावा"}, "an_lei_xing_cha_kan": {"message": "प्रकारानुसार पहा"}, "an_yue_dai_xiao_pai_xu": {"message": "ड्रॉपशिपिंग विक्रीद्वारे रँकिंग"}, "apra_btn__cat_name": {"message": "पुनरावलोकनांचे विश्लेषण"}, "apra_chart__name": {"message": "देशानुसार उत्पादन विक्रीची टक्केवारी"}, "apra_chart__update_at": {"message": "अपडेट वेळ $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "देशांच्या विक्रीची आकडेवारी"}, "auto_opening": {"message": "$num$ सेकंदात स्वयंचलितपणे उघडेल", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "पुढील $autoPaging$ पृष्ठे", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "शेल्फ दिवसांवर सरासरी"}, "average_hui_fu_lv": {"message": "सरासरी उत्तर दर"}, "average_ping_gong_ying_shang_deng_ji": {"message": "पुरवठादाराची सरासरी पातळी"}, "average_price": {"message": "सरासरी किंमत"}, "average_qi_ding_liang": {"message": "सरासरी MOQ"}, "average_rating": {"message": "सरासरी रेटिंग"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "प्रमाणीकरणाची सरासरी वर्षे"}, "average_revenue": {"message": "सरासरी कमाई"}, "average_revenue_per_product": {"message": "एकूण कमाई ÷ उत्पादनांची संख्या"}, "average_sales": {"message": "सरासरी विक्री"}, "average_sales_per_product": {"message": "एकूण विक्री ÷ उत्पादनांची संख्या"}, "bao_han": {"message": "समाविष्ट आहे"}, "bao_zheng_jin": {"message": "समास"}, "bian_ti_shu": {"message": "तफावत"}, "biao_ti": {"message": "शीर्षक"}, "blacklist_add_blacklist": {"message": "हे दुकान ब्लॉक करा"}, "blacklist_address_incorrect": {"message": "पत्ता चुकीचा आहे. कृपया ते तपासा."}, "blacklist_blacked_out": {"message": "स्टोअर ब्लॉक केले आहे"}, "blacklist_blacklist": {"message": "ब्लॅकलिस्ट"}, "blacklist_no_records_yet": {"message": "अद्याप रेकॉर्ड नाही!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "ब्रँड"}, "btn_aliprice_agent__daigou": {"message": "मध्यस्थ खरेदी करा"}, "btn_aliprice_agent__dropshipping": {"message": "ड्रॉपशिपिंग"}, "btn_have_a_try": {"message": "प्रयत्न करा"}, "btn_refresh": {"message": "रीफ्रेश"}, "btn_try_it_now": {"message": "आता प्रयत्न करा"}, "btn_txt_view_on_aliprice": {"message": "<PERSON><PERSON><PERSON> वर पहा"}, "bu_bao_han": {"message": "समाविष्ट नाही"}, "bulk_copy_links": {"message": "मोठ्या प्रमाणात कॉपी लिंक"}, "bulk_copy_products": {"message": "मोठ्या प्रमाणात कॉपी उत्पादने"}, "cai_gou_zi_xun": {"message": "ग्राहक सेवा"}, "cai_gou_zi_xun__desc": {"message": "विक्रेत्याचा तीन मिनिटांचा प्रतिसाद दर"}, "can_ping_lei_xing": {"message": "प्र<PERSON><PERSON>र"}, "cao_zuo": {"message": "ऑपरेशन"}, "chan_pin_ID": {"message": "उत्पादन आयडी"}, "chan_pin_e_wai_xin_xi": {"message": "उत्पादन अतिरिक्त माहिती"}, "chan_pin_lian_jie": {"message": "उत्पादन लिंक"}, "cheng_li_shi_jian": {"message": "स्थापनेची वेळ"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "उत्पादन माहिती कॉपी करा क्लिक करा"}, "commmon_txt_expired": {"message": "कालबाह्य"}, "common__date_range_12m": {"message": "1 वर्ष"}, "common__date_range_1m": {"message": "1 महिना"}, "common__date_range_1w": {"message": "1 आठवडा"}, "common__date_range_2w": {"message": "2 आठवडे"}, "common__date_range_3m": {"message": "3 महिने"}, "common__date_range_3w": {"message": "3 आठवडे"}, "common__date_range_6m": {"message": "6 महिने"}, "common_btn_cancel": {"message": "र<PERSON><PERSON><PERSON> करा"}, "common_btn_close": {"message": "बंद"}, "common_btn_save": {"message": "ज<PERSON><PERSON> करा"}, "common_btn_setting": {"message": "सेटअप"}, "common_email": {"message": "ईमेल"}, "common_error_msg_no_data": {"message": "माहिती उपलब्ध नाही"}, "common_error_msg_no_result": {"message": "क्षमस्व, कोणताही परिणाम आढळला नाही."}, "common_favorites": {"message": "आवडी"}, "common_feedback": {"message": "अभिप्राय"}, "common_help": {"message": "मद<PERSON> करा"}, "common_loading": {"message": "लोड करीत आहे"}, "common_login": {"message": "लॉगिन"}, "common_logout": {"message": "बाहेर पडणे"}, "common_no": {"message": "नाही"}, "common_powered_by_aliprice": {"message": "AliPrice.com द्वारा समर्थित"}, "common_setting": {"message": "सेटिंग"}, "common_sign_up": {"message": "साइन अप करा"}, "common_system_upgrading_title": {"message": "सिस्टम अपग्रेडिंग"}, "common_system_upgrading_txt": {"message": "कृपया नंतर प्रयत्न करा"}, "common_txt__currency": {"message": "चलन"}, "common_txt__video_tutorial": {"message": "व्हिडिओ ट्यूटोरियल"}, "common_txt_ago_time": {"message": "$time$ दिवसांपूर्वी", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "सर्व"}, "common_txt_analysis": {"message": "विश्लेषण"}, "common_txt_basically_used": {"message": "जवळजवळ कधीही वापरलेले नाही"}, "common_txt_biaoti_link": {"message": "शीर्षक+लिंक"}, "common_txt_biaoti_link_dian_pu": {"message": "शीर्षक+लिंक+स्टोअरचे नाव"}, "common_txt_blacklist": {"message": "ब्लॉकलिस्ट"}, "common_txt_cancel": {"message": "र<PERSON><PERSON><PERSON> करा"}, "common_txt_category": {"message": "श्रेणी"}, "common_txt_chakan": {"message": "तपासा"}, "common_txt_colors": {"message": "रंग"}, "common_txt_confirm": {"message": "पुष्टी"}, "common_txt_copied": {"message": "कॉपी केले"}, "common_txt_copy": {"message": "कॉपी करा"}, "common_txt_copy_link": {"message": "लिंक कॉपी करा"}, "common_txt_copy_title": {"message": "कॉपी टायटल"}, "common_txt_copy_title__link": {"message": "शीर्षक आणि लिंक कॉपी करा"}, "common_txt_day": {"message": "आक<PERSON><PERSON>"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "हटवा"}, "common_txt_dian_pu_link": {"message": "स्टोअरचे नाव + लिंक कॉपी करा"}, "common_txt_download": {"message": "डाउनलोड करा"}, "common_txt_downloaded": {"message": "डाउनलोड करा"}, "common_txt_export_as_csv": {"message": "एक्सेल एक्सपोर्ट करा"}, "common_txt_export_as_txt": {"message": "एक्सपोर्ट टेक्स्ट"}, "common_txt_fail": {"message": "अपयशी"}, "common_txt_format": {"message": "स्वरूप"}, "common_txt_get": {"message": "मिळवा"}, "common_txt_incert_selection": {"message": "निवड उलटा"}, "common_txt_install": {"message": "स्थापित करा"}, "common_txt_load_failed": {"message": "लोड करण्यात अयशस्वी"}, "common_txt_month": {"message": "महिना"}, "common_txt_more": {"message": "अधिक"}, "common_txt_new_unused": {"message": "अगदी नवीन, न वापरलेले"}, "common_txt_next": {"message": "पुढे"}, "common_txt_no_limit": {"message": "अमर्यादित"}, "common_txt_no_noticeable": {"message": "कोणतेही दृश्यमान ओरखडे किंवा घाण नाही"}, "common_txt_on_sale": {"message": "उपलब्ध"}, "common_txt_opt_in_out": {"message": "चालु बंद"}, "common_txt_order": {"message": "ऑर्डर"}, "common_txt_others": {"message": "इतर"}, "common_txt_overall_poor_condition": {"message": "एकूणच वाईट स्थिती"}, "common_txt_patterns": {"message": "नमुने"}, "common_txt_platform": {"message": "प्लॅटफॉर्म"}, "common_txt_please_select": {"message": "कृपया निवडा"}, "common_txt_prev": {"message": "माग<PERSON><PERSON>"}, "common_txt_price": {"message": "किंमत"}, "common_txt_privacy_policy": {"message": "गोपनीयता धोरण"}, "common_txt_product_condition": {"message": "उत्पादनाची स्थिती"}, "common_txt_rating": {"message": "रेटिंग"}, "common_txt_ratings": {"message": "रेटिंग"}, "common_txt_reload": {"message": "रीलोड करा"}, "common_txt_reset": {"message": "रीसेट करा"}, "common_txt_review": {"message": "पुनरावलोकन"}, "common_txt_sale": {"message": "उपलब्ध"}, "common_txt_same": {"message": "त्याच"}, "common_txt_scratches_and_dirt": {"message": "ओरखडे आणि घाणीसह"}, "common_txt_search_title": {"message": "शोध शीर्षक"}, "common_txt_select_all": {"message": "सर्व निवडा"}, "common_txt_selected": {"message": "निवडले"}, "common_txt_share": {"message": "सामायिक करा"}, "common_txt_sold": {"message": "विकले"}, "common_txt_sold_out": {"message": "विकले गेले"}, "common_txt_some_scratches": {"message": "काही ओरखडे आणि घाण"}, "common_txt_sort_by": {"message": "यानुसार क्रमवारी लावा"}, "common_txt_state": {"message": "स्थिती"}, "common_txt_success": {"message": "यश"}, "common_txt_sys_err": {"message": "सिस्टम त्रुटी"}, "common_txt_today": {"message": "आज"}, "common_txt_total": {"message": "सर्व"}, "common_txt_unselect_all": {"message": "निवड उलटा"}, "common_txt_upload_image": {"message": "प्रतिमा अपलोड करा"}, "common_txt_visit": {"message": "भेट"}, "common_txt_whitelist": {"message": "श्वेतसूची"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "वर्ष"}, "common_yes": {"message": "होय"}, "compare_tool_btn_clear_all": {"message": "सर्व साफ करा"}, "compare_tool_btn_compare": {"message": "तुलना करा"}, "compare_tool_btn_contact": {"message": "संपर्क"}, "compare_tool_tips_max_compared": {"message": "$maxComparedCount$ पर्यंत जोडा", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "सूचना कॉन्फिगर करा"}, "contact_us": {"message": "आमच्याशी संपर्क साधा"}, "context_menu_screenshot_search": {"message": "प्रतिमेद्वारे शोधण्यासाठी कॅप्चर करा"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON><PERSON><PERSON> वर प्रतिमा शोधा"}, "context_menus_goote_trans": {"message": "पृष्ठाचे भाषांतर करा/मूळ दर्शवा"}, "context_menus_search_by_image": {"message": "$storeName$ वर प्रतिमेद्वारे शोधा", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$ वर कॅप्चर करा", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "भाषांतर करण्यासाठी कॅप्चर करा"}, "converter_modal_amount_placeholder": {"message": "येथे रक्कम प्रविष्ट करा"}, "converter_modal_btn_convert": {"message": "रूपांतरित करा"}, "converter_modal_exchange_rate_source": {"message": "डेटा $boc$ वरून येतो परकीय चलन दर अद्यतन वेळ: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "चलन रूपांतर"}, "converter_modal_search_placeholder": {"message": "शोध चलन"}, "copy_all_contact_us_notice": {"message": "ही साइट यावेळी समर्थित नाही, कृपया आमच्याशी संपर्क साधा"}, "copy_product_info": {"message": "उत्पादन माहिती कॉपी करा"}, "copy_suggest_search_kw": {"message": "ड्रॉपडाउन सूची कॉपी करा"}, "country__han_gou": {"message": "दक्षिण कोरिया"}, "country__ri_ben": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "country__yue_nan": {"message": "व्हिएतनाम"}, "currency_convert__custom": {"message": "सानुकूल विनिमय दर"}, "currency_convert__sync_server": {"message": "सर्व्हर समक्रमित करा"}, "dang_ri_fa_huo": {"message": "त्याच दिवशी शिपिंग"}, "dao_chu_quan_dian_shang_pin": {"message": "सर्व स्टोअर उत्पादने निर्यात करा"}, "dao_chu_wei_CSV": {"message": "निर्यात करा"}, "dao_chu_zi_duan": {"message": "निर्यात फील्ड"}, "delivery_address": {"message": "शिपिंग पत्ता"}, "delivery_company": {"message": "वितरण कंपनी"}, "di_zhi": {"message": "पत्ता"}, "dian_ji_cha_xun": {"message": "क्वेरी करण्यासाठी क्लिक करा"}, "dian_pu_ID": {"message": "स्टोअर आयडी"}, "dian_pu_di_zhi": {"message": "स्टोअर पत्ता"}, "dian_pu_lian_jie": {"message": "स्टोअर लिंक"}, "dian_pu_ming": {"message": "स्टोअरचे नाव"}, "dian_pu_ming_cheng": {"message": "स्टोअरचे नाव"}, "dian_pu_shang_pin_zong_hsu": {"message": "स्टोअरमधील उत्पादनांची एकूण संख्या: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "माहिती साठवा"}, "ding_zai_zuo_ce": {"message": "डावीकडे खिळे ठोकले"}, "disable_old_version_tips_disable_btn_title": {"message": "जुनी आवृत्ती अक्षम करा"}, "download_image__SKU_variant_images": {"message": "SKU प्रकारातील प्रतिमा"}, "download_image__assume": {"message": "उदाहरणार्थ, आमच्याकडे 2 प्रतिमा आहेत, product1.jpg आणि product2.gif.\nimg_{$no$} चे नाव बदलून img_01.jpg, img_02.gif असे केले जाईल;\n{$group$}_{$no$} चे नाव बदलून main_image_01.jpg, main_image_02.gif केले जाईल;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "बॅच डाउनलोड"}, "download_image__combined_image": {"message": "एकत्रित उत्पादन तपशील प्रतिमा"}, "download_image__continue_downloading": {"message": "डाउनलोड करणे सुरू ठेवा"}, "download_image__description_images": {"message": "वर्णन प्रतिमा"}, "download_image__download_combined_image": {"message": "एकत्रित उत्पादन तपशील प्रतिमा डाउनलोड करा"}, "download_image__download_zip": {"message": "झिप डाउनलोड करा"}, "download_image__enlarge_check": {"message": "केवळ JPEG, <PERSON><PERSON>, GIF आणि PNG प्रतिमांना समर्थन देते, एका प्रतिमेचा कमाल आकार: 1600 * 1600"}, "download_image__enlarge_image": {"message": "प्रतिमा वाढवा"}, "download_image__export": {"message": "निर्यात करा"}, "download_image__height": {"message": "उंची"}, "download_image__ignore_videos": {"message": "व्हिडिओकडे दुर्लक्ष केले गेले आहे, कारण तो निर्यात केला जाऊ शकत नाही"}, "download_image__img_translate": {"message": "प्रतिमा अनुवाद"}, "download_image__main_image": {"message": "मुख्य प्रतिमा"}, "download_image__multi_folder": {"message": "मल्टी-फोल्डर"}, "download_image__name": {"message": "प्रतिमा डाउनलोड करा"}, "download_image__notice_content": {"message": "कृपया तुमच्या ब्राउझरच्या डाउनलोड सेटिंग्जमध्ये \"डाउनलोड करण्यापूर्वी प्रत्येक फाइल कुठे सेव्ह करायची ते विचारा\" हे तपासू नका!!! अन्यथा बरेच डायलॉग बॉक्स असतील."}, "download_image__notice_ignore": {"message": "या संदेशासाठी पुन्हा सूचित करू नका"}, "download_image__order_number": {"message": "{$no$} अनुक्रमांक; {$group$} गटाचे नाव; {$date$} टाइमस्टॅम्प", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "आढावा"}, "download_image__prompt_download_zip": {"message": "बर्याच प्रतिमा आहेत, तुम्ही त्यांना झिप फोल्डर म्हणून डाउनलोड करणे चांगले."}, "download_image__rename": {"message": "नाव बदला"}, "download_image__rule": {"message": "नामकरणाचे नियम"}, "download_image__single_folder": {"message": "सिंगल फोल्डर"}, "download_image__sku_image": {"message": "SKU प्रतिमा"}, "download_image__video": {"message": "व्हिडिओ"}, "download_image__width": {"message": "रुंदी"}, "download_reviews__download_images": {"message": "पुनरावलोकन प्रतिमा डाउनलोड करा"}, "download_reviews__dropdown_title": {"message": "पुनरावलोकन प्रतिमा डाउनलोड करा"}, "download_reviews__export_csv": {"message": "CSV निर्यात करा"}, "download_reviews__no_images": {"message": "डाउनलोड करण्यासाठी 0 चित्रे उपलब्ध आहेत"}, "download_reviews__no_reviews": {"message": "डाउनलोड करण्यासाठी कोणतेही पुनरावलोकन नाही!"}, "download_reviews__notice": {"message": "टीप:"}, "download_reviews__notice__chrome_settings": {"message": "डाउनलोड करण्यापूर्वी प्रत्येक फाइल कुठे सेव्ह करायची हे विचारण्यासाठी Chrome ब्राउझर सेट करा, \"बंद\" वर सेट करा"}, "download_reviews__notice__wait": {"message": "पुनरावलोकनांच्या संख्येवर अवलंबून, प्रतीक्षा वेळ जास्त असू शकतो"}, "download_reviews__pages_list__all": {"message": "सर्व"}, "download_reviews__pages_list__page": {"message": "मागील $page$ पृष्ठे", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "निवड श्रेणी"}, "export_shopping_cart__csv_filed__details_url": {"message": "उत्पादन लिंक"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "इको SKU लिंक"}, "export_shopping_cart__csv_filed__images": {"message": "इमेज लिंक"}, "export_shopping_cart__csv_filed__quantity": {"message": "प्रमाण"}, "export_shopping_cart__csv_filed__sale_price": {"message": "किंमत"}, "export_shopping_cart__csv_filed__specs": {"message": "तपशील"}, "export_shopping_cart__csv_filed__store_name": {"message": "स्टोअरचे नाव"}, "export_shopping_cart__csv_filed__store_url": {"message": "स्टोअर लिंक"}, "export_shopping_cart__csv_filed__title": {"message": "उत्पादनाचे नांव"}, "export_shopping_cart__export_btn": {"message": "निर्यात करा"}, "export_shopping_cart__export_empty": {"message": "कृपया उत्पादन निवडा!"}, "fa_huo_shi_jian": {"message": "शिपिंग"}, "favorite_add_email": {"message": "ईमेल जोडा"}, "favorite_add_favorites": {"message": "आवडींमध्ये जोडा"}, "favorite_added": {"message": "जोडले"}, "favorite_btn_add": {"message": "किंमत ड्रॉप अलर्ट."}, "favorite_btn_notify": {"message": "ट्रॅक किंमत"}, "favorite_cate_name_all": {"message": "सर्व उत्पादने"}, "favorite_current_price": {"message": "सध्याची किंमत"}, "favorite_due_date": {"message": "देय तारीख"}, "favorite_enable_notification": {"message": "कृपया ईमेल सूचना सक्षम करा"}, "favorite_expired": {"message": "कालबाह्य"}, "favorite_go_to_enable": {"message": "सक्षम करा वर जा"}, "favorite_msg_add_success": {"message": "आवडींमध्ये जोडले"}, "favorite_msg_del_success": {"message": "आवडींमधून हटविले"}, "favorite_msg_failure": {"message": "अपयशी! रीफ्रेश करा पृष्ठ आणि पुन्हा प्रयत्न करा."}, "favorite_please_add_email": {"message": "कृपया ईमेल जोडा"}, "favorite_price_drop": {"message": "खाली"}, "favorite_price_rise": {"message": "वर"}, "favorite_price_untracked": {"message": "किंमत अनट्रॅक"}, "favorite_saved_price": {"message": "जतन केलेली किंमत"}, "favorite_stop_tracking": {"message": "ट्रॅकिंग थांबवा"}, "favorite_sub_email_address": {"message": "सदस्यता ईमेल पत्ता"}, "favorite_tracking_period": {"message": "ट्रॅकिंग कालावधी"}, "favorite_tracking_prices": {"message": "किंमतींचा मागोवा घेणे"}, "favorite_verify_email": {"message": "ईमेल पत्ता सत्यापित करा"}, "favorites_list_remove_prompt_msg": {"message": "आपणास खात्री आहे की हे हटविले आहे?"}, "favorites_update_button": {"message": "आता किमती अपडेट करा"}, "fen_lei": {"message": "श्रेणी"}, "fen_xia_yan_xuan": {"message": "वितरकाची निवड"}, "find_similar": {"message": "समान शोधा"}, "first_ali_price_date": {"message": "AliPrice क्रॉलरने प्रथम कॅप्चर केलेली तारीख"}, "fooview_coupons_modal_no_data": {"message": "कूपन नाहीत"}, "fooview_coupons_modal_title": {"message": "कूपन"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "किंमत < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "किंवा किंमत > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "अंतिम मुदत"}, "fooview_favorites_error_msg_no_favorites": {"message": "किंमत ड्रॉप अ‍ॅलर्ट प्राप्त करण्यासाठी येथे आवडती उत्पादने जोडा."}, "fooview_favorites_filter_latest": {"message": "नवीनतम"}, "fooview_favorites_filter_price_drop": {"message": "किंमत कमी"}, "fooview_favorites_filter_price_up": {"message": "किंमत वाढ"}, "fooview_favorites_modal_title": {"message": "माझे आवडते"}, "fooview_favorites_modal_title_title": {"message": "AliPrice आवडत्या वर जा"}, "fooview_favorites_track_price": {"message": "किंमत ट्रॅक करण्यासाठी"}, "fooview_price_history_app_price": {"message": "एपीपी किंमत:"}, "fooview_price_history_title": {"message": "किंमत इतिहास"}, "fooview_product_list_feedback": {"message": "अभिप्राय"}, "fooview_product_list_orders": {"message": "आदेश"}, "fooview_product_list_price": {"message": "किंमत"}, "fooview_reviews_error_msg_no_review": {"message": "आम्हाला या उत्पादनासाठी कोणतीही पुनरावलोकने आढळली नाहीत."}, "fooview_reviews_filter_buyer_reviews": {"message": "खरेदीदारांचे फोटो"}, "fooview_reviews_modal_title": {"message": "पुनरावलोकने"}, "fooview_same_product_choose_category": {"message": "श्रेणी निवडा"}, "fooview_same_product_filter_feedback": {"message": "अभिप्राय"}, "fooview_same_product_filter_orders": {"message": "आदेश"}, "fooview_same_product_filter_price": {"message": "किंमत"}, "fooview_same_product_filter_rating": {"message": "रेटिंग"}, "fooview_same_product_modal_title": {"message": "समान उत्पादन शोधा"}, "fooview_same_product_search_by_image": {"message": "प्रतिमेनुसार शोधा"}, "fooview_seller_analysis_modal_title": {"message": "विक्रेता विश्लेषण"}, "for_12_months": {"message": "1 वर्षासाठी"}, "for_12_months_list_pro": {"message": "12 महिने"}, "for_12_months_nei": {"message": "12 महिन्यांत"}, "for_1_months": {"message": "1 महिना"}, "for_1_months_nei": {"message": "1 महिन्याच्या आत"}, "for_3_months": {"message": "3 महिन्यांसाठी"}, "for_3_months_nei": {"message": "3 महिन्यांत"}, "for_6_months": {"message": "6 महिने"}, "for_6_months_nei": {"message": "6 महिन्यांत"}, "for_9_months": {"message": "9 महिने"}, "for_9_months_nei": {"message": "9 महिन्यांत"}, "fu_gou_lv": {"message": "पुनर्खरेदी दर"}, "gao_liang_bu_tong_dian": {"message": "फरक हायलाइट करा"}, "gao_liang_guang_gao_chan_pin": {"message": "जाहिरात उत्पादने हायलाइट करा"}, "geng_duo_xin_xi": {"message": "अधिक माहिती"}, "geng_xin_shi_jian": {"message": "अपडेट वेळ"}, "get_store_products_fail_tip": {"message": "सामान्य प्रवेश सुनिश्चित करण्यासाठी पडताळणीवर जाण्यासाठी ओके क्लिक करा."}, "gong_x_kuan_shang_pin": {"message": "एकूण $amount$ उत्पादने", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "पुरवठादार"}, "gong_ying_shang_ID": {"message": "पुरवठादार आयडी"}, "gong_ying_shang_deng_ji": {"message": "पुरवठादार रेटिंग"}, "gong_ying_shang_nian_zhan": {"message": "पुरवठादार जुना आहे"}, "gong_ying_shang_xin_xi": {"message": "पुरवठादार माहिती"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "पुरवठादार मुख्यपृष्ठ दुवा"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "अंदाजे विक्री किंमत"}, "guan_jian_zi": {"message": "कीवर्ड"}, "guang_gao_chan_pin": {"message": "ॲड. उत्पादने"}, "guang_gao_zhan_bi": {"message": "ॲड. प्रमाण"}, "guo_ji_wu_liu_yun_fei": {"message": "आंतरराष्ट्रीय शिपिंग शुल्क"}, "guo_lv_tiao_jian": {"message": "फिल्टर"}, "hao_ping_lv": {"message": "सकारात्मक रेटिंग"}, "highest_price": {"message": "उच्च"}, "historical_trend": {"message": "ऐतिहासिक कल"}, "how_to_screenshot": {"message": "क्षेत्र निवडण्यासाठी डावे माऊस बटण दाबून ठेवा, स्क्रीनशॉटमधून बाहेर पडण्यासाठी उजवे माऊस बटण किंवा Esc की टॅप करा"}, "howt_it_works": {"message": "हे कसे कार्य करते"}, "hui_fu_lv": {"message": "प्रतिसाद दर"}, "hui_tou_lv": {"message": "परतावा दर"}, "inquire_freightFee": {"message": "मालवाहतूक चौकशी"}, "inquire_freightFee_Yuan": {"message": "मालवाहतूक/युआन"}, "inquire_freightFee_province": {"message": "प्रांत"}, "inquire_freightFee_the": {"message": "मालवाहतूक $num$ आहे, याचा अर्थ प्रदेशात विनामूल्य शिपिंग आहे.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "ॲड."}, "jia_ge": {"message": "किंमत"}, "jia_ge_dan_wei": {"message": "युनिट"}, "jia_ge_qu_shi": {"message": "कल"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ उत्पादने लोड करा", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "गेल्या 30 दिवसांतील विक्रीच्या प्रमाणाची टक्केवारी"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "गेल्या 30 दिवसांतील कमाईची टक्केवारी"}, "jin_30d_xiao_liang": {"message": "विक्री"}, "jin_30d_xiao_liang__desc": {"message": "गेल्या 30 दिवसातील एकूण विक्री"}, "jin_30d_xiao_shou_e": {"message": "उलाढाल"}, "jin_30d_xiao_shou_e__desc": {"message": "गेल्या 30 दिवसातील एकूण उलाढाल"}, "jin_90_tian_mai_jia_shu": {"message": "मागील 90 दिवसातील खरेदीदार"}, "jin_90_tian_xiao_shou_liang": {"message": "गेल्या ९० दिवसात विक्री"}, "jing_xuan_huo_yuan": {"message": "निवडक स्रोत"}, "jing_ying_mo_shi": {"message": "व्यवसाय मॉडेल"}, "jing_ying_mo_shi__gong_chang": {"message": "निर्माता"}, "jiu_fen_jie_jue": {"message": "वाद निराकरण"}, "jiu_fen_jie_jue__desc": {"message": "विक्रेत्यांच्या स्टोअर हक्क विवादांचे लेखांकन"}, "jiu_fen_lv": {"message": "विवाद दर"}, "jiu_fen_lv__desc": {"message": "मागील 30 दिवसात पूर्ण झालेल्या तक्रारींसह ऑर्डरचे प्रमाण आणि विक्रेता किंवा दोन्ही पक्षांची जबाबदारी आहे"}, "kai_dian_ri_qi": {"message": "उघडण्याची तारीख"}, "keywords": {"message": "कीवर्ड"}, "kua_jin_Select_pan_huo": {"message": "सीमा ओलांडून निवडा"}, "last15_days": {"message": "शेवटचे 15 दिवस"}, "last180_days": {"message": "शेवटचे १८० दिवस"}, "last30_days": {"message": "गेल्या 30 दिवसात"}, "last360_days": {"message": "शेवटचे ३६० दिवस"}, "last45_days": {"message": "शेवटचे ४५ दिवस"}, "last60_days": {"message": "शेवटचे 60 दिवस"}, "last7_days": {"message": "शेवटचे ७ दिवस"}, "last90_days": {"message": "शेवटचे ९० दिवस"}, "last_30d_sales": {"message": "मागील 30 दिवसांची विक्री"}, "lei_ji": {"message": "संचयी"}, "lei_ji_xiao_liang": {"message": "एकूण"}, "lei_ji_xiao_liang__desc": {"message": "शेल्फवर उत्पादनानंतर सर्व विक्री"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "गेल्या 30 दिवसांमधील संचयी विक्री खंड, उच्च ते निम्न क्रमवारीत"}, "lian_xi_fang_shi": {"message": "संपर्क माहिती"}, "list_time": {"message": "शेल्फ तारखेला"}, "load_more": {"message": "अधिक लोड करा"}, "login_to_aliprice": {"message": "<PERSON><PERSON><PERSON> मध्ये लॉग इन करा"}, "long_link": {"message": "लांब दुवा"}, "lowest_price": {"message": "कमी"}, "mai_jia_shu": {"message": "विक्रेता संख्या"}, "mao_li_lv": {"message": "एकूण मार्जिन"}, "mobile_view__dkxbqy": {"message": "नवीन टॅब उघडा"}, "mobile_view__sjdxq": {"message": "ॲपमध्ये तपशील"}, "mobile_view__sjdxqy": {"message": "ॲपमध्ये तपशीलवार पृष्ठ"}, "mobile_view__smck": {"message": "पाहण्यासाठी स्कॅन करा"}, "mobile_view__smckms": {"message": "कृपया स्कॅन आणि पाहण्यासाठी कॅमेरा किंवा ॲप वापरा"}, "modified_failed": {"message": "सुधारणा अयशस्वी"}, "modified_successfully": {"message": "यशस्वीरित्या सुधारित केले"}, "nav_btn_favorites": {"message": "माझे संग्रह"}, "nav_btn_package": {"message": "पॅकेज"}, "nav_btn_product_info": {"message": "उत्पादनाबद्दल"}, "nav_btn_viewed": {"message": "पाहिले"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "काह<PERSON><PERSON>ी नाही"}, "normal_link": {"message": "सामान्य दुवा"}, "notice": {"message": "इशारा"}, "number_reviews": {"message": "पुनरावलोकने"}, "only_show_num": {"message": "एकूण उत्पादने: $allnum$, लपलेले: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "अनचेक काढा"}, "open": {"message": "उघडा"}, "open_links": {"message": "लिंक्स उघडा"}, "options_page_tab_check_links": {"message": "दुवे तपासा"}, "options_page_tab_gernal": {"message": "सामान्य"}, "options_page_tab_notifications": {"message": "अधिसूचना"}, "options_page_tab_others": {"message": "इतर"}, "options_page_tab_sbi": {"message": "प्रतिमेनुसार शोधा"}, "options_page_tab_shortcuts": {"message": "शॉर्टकट्स"}, "options_page_tab_shortcuts_title": {"message": "शॉर्टकटसाठी फॉन्ट आकार"}, "options_page_tab_similar_products": {"message": "समान उत्पादने"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "अनेक उत्पादनांचे दुवे उघडणार आहेत"}, "order_list_sku_show_title": {"message": "शेअर केलेल्या लिंक्समध्ये निवडक रूपे दाखवा"}, "orders_last30_days": {"message": "गेल्या 30 दिवसांतील ऑर्डरची संख्या"}, "pTutorial_favorites_block1_desc1": {"message": "आपण ट्रॅक केलेली उत्पादने येथे सूचीबद्ध आहेत"}, "pTutorial_favorites_block1_title": {"message": "आवडी"}, "pTutorial_popup_block1_desc1": {"message": "ग्रीन लेबल म्हणजे किंमत कमी केलेली उत्पादने आहेत"}, "pTutorial_popup_block1_title": {"message": "शॉर्टकट आणि आवडी"}, "pTutorial_price_history_block1_desc1": {"message": "ट्रॅक किंमत क्लिक करा, आवडीमध्ये उत्पादने जोडा. एकदा त्यांच्या किंमती खाली आल्या की आपल्याला सूचना प्राप्त होतील"}, "pTutorial_price_history_block1_title": {"message": "ट्रॅक किंमत"}, "pTutorial_reviews_block1_desc1": {"message": "Itao कडून खरेदीदारांचे पुनरावलोकन आणि AliExpress अभिप्रायामधील वास्तविक फोटो"}, "pTutorial_reviews_block1_title": {"message": "पुनरावलोकने"}, "pTutorial_reviews_block2_desc1": {"message": "इतर खरेदीदारांकडील पुनरावलोकने तपासणे नेहमीच उपयुक्त ठरते"}, "pTutorial_same_products_block1_desc1": {"message": "सर्वोत्तम निवड करण्यासाठी आपण त्यांची तुलना करू शकता"}, "pTutorial_same_products_block1_desc2": {"message": "प्रतिमेद्वारे शोधा वर 'अधिक' क्लिक करा"}, "pTutorial_same_products_block1_title": {"message": "समान उत्पादने"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "तेथे उत्पादन प्रतिमा ड्रॉप करा आणि एक श्रेणी निवडा"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "प्रतिमेनुसार शोधा"}, "pTutorial_seller_analysis_block1_desc1": {"message": "विक्रेत्याचा सकारात्मक फीडबॅक रेट, फीडबॅक स्कोअर आणि विक्रेता बाजारात किती काळ राहिला आहे"}, "pTutorial_seller_analysis_block1_title": {"message": "विक्रेता रेटिंग"}, "pTutorial_seller_analysis_block2_desc2": {"message": "विक्रेता रेटिंग 3 अनुक्रमणिकांवर आधारित आहे: वर्णन केल्यानुसार आयटम, संप्रेषण शिपिंग गती"}, "pTutorial_seller_analysis_block3_desc3": {"message": "विक्रेत्यांचा विश्वास स्तर दर्शविण्यासाठी आम्ही 3 रंग आणि चिन्हे वापरतो"}, "page_count": {"message": "पृष्ठांची संख्या"}, "pai_chu": {"message": "वगळले"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "हाँगकाँग-प्रतिबंधित वगळा"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "जपान-प्रतिबंधित वगळा"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "कोरिया-प्रतिबंधित वगळा"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "कझाकिस्तान-प्रतिबंधित वगळा"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "मकाऊ-प्रतिबंधित वगळा"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "पूर्व युरोप-प्रतिबंधित वगळा"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "सौदी अरेबिया-प्रतिबंधित वगळा"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "तैवान-प्रतिबंधित वगळा"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "यू.एस.-प्रतिबंधित वगळा"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "व्हिएतनाम-प्रतिबंधित वगळा"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "प्रतिबंधित आयटम वगळा"}, "payable_price_formula": {"message": "किंमत + शिपिंग + सवलत"}, "pdd_check_retail_btn_txt": {"message": "किरकोळ तपासा"}, "pdd_pifa_to_retail_btn_txt": {"message": "किरकोळ खरेदी करा"}, "pdp_copy_fail": {"message": "कॉपी अयशस्वी!"}, "pdp_copy_success": {"message": "कॉपी यशस्वी झाली!"}, "pdp_share_modal_subtitle": {"message": "स्क्रीनशॉट शेअर करा, तो/तिला तुमचा पर्याय दिसेल."}, "pdp_share_modal_title": {"message": "तुमची निवड शेअर करा"}, "pdp_share_screenshot": {"message": "स्क्रीनशॉट शेअर करा"}, "pei_song": {"message": "शिपिंग पद्धत"}, "pin_lei": {"message": "श्रेणी"}, "pin_zhi_ti_yan": {"message": "उत्पादन गुणवत्ता"}, "pin_zhi_ti_yan__desc": {"message": "विक्रेत्याच्या स्टोअरचा गुणवत्ता परतावा दर"}, "pin_zhi_tui_kuan_lv": {"message": "परतावा दर"}, "pin_zhi_tui_kuan_lv__desc": {"message": "मागच्या 30 दिवसात फक्त परतावा मिळालेल्या आणि परत केलेल्या ऑर्डरचे प्रमाण"}, "ping_fen": {"message": "रेटिंग"}, "ping_jun_fa_huo_su_du": {"message": "सरासरी शिपिंग गती"}, "pkgInfo_hide": {"message": "लॉजिस्टिक माहिती: चालू/बंद"}, "pkgInfo_no_trace": {"message": "लॉजिस्टिक माहिती नाही"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "डीएचगेट"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "इबे"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "पिंडूडो"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo घाऊक"}, "platform_name__shopee": {"message": "शॉपी"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "ताओबाओ"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "वॉलमार्ट"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "मुख्यपृष्ठ"}, "popup_go_to_platform": {"message": "$name$ वर जा", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "मी खरेदी करीत आहे ..."}, "popup_track_package_btn_track": {"message": "ट्रॅक"}, "popup_track_package_desc": {"message": "सर्व-इन-पॅकेज ट्रॅकिंग"}, "popup_track_package_search_placeholder": {"message": "ट्रॅकिंग क्रमांक"}, "popup_translate_search_placeholder": {"message": "अनुवाद करा आणि $searchOn$ वर शोधा", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "किंमत इतिहास"}, "price_history_chart_tip_ae": {"message": "टीप: ऑर्डरची संख्या ही लाँच झाल्यापासूनच्या ऑर्डरची एकत्रित संख्या आहे"}, "price_history_chart_tip_coupang": {"message": "टीप: Coupang फसव्या ऑर्डरची ऑर्डर संख्या हटवेल"}, "price_history_inm_1688_l1": {"message": "कृपया स्थापित करा"}, "price_history_inm_1688_l2": {"message": "1688 साठी अलीप्रिस शॉपिंग सहाय्यक"}, "price_history_panel_lowest_price": {"message": "सर्वात कमी किंमत:"}, "price_history_panel_tab_price_tracking": {"message": "किंमत इतिहास"}, "price_history_panel_tab_seller_analysis": {"message": "विक्रेता विश्लेषण"}, "price_history_pro_modal_title": {"message": "किंमत इतिहास आणि ऑर्डर इतिहास"}, "privacy_consent__btn_agree": {"message": "डेटा संकलनाची संमती पुन्हा भेट द्या"}, "privacy_consent__btn_disable_all": {"message": "स्वीकारत नाही"}, "privacy_consent__btn_enable_all": {"message": "सर्व सक्षम करा"}, "privacy_consent__btn_uninstall": {"message": "काढा"}, "privacy_consent__desc_privacy": {"message": "लक्षात ठेवा डेटा किंवा कुकीजशिवाय काही फंक्शन्स बंद असतील कारण त्या फंक्शन्सना डेटा किंवा कुकीजच्या स्पष्टीकरणांची आवश्यकता असते, परंतु आपण अद्याप इतर कार्ये वापरू शकता."}, "privacy_consent__desc_privacy_L1": {"message": "दुर्दैवाने, डेटा किंवा कुकीजशिवाय हे कार्य करणार नाही कारण आम्हाला डेटा किंवा कुकीजच्या स्पष्टीकरणांची आवश्यकता आहे."}, "privacy_consent__desc_privacy_L2": {"message": "आपण आम्हाला ही माहिती गोळा करण्यास परवानगी देत ​​नसल्यास कृपया ते काढा."}, "privacy_consent__item_cookies_desc": {"message": "कुकी, किंमतीचा इतिहास दर्शविण्यासाठी आम्ही ऑनलाइन खरेदी करताना आमच्या कुकीजमध्ये केवळ आपला चलन डेटा मिळतो."}, "privacy_consent__item_cookies_title": {"message": "आवश्यक कुकीज"}, "privacy_consent__item_functional_desc_L1": {"message": "1. आपला संगणक किंवा डिव्हाइस अज्ञातपणे ओळखण्यासाठी ब्राउझरमध्ये कुकीज जोडा.\n२. फंक्शनसह कार्य करण्यासाठी अ‍ॅड-ऑनमध्ये कार्यात्मक डेटा जोडा."}, "privacy_consent__item_functional_desc_L2": {"message": "2.undefined"}, "privacy_consent__item_functional_title": {"message": "कार्यात्मक आणि विश्लेषक कुकीज"}, "privacy_consent__more_desc": {"message": "कृपया हे जाणून घ्या की आम्ही आपला वैयक्तिक डेटा इतर कंपन्यांसह सामायिक करीत नाही आणि कोणतीही जाहिरात कंपन्या आमच्या सेवेद्वारे डेटा संकलित करत नाहीत."}, "privacy_consent__options__btn__desc": {"message": "सर्व वैशिष्ट्ये वापरण्यासाठी, आपल्याला ते चालू करण्याची आवश्यकता आहे."}, "privacy_consent__options__btn__label": {"message": "हे सुरु करा"}, "privacy_consent__options__desc_L1": {"message": "आम्ही आपल्याला पुढील डेटा एकत्रित करतो जो आपल्याला वैयक्तिकरित्या ओळखतो:"}, "privacy_consent__options__desc_L2": {"message": "- कूकीज, आम्ही केवळ आपला कुकीजमध्ये चलन डेटा मिळवितो जेव्हा आपण किंमतीचा इतिहास दर्शविण्यासाठी ऑनलाइन खरेदी करता."}, "privacy_consent__options__desc_L3": {"message": "- आणि आपला संगणक किंवा डिव्हाइस अनामिकपणे ओळखण्यासाठी ब्राउझरमध्ये कुकीज जोडा."}, "privacy_consent__options__desc_L4": {"message": "- इतर अज्ञात डेटा हा विस्तार अधिक सोयीस्कर बनवितो."}, "privacy_consent__options__desc_L5": {"message": "कृपया लक्षात घ्या की आम्ही आपला वैयक्तिक डेटा इतर कंपन्यांसह सामायिक करीत नाही आणि कोणतीही जाहिरात कंपन्या आमच्या सेवेद्वारे डेटा संकलित करत नाहीत."}, "privacy_consent__privacy_preferences": {"message": "गोपनीयता प्राधान्ये"}, "privacy_consent__read_more": {"message": "अधिक वाचा >>"}, "privacy_consent__title_privacy": {"message": "गोपनीयता"}, "product_info": {"message": "उत्पादन माहिती"}, "product_recommend__name": {"message": "समान उत्पादने"}, "product_research": {"message": "उत्पादन संशोधन"}, "product_sub__email_desc": {"message": "किंमत सूचना ईमेल"}, "product_sub__email_edit": {"message": "सुधारणे"}, "product_sub__email_not_verified": {"message": "कृपया ईमेल सत्यापित करा"}, "product_sub__email_required": {"message": "कृपया ईमेल प्रदान करा"}, "product_sub__form_countdown": {"message": "$seconds$ सेकंदांनंतर स्वयंचलित बंद", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "स्मरणपत्र जोडण्यात अयशस्वी!"}, "product_sub__form_input_price": {"message": "इनपुट किंमत"}, "product_sub__form_item_country": {"message": "राष्ट्र"}, "product_sub__form_item_current_price": {"message": "चालू किंमत"}, "product_sub__form_item_duration": {"message": "ट्रॅक"}, "product_sub__form_item_higher_price": {"message": "किंवा किंमत >"}, "product_sub__form_item_invalid_higher_price": {"message": "किंमत $price$ पेक्षा जास्त असणे आवश्यक आहे", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "किंमत $price$ पेक्षा कमी असणे आवश्यक आहे", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "जेव्हा किंमत <"}, "product_sub__form_submit": {"message": "प्रस्तुत करणे"}, "product_sub__form_success": {"message": "स्मरणपत्र जोडण्यात यशस्वी!"}, "product_sub__high_price_notify": {"message": "किमतीत वाढ झाल्याबद्दल मला सूचित करा"}, "product_sub__low_price_notify": {"message": "किमती कपातीबद्दल मला सूचित करा"}, "product_sub__modal_title": {"message": "सदस्यता किंमत बदल स्मरणपत्र"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ आणि MOP"}, "qi_ye_mian_ji": {"message": "एंटरप्राइझ क्षेत्र"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "कृपया किमान एक उत्पादन निवडा"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "कृपया किमान एक फील्ड निवडा"}, "qu_deng_lu": {"message": "लॉग इन करा"}, "quan_guo_yan_xuan": {"message": "जागतिक निवड"}, "recommendation_popup_banner_btn_install": {"message": "स्थापित करा"}, "recommendation_popup_banner_desc": {"message": "3/6 महिन्यांच्या आत किंमत इतिहास आणि किंमत ड्रॉप सूचना प्रदर्शित करा"}, "region__all": {"message": "सर्व प्रदेश"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "उजवे क्लिक सक्षम करा"}, "ren_zheng_gong_chang": {"message": "प्रमाणित कारखाना"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "प्रमाणित पुरवठादार म्हणून वर्षे"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON><PERSON> मध्ये लॉग इन करणे आवश्यक आहे"}, "revenue_last30_days": {"message": "माग<PERSON><PERSON> 30 दिवसांतील विक्रीची रक्कम"}, "review_counts": {"message": "संग्राहकांची संख्या"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "प्रवेश कालावधी"}, "sales_amount_last30_days": {"message": "गेल्या 30 दिवसातील एकूण विक्री"}, "sales_last30_days": {"message": "गेल्या 30 दिवसांतील विक्री"}, "sbi_alibaba_cate__accessories": {"message": "अ‍ॅक्सेसरीज"}, "sbi_alibaba_cate__aqfk": {"message": "Keselamatan"}, "sbi_alibaba_cate__bags_cases": {"message": "बॅग आणि प्रकरणे"}, "sbi_alibaba_cate__beauty": {"message": "सौंदर्य"}, "sbi_alibaba_cate__beverage": {"message": "पेय"}, "sbi_alibaba_cate__bgwh": {"message": "Budaya pejabat"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Perala<PERSON> dapur"}, "sbi_alibaba_cate__clothes": {"message": "परिधान"}, "sbi_alibaba_cate__cmgd": {"message": "Penyiaran Media"}, "sbi_alibaba_cate__coat_jacket": {"message": "कोट आणि जाकीट"}, "sbi_alibaba_cate__consumer_electronics": {"message": "उपभोक्ता इलेक्ट्रॉनिक्स"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__csyp": {"message": "<PERSON><PERSON><PERSON><PERSON> katil"}, "sbi_alibaba_cate__cwyy": {"message": "Be<PERSON><PERSON><PERSON> haiwan peli<PERSON>an"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON> segar"}, "sbi_alibaba_cate__dgdq": {"message": "Juruelektrik"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "ड्रेस आणि सूट"}, "sbi_alibaba_cate__dszm": {"message": "Pencahayaan"}, "sbi_alibaba_cate__dzqj": {"message": "Peranti elektronik"}, "sbi_alibaba_cate__essb": {"message": "Peralatan Terpakai"}, "sbi_alibaba_cate__food": {"message": "अन्न"}, "sbi_alibaba_cate__fspj": {"message": "Pakaian & Aksesori"}, "sbi_alibaba_cate__furniture": {"message": "फर्निचर"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "<PERSON><PERSON><PERSON><PERSON> diri"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Kraftangan"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON> alam"}, "sbi_alibaba_cate__hfcz": {"message": "Solekan penja<PERSON>an kulit"}, "sbi_alibaba_cate__hg": {"message": "Industri kimia"}, "sbi_alibaba_cate__jg": {"message": "Memproses"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON>at mesin"}, "sbi_alibaba_cate__jjry": {"message": "kegunaan harian isi rumah"}, "sbi_alibaba_cate__jtys": {"message": "Pengangkutan"}, "sbi_alibaba_cate__jxsb": {"message": "perala<PERSON>"}, "sbi_alibaba_cate__jxwj": {"message": "यांत्रिक हार्डवेअर"}, "sbi_alibaba_cate__jydq": {"message": "<PERSON><PERSON><PERSON> rumah"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON><PERSON> binaan pembaikan rumah"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__mj": {"message": "tuala"}, "sbi_alibaba_cate__myyp": {"message": "Produk Bayi"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON> wanita"}, "sbi_alibaba_cate__ny": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__others": {"message": "इतर"}, "sbi_alibaba_cate__qcyp": {"message": "Aksesori Auto"}, "sbi_alibaba_cate__qmpj": {"message": "Bahagian-bahagian auto"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Komputer digital"}, "sbi_alibaba_cate__snqj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan pem<PERSON>an"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON>kh<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "पायघोळ आणि स्कर्ट"}, "sbi_alibaba_cate__txcp": {"message": "Produk komunikasi"}, "sbi_alibaba_cate__tz": {"message": "Pakaian kanak-kanak"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Alat perkakasan"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON>g kulit"}, "sbi_alibaba_cate__xmhz": {"message": "k<PERSON><PERSON><PERSON> projek"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "<PERSON><PERSON><PERSON> sukan"}, "sbi_alibaba_cate__ydhw": {"message": "<PERSON><PERSON> luar"}, "sbi_alibaba_cate__yjkc": {"message": "Mineral Metalurgi"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentasi"}, "sbi_alibaba_cate__ys": {"message": "Cetak"}, "sbi_alibaba_cate__yyby": {"message": "Rawatan perubatan"}, "sbi_alibaba_cn_kj_90mjs": {"message": "मागील 90 दिवसांत खरेदीदारांची संख्या"}, "sbi_alibaba_cn_kj_90xsl": {"message": "मागी<PERSON> 90 दिवसांत विक्रीचे प्रमाण"}, "sbi_alibaba_cn_kj_gjsj": {"message": "अंदाजित किंमत"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "आंतरराष्ट्रीय शिपिंग फी"}, "sbi_alibaba_cn_kj_gjyf": {"message": "शिपिंग फी"}, "sbi_alibaba_cn_kj_gssj": {"message": "अंदाजित किंमत"}, "sbi_alibaba_cn_kj_lr": {"message": "नफा"}, "sbi_alibaba_cn_kj_lrgs": {"message": "नफा = अंदाजित किंमत x नफा मार्जिन"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "सरासरी वितरण वेग"}, "sbi_alibaba_cn_kj_qtfy": {"message": "इतर फी"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "इतर किंमत = अंदाजे किंमत x इतर किंमतीचे प्रमाण"}, "sbi_alibaba_cn_kj_spjg": {"message": "किंमत"}, "sbi_alibaba_cn_kj_spzl": {"message": "वजन"}, "sbi_alibaba_cn_kj_szd": {"message": "स्थान"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "तुकडे"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "तुकडे"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "हरभरा"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "खरेदीदार"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "तुकडे"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "सेट्स"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "दिवस"}, "sbi_alibaba_cn_kj_zwbj": {"message": "किंमत नाही"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "किंमत"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "विक्रीसाठी उपलब्ध"}, "sbi_aliprice_alibaba_cn__moren": {"message": "डीफॉल्ट"}, "sbi_aliprice_alibaba_cn__queding": {"message": "नक्की"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "विक्री"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "फर्निचर"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "अल्पोपहार"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "मेकअप"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "मुख्य कपड्याखाली घालायचे आतील कपडे"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "अॅक्सेसरीज"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "बाटलीबंद पेय"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "इतर"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "परकर"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "जाकीट"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "इलेक्ट्रॉनिक्स"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "खेळणी"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "सामान"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "तळ"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "बूट"}, "sbi_aliprice_cate__apparel": {"message": "परिधान"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "वाहन आणि मोटारसायकली"}, "sbi_aliprice_cate__beauty_health": {"message": "सौंदर्य आणि आरोग्य"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "सेलफोन आणि दूरसंचार"}, "sbi_aliprice_cate__computer_office": {"message": "संगणक व कार्यालय"}, "sbi_aliprice_cate__consumer_electronics": {"message": "उपभोक्ता इलेक्ट्रॉनिक्स"}, "sbi_aliprice_cate__education_office_supplies": {"message": "शिक्षण व कार्यालयीन पुरवठा"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "इलेक्ट्रॉनिक घटक आणि पुरवठा"}, "sbi_aliprice_cate__furniture": {"message": "फर्निचर"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "केस विस्तार आणि विग"}, "sbi_aliprice_cate__home_garden": {"message": "घर आणि बाग"}, "sbi_aliprice_cate__home_improvement": {"message": "गृह सुधार"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "दागिने व अ‍ॅक्सेसरीज"}, "sbi_aliprice_cate__luggage_bags": {"message": "सामान आणि बॅग"}, "sbi_aliprice_cate__mother_kids": {"message": "आई आणि मुले"}, "sbi_aliprice_cate__novelty_special_use": {"message": "नवीनता आणि विशेष उपयोग"}, "sbi_aliprice_cate__security_protection": {"message": "सुरक्षा आणि संरक्षण"}, "sbi_aliprice_cate__shoes": {"message": "शूज"}, "sbi_aliprice_cate__sports_entertainment": {"message": "खेळ आणि करमणूक"}, "sbi_aliprice_cate__toys_hobbies": {"message": "खेळणी आणि छंद"}, "sbi_aliprice_cate__watches": {"message": "घड्याळे"}, "sbi_aliprice_cate__weddings_events": {"message": "विवाहसोहळा आणि कार्यक्रम"}, "sbi_btn_capture_txt": {"message": "कॅप्चर करा"}, "sbi_btn_source_now_txt": {"message": "आता स्रोत"}, "sbi_button__chat_with_me": {"message": "माझ्याशी गप्पा मार"}, "sbi_button__contact_supplier": {"message": "संपर्क"}, "sbi_button__hide_on_this_site": {"message": "या साइटवर दर्शवू नका"}, "sbi_button__open_settings": {"message": "प्रतिमेनुसार शोध कॉन्फिगर करा"}, "sbi_capture_shortcut_tip": {"message": "किंवा कीबोर्डवरील \"एंटर\" की दाबा"}, "sbi_capturing_tip": {"message": "कॅप्चरिंग"}, "sbi_composed_rating_45": {"message": "४.५ - ५.० तारे"}, "sbi_crop_and_search": {"message": "शोधा"}, "sbi_crop_start": {"message": "स्क्रीनशॉट वापरा"}, "sbi_err_captcha_action": {"message": "सत्यापित करा"}, "sbi_err_captcha_for_alibaba_cn": {"message": "सत्यापन आवश्यक आहे, कृपया सत्यापित करण्यासाठी एक चित्र अपलोड करा. ($video_tutorial$ पहा किंवा कुकीज साफ करण्याचा प्रयत्न करा)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "असामान्य रहदारी, कृपया सत्यापित करा"}, "sbi_err_captcha_for_taobao": {"message": "ताबाओ तुम्हाला सत्यापित करण्यासाठी विनंती करतात, कृपया व्यक्तिचलितरित्या एक चित्र अपलोड करा आणि ते सत्यापित करण्यासाठी शोधा. ही त्रुटी \"प्रतिमेद्वारे TaoBao शोध\" नवीन सत्यापित धोरणामुळे झाली आहे, आम्ही आपणास सूचित करतो की Taobao $feedback$ वर वारंवार तक्रारीची पडताळणी करा.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "अभिप्राय"}, "sbi_err_captcha_msg": {"message": "$platform$ ला तुम्ही शोधण्यासाठी इमेज अपलोड करणे आवश्यक आहे किंवा शोध निर्बंध काढून टाकण्यासाठी सुरक्षा पडताळणी पूर्ण करणे आवश्यक आहे", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "ती नवीनतम आवृत्ती आहे का ते तपासा"}, "sbi_err_cookie_btn_clear": {"message": "कुकीज साफ करा"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 कुकीज साफ करा? (पुन्हा लॉग इन करणे आवश्यक आहे)"}, "sbi_err_desperate_feature_pdd": {"message": "प्रतिमा शोध कार्य प्रतिमा विस्ताराद्वारे Pinduoduo शोध मध्ये हलविले गेले आहे."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "इमेज सर्चचा यशाचा दर कसा वाढवायचा?"}, "sbi_err_img_undersize": {"message": "प्रतिमा > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "लॉग इन करा $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "लॉग इन करा"}, "sbi_err_low_version": {"message": "नवीनतम आवृत्ती स्थापित करा ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "डाउनलोड करा"}, "sbi_err_need_help": {"message": "Help. मदत हवी आहे"}, "sbi_err_network": {"message": "नेटवर्क एरर, तुम्ही वेबसाइटला भेट देऊ शकता याची खात्री करा"}, "sbi_err_not_low_version": {"message": "नवीनतम आवृत्ती स्थापित केली गेली आहे ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "पुन्हा प्रयत्न करा"}, "sbi_err_try_again_action": {"message": "पुन्हा प्रयत्न करा"}, "sbi_err_visit_and_try": {"message": "पुन्हा प्रयत्न करा किंवा पुन्हा प्रयत्न करण्यासाठी $website$ ला भेट द्या", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ मुख्यपृष्ठास भेट द्या", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "लोड करणे अयशस्वी झाले, कृपया पृष्ठ रीफ्रेश करा आणि पुन्हा प्रयत्न करा."}, "sbi_kuajing_filter_area": {"message": "क्षेत्रफळ"}, "sbi_kuajing_filter_au": {"message": "ऑस्ट्रेलिया"}, "sbi_kuajing_filter_btn_confirm": {"message": "पुष्टी"}, "sbi_kuajing_filter_de": {"message": "जर्मनी"}, "sbi_kuajing_filter_destination_country": {"message": "गंतव्य देश"}, "sbi_kuajing_filter_es": {"message": "स्पेन"}, "sbi_kuajing_filter_estimate": {"message": "अंदाज"}, "sbi_kuajing_filter_estimate_price": {"message": "अंदाजे किंमत"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "अंदाजित किंमत सूत्र = (वस्तूंची किंमत + आंतरराष्ट्रीय रसद मालवाहतूक)/(1 - नफा मार्जिन - इतर खर्च गुणोत्तर)"}, "sbi_kuajing_filter_fr": {"message": "फ्रान्स"}, "sbi_kuajing_filter_kw_placeholder": {"message": "शीर्षकाशी जुळण्यासाठी कीवर्ड प्रविष्ट करा"}, "sbi_kuajing_filter_logistics": {"message": "<PERSON><PERSON><PERSON> साचा"}, "sbi_kuajing_filter_logistics_china_post": {"message": "चायना पोस्ट एअर मेल"}, "sbi_kuajing_filter_logistics_discount": {"message": "रसद सवलत"}, "sbi_kuajing_filter_logistics_epacket": {"message": "एपॅकेट"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "आंतरराष्ट्रीय रसद मालवाहतूक = (वजन x शिपिंग किंमत + नोंदणी शुल्क) x (1 - सवलत)"}, "sbi_kuajing_filter_others_fee": {"message": "इतर शुल्क"}, "sbi_kuajing_filter_profit_percent": {"message": "नफ्याचे अंतर"}, "sbi_kuajing_filter_prop": {"message": "गुणधर्म"}, "sbi_kuajing_filter_ru": {"message": "रशिया"}, "sbi_kuajing_filter_total": {"message": "$count$ सारखे आयटम जुळवा", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "यूके"}, "sbi_kuajing_filter_usa": {"message": "अमेरिका"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo घाऊक"}, "sbi_msg_no_result": {"message": "कोणताही परिणाम आढळला नाही,कृपया $loginSite$ वर लॉग इन करा किंवा दुसरे चित्र वापरून पहा", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "सफारीसाठी तात्पुरते उपलब्ध नाही, कृपया $supportPage$ वापरा.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome ब्राउझर आणि त्याचे विस्तार"}, "sbi_msg_no_result_reinstall_l1": {"message": "कोणताही परिणाम आढळला नाही, कृपया $loginSite$ वर लॉग इन करा किंवा दुसरे चित्र वापरुन पहा किंवा नवीन आवृत्ती $latestExtUrl$ पुन्हा स्थापित करा", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "नवीनतम आवृत्ती", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "निवडलेले क्षेत्र"}, "sbi_shipping_": {"message": "त्याच दिवशी शिपिंग"}, "sbi_specify_category": {"message": "श्रेणी निर्दिष्ट करा:"}, "sbi_start_crop": {"message": "क्षेत्र निवडा"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 परदेशात"}, "sbi_tutorial_btn_more": {"message": "अधिक मार्ग"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao कूपन शोधा"}, "sbi_txt__empty_retry": {"message": "क्षमस्व, कोणतेही परिणाम आढळले नाहीत, कृपया पुन्हा प्रयत्न करा."}, "sbi_txt__min_order": {"message": "मि. ऑर्डर"}, "sbi_visiting": {"message": "ब्राउझिंग"}, "sbi_yiwugo__jiagexiangtan": {"message": "किंमतीसाठी विक्रेत्याशी संपर्क साधा"}, "sbi_yiwugo__qigou": {"message": "$num$ तुकडे (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "तारे"}, "searchByImage_screenshot": {"message": "एक-क्लिक स्क्रीनशॉट"}, "searchByImage_search": {"message": "एकाच आयटमसाठी एक-क्लिक शोधा"}, "searchByImage_size_type": {"message": "फाइलचा आकार $num$ MB, $type$ पेक्षा मोठा असू शकत नाही", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "प्रतिमेचे विश्लेषण करीत आहे"}, "search_by_image_progress_searching": {"message": "उत्पादनांचा शोध घ्या"}, "search_by_image_progress_sending": {"message": "प्रतिमा पाठवित आहे"}, "search_by_image_response_rate": {"message": "प्रतिसाद दर: या पुरवठादाराशी संपर्क साधणा bu्या खरेदीदारांच्या $responseRate$ $responseInHour$ तासांच्या आत प्रतिसाद मिळाला.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "कीवर्डनुसार शोधा:"}, "select_country_language_modal_title_country": {"message": "देश"}, "select_country_language_modal_title_language": {"message": "इंग्रजी"}, "select_country_region_modal_title": {"message": "एखादा देश / प्रांत निवडा"}, "select_language_modal_title": {"message": "एक भाषा निवडा:"}, "select_shop": {"message": "स्टोअर निवडा"}, "sellers_count": {"message": "वर्तमान पृष्ठावरील विक्रेत्यांची संख्या"}, "sellers_count_per_page": {"message": "वर्तमान पृष्ठावरील विक्रेत्यांची संख्या"}, "service_score": {"message": "सर्वसमावेशक सेवा रेटिंग"}, "set_shortcut_keys": {"message": "शॉर्टकट की सेट करा"}, "setting_logo_title": {"message": "खरेदी सहाय्यक"}, "setting_modal_options_position_title": {"message": "प्लग-इन स्थिती"}, "setting_modal_options_position_value_left": {"message": "डावा कोपरा"}, "setting_modal_options_position_value_right": {"message": "उजवा कोपरा"}, "setting_modal_options_theme_title": {"message": "थीम रंग"}, "setting_modal_options_theme_value_dark": {"message": "गडद"}, "setting_modal_options_theme_value_light": {"message": "प्रक<PERSON>श"}, "setting_modal_title": {"message": "सेटिंग्ज"}, "setting_options_country_title": {"message": "देश / प्रदेश"}, "setting_options_hover_zoom_desc": {"message": "झूम वाढविण्यासाठी माउस प्रती"}, "setting_options_hover_zoom_title": {"message": "झूम झूम करा"}, "setting_options_jd_coupon_desc": {"message": "JD.com वर कूपन सापडले"}, "setting_options_jd_coupon_title": {"message": "JD.com कूपन"}, "setting_options_language_title": {"message": "इंग्रजी"}, "setting_options_price_drop_alert_desc": {"message": "जेव्हा माझ्या आवडत्या उत्पादनांची किंमत कमी होते, तेव्हा आपणास पुश सूचना प्राप्त होईल."}, "setting_options_price_drop_alert_title": {"message": "किंमत कमी करण्याचा इशारा"}, "setting_options_price_history_on_list_page_desc": {"message": "उत्पादन शोध पृष्ठावर किंमत इतिहास प्रदर्शित करा"}, "setting_options_price_history_on_list_page_title": {"message": "किंमत इतिहास (सूची पृष्ठ)"}, "setting_options_price_history_on_produt_page_desc": {"message": "उत्पादन तपशील पृष्ठावर उत्पादन इतिहास प्रदर्शित करा"}, "setting_options_price_history_on_produt_page_title": {"message": "किंमत इतिहास (तपशील पृष्ठ)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ उत्पादन सूची पृष्ठावरील किंमत, विक्रीचे प्रमाण, विक्रेत्यांची संख्या आणि स्टोअर विक्री गुणोत्तर यांच्या आकडेवारीचे समर्थन करा", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "विक्री विश्लेषण"}, "setting_options_save_success_msg": {"message": "यश"}, "setting_options_tacking_price_title": {"message": "किंमत बदला सूचना"}, "setting_options_value_off": {"message": "बंद"}, "setting_options_value_on": {"message": "चालू"}, "setting_pkg_quick_view_desc": {"message": "समर्थन: 1688 आणि Taobao"}, "setting_saved_message": {"message": "बदल यशस्वीरित्या जतन केले"}, "setting_section_enable_platform_title": {"message": "चालु बंद"}, "setting_section_setting_title": {"message": "सेटिंग्ज"}, "setting_section_shortcuts_title": {"message": "शॉर्टकट्स"}, "settings_aliprice_agent__desc": {"message": "$platforms$ उत्पादन तपशील पृष्ठावर प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "माझ्यासाठी खरेदी करा"}, "settings_copy_link__desc": {"message": "उत्पादन तपशील पृष्ठावर प्रदर्शित करा"}, "settings_copy_link__title": {"message": "कॉपी बटण आणि शीर्षक शोधा"}, "settings_currency_desc__for_detail": {"message": "समर्थन 1688 उत्पादन तपशील पृष्ठ"}, "settings_currency_desc__for_list": {"message": "प्रतिमेनुसार शोधा (परदेशात / ताओबाओमध्ये 1688/1688 समाविष्ट करा)"}, "settings_currency_desc__for_sbi": {"message": "किंमत निवडा"}, "settings_currency_desc_display_for_list": {"message": "इमेज शोधात दाखवले (१६८८/१६८८ परदेशी/ताओबाओसह)"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\" वरून विनिमय दर अद्यतनित करत आहे", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "बँक ऑफ चायना"}, "settings_download_images__desc": {"message": "$platforms$ वरून प्रतिमा डाउनलोड करण्यासाठी समर्थन", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "प्रतिमा डाउनलोड करा बटण"}, "settings_download_reviews__desc": {"message": "$platforms$ उत्पादन तपशील पृष्ठावर प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "पुनरावलोकन प्रतिमा डाउनलोड करा"}, "settings_google_translate_desc": {"message": "गूगल ट्रान्सलेशन बार मिळविण्यासाठी राइट क्लिक करा"}, "settings_google_translate_title": {"message": "वेब पृष्ठ अनुवाद"}, "settings_historical_trend_desc": {"message": "उत्पादन सूची पृष्ठावरील प्रतिमेच्या तळाशी उजव्या कोपऱ्यात प्रदर्शित करा"}, "settings_modal_btn_more": {"message": "अधिक सेटिंग्ज"}, "settings_productInfo_desc": {"message": "उत्पादन सूची पृष्ठावर अधिक तपशीलवार उत्पादन माहिती प्रदर्शित करा. हे सक्षम केल्याने संगणकाचा भार वाढू शकतो आणि पृष्ठ विलंब होऊ शकतो. जर ते कार्यक्षमतेवर परिणाम करत असेल, तर ते अक्षम करण्याची शिफारस केली जाते."}, "settings_product_recommend__desc": {"message": "$platforms$ उत्पादन तपशील पृष्ठावरील मुख्य प्रतिमेच्या खाली प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "उत्पादने शिफारस"}, "settings_research_desc": {"message": "उत्पादन सूची पृष्ठावर अधिक तपशीलवार माहितीसाठी चौकशी करा"}, "settings_sbi_add_to_list": {"message": "$listType$ मध्ये जोडा", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "प्रतिमा शोध परिणाम लघुप्रतिमा"}, "settings_sbi_remove_from_list": {"message": "$listType$ मधून काढा", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "ब्लॉकलिस्टमध्ये जोडा"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "प्रवेशाची स्थिती समायोजित करा"}, "settings_search_by_image_blacklist_desc": {"message": "ब्लॅकलिस्टमध्ये वेबसाइटवर चिन्ह दर्शवू नका."}, "settings_search_by_image_blacklist_title": {"message": "ब्लॉकलिस्ट"}, "settings_search_by_image_bottom_left": {"message": "खाली डावीकडे"}, "settings_search_by_image_bottom_right": {"message": "खाली उजवीकडे"}, "settings_search_by_image_clear_blacklist": {"message": "ब्लॉकलिस्ट साफ करा"}, "settings_search_by_image_detail_page_icon_title": {"message": "लघुप्रतिमा"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "मोठा"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "प्रतिमेनुसार शोधण्यासाठी चिन्हावर क्लिक करा"}, "settings_search_by_image_display_button_title": {"message": "प्रतिमांवर चिन्ह"}, "settings_search_by_image_sourece_websites_desc": {"message": "या वेबसाइटवर स्त्रोत उत्पादन शोधा"}, "settings_search_by_image_sourece_websites_title": {"message": "प्रतिमा निकालानुसार शोधा"}, "settings_search_by_image_top_left": {"message": "वर डावीकडे"}, "settings_search_by_image_top_right": {"message": "वर उजवीकडे"}, "settings_search_keyword_on_x__desc": {"message": "$platform$ वर शब्द शोधा", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "शब्द निवडल्यावर $platform$ चिन्ह दाखवा", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "त्या वेबसाइटवर समान उत्पादन शोधण्याचा प्रयत्न करा (जास्तीत जास्त 5)"}, "settings_similar_products_title": {"message": "समान उत्पादन शोधा"}, "settings_toolbar_expand_title": {"message": "प्लग-इन कमी करा"}, "settings_top_toolbar_desc": {"message": "पृष्ठाच्या शीर्षस्थानी शोध बार"}, "settings_top_toolbar_title": {"message": "शो<PERSON> बार"}, "settings_translate_search_desc": {"message": "चीनी भाषेत भाषांतर करा आणि शोधा"}, "settings_translate_search_title": {"message": "बहुभाषिक शोध"}, "settings_translator_contextmenu_title": {"message": "भाषांतर करण्यासाठी कॅप्चर करा"}, "settings_translator_title": {"message": "भाषां<PERSON>र करा"}, "shai_xuan_dao_chu": {"message": "निर्यात करण्यासाठी फिल्टर करा"}, "shai_xuan_zi_duan": {"message": "फील्ड फिल्टर करा"}, "shang_jia_shi_jian": {"message": "शेल्फ वेळेवर"}, "shang_pin_biao_ti": {"message": "उत्पादन शीर्षक"}, "shang_pin_dui_bi": {"message": "उत्पादन तुलना"}, "shang_pin_lian_jie": {"message": "उत्पादन लिंक"}, "shang_pin_xin_xi": {"message": "उत्पादन माहिती"}, "share_modal__content": {"message": "आपल्या मित्रांसह सामायिक करा"}, "share_modal__disable_for_while": {"message": "मला काहीही सामायिक करायचे नाही"}, "share_modal__title": {"message": "आपल्याला एएए आवडते?"}, "sheng_yu_ku_cun": {"message": "बा<PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "ते सानुकूल करण्यायोग्य आहे का?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "प्रमाणित पुरवठादार"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "प्रमाणित पुरवठादार"}, "shi_fou_you_mao_yi_dan_bao": {"message": "व्यापार आश्वासन"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "व्यापार हमी"}, "shipping_fee": {"message": "शिपिंग शुल्क"}, "shop_followers": {"message": "खरेदी करणारे अनुयायी"}, "shou_qi": {"message": "कमी"}, "similar_products_warn_max_platforms": {"message": "कमाल ते 5"}, "sku_calc_price": {"message": "गणित किंमत"}, "sku_calc_price_settings": {"message": "गणित किंमत सेटिंग्ज"}, "sku_formula": {"message": "सूत्र"}, "sku_formula_desc": {"message": "सूत्र वर्णन"}, "sku_formula_desc_text": {"message": "जटिल गणितीय सूत्रांना समर्थन देते, ज्यामध्ये मूळ किंमत A ने दर्शविली जाते आणि मालवाहतूक B ने दर्शविली जाते\n\n<br/>\n\nकंस (), अधिक +, वजा -, गुणाकार * आणि भागाकार /\n\n<br/>\n\nउदाहरण:\n\n<br/>\n\n१. मूळ किंमतीच्या १.२ पट साध्य करण्यासाठी आणि नंतर मालवाहतूक जोडण्यासाठी, सूत्र आहे: A*१.२+B\n\n<br/>\n\n२. मूळ किंमत अधिक १ युआन साध्य करण्यासाठी, नंतर १.२ पट गुणाकार करण्यासाठी, सूत्र आहे: (A+१)*१.२\n\n<br/>\n\n३. मूळ किंमत अधिक १० युआन साध्य करण्यासाठी, नंतर १.२ पट गुणाकार करा आणि नंतर ३ युआन वजा करा, सूत्र आहे: (A+१०)*१.२-३"}, "sku_in_stock": {"message": "स्टॉकमध्ये"}, "sku_invalid_formula_format": {"message": "अवैध सूत्र स्वरूप"}, "sku_inventory": {"message": "इन्व्हेंटरी"}, "sku_link_copy_fail": {"message": "यशस्वीरित्या कॉपी केले, sku तपशील आणि विशेषता निवडल्या नाहीत"}, "sku_link_copy_success": {"message": "यशस्वीरित्या कॉपी केले, sku तपशील आणि विशेषता निवडल्या"}, "sku_list": {"message": "SKU यादी"}, "sku_min_qrder_qty": {"message": "किमान ऑर्डर प्रमाण"}, "sku_name": {"message": "SKU नाव"}, "sku_no": {"message": "क्रमांक"}, "sku_original_price": {"message": "मूळ किंमत"}, "sku_price": {"message": "SKU किंमत"}, "stop_track_time_label": {"message": "ट्रॅक करण्याची अंतिम मुदत:"}, "suo_zai_di_qu": {"message": "स्थान"}, "tab_pkg_quick_view": {"message": "लॉजिस्टिक मॉनिटर"}, "tab_product_details_price_history": {"message": "किंमत इतिहास"}, "tab_product_details_reviews": {"message": "फोटो पुनरावलोकने"}, "tab_product_details_seller_analysis": {"message": "विक्रेता विश्लेषण"}, "tab_product_details_similar_products": {"message": "समान उत्पादने"}, "total_days_listed_per_product": {"message": "शेल्फ दिवसांची बेरीज ÷ उत्पादनांची संख्या"}, "total_items": {"message": "उत्पादनांची एकूण संख्या"}, "total_price_per_product": {"message": "किमतींची बेरीज ÷ उत्पादनांची संख्या"}, "total_rating_per_product": {"message": "रेटिंगची बेरीज ÷ उत्पादनांची संख्या"}, "total_revenue": {"message": "एकूण महसूल"}, "total_revenue40_items": {"message": "वर्तमान पृष्ठावरील $amount$ उत्पादनांची एकूण कमाई", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "एकूण विक्री"}, "total_sales40_items": {"message": "वर्तमान पृष्ठावरील $amount$ उत्पादनांची एकूण विक्री", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "यासाठी मागोवा घ्या: 1 वर्ष"}, "track_for_3_months": {"message": "यासाठी मागोवा घ्या: 3 महिने"}, "track_for_6_months": {"message": "यासाठी मागोवा घ्या: 6 महिने"}, "tracking_price_email_add_btn": {"message": "ईमेल जोडा"}, "tracking_price_email_edit_btn": {"message": "ईमेल संपादित करा"}, "tracking_price_email_intro": {"message": "आम्ही आपल्याला ईमेलद्वारे सूचित करू."}, "tracking_price_email_invalid": {"message": "कृपया वैध ईमेल द्या"}, "tracking_price_email_verified_desc": {"message": "आपण आता आमची किंमत सोडण्याची सूचना प्राप्त करू शकता."}, "tracking_price_email_verified_title": {"message": "यशस्वीरित्या सत्यापित"}, "tracking_price_email_verify_desc_line1": {"message": "आम्ही आपल्या ईमेल पत्त्यावर एक सत्यापन दुवा पाठविला आहे,"}, "tracking_price_email_verify_desc_line2": {"message": "कृपया आपला ईमेल इनबॉक्स तपासा."}, "tracking_price_email_verify_title": {"message": "ईमेल सत्यापित करा"}, "tracking_price_web_push_notification_intro": {"message": "डेस्कटॉपवर: अलिप्रिस आपल्यासाठी कोणत्याही उत्पादनाचे परीक्षण करू शकते आणि किंमत बदलल्यानंतर आपल्याला वेब पुश सूचना पाठवते."}, "tracking_price_web_push_notification_title": {"message": "वेब पुश सूचना"}, "translate_im__login_required": {"message": "Ali<PERSON><PERSON> द्वारे अनुवादित, कृपया $loginUrl$ मध्ये लॉग इन करा", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "क्लिपबोर्डवर भाषांतरित आणि कॉपी केले, परंतु अलीवांगवांगच्या मर्यादेमुळे, तुम्हाला ते व्यक्तिचलितपणे पेस्ट करणे आवश्यक आहे!"}, "translate_im__send": {"message": "भाषांतर करा आणि पाठवा"}, "translate_search": {"message": "भाषांतर आणि शोध"}, "translation_originals_translated": {"message": "मूळ आणि चीनी"}, "translation_translated": {"message": "चिनी"}, "translator_btn_capture_txt": {"message": "भाषां<PERSON>र करा"}, "translator_language_auto_detect": {"message": "स्वयंचलित ओळख"}, "translator_language_detected": {"message": "शोधले"}, "translator_language_search_placeholder": {"message": "भाषा शोधा"}, "try_again": {"message": "पुन्हा प्रयत्न करा"}, "tu_pian_chi_cun": {"message": "प्रतिमेचा आकार:"}, "tu_pian_lian_jie": {"message": "इमेज लिंक"}, "tui_huan_ti_yan": {"message": "अनुभव परत करा"}, "tui_huan_ti_yan__desc": {"message": "विक्रेत्यांच्या विक्रीनंतरच्या निर्देशकांचे मूल्यांकन करा"}, "tutorial__show_all": {"message": "सर्व वैशिष्ट्ये"}, "tutorial_ae_popup_title": {"message": "विस्तार पिन करा, Aliexpress उघडा"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress पुनरावलोकन विश्लेषण"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "USD ला सपोर्ट करा"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "कोरिया/जपान/मेनलँड चीनला शिपिंग"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 परदेशातील खरेदीला समर्थन देते"}, "tutorial_auto_apply_coupon_title": {"message": "कूपन स्वयं लागू करा"}, "tutorial_btn_end": {"message": "शेवट"}, "tutorial_btn_example": {"message": "उदाहरण"}, "tutorial_btn_see_more": {"message": "अधिक"}, "tutorial_compare_products": {"message": "उत्पादनांची तुलना करा"}, "tutorial_currency_convert_title": {"message": "चलन रूपांतर"}, "tutorial_export_shopping_cart": {"message": "CSV, सपोर्ट Taobao आणि 1688 म्हणून निर्यात करा"}, "tutorial_export_shopping_cart_title": {"message": "कार्ट निर्यात करा"}, "tutorial_price_history_pro": {"message": "उत्पादन तपशील पृष्ठावर प्रदर्शित.\nShop<PERSON>, Lazada, Amazon, Ebay ला सपोर्ट करा"}, "tutorial_price_history_pro_title": {"message": "संपूर्ण वर्ष किंमत इतिहास आणि ऑर्डर इतिहास"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "प्रतिमेद्वारे शोधण्यासाठी कॅप्चर करा"}, "tutorial_translate_search": {"message": "शोधण्यासाठी भाषांतर करा"}, "tutorial_translate_search_and_package_tracking": {"message": "भाषांतर शोध आणि पॅकेज ट्रॅकिंग"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "आदेश"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pcs"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "सपोर्टशी संपर्क साधा"}, "verify_human_verification": {"message": "मानवी पडताळणी"}, "verify_unusual_access": {"message": "असामान्य प्रवेश आढळला"}, "view_history_clean_all": {"message": "सर्व स्वच्छ करा"}, "view_history_clean_all_warring": {"message": "सर्व पाहिलेली रेकॉर्ड साफ करायची?"}, "view_history_clean_all_warring_title": {"message": "चेतावणी"}, "view_history_viewd": {"message": "पाहिले"}, "website": {"message": "वेबसाइट"}, "weight": {"message": "वजन"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "डेटा मिळवण्यात अक्षम"}, "wu_liu_shi_xiao": {"message": "वेळेवर शिपमेंट"}, "wu_liu_shi_xiao__desc": {"message": "विक्रेत्याच्या स्टोअरचा 48-तासांचा संग्रह दर आणि पूर्तता दर"}, "xia_dan_jia": {"message": "अंतिम किंमत"}, "xian_xuan_ze_product_attributes": {"message": "उत्पादन गुणधर्म निवडा"}, "xiao_liang": {"message": "विक्री खंड"}, "xiao_liang_zhan_bi": {"message": "विक्री खंडाची टक्केवारी"}, "xiao_shi": {"message": "$num$ तास", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "महसूल"}, "xiao_shou_e_zhan_bi": {"message": "कमाईची टक्केवारी"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ रेकॉर्ड निवडा", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "निवड"}, "yi_ding_zai_zuo_ce": {"message": "पिन केलेला"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "सर्व उत्पादने लोड केली"}, "yi_nian_xiao_liang": {"message": "वार्षिक विक्री"}, "yi_nian_xiao_liang_zhan_bi": {"message": "वार्षिक विक्री शेअर"}, "yi_nian_xiao_shou_e": {"message": "वार्षिक उलाढाल"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "वार्षिक उलाढाल शेअर"}, "yi_shua_xin": {"message": "ताजेतवाने"}, "yin_cang_xiang_tong_dian": {"message": "समानता लपवा"}, "you_xiao_liang": {"message": "विक्री खंड सह"}, "yu_ji_dao_da_shi_jian": {"message": "अंदाजे आगमन वेळ"}, "yuan_gong_ren_shu": {"message": "कर्मचाऱ्यांची संख्या"}, "yue_cheng_jiao": {"message": "मासिक खंड"}, "yue_dai_xiao": {"message": "ड्रॉपशिपिंग"}, "yue_dai_xiao__desc": {"message": "गेल्या 30 दिवसांमध्ये ड्रॉपशिपिंग विक्री"}, "yue_dai_xiao_pai_xu__desc": {"message": "गेल्या 30 दिवसांमधील ड्रॉपशिपिंग विक्री, उच्च ते निम्न क्रमवारीत"}, "yue_xiao_liang__desc": {"message": "गेल्या 30 दिवसांतील विक्रीचे प्रमाण"}, "zhan_kai": {"message": "अधिक"}, "zhe_kou": {"message": "सवलत"}, "zhi_chi_yi_jian_dai_fa": {"message": "ड्रॉपशिपिंग"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "मोफत शिपिंग"}, "zhi_fu_ding_dan_shu": {"message": "सशुल्क ऑर्डर"}, "zhi_fu_ding_dan_shu__desc": {"message": "या उत्पादनासाठी ऑर्डरची संख्या (३० दिवस)"}, "zhu_ce_xing_zhi": {"message": "नोंदणीचे स्वरूप"}, "zi_ding_yi_tiao_jian": {"message": "सानुकूल अटी"}, "zi_duan": {"message": "फील्ड"}, "zi_ti_xiao_liang": {"message": "तफावत विकली"}, "zong_he_fu_wu_fen": {"message": "एकूण रेटिंग"}, "zong_he_fu_wu_fen__desc": {"message": "विक्रेता सेवेचे एकूण रेटिंग"}, "zong_he_fu_wu_fen__short": {"message": "रेटिंग"}, "zong_he_ti_yan_fen": {"message": "रेटिंग"}, "zong_he_ti_yan_fen_3": {"message": "4 तारे खाली"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 तारे"}, "zong_he_ti_yan_fen_4_5": {"message": "४.५ - ५.० तारे"}, "zong_he_ti_yan_fen_5": {"message": "5 तारे"}, "zong_ku_cun": {"message": "एकूण यादी"}, "zong_xiao_liang": {"message": "एकूण विक्री"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "गेल्या 30 दिवसांमध्ये 3-मिनिटांचा प्रतिसाद दर"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "गेल्या 30 दिवसांमध्ये 48H पुनर्प्राप्ती दर"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "गेल्या 30 दिवसांमध्ये 48H कामगिरी दर"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "ट्रेडिंग रेकॉर्ड (३० दिवस)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "ट्रेडिंग रेकॉर्ड (३० दिवस)"}, "zui_jin_30D_jiu_fen_lv": {"message": "गेल्या 30 दिवसांमधील विवाद दर"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "गेल्या 30 दिवसांतील गुणवत्ता परतावा दर"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "गेल्या 30 दिवसांमध्ये पेमेंट ऑर्डरची संख्या"}}