{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Titik Penjualan Panas Lintas Batas"}, "1688_shi_li_ren_zheng": {"message": "1688 Sertifikasi Ke<PERSON>n"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Lebih dari 1 tahun"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Pengiriman dalam waktu 24 jam"}, "24H_lan_shou_lv": {"message": "<PERSON><PERSON><PERSON> 24 jam"}, "30D_shang_xin": {"message": "Barang Baru B<PERSON>nan"}, "30d_sales": {"message": "$amount$ terjual dalam 30 hari", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON><PERSON><PERSON> dalam 3 menit."}, "3Min_xiang_ying_lv__desc": {"message": "Proporsi respons efektif <PERSON> terhadap pesan pertanyaan pembeli dalam 3 menit dalam 30 hari terakhir"}, "48H": {"message": "48 jam"}, "48H_fa_huo": {"message": "Pengiriman dalam waktu 48 jam"}, "48H_lan_shou_lv": {"message": "<PERSON><PERSON><PERSON> 48 jam"}, "48H_lan_shou_lv__desc": {"message": "Rasio jumlah pesanan yang diambil dalam waktu 48 jam dengan jumlah total pesanan"}, "48H_lv_yue_lv": {"message": "Tingkat kinerja 48 jam"}, "48H_lv_yue_lv__desc": {"message": "Rasio jumlah pesanan yang diambil atau dikirimkan dalam waktu 48 jam terhadap jumlah total pesanan"}, "72H": {"message": "72 jam"}, "7D_shang_xin": {"message": "Barang Baru Ming<PERSON>n"}, "7D_wu_li_you": {"message": "7 hari bebas perawatan"}, "ABS_title_text": {"message": "Iklan ini menyertakan cerita merek"}, "AC_title_text": {"message": "Iklan ini memiliki lencana <PERSON>"}, "A_title_text": {"message": "Iklan ini memiliki halaman konten A+"}, "BS_title_text": {"message": "Iklan ini diperingkat sebagai Penjual Terbaik $num$ dalam kategori $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Penawaran Waktu Terbatas) berarti iklan ini merupakan bagian dari acara \"promosi 7 hari\""}, "NR_title_text": {"message": "Iklan ini diperingkat sebagai Rilis Baru $num$ dalam kategori $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Iklan ini memiliki iklan video, jenis iklan PPC yang biasanya muncul di tengah hasil penelusuran"}, "SB_title_text": {"message": "Iklan ini memiliki iklan merek, jenis iklan PPC yang biasanya muncul di bagian atas atau bawah hasil penelusuran"}, "SP_title_text": {"message": "Iklan ini memiliki iklan Produk Bersponsor"}, "V_title_text": {"message": "Iklan ini memiliki video pengantar"}, "advanced_research": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688___my_order": {"message": "pesananku"}, "agent_ds1688__add_to_cart": {"message": "<PERSON><PERSON>bel<PERSON>"}, "agent_ds1688__cart": {"message": "Bevás<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__desc": {"message": "Disediakan oleh 1688. Mendukung pembelian langsung dari luar negeri, pembayaran dalam USD, dan pen<PERSON> ke gudang transit Anda di Tiongkok."}, "agent_ds1688__freight": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__help": {"message": "Membantu"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Pusat P<PERSON>badi"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON><PERSON> saya"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_available": {"message": "Tersedia"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON> tidak men<PERSON>, silakan isi ulang"}, "ai_comment_analysis_behavior": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_characteristic": {"message": "Karakteristik kerumunan"}, "ai_comment_analysis_comment": {"message": "Produk tidak memiliki cukup ulasan untuk menarik kesimpulan yang akurat, silakan pilih produk dengan lebih banyak ulasan."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_default": {"message": "Ulasan default"}, "ai_comment_analysis_desire": {"message": "Ekspektasi pelanggan"}, "ai_comment_analysis_disadvantage": {"message": "Kekurangan"}, "ai_comment_analysis_free": {"message": "Percobaan gratis"}, "ai_comment_analysis_freeNum": {"message": "1 kredit gratis akan digunakan"}, "ai_comment_analysis_go_recharge": {"message": "<PERSON><PERSON> isi ulang"}, "ai_comment_analysis_intelligence": {"message": "<PERSON><PERSON><PERSON> ul<PERSON> cerdas"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_motive": {"message": "<PERSON><PERSON><PERSON><PERSON>em<PERSON>"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON>, silakan coba lagi"}, "ai_comment_analysis_normal": {"message": "<PERSON>lasan foto"}, "ai_comment_analysis_number_reviews": {"message": "<PERSON><PERSON><PERSON>: $num$, Estimasi Konsumsi: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON><PERSON> umum"}, "ai_comment_analysis_percentage": {"message": "Persentase"}, "ai_comment_analysis_problem": {"message": "<PERSON><PERSON><PERSON> dengan pem<PERSON>"}, "ai_comment_analysis_reanalysis": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON> telah mengisi ulang"}, "ai_comment_analysis_retry": {"message": "Coba lagi"}, "ai_comment_analysis_scene": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_start": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_tool": {"message": "Alat AI"}, "ai_comment_analysis_user_portrait": {"message": "<PERSON>il <PERSON>"}, "ai_comment_analysis_welcome": {"message": "Selamat datang di analisis ulasan AI"}, "ai_comment_analysis_year": {"message": "<PERSON><PERSON><PERSON> dari tahun lalu"}, "ai_listing_Exclude_keywords": {"message": "Kecualikan kata kunci"}, "ai_listing_Login_the_feature": {"message": "<PERSON><PERSON> diperlukan untuk fitur ini"}, "ai_listing_aI_generation": {"message": "generasi AI"}, "ai_listing_add_automatic": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_add_dictionary_new": {"message": "<PERSON><PERSON>t per<PERSON>an baru"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON>n kata kunci"}, "ai_listing_add_inputkey_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> nilai jual dan tekan $key$ untuk menyelesaikan penambahan", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Batas terlampaui, hingga $amount$ poin penjualan", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Tambahkan kata kunci"}, "ai_listing_add_manually": {"message": "Tambahkan secara manual"}, "ai_listing_add_selling": {"message": "<PERSON><PERSON><PERSON> nilai jual"}, "ai_listing_added_keywords": {"message": "Menambahkan kata kunci"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON>kkan kata kunci yang dikecualikan, tekan enter untuk menyelesaikan penambahan."}, "ai_listing_adding_selling": {"message": "Menambah<PERSON> nilai jual"}, "ai_listing_addkeyword_enter": {"message": "Ketik kata atribut kunci dan tekan enter untuk menyelesaikan penambahan"}, "ai_listing_ai_description": {"message": "Perpustakaan kata <PERSON>ripsi AI"}, "ai_listing_ai_dictionary": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kata judul AI"}, "ai_listing_ai_title": {"message": "Judul AI"}, "ai_listing_aidescription_repeated": {"message": "<PERSON><PERSON>an kata deskripsi AI tidak dapat diulang"}, "ai_listing_aititle_repeated": {"message": "<PERSON><PERSON>an kata judul AI tidak dapat diulang"}, "ai_listing_data_comes_from": {"message": "Data ini berasal dari:"}, "ai_listing_deleted_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_dictionary_name": {"message": "<PERSON><PERSON>"}, "ai_listing_edit_dictionary": {"message": "<PERSON><PERSON> per<PERSON>..."}, "ai_listing_edit_word_library": {"message": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> kata"}, "ai_listing_enter_keywords": {"message": "Ma<PERSON>kkan kata kunci dan tekan $key$ untuk menyelesaikan penambahan", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Batas telah terlampaui, maksimum $amount$ kata kunci", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Perpustakaan kata yang dikecualikan"}, "ai_listing_generate_characters": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>"}, "ai_listing_generation_platform": {"message": "Platform generasi"}, "ai_listing_help_optimize": {"message": "<PERSON><PERSON> saya men<PERSON><PERSON><PERSON><PERSON><PERSON> judul produk, judul as<PERSON>ya"}, "ai_listing_include_selling": {"message": "<PERSON><PERSON> jual la<PERSON>:"}, "ai_listing_included_keyword": {"message": "Termasuk kata kunci"}, "ai_listing_included_keywords": {"message": "Termasuk kata kunci"}, "ai_listing_input_selling": {"message": "<PERSON><PERSON><PERSON><PERSON> nilai jual"}, "ai_listing_input_selling_fit": {"message": "<PERSON><PERSON><PERSON><PERSON> nilai jual sesuai judul"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON><PERSON> masukkan poin pen<PERSON>"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON><PERSON><PERSON> konten yang diperlukan di atas untuk menghasilkan judul dengan cerdas"}, "ai_listing_keyword_product_title": {"message": "<PERSON><PERSON><PERSON> produk kata kunci"}, "ai_listing_keywords_repeated": {"message": "Kata kunci tidak dapat diulang"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON><PERSON><PERSON> nilai jual<PERSON>"}, "ai_listing_long_title_1": {"message": "Berisi informasi dasar se<PERSON>i na<PERSON> mere<PERSON>, j<PERSON><PERSON> produk, fitur produk, dll."}, "ai_listing_long_title_2": {"message": "Berdasarkan judul produk standar, kata kunci yang kondusif untuk SEO ditambahkan."}, "ai_listing_long_title_3": {"message": "<PERSON><PERSON> berisi <PERSON>, j<PERSON><PERSON> produk, fitur produk, dan kata kunci, kata kunci ekor panjang juga disertakan untuk mencapai peringkat lebih tinggi dalam kueri penelusuran yang spesifik dan tersegmentasi."}, "ai_listing_longtail_keyword_product_title": {"message": "<PERSON><PERSON><PERSON> produk kata kunci ekor panjang"}, "ai_listing_manually_enter": {"message": "Ma<PERSON>kka<PERSON> secara manual ..."}, "ai_listing_network_not_working": {"message": "Internet tidak tersedia, VPN diperlukan untuk mengakses ChatGPT"}, "ai_listing_new_dictionary": {"message": "Buat perpustakaan kata baru ..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Kata-kata opsional"}, "ai_listing_original_title": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_other_keywords_included": {"message": "<PERSON>a kunci lainnya term<PERSON>:"}, "ai_listing_please_again": {"message": "Silakan coba lagi"}, "ai_listing_please_select": {"message": "Judul-judul berikut telah dibuat untuk Anda, silakan pilih:"}, "ai_listing_product_category": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_product_category_is": {"message": "<PERSON><PERSON><PERSON> produknya adalah"}, "ai_listing_product_category_to": {"message": "Produk tersebut termasuk dalam kategori apa?"}, "ai_listing_random_keywords": {"message": "Kata kunci $amount$ acak", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Poin penjualan acak sebesar $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Acak dari perpusta<PERSON>an kata"}, "ai_listing_search_selling": {"message": "<PERSON><PERSON> be<PERSON> nilai jual"}, "ai_listing_select_product_categories": {"message": "<PERSON><PERSON><PERSON> kategori produk secara otomatis."}, "ai_listing_select_product_selling_points": {"message": "<PERSON><PERSON><PERSON> nilai jual produk secara otomatis"}, "ai_listing_select_word_library": {"message": "<PERSON><PERSON><PERSON> kata"}, "ai_listing_selling": {"message": "<PERSON><PERSON>"}, "ai_listing_selling_ask": {"message": "<PERSON><PERSON><PERSON><PERSON> nilai jual apa lagi yang ada untuk judul tersebut?"}, "ai_listing_selling_optional": {"message": "<PERSON><PERSON> jual op<PERSON>"}, "ai_listing_selling_repeat": {"message": "<PERSON>in tidak dapat <PERSON>"}, "ai_listing_set_excluded": {"message": "Tetapkan sebagai perpustakaan kata yang dikecualikan"}, "ai_listing_set_include_selling_points": {"message": "<PERSON><PERSON><PERSON> nilai jual"}, "ai_listing_set_included": {"message": "Tetapkan sebagai perpustakaan kata yang disertakan"}, "ai_listing_set_selling_dictionary": {"message": "Tetapkan sebagai perpusta<PERSON>an nilai jual"}, "ai_listing_standard_product_title": {"message": "<PERSON><PERSON><PERSON> produk standar"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "ai_listing_visit_chatGPT": {"message": "Kunjungi <PERSON>"}, "ai_listing_what_other_keywords": {"message": "Kata kunci apa lagi yang diperlukan untuk judul?"}, "aliprice_coupons_apply_again": {"message": "<PERSON>"}, "aliprice_coupons_apply_coupons": {"message": "Terapkan kupon"}, "aliprice_coupons_apply_success": {"message": "<PERSON><PERSON><PERSON> yang di<PERSON>ukan: Hemat $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Menguji kode untuk penawaran terbaik..."}, "aliprice_coupons_applying_desc": {"message": "Memeriksa: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Lanjutkan ke Pembayaran"}, "aliprice_coupons_found_coupons": {"message": "<PERSON><PERSON> kupon $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Siap untuk checkout? Mari pastikan Anda mendapatkan harga terbaik!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Kode-kode itu tidak berfungsi. <PERSON><PERSON><PERSON> masalah besar—<PERSON>a sudah mendapatkan harga terbaik."}, "aliprice_coupons_toolbar_btn": {"message": "Dapatkan Kupon"}, "aliww_translate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "aliww_translate_supports": {"message": "Dukungan: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON> kunci"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON> semua"}, "amazon_extended_keywords_more": {"message": "Selengkapnya"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Urutkan berdasarkan penjualan kumulatif"}, "an_lei_xing_cha_kan": {"message": "Tipe"}, "an_yue_dai_xiao_pai_xu": {"message": "Pemeringkatan berdasarkan penjualan dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON><PERSON>"}, "apra_chart__name": {"message": "Persentase penjualan produk menurut negara"}, "apra_chart__update_at": {"message": "Perbarui waktu $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statistik penjualan negara"}, "auto_opening": {"message": "Otomatis dibuka dalam $num$ detik", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "$autoPaging$ halaman berikutnya", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Rata-rata pada hari pen<PERSON>n"}, "average_hui_fu_lv": {"message": "<PERSON><PERSON><PERSON> balasan rata-rata"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Tingkat pemasok rata-rata"}, "average_price": {"message": "Harga rata-rata"}, "average_qi_ding_liang": {"message": "<PERSON><PERSON><PERSON><PERSON>a <PERSON>"}, "average_rating": {"message": "<PERSON><PERSON><PERSON> rata-rata"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON>a-rata tahun sertif<PERSON>si"}, "average_revenue": {"message": "Pendapatan rata-rata"}, "average_revenue_per_product": {"message": "Pendapatan total <PERSON><PERSON><PERSON> produk"}, "average_sales": {"message": "Penju<PERSON> rata-rata"}, "average_sales_per_product": {"message": "Total penjualan Jumlah produk"}, "bao_han": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "Menyetorkan"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "<PERSON><PERSON><PERSON>"}, "blacklist_add_blacklist": {"message": "B<PERSON><PERSON>r toko ini"}, "blacklist_address_incorrect": {"message": "<PERSON><PERSON><PERSON><PERSON> salah. <PERSON><PERSON> diperiksa."}, "blacklist_blacked_out": {"message": "<PERSON><PERSON> telah di<PERSON>lo<PERSON>"}, "blacklist_blacklist": {"message": "<PERSON><PERSON><PERSON> hitam"}, "blacklist_no_records_yet": {"message": "Belum ada catatan!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "<PERSON><PERSON><PERSON> pem<PERSON>"}, "btn_aliprice_agent__dropshipping": {"message": "Pengiriman drop"}, "btn_have_a_try": {"message": "Selamat mencoba"}, "btn_refresh": {"message": "Menyegarkan"}, "btn_try_it_now": {"message": "<PERSON><PERSON>"}, "btn_txt_view_on_aliprice": {"message": "<PERSON><PERSON> di AliPrice"}, "bu_bao_han": {"message": "Tidak mengandung"}, "bulk_copy_links": {"message": "Tautan Sal<PERSON> Massal"}, "bulk_copy_products": {"message": "Produk Salinan Massal"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON><PERSON> pela<PERSON>"}, "cai_gou_zi_xun__desc": {"message": "Tingkat respons penjual dalam tiga menit"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON>"}, "cao_zuo": {"message": "Operasi"}, "chan_pin_ID": {"message": "ID Produk"}, "chan_pin_e_wai_xin_xi": {"message": "Info tambahan produk"}, "chan_pin_lian_jie": {"message": "<PERSON><PERSON>"}, "cheng_li_shi_jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klik Salin info produk"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 tahun"}, "common__date_range_1m": {"message": "1 bulan"}, "common__date_range_1w": {"message": "1 minggu"}, "common__date_range_2w": {"message": "2 minggu"}, "common__date_range_3m": {"message": "3 bulan"}, "common__date_range_3w": {"message": "3 minggu"}, "common__date_range_6m": {"message": "6 bulan"}, "common_btn_cancel": {"message": "Membatalkan"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "Menyimpan"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_email": {"message": "<PERSON><PERSON>"}, "common_error_msg_no_data": {"message": "Tidak ada data"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON>, tidak ada hasil yang di<PERSON>ukan."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "<PERSON><PERSON> balik"}, "common_help": {"message": "Tolong"}, "common_loading": {"message": "Memuat"}, "common_login": {"message": "<PERSON><PERSON><PERSON>"}, "common_logout": {"message": "<PERSON><PERSON><PERSON>"}, "common_no": {"message": "Tidak"}, "common_powered_by_aliprice": {"message": "Didukung oleh AliPrice.com"}, "common_setting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON>"}, "common_system_upgrading_title": {"message": "Peningkatan sistem"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON>an coba nanti"}, "common_txt__currency": {"message": "<PERSON>"}, "common_txt__video_tutorial": {"message": "Video tutorial"}, "common_txt_ago_time": {"message": "$time$ hari yang lalu", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "semua"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> tidak pernah digunakan"}, "common_txt_biaoti_link": {"message": "Judul+Tautan"}, "common_txt_biaoti_link_dian_pu": {"message": "Judul+Tautan+<PERSON><PERSON>"}, "common_txt_blacklist": {"message": "Daftar Blokir"}, "common_txt_cancel": {"message": "Membatalkan"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "Memeriksa"}, "common_txt_colors": {"message": "warna"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Di<PERSON>in"}, "common_txt_copy": {"message": "Salinan"}, "common_txt_copy_link": {"message": "<PERSON><PERSON>an"}, "common_txt_copy_title": {"message": "<PERSON><PERSON> judul"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON> judul dan tautan"}, "common_txt_day": {"message": "langit"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "<PERSON>in nama toko + tautan"}, "common_txt_download": {"message": "unduh"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Ekspor Excel"}, "common_txt_export_as_txt": {"message": "Ekspor Teks"}, "common_txt_fail": {"message": "Gaga<PERSON>"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "mendapatkan"}, "common_txt_incert_selection": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_install": {"message": "Install"}, "common_txt_load_failed": {"message": "Gagal untuk memuat"}, "common_txt_month": {"message": "bulan"}, "common_txt_more": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Barang baru, belum pernah dipakai"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_limit": {"message": "Tak terbatas"}, "common_txt_no_noticeable": {"message": "Tidak ada goresan atau kotoran yang terlihat"}, "common_txt_on_sale": {"message": "Tersedia"}, "common_txt_opt_in_out": {"message": "Hidup/Mati"}, "common_txt_order": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_others": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> buruk"}, "common_txt_patterns": {"message": "pola"}, "common_txt_platform": {"message": "Platform"}, "common_txt_please_select": {"message": "<PERSON><PERSON><PERSON> pilih"}, "common_txt_prev": {"message": "Sblm"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Kebijakan pribadi"}, "common_txt_product_condition": {"message": "Status Produk"}, "common_txt_rating": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON> ul<PERSON>"}, "common_txt_reset": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_retail": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_review": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sale": {"message": "Tersedia"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "<PERSON><PERSON> goresan dan kotoran"}, "common_txt_search_title": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_selected": {"message": "terp<PERSON><PERSON>"}, "common_txt_share": {"message": "Bagikan"}, "common_txt_sold": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_sold_out": {"message": "terjual habis"}, "common_txt_some_scratches": {"message": "Beberapa goresan dan kotoran"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON> den<PERSON>"}, "common_txt_state": {"message": "Status"}, "common_txt_success": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_sys_err": {"message": "sistem bermasalah"}, "common_txt_today": {"message": "<PERSON> ini"}, "common_txt_total": {"message": "semua"}, "common_txt_unselect_all": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_upload_image": {"message": "Unggah gambar"}, "common_txt_visit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_whitelist": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_wholesale": {"message": "Grosir"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "<PERSON><PERSON>"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON><PERSON> semua"}, "compare_tool_btn_compare": {"message": "Membandingkan"}, "compare_tool_btn_contact": {"message": "Kontak"}, "compare_tool_tips_max_compared": {"message": "Tambahkan hingga $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Konfigu<PERSON>i Notif<PERSON>"}, "contact_us": {"message": "<PERSON><PERSON><PERSON><PERSON> kami"}, "context_menu_screenshot_search": {"message": "Tangkap untuk mencari berdasarkan gambar"}, "context_menus_aliprice_search_by_image": {"message": "Cari Gambar di AliPrice"}, "context_menus_goote_trans": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>/<PERSON><PERSON><PERSON><PERSON> yang asli"}, "context_menus_search_by_image": {"message": "Telusuri pakai gambar di $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Tangkap ke $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Tangkap untuk diterjemahkan"}, "converter_modal_amount_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> jumlah di sini"}, "converter_modal_btn_convert": {"message": "mengubah"}, "converter_modal_exchange_rate_source": {"message": "Data berasal dari nilai tukar mata uang asing $boc$ Waktu pembaruan: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "konversi mata uang"}, "converter_modal_search_placeholder": {"message": "cari mata uang"}, "copy_product_info": {"message": "Salin info produk"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON> Dropdown"}, "country__han_gou": {"message": "Korea Selatan"}, "country__ri_ben": {"message": "<PERSON><PERSON><PERSON>"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "<PERSON><PERSON> tukar kustom"}, "currency_convert__sync_server": {"message": "Sinkronisasi server"}, "dang_ri_fa_huo": {"message": "Pengiriman di hari yang sama"}, "dao_chu_quan_dian_shang_pin": {"message": "Ekspor Semua Produk Toko"}, "dao_chu_wei_CSV": {"message": "Ekspor"}, "dao_chu_zi_duan": {"message": "Ekspor Bidang"}, "delivery_address": {"message": "<PERSON><PERSON><PERSON>"}, "delivery_company": {"message": "<PERSON><PERSON><PERSON><PERSON> pengantar"}, "di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "Klik untuk menanyakan"}, "dian_pu_ID": {"message": "ID toko"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_pu_lian_jie": {"message": "<PERSON><PERSON>"}, "dian_pu_ming": {"message": "<PERSON><PERSON>"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON> toko"}, "dian_pu_shang_pin_zong_hsu": {"message": "Jumlah Total Produk di Toko: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Informasi toko"}, "ding_zai_zuo_ce": {"message": "<PERSON>u di sebelah kiri"}, "disable_old_version_tips_disable_btn_title": {"message": "Nonaktifkan versi lama"}, "download_image__SKU_variant_images": {"message": "Gambar varian SKU"}, "download_image__assume": {"message": "<PERSON><PERSON><PERSON>, kita memiliki 2 gambar, product1.jpg dan product2.gif.\nimg_{$no$} akan diganti namanya menjadi img_01.jpg, img_02.gif;\n{$group$}_{$no$} akan diganti namanya menjadi main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__combined_image": {"message": "Gambar detail produk gabungan"}, "download_image__continue_downloading": {"message": "Lanju<PERSON><PERSON>"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__download_combined_image": {"message": "Unduh gambar detail produk gabungan"}, "download_image__download_zip": {"message": "Unduh zip"}, "download_image__enlarge_check": {"message": "<PERSON>ya mendukung gambar JPEG, JPG, GIF, dan <PERSON>, ukuran maksimum satu gambar: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Perbesar gambar"}, "download_image__export": {"message": "Ekspor"}, "download_image__height": {"message": "Tingg<PERSON>"}, "download_image__ignore_videos": {"message": "Video telah <PERSON>, karena tidak dapat diekspor"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "gambar utama"}, "download_image__multi_folder": {"message": "Multifolder"}, "download_image__name": {"message": "unduh gambar"}, "download_image__notice_content": {"message": "Tolong jangan centang \"Tanyakan di mana menyimpan setiap file sebelum mengunduh\" di pengaturan unduhan browser Anda!!! <PERSON><PERSON> t<PERSON>, akan ada banyak kotak dialog."}, "download_image__notice_ignore": {"message": "<PERSON>an minta pesan ini lagi"}, "download_image__order_number": {"message": "Nomor seri {$no$}; Nama grup {$group$}; Stempel waktu {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__prompt_download_zip": {"message": "<PERSON><PERSON><PERSON><PERSON> banyak gambar, <PERSON><PERSON><PERSON><PERSON> <PERSON>a mengunduhnya sebagai folder zip."}, "download_image__rename": {"message": "G<PERSON>"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__single_folder": {"message": "Folder tunggal"}, "download_image__sku_image": {"message": "gambar SKU"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "<PERSON><PERSON>"}, "download_reviews__download_images": {"message": "<PERSON><PERSON><PERSON> gambar <PERSON>n"}, "download_reviews__dropdown_title": {"message": "<PERSON><PERSON><PERSON> gambar <PERSON>n"}, "download_reviews__export_csv": {"message": "ekspor CSV"}, "download_reviews__no_images": {"message": "0 gambar tersedia untuk diunduh"}, "download_reviews__no_reviews": {"message": "Tidak ada ulasan untuk diunduh!"}, "download_reviews__notice": {"message": "Tip:"}, "download_reviews__notice__chrome_settings": {"message": "Setel browser Chrome untuk menanyakan tempat menyimpan setiap file sebelum mengunduh, setel ke \"Nonaktif\""}, "download_reviews__notice__wait": {"message": "<PERSON><PERSON><PERSON> pada jumlah ul<PERSON>, waktu tunggu mungkin lebih lama"}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__pages_list__page": {"message": "halaman $page$ sebelumnya", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON><PERSON> pilihan"}, "export_shopping_cart__csv_filed__details_url": {"message": "<PERSON><PERSON> produk"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Tautan gema SKU"}, "export_shopping_cart__csv_filed__images": {"message": "Tautan gambar"}, "export_shopping_cart__csv_filed__quantity": {"message": "Kuantitas"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON> toko"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON><PERSON> toko"}, "export_shopping_cart__csv_filed__title": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__export_btn": {"message": "Ekspor"}, "export_shopping_cart__export_empty": {"message": "<PERSON><PERSON>an pilih produk!"}, "fa_huo_shi_jian": {"message": "Pen<PERSON><PERSON>"}, "favorite_add_email": {"message": "Tambah Email"}, "favorite_add_favorites": {"message": "Tambahkan ke Favorit"}, "favorite_added": {"message": "Ditambahkan"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON>."}, "favorite_btn_notify": {"message": "<PERSON><PERSON> harga"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON><PERSON> produk"}, "favorite_current_price": {"message": "Harga saat ini"}, "favorite_due_date": {"message": "<PERSON><PERSON> jatuh tempo"}, "favorite_enable_notification": {"message": "Harap aktifkan pemberitahuan email"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "Buka untuk mengaktifkan"}, "favorite_msg_add_success": {"message": "Ditambahkan ke favorit"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON><PERSON> dari favorit"}, "favorite_msg_failure": {"message": "Gagal! Segarkan <PERSON>aman dan coba lagi."}, "favorite_please_add_email": {"message": "<PERSON><PERSON><PERSON> ta<PERSON>"}, "favorite_price_drop": {"message": "<PERSON><PERSON>"}, "favorite_price_rise": {"message": "<PERSON><PERSON>"}, "favorite_price_untracked": {"message": "Harga tidak terlacak"}, "favorite_saved_price": {"message": "<PERSON><PERSON> te<PERSON><PERSON><PERSON>"}, "favorite_stop_tracking": {"message": "Hentikan pelacakan"}, "favorite_sub_email_address": {"message": "Alamat email langganan"}, "favorite_tracking_period": {"message": "Periode pelacakan"}, "favorite_tracking_prices": {"message": "Pelacakan harga"}, "favorite_verify_email": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> email"}, "favorites_list_remove_prompt_msg": {"message": "Anda yakin ingin menghap<PERSON>?"}, "favorites_update_button": {"message": "<PERSON><PERSON><PERSON> harga sekarang"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Pilihan Distributor"}, "find_similar": {"message": "<PERSON><PERSON><PERSON>"}, "first_ali_price_date": {"message": "Tanggal ketika pertama kali ditangkap oleh crawler AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Tidak ada kupon"}, "fooview_coupons_modal_title": {"message": "kupon"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Harga < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "atau harga > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Tenggat waktu"}, "fooview_favorites_error_msg_no_favorites": {"message": "Tambahkan produk favorit di sini untuk menerima peringatan penurunan harga."}, "fooview_favorites_filter_latest": {"message": "Terbaru"}, "fooview_favorites_filter_price_drop": {"message": "TURUN"}, "fooview_favorites_filter_price_up": {"message": "KE ATAS"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Buka AliPrice Favourite"}, "fooview_favorites_track_price": {"message": "Untuk melacak harga"}, "fooview_price_history_app_price": {"message": "<PERSON>rga APP:"}, "fooview_price_history_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON> balik"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON><PERSON> tidak menemukan review untuk produk ini."}, "fooview_reviews_filter_buyer_reviews": {"message": "Foto pembeli"}, "fooview_reviews_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON> balik"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_modal_title": {"message": "Temukan produk yang sama"}, "fooview_same_product_search_by_image": {"message": "Telusuri pakai gambar"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "for_12_months": {"message": "Selama 1 tahun"}, "for_12_months_list_pro": {"message": "12 bulan"}, "for_12_months_nei": {"message": "<PERSON><PERSON> waktu 12 bulan"}, "for_1_months": {"message": "1 bulan"}, "for_1_months_nei": {"message": "Dalam waktu 1 bulan"}, "for_3_months": {"message": "Untuk 3 bulan"}, "for_3_months_nei": {"message": "Dalam waktu 3 bulan"}, "for_6_months": {"message": "Selama 6 bulan"}, "for_6_months_nei": {"message": "<PERSON><PERSON> waktu 6 bulan"}, "for_9_months": {"message": "9 bulan"}, "for_9_months_nei": {"message": "<PERSON><PERSON> waktu 9 bulan"}, "fu_gou_lv": {"message": "Tingkat pembelian:"}, "gao_liang_bu_tong_dian": {"message": "Poin tinggi yang berbeda"}, "gao_liang_guang_gao_chan_pin": {"message": "Sorot Produk Iklan"}, "geng_duo_xin_xi": {"message": "Info lebih lanjut"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON> pem<PERSON>uan"}, "get_store_products_fail_tip": {"message": "Klik OK untuk melanjutkan ke verifikasi untuk memastikan akses normal"}, "gong_x_kuan_shang_pin": {"message": "Total $amount$ versi", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Pemasok"}, "gong_ying_shang_ID": {"message": "ID Pemasok"}, "gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON> Pemasok"}, "gong_ying_shang_nian_zhan": {"message": "Pemasok lebih tua"}, "gong_ying_shang_xin_xi": {"message": "Informasi Pemasok"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "<PERSON><PERSON>"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "guan_jian_zi": {"message": "<PERSON><PERSON> kunci"}, "guang_gao_chan_pin": {"message": "Produk iklan"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "Biaya Pengiriman Internasional"}, "guo_lv_tiao_jian": {"message": "Filter"}, "hao_ping_lv": {"message": "Peringkat positif"}, "highest_price": {"message": "Tingg<PERSON>"}, "historical_trend": {"message": "<PERSON><PERSON>ah"}, "how_to_screenshot": {"message": "<PERSON>han tombol kiri mouse untuk memilih area, ketuk tombol kanan mouse atau tombol Esc untuk keluar dari tangkapan layar"}, "howt_it_works": {"message": "Bagaimana itu bekerja"}, "hui_fu_lv": {"message": "Ting<PERSON> Respon"}, "hui_tou_lv": {"message": "<PERSON>g<PERSON> penge<PERSON>lian"}, "inquire_freightFee": {"message": "Pertanyaan tentang Pengiriman"}, "inquire_freightFee_Yuan": {"message": "Pengiriman/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Pengiriman adalah $num$, yang berarti bahwa wilayah tersebut memiliki pengiriman gratis.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Iklan."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Unit"}, "jia_ge_qu_shi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jia_zai_n_ge_shang_pin": {"message": "Muat $num$ Produk", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Persentase volume penjualan dalam 30 hari terakhir"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Persentase pendapatan dalam 30 hari terakhir"}, "jin_30d_xiao_liang": {"message": "Penjualan"}, "jin_30d_xiao_liang__desc": {"message": "Total penjualan dalam 30 hari terakhir"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "Total turnover dalam 30 hari terakhir"}, "jin_90_tian_mai_jia_shu": {"message": "<PERSON><PERSON>beli dalam 90 <PERSON>"}, "jin_90_tian_xiao_shou_liang": {"message": "Penjualan dalam 90 Hari <PERSON>"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON> terpilih"}, "jing_ying_mo_shi": {"message": "Model bisnis"}, "jing_ying_mo_shi__gong_chang": {"message": "Pabrikan"}, "jiu_fen_jie_jue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> seng<PERSON>a"}, "jiu_fen_jie_jue__desc": {"message": "Akuntansi persel<PERSON> hak toko pen<PERSON>al"}, "jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON>"}, "jiu_fen_lv__desc": {"message": "Proporsi pesanan dengan keluhan yang diselesaikan dalam 30 hari terakhir dan dinilai menjadi tanggung jawab penjual atau kedua belah pihak"}, "kai_dian_ri_qi": {"message": "<PERSON><PERSON> pem<PERSON>"}, "keywords": {"message": "<PERSON><PERSON> kunci"}, "kua_jin_Select_pan_huo": {"message": "<PERSON><PERSON><PERSON>"}, "last15_days": {"message": "15 hari terakhir"}, "last180_days": {"message": "180 hari terakhir"}, "last30_days": {"message": "Dalam 30 hari terakhir"}, "last360_days": {"message": "360 hari terakhir"}, "last45_days": {"message": "45 hari terakhir"}, "last60_days": {"message": "60 hari terakhir"}, "last7_days": {"message": "7 hari terakhir"}, "last90_days": {"message": "90 hari terakhir"}, "last_30d_sales": {"message": "Penjualan 30 hari terakhir"}, "lei_ji": {"message": "Kumulatif"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON> setelah produk di rak"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volume penjualan kumulatif dalam 30 hari terakhir, diuru<PERSON>kan dari tinggi ke rendah"}, "lian_xi_fang_shi": {"message": "Kontak informasi"}, "list_time": {"message": "Pada tanggal rak"}, "load_more": {"message": "<PERSON><PERSON>"}, "login_to_aliprice": {"message": "Ma<PERSON>k ke AliPrice"}, "long_link": {"message": "<PERSON><PERSON>"}, "lowest_price": {"message": "Rendah"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON>"}, "mao_li_lv": {"message": "<PERSON><PERSON> kotor"}, "mobile_view__dkxbqy": {"message": "<PERSON><PERSON>"}, "mobile_view__sjdxq": {"message": "Detail di Aplikasi"}, "mobile_view__sjdxqy": {"message": "Halaman Detail di Aplikasi"}, "mobile_view__smck": {"message": "Pindai untuk Melihat"}, "mobile_view__smckms": {"message": "<PERSON><PERSON><PERSON> gunakan kamera atau aplikasi untuk memindai dan melihat"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal"}, "modified_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_btn_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nav_btn_package": {"message": "<PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Tentang produk"}, "nav_btn_viewed": {"message": "<PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Tidak ada"}, "normal_link": {"message": "Tautan Normal"}, "notice": {"message": "petunjuk"}, "number_reviews": {"message": "<PERSON><PERSON><PERSON>"}, "only_show_num": {"message": "Total produk: $allnum$, Tersembunyi: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Hapus yang Tidak Dicentang"}, "open": {"message": "<PERSON><PERSON>"}, "open_links": {"message": "<PERSON><PERSON>"}, "options_page_tab_check_links": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_gernal": {"message": "<PERSON><PERSON>"}, "options_page_tab_notifications": {"message": "Notif<PERSON><PERSON>"}, "options_page_tab_others": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "Telusuri pakai gambar"}, "options_page_tab_shortcuts": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_shortcuts_title": {"message": "Ukuran font untuk pintasan"}, "options_page_tab_similar_products": {"message": "<PERSON>duk yang sama"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Be<PERSON><PERSON> tautan produk akan segera dibuka"}, "order_list_sku_show_title": {"message": "<PERSON><PERSON><PERSON><PERSON> varian yang dipilih di tautan bersama"}, "orders_last30_days": {"message": "<PERSON><PERSON><PERSON> p<PERSON>an dalam 30 hari terakhir"}, "pTutorial_favorites_block1_desc1": {"message": "Produk yang Anda lacak tercantum di sini"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Label hijau berarti ada produk yang harganya turun"}, "pTutorial_popup_block1_title": {"message": "<PERSON><PERSON><PERSON> dan <PERSON>"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON><PERSON> \"<PERSON><PERSON> Harga\", tambahkan produk ke Favorit. <PERSON><PERSON><PERSON> harga mereka turun, <PERSON><PERSON> akan meneri<PERSON> notifikasi"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON> harga"}, "pTutorial_reviews_block1_desc1": {"message": "<PERSON><PERSON><PERSON> pembeli dari <PERSON>ao dan foto asli dari umpan balik AliExpress"}, "pTutorial_reviews_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_reviews_block2_desc1": {"message": "Itu selalu membantu untuk memeriksa ulasan dari pembeli lain"}, "pTutorial_same_products_block1_desc1": {"message": "Anda dapat membandingkannya untuk membuat pilihan terbaik"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' un<PERSON>k \"Telusuri pakai gambar\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON>duk yang sama"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Letakkan gambar produk di sana dan pilih kategori"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Telusuri pakai gambar"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Ting<PERSON> umpan balik positif penjual, skor umpan balik dan berapa lama penjual berada di pasar"}, "pTutorial_seller_analysis_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Per<PERSON><PERSON> penjual didasarkan pada 3 indeks: item as Described, Communication Shipping Speed"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Kami menggunakan 3 warna dan ikon untuk menunjukkan tingkat kepercayaan penjual"}, "page_count": {"message": "<PERSON><PERSON><PERSON>"}, "pai_chu": {"message": "Pengecualian"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Hong Kong"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Jepang"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Korea"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Kazakhstan"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Makau"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Eropa Timur"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Arab Saudi"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Taiwan"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas AS"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas Vietnam"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Kecuali Barang Terbatas"}, "payable_price_formula": {"message": "Harga + Ongkos <PERSON> + Diskon"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON> eceran"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON> e<PERSON>an"}, "pdp_copy_fail": {"message": "Penyalinan gagal!"}, "pdp_copy_success": {"message": "<PERSON>in berhasil!"}, "pdp_share_modal_subtitle": {"message": "Bagikan tang<PERSON>pan layar, dia akan melihat pilihan <PERSON>."}, "pdp_share_modal_title": {"message": "Bagikan pilihan Anda"}, "pdp_share_screenshot": {"message": "Bagikan tangkapan layar"}, "pei_song": {"message": "<PERSON><PERSON>"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "Ku<PERSON>tas produk"}, "pin_zhi_ti_yan__desc": {"message": "Tingkat pengembalian uang berkualitas dari toko penjual"}, "pin_zhi_tui_kuan_lv": {"message": "Tingkat pengembalian dana"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proporsi pesanan yang hanya dikembalikan dananya dan dikembalikan dalam 30 hari terakhir"}, "ping_fen": {"message": "<PERSON><PERSON><PERSON>"}, "ping_jun_fa_huo_su_du": {"message": "Kecepatan Pengiriman Rata-rata"}, "pkgInfo_hide": {"message": "Info logistik: hidup/mati"}, "pkgInfo_no_trace": {"message": "Tidak ada info logistik"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo g<PERSON>ir"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Buka $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON><PERSON> be<PERSON> untuk ..."}, "popup_track_package_btn_track": {"message": "JALUR"}, "popup_track_package_desc": {"message": "PELACAKAN PAKET ALL-IN-ONE"}, "popup_track_package_search_placeholder": {"message": "Melacak nomor"}, "popup_translate_search_placeholder": {"message": "Terjemahkan dan telusuri di $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_chart_tip_ae": {"message": "Tip: <PERSON><PERSON><PERSON> pesanan adalah jumlah kumulatif pesanan sejak diluncurkan"}, "price_history_inm_1688_l1": {"message": "<PERSON><PERSON><PERSON> instal"}, "price_history_inm_1688_l2": {"message": "<PERSON><PERSON><PERSON> Belanja AliPrice untuk 1688"}, "price_history_panel_lowest_price": {"message": "<PERSON>rga Termurah: "}, "price_history_panel_tab_price_tracking": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_pro_modal_title": {"message": "Riwayat harga & Riwayat pesanan"}, "privacy_consent__btn_agree": {"message": "Tinjau kembali izin pengumpulan data"}, "privacy_consent__btn_disable_all": {"message": "Tidak terima"}, "privacy_consent__btn_enable_all": {"message": "Aktifkan Semua"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON> bahwa, tanpa data atau cookie beberapa fungsi akan mati karena fungsi tersebut memerlukan penjelasan data atau cookie, tetapi Anda tetap dapat menggunakan fungsi lainnya."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON><PERSON>, tanpa data atau cookie itu tidak akan berfungsi karena kami membutuhkan penjelasan tentang data atau cookie."}, "privacy_consent__desc_privacy_L2": {"message": "Jika Anda tidak mengizinkan kami mengumpulkan informasi ini, ha<PERSON> ha<PERSON>."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, kita hanya mendapatkan data mata uang Anda dalam cookie saat berbelanja online untuk menunjukkan sejarah harga."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON> ya<PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Tambahkan cookie di browser untuk mengidentifikasi komputer atau perangkat Anda secara anonim."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Tambahkan data fungsional di add-on untuk bekerja dengan fungsi."}, "privacy_consent__item_functional_title": {"message": "<PERSON><PERSON> dan <PERSON>"}, "privacy_consent__more_desc": {"message": "Ke<PERSON>uilah bahwa kami tidak membagikan data pribadi Anda dengan perusahaan lain dan tidak ada perusahaan iklan yang mengumpulkan data melalui layanan kami."}, "privacy_consent__options__btn__desc": {"message": "<PERSON><PERSON>k mengg<PERSON>kan semua fitur, <PERSON><PERSON> ha<PERSON> mengakt<PERSON>."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON> akan mengumpulkan data berikut yang mengidentifikasi Anda secara pribadi:"}, "privacy_consent__options__desc_L2": {"message": "- cookie, kami hanya mendapatkan data mata uang Anda dalam cookie ketika Anda berbelanja online untuk menunjukkan riwayat harga."}, "privacy_consent__options__desc_L3": {"message": "- dan tambahkan cookie di Browser untuk mengidentifikasi komputer atau perangkat Anda secara anonim."}, "privacy_consent__options__desc_L4": {"message": "- data anonim lainnya membuat ekstensi ini lebih nyaman."}, "privacy_consent__options__desc_L5": {"message": "Harap perhatikan bahwa kami tidak membagikan data pribadi Anda dengan perusahaan lain dan tidak ada perusahaan iklan yang mengumpulkan data melalui layanan kami."}, "privacy_consent__privacy_preferences": {"message": "Preferensi privasi"}, "privacy_consent__read_more": {"message": "Baca lebih lanjut >>"}, "privacy_consent__title_privacy": {"message": "P<PERSON><PERSON><PERSON>"}, "product_info": {"message": "Info Produk"}, "product_recommend__name": {"message": "<PERSON>duk yang sama"}, "product_research": {"message": "<PERSON><PERSON> Produk"}, "product_sub__email_desc": {"message": "<PERSON><PERSON> per<PERSON> harga"}, "product_sub__email_edit": {"message": "edit"}, "product_sub__email_not_verified": {"message": "Harap veri<PERSON><PERSON>i email"}, "product_sub__email_required": {"message": "Harap berikan email"}, "product_sub__form_countdown": {"message": "Tutup otomatis setelah $seconds$ detik", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Gagal menambahkan pengingat!"}, "product_sub__form_input_price": {"message": "harga input"}, "product_sub__form_item_country": {"message": "bang<PERSON>"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON>"}, "product_sub__form_item_duration": {"message": "melacak"}, "product_sub__form_item_higher_price": {"message": "Atau harga>"}, "product_sub__form_item_invalid_higher_price": {"message": "Harga harus lebih besar dari $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Harga harus lebih rendah dari $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON> harga <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON>"}, "product_sub__form_success": {"message": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>kan pengingat!"}, "product_sub__high_price_notify": {"message": "<PERSON><PERSON><PERSON> saya tentang kenaikan harga"}, "product_sub__low_price_notify": {"message": "<PERSON><PERSON><PERSON> saya tentang pengurangan harga"}, "product_sub__modal_title": {"message": "Pengingat perubahan harga langganan"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Area Perusahaan"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON>an pilih setidaknya satu produk"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON>an pilih setidaknya satu bidang"}, "qu_deng_lu": {"message": "<PERSON><PERSON><PERSON>"}, "quan_guo_yan_xuan": {"message": "Pilihan Global"}, "recommendation_popup_banner_btn_install": {"message": "<PERSON><PERSON> itu"}, "recommendation_popup_banner_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> riwayat harga dalam 3/6 bulan, dan pember<PERSON>huan penurunan harga"}, "region__all": {"message": "<PERSON><PERSON><PERSON> wilayah"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Aktifkan klik kanan"}, "ren_zheng_gong_chang": {"message": "Pabrik Bersertifikat"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Bertahun-<PERSON><PERSON> sebagai Pemasok Bersertif<PERSON>"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON> masuk ke AliPrice"}, "revenue_last30_days": {"message": "<PERSON><PERSON><PERSON> dalam 30 hari terakhir"}, "review_counts": {"message": "<PERSON><PERSON><PERSON>"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Periode"}, "sales_amount_last30_days": {"message": "Total penjualan dalam 30 hari terakhir"}, "sales_last30_days": {"message": "Penjualan dalam 30 hari terakhir"}, "sbi_alibaba_cate__accessories": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bags_cases": {"message": "Tas & Kasus"}, "sbi_alibaba_cate__beauty": {"message": "Kecantikan"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON> kantor"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Perala<PERSON> dapur"}, "sbi_alibaba_cate__clothes": {"message": "Pakaia<PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Penyiaran Media"}, "sbi_alibaba_cate__coat_jacket": {"message": "Mantel & Jaket"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Elektronik Konsumen"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__csyp": {"message": "Pelapis tempat tidur"}, "sbi_alibaba_cate__cwyy": {"message": "<PERSON><PERSON><PERSON><PERSON> hewan p<PERSON>"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON> segar"}, "sbi_alibaba_cate__dgdq": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON>ing"}, "sbi_alibaba_cate__dress_suits": {"message": "Gaun & Jas"}, "sbi_alibaba_cate__dszm": {"message": "Petir"}, "sbi_alibaba_cate__dzqj": {"message": "Peralatan elektronik"}, "sbi_alibaba_cate__essb": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Pakaian & Aksesoris"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "Perawatan Pribadi"}, "sbi_alibaba_cate__gt": {"message": "Baja"}, "sbi_alibaba_cate__gyp": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON><PERSON> kulit"}, "sbi_alibaba_cate__hg": {"message": "Industri kimia"}, "sbi_alibaba_cate__jg": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON>at mesin"}, "sbi_alibaba_cate__jjry": {"message": "<PERSON><PERSON><PERSON><PERSON> sehari-hari rumah tangga"}, "sbi_alibaba_cate__jtys": {"message": "Angkutan"}, "sbi_alibaba_cate__jxsb": {"message": "Peralatan"}, "sbi_alibaba_cate__jxwj": {"message": "Perang<PERSON> keras mekanis"}, "sbi_alibaba_cate__jydq": {"message": "<PERSON><PERSON><PERSON> tan<PERSON>a"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON><PERSON> bangunan per<PERSON>ikan rumah"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__mj": {"message": "Hand<PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Produk Bayi"}, "sbi_alibaba_cate__nanz": {"message": "Pria"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "Energi"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qmpj": {"message": "Suku Cadang Mobil"}, "sbi_alibaba_cate__shoes": {"message": "Sepatu"}, "sbi_alibaba_cate__smdn": {"message": "Komputer digital"}, "sbi_alibaba_cate__snqj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan pem<PERSON>an"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON><PERSON><PERSON> bisnis"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "<PERSON><PERSON> & Rok"}, "sbi_alibaba_cate__txcp": {"message": "Produk komunikasi"}, "sbi_alibaba_cate__tz": {"message": "<PERSON><PERSON><PERSON> anak anak"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Alat perangkat keras"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "ker<PERSON><PERSON> proyek"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Pakaia<PERSON>"}, "sbi_alibaba_cate__ydhw": {"message": "<PERSON><PERSON><PERSON> luar ruangan"}, "sbi_alibaba_cate__yjkc": {"message": "Mineral Metalurgi"}, "sbi_alibaba_cate__yqyb": {"message": "Peralatan"}, "sbi_alibaba_cate__ys": {"message": "Mencetak"}, "sbi_alibaba_cate__yyby": {"message": "Perawatan medis"}, "sbi_alibaba_cn_kj_90mjs": {"message": "<PERSON><PERSON><PERSON> pem<PERSON>i dalam 90 hari terakhir"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volume penjualan dalam 90 hari terakhir"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON><PERSON> harga"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Biaya pengiriman internasional"}, "sbi_alibaba_cn_kj_gjyf": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON><PERSON> harga"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Keuntungan = perkiraan harga x margin keuntungan"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Kecepatan pengiriman rata-rata"}, "sbi_alibaba_cn_kj_qtfy": {"message": "biaya lainnya"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "B<PERSON>ya lain = per<PERSON><PERSON>an harga x rasio biaya lainnya"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Potongan"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Potongan"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON>em<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Potongan"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Set"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "hari"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Tidak ada harga"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "harga"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Tersedia untuk dijual"}, "sbi_aliprice_alibaba_cn__moren": {"message": "bawaan"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Penjualan"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "mebel"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "camilan"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "rias wajah"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Minuman botol"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON> lain"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Rok"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagasi"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "sepatu"}, "sbi_aliprice_cate__apparel": {"message": "Pakaia<PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Mobil & Sepeda Motor"}, "sbi_aliprice_cate__beauty_health": {"message": "kecantikan"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Ponsel & Telekomunikasi"}, "sbi_aliprice_cate__computer_office": {"message": "Komputer & Kantor"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Elektronik Konsumen"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Pendidikan & Perlengkapan Kantor"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Komponen & Perlengkapan Elektronik"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Ekstensi Rambut & Wig"}, "sbi_aliprice_cate__home_garden": {"message": "rumah dan <PERSON>"}, "sbi_aliprice_cate__home_improvement": {"message": "<PERSON><PERSON><PERSON><PERSON> rumah"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Perhiasan & Aksesoris"}, "sbi_aliprice_cate__luggage_bags": {"message": "Koper & Tas"}, "sbi_aliprice_cate__mother_kids": {"message": "Ibu & Anak"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Penggunaan Baru & Khusus"}, "sbi_aliprice_cate__security_protection": {"message": "<PERSON><PERSON><PERSON><PERSON> kea<PERSON>n"}, "sbi_aliprice_cate__shoes": {"message": "Sepatu"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Olahraga & Hiburan"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Mainan & Hobi"}, "sbi_aliprice_cate__watches": {"message": "<PERSON> tangan"}, "sbi_aliprice_cate__weddings_events": {"message": "Pernikahan & Acara"}, "sbi_btn_capture_txt": {"message": "Menangkap"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON> se<PERSON>ng"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON><PERSON> saya"}, "sbi_button__contact_supplier": {"message": "Kontak"}, "sbi_button__hide_on_this_site": {"message": "<PERSON><PERSON> tampilkan di situs ini"}, "sbi_button__open_settings": {"message": "Konfigurasikan pencarian berdasarkan gambar"}, "sbi_capture_shortcut_tip": {"message": "atau tekan tombol \"Enter\" pada keyboard"}, "sbi_capturing_tip": {"message": "Menangkap"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 <PERSON><PERSON>g"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON>"}, "sbi_crop_start": {"message": "<PERSON><PERSON><PERSON> Screenshot"}, "sbi_err_captcha_action": {"message": "Memeriksa"}, "sbi_err_captcha_for_alibaba_cn": {"message": "<PERSON><PERSON>, harap unggah gambar untuk memverifikasi. (Lihat $video_tutorial$ atau coba hapus cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "<PERSON><PERSON> lintas yang tidak <PERSON>a, harap veri<PERSON><PERSON>i"}, "sbi_err_captcha_for_taobao": {"message": "Taobao meminta Anda untuk memverifika<PERSON>, harap unggah gambar secara manual dan cari untuk memverifikasinya. Kesalahan ini disebabkan oleh kebijakan verifikasi baru \"Pencarian Taobao berdasarkan gambar\", kami menyar<PERSON>kan Anda bahwa verifikasi keluhan terlalu sering di Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "umpan balik"}, "sbi_err_captcha_msg": {"message": "$platform$ mengharuskan Anda mengunggah gambar untuk mencari atau menyelesaikan verifikasi keamanan untuk menghapus batasan pencarian", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON> apakah itu versi terbaru"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON>"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Hapus cookie 1688? (<PERSON><PERSON> login lagi)"}, "sbi_err_desperate_feature_pdd": {"message": "Fungsi pencarian gambar telah dipindahkan ke Pencarian Pinduoduo dengan ekstensi Gambar."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Bagaimana cara meningkatkan tingkat keberhasilan pencarian gambar?"}, "sbi_err_img_undersize": {"message": "Gambar > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Masuk $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_low_version": {"message": "Instal versi terbaru ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "<PERSON><PERSON>"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON>, pastikan <PERSON>a dapat mengunjungi situs web"}, "sbi_err_not_low_version": {"message": "Versi terbaru telah diinstal ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Coba lagi"}, "sbi_err_try_again_action": {"message": "Coba lagi"}, "sbi_err_visit_and_try": {"message": "Coba lagi, atau kunjungi $website$ untuk mencoba lagi", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Kunjungi beranda $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON> me<PERSON>, segarkan laman dan coba lagi."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_es": {"message": "Spanyol"}, "sbi_kuajing_filter_estimate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON> harga"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Estimasi formula harga = (harga komoditas + pengiriman logistik internasional)/(1 - margin keuntungan - rasio biaya lainnya)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON>kkan kata kunci yang sesuai dengan judul"}, "sbi_kuajing_filter_logistics": {"message": "Template logistik"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "Diskon logistik"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Pengiriman logistik internasional = (berat x harga pengiriman + biaya pendaftaran) x (1 - diskon)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON><PERSON> la<PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON><PERSON> k<PERSON>"}, "sbi_kuajing_filter_prop": {"message": "Atribut"}, "sbi_kuajing_filter_ru": {"message": "Rusia"}, "sbi_kuajing_filter_total": {"message": "Cocokkan $count$ item serupa", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "Tidak ada hasil yang <PERSON>,silakan login ke $loginSite$ atau coba gambar lain", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Untuk sementara tidak tersedia untuk Safari, harap gunakan $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Browser Chrome dan eks<PERSON><PERSON>ya"}, "sbi_msg_no_result_reinstall_l1": {"message": "Tidak ada hasil yang <PERSON>, silakan masuk ke $loginSite$ atau coba gambar lain, atau instal ulang versi terbaru $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Versi terbaru", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Area yang dipilih"}, "sbi_shipping_": {"message": "Pengiriman di hari yang sama"}, "sbi_specify_category": {"message": "Tentukan kategori:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON> da<PERSON>h"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 luar negeri"}, "sbi_tutorial_btn_more": {"message": "Penggunaan 2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Temu<PERSON>"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON>, tidak ada hasil yang di<PERSON>, silakan coba lagi."}, "sbi_txt__min_order": {"message": "<PERSON>. memesan"}, "sbi_visiting": {"message": "Menjelajah"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON><PERSON><PERSON><PERSON> penjual untuk harga"}, "sbi_yiwugo__qigou": {"message": "$num$ Potongan (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "bintang"}, "searchByImage_screenshot": {"message": "Tangkapan layar sekali klik"}, "searchByImage_search": {"message": "Pencarian sekali klik untuk item yang sama"}, "searchByImage_size_type": {"message": "Ukuran file tidak boleh lebih besar dari $num$ MB, hanya $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Menganalis<PERSON> gambar"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON> produk"}, "search_by_image_progress_sending": {"message": "Mengirim gambar"}, "search_by_image_response_rate": {"message": "Tingkat Respons: $responseRate$ pembeli yang menghubungi pemasok ini menerima respons dalam $responseInHour$ jam.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Cari menggunakan kata kunci:"}, "select_country_language_modal_title_country": {"message": "Negara"}, "select_country_language_modal_title_language": {"message": "Bahasa"}, "select_country_region_modal_title": {"message": "Pilih negara/wilayah"}, "select_language_modal_title": {"message": "<PERSON><PERSON><PERSON> bahasa:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON> toko"}, "sellers_count": {"message": "<PERSON><PERSON><PERSON> pen<PERSON> di halaman saat ini"}, "sellers_count_per_page": {"message": "<PERSON><PERSON><PERSON> pen<PERSON> di halaman saat ini"}, "service_score": {"message": "<PERSON><PERSON><PERSON> layanan k<PERSON>"}, "set_shortcut_keys": {"message": "<PERSON><PERSON> tombol pintas"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_position_title": {"message": "Posisi plug-in"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON>t kiri"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON> kanan"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> tema"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "Negara / Wilayah"}, "setting_options_hover_zoom_desc": {"message": "<PERSON><PERSON>kan mouse untuk memperbesar"}, "setting_options_hover_zoom_title": {"message": "Hover Zoom"}, "setting_options_jd_coupon_desc": {"message": "Kupon yang ditemukan di JD.com"}, "setting_options_jd_coupon_title": {"message": "kupon JD.com"}, "setting_options_language_title": {"message": "Bahasa"}, "setting_options_price_drop_alert_desc": {"message": "Saat harga produk di <PERSON><PERSON>t <PERSON>, <PERSON><PERSON> akan menerima notifika<PERSON> push."}, "setting_options_price_drop_alert_title": {"message": "<PERSON><PERSON><PERSON> penurunan harga"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> riwayat harga pada halaman pencarian produk"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON><PERSON><PERSON> (Halaman Daftar)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Tampilkan riwayat produk pada halaman detail produk"}, "setting_options_price_history_on_produt_page_title": {"message": "<PERSON><PERSON><PERSON><PERSON> harga (halaman detail)"}, "setting_options_sales_analysis_desc": {"message": "Dukung statistik harga, volume pen<PERSON><PERSON>, j<PERSON><PERSON> pen<PERSON>, dan rasio penjualan toko di halaman daftar produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "Keberhasilan"}, "setting_options_tacking_price_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_value_off": {"message": "<PERSON><PERSON>"}, "setting_options_value_on": {"message": "Di"}, "setting_pkg_quick_view_desc": {"message": "Dukungan: 1688 & Taobao"}, "setting_saved_message": {"message": "<PERSON><PERSON><PERSON> ber<PERSON><PERSON> disimpan"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "<PERSON><PERSON><PERSON>"}, "settings_aliprice_agent__desc": {"message": "Ditam<PERSON><PERSON>an di halaman detail produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON>i untuk saya"}, "settings_copy_link__desc": {"message": "Tampilan di halaman detail produk"}, "settings_copy_link__title": {"message": "<PERSON><PERSON> dan <PERSON>"}, "settings_currency_desc__for_detail": {"message": "Mendukung halaman detail produk 1688"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON> be<PERSON> gambar (termasuk 1688/1688 di luar negeri/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON> harga"}, "settings_currency_desc_display_for_list": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dalam pencarian gambar (termasuk 1688/1688 di luar negeri/Taobao)"}, "settings_currency_rate_desc": {"message": "Pembaruan nilai tukar dari \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank Cina"}, "settings_download_images__desc": {"message": "Dukungan untuk mengunduh gambar dari $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "tombol unduh gambar"}, "settings_download_reviews__desc": {"message": "Ditam<PERSON><PERSON>an di halaman detail produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "<PERSON><PERSON><PERSON> gambar <PERSON>n"}, "settings_google_translate_desc": {"message": "Klik kanan untuk mendapatkan bilah terjemahan google"}, "settings_google_translate_title": {"message": "ter<PERSON><PERSON>an halaman web"}, "settings_historical_trend_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> di sudut kanan bawah gambar pada halaman daftar produk"}, "settings_modal_btn_more": {"message": "Le<PERSON>h banyak pengaturan"}, "settings_productInfo_desc": {"message": "Tampilkan informasi produk yang lebih rinci pada halaman daftar produk. Mengaktifkan fitur ini dapat meningkatkan beban komputer dan menyebabkan halaman menjadi lambat. Jika fitur ini memengaruhi kinerja, seba<PERSON><PERSON> nonaktifkan fitur ini."}, "settings_product_recommend__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> di bawah gambar utama pada laman detail produk $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produk <PERSON>"}, "settings_research_desc": {"message": "Minta informasi lebih rinci pada halaman daftar produk"}, "settings_sbi_add_to_list": {"message": "Tambahkan ke $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Gambar mini hasil pencarian gambar"}, "settings_sbi_remove_from_list": {"message": "Hapus dari $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Tambahkan ke daftar blokir"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Se<PERSON>aikan posisi masuk"}, "settings_search_by_image_blacklist_desc": {"message": "<PERSON>an tampilkan ikon di situs web dalam daftar hitam."}, "settings_search_by_image_blacklist_title": {"message": "Daftar blokir"}, "settings_search_by_image_bottom_left": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_bottom_right": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON><PERSON> daftar blokir"}, "settings_search_by_image_detail_page_icon_title": {"message": "gambar mini"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON> besar"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON><PERSON> kecil"}, "settings_search_by_image_display_button_desc": {"message": "Satu klik pada ikon untuk mencari berdasarkan gambar"}, "settings_search_by_image_display_button_title": {"message": "Ikon pada gambar"}, "settings_search_by_image_sourece_websites_desc": {"message": "Temukan produk sumber di situs web ini"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON> be<PERSON> hasil gambar"}, "settings_search_by_image_top_left": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON>"}, "settings_search_keyword_on_x__desc": {"message": "Cari kata di $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Tampilkan ikon $platform$ saat kata-kata yang dipilih", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Cobalah untuk menemukan produk yang sama di situs web tersebut (maks hingga 5)"}, "settings_similar_products_title": {"message": "Temukan produk yang sama"}, "settings_toolbar_expand_title": {"message": "Minimalkan plug-in"}, "settings_top_toolbar_desc": {"message": "<PERSON><PERSON><PERSON> di <PERSON>ian atas halaman"}, "settings_top_toolbar_title": {"message": "<PERSON><PERSON><PERSON>"}, "settings_translate_search_desc": {"message": "Terjemahkan ke dalam bahasa <PERSON>ina dan cari"}, "settings_translate_search_title": {"message": "Pencarian multibahasa"}, "settings_translator_contextmenu_title": {"message": "Tangkap untuk diterjemahkan"}, "settings_translator_title": {"message": "Menerjemahkan"}, "shai_xuan_dao_chu": {"message": "Filter untuk Mengekspor"}, "shai_xuan_zi_duan": {"message": "Filter kolom"}, "shang_jia_shi_jian": {"message": "Pada waktu rak"}, "shang_pin_biao_ti": {"message": "<PERSON><PERSON><PERSON> produk"}, "shang_pin_dui_bi": {"message": "Perbandingan Produk"}, "shang_pin_lian_jie": {"message": "<PERSON><PERSON> produk"}, "shang_pin_xin_xi": {"message": "Info produk"}, "share_modal__content": {"message": "Bagikan dengan temanmu"}, "share_modal__disable_for_while": {"message": "Saya tidak ingin membagikan apa pun"}, "share_modal__title": {"message": "Apakah Anda suka $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "Apakah itu dapat disesuaikan?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Pemasok Bersertifikat"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Pemasok bersertifikat"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Jaminan <PERSON>"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Jaminan per<PERSON>"}, "shipping_fee": {"message": "<PERSON><PERSON><PERSON>"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON> toko"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Maks sampai 5"}, "sku_calc_price": {"message": "<PERSON><PERSON> yang <PERSON>"}, "sku_calc_price_settings": {"message": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>"}, "sku_formula": {"message": "<PERSON><PERSON><PERSON>"}, "sku_formula_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_formula_desc_text": {"message": "Mendukung rumus matematika yang rumit, dengan harga asli diwakili oleh A dan biaya angkut diwakili oleh B\n\n<br/>\n\nMendukung tanda kurung (), plus +, minus -, perkalian *, dan pembagian /\n\n<br/>\n\nContoh:\n\n<br/>\n\n1. Untuk mencapai 1,2 kali harga asli dan kemudian menambahkan biaya angkut, rumusnya adalah: A*1.2+B\n\n<br/>\n\n2. Untuk mencapai harga asli ditambah 1 yuan, kemudian dikalikan dengan 1,2 kali, rumusnya adalah: (A+1)*1.2\n\n<br/>\n\n3. Untuk mencapai harga asli ditambah 10 yuan, kemudian dikalikan dengan 1,2 kali, dan kemudian dikurangi 3 yuan, rumusnya adalah: (A+10)*1.2-3"}, "sku_in_stock": {"message": "Stok Tersedia"}, "sku_invalid_formula_format": {"message": "Format rumus tidak valid"}, "sku_inventory": {"message": "Inventaris"}, "sku_link_copy_fail": {"message": "<PERSON><PERSON><PERSON><PERSON>, spesif<PERSON><PERSON> dan atribut sku tidak dipilih"}, "sku_link_copy_success": {"message": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>, spesif<PERSON>si sku dan atribut dipilih"}, "sku_list": {"message": "Daftar SKU"}, "sku_min_qrder_qty": {"message": "<PERSON><PERSON><PERSON>"}, "sku_name": {"message": "Nama SKU"}, "sku_no": {"message": "No."}, "sku_original_price": {"message": "<PERSON><PERSON>"}, "sku_price": {"message": "Harga SKU"}, "stop_track_time_label": {"message": "Melacak tenggat waktu:"}, "suo_zai_di_qu": {"message": "<PERSON><PERSON><PERSON>"}, "tab_pkg_quick_view": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_price_history": {"message": "<PERSON><PERSON><PERSON> harga"}, "tab_product_details_reviews": {"message": "<PERSON>lasan foto"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON>duk yang sama"}, "total_days_listed_per_product": {"message": "<PERSON><PERSON><PERSON> ÷ <PERSON><PERSON><PERSON> produk"}, "total_items": {"message": "<PERSON><PERSON><PERSON> total produk"}, "total_price_per_product": {"message": "<PERSON><PERSON><PERSON> harga <PERSON><PERSON> produk"}, "total_rating_per_product": {"message": "<PERSON><PERSON><PERSON> produk"}, "total_revenue": {"message": "Total pendapatan"}, "total_revenue40_items": {"message": "Total pendapatan dari $amount$ produk pada halaman saat ini", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON><PERSON>"}, "total_sales40_items": {"message": "Total penjualan $amount$ produk pada halaman saat ini", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Track selama: 1 tahun"}, "track_for_3_months": {"message": "Lacak selama: 3 bulan"}, "track_for_6_months": {"message": "Lacak selama: 6 bulan"}, "tracking_price_email_add_btn": {"message": "Tambahkan email"}, "tracking_price_email_edit_btn": {"message": "Edit email"}, "tracking_price_email_intro": {"message": "<PERSON><PERSON> akan memberi tahu <PERSON> email."}, "tracking_price_email_invalid": {"message": "Harap berikan email yang valid"}, "tracking_price_email_verified_desc": {"message": "<PERSON>a sekarang dapat menerima peringatan penurunan harga kami."}, "tracking_price_email_verified_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tracking_price_email_verify_desc_line1": {"message": "<PERSON><PERSON> telah men<PERSON>kan tautan verifikasi ke alamat email And<PERSON>,"}, "tracking_price_email_verify_desc_line2": {"message": "silahkan cek inbox email anda."}, "tracking_price_email_verify_title": {"message": "Verifikasi email"}, "tracking_price_web_push_notification_intro": {"message": "Di Desktop: <PERSON><PERSON><PERSON> dapat memantau produk apa pun untuk Anda dan mengirimkan Pemberitahuan Push Web setelah harga berubah."}, "tracking_price_web_push_notification_title": {"message": "Pemberitahuan Web Push"}, "translate_im__login_required": {"message": "Di<PERSON><PERSON><PERSON><PERSON>, silakan masuk ke $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Diterjemahkan dan disalin ke clipboard, namun karena keter<PERSON><PERSON><PERSON>, <PERSON>a perlu menempelkannya secara manual!"}, "translate_im__send": {"message": "Terjemahkan & Kirim"}, "translate_search": {"message": "Menerjemah<PERSON> dan mencari"}, "translation_originals_translated": {"message": "<PERSON><PERSON> dan <PERSON>"}, "translation_translated": {"message": "Cina"}, "translator_btn_capture_txt": {"message": "Menerjemahkan"}, "translator_language_auto_detect": {"message": "<PERSON><PERSON><PERSON> otomatis"}, "translator_language_detected": {"message": "Terdeteksi"}, "translator_language_search_placeholder": {"message": "<PERSON><PERSON> bahasa"}, "try_again": {"message": "Coba lagi"}, "tu_pian_chi_cun": {"message": "Ukuran gambar:"}, "tu_pian_lian_jie": {"message": "Tautan Gambar"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tui_huan_ti_yan__desc": {"message": "<PERSON><PERSON><PERSON> indi<PERSON> purna jual penjual"}, "tutorial__show_all": {"message": "<PERSON><PERSON><PERSON> fitur"}, "tutorial_ae_popup_title": {"message": "Sematkan <PERSON>i, buka Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress Ulasan <PERSON>"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Mendukung USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Pengiriman ke Korea/Jepang/Tiongkok Daratan"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Mendukung Pembelian Luar Negeri"}, "tutorial_auto_apply_coupon_title": {"message": "Berlak<PERSON> otomatis k<PERSON>"}, "tutorial_btn_end": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_btn_example": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_btn_have_a_try": {"message": "Ok, cobalah"}, "tutorial_btn_next": {"message": "Berikutnya"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Bandingkan Produk"}, "tutorial_currency_convert_title": {"message": "konversi mata uang"}, "tutorial_export_shopping_cart": {"message": "Ekspor sebagai CSV, Dukungan Taobao dan 1688"}, "tutorial_export_shopping_cart_title": {"message": "Keranjang ekspor"}, "tutorial_price_history_pro": {"message": "<PERSON><PERSON><PERSON><PERSON>an pada halaman detail produk.\nMendukung Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Sepanjang tahun Riwayat harga & Riwayat pesanan"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Tangkap untuk mencari berdasarkan gambar"}, "tutorial_translate_search": {"message": "Terjemahkan ke penelusuran"}, "tutorial_translate_search_and_package_tracking": {"message": "<PERSON><PERSON><PERSON> terjemahan dan pelacakan paket"}, "unit_bao": {"message": "buah"}, "unit_ben": {"message": "buah"}, "unit_bi": {"message": "pesanan"}, "unit_chuang": {"message": "buah"}, "unit_dai": {"message": "buah"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "buah"}, "unit_ge": {"message": "buah"}, "unit_he": {"message": "buah"}, "unit_jian": {"message": "buah"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "buah"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "buah"}, "unit_ti": {"message": "buah"}, "unit_tiao": {"message": "buah"}, "unit_xiang": {"message": "buah"}, "unit_zhang": {"message": "buah"}, "unit_zhi": {"message": "buah"}, "verify_contact_support": {"message": "Hubungi <PERSON>"}, "verify_human_verification": {"message": "Verifikasi Manusia"}, "verify_unusual_access": {"message": "<PERSON><PERSON><PERSON> tidak wajar terde<PERSON>i"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "view_history_clean_all_warring": {"message": "Be<PERSON><PERSON><PERSON> semua catatan yang dilihat?"}, "view_history_clean_all_warring_title": {"message": "Peringatan"}, "view_history_viewd": {"message": "<PERSON><PERSON><PERSON>"}, "website": {"message": "situs web"}, "weight": {"message": "<PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Tidak dapat memperoleh datanya"}, "wu_liu_shi_xiao": {"message": "Pengiriman tepat waktu"}, "wu_liu_shi_xiao__desc": {"message": "Tingkat pengumpulan 48 jam dan tingkat pemenuhan toko penjual"}, "xia_dan_jia": {"message": "<PERSON><PERSON> a<PERSON><PERSON>"}, "xian_xuan_ze_product_attributes": {"message": "Pilih atribut produk"}, "xiao_liang": {"message": "Volume penjualan"}, "xiao_liang_zhan_bi": {"message": "Persentase volume penjualan"}, "xiao_shi": {"message": "$num$ Jam", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Pendapatan"}, "xiao_shou_e_zhan_bi": {"message": "Persentase pendapatan"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Pilih $amount$ catatan", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Bagian Pen<PERSON><PERSON>"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Bagian <PERSON>m<PERSON><PERSON>"}, "yi_shua_xin": {"message": "Disegarkan"}, "yin_cang_xiang_tong_dian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "you_xiao_liang": {"message": "Dengan Volume Penjualan"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON>raan waktu kedatangan"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON><PERSON>"}, "yue_cheng_jiao": {"message": "Volume bulanan"}, "yue_dai_xiao": {"message": "Pengiriman drop"}, "yue_dai_xiao__desc": {"message": "Penjualan dropship dalam 30 hari terakhir"}, "yue_dai_xiao_pai_xu__desc": {"message": "Penjualan dropship dalam 30 hari terakhir, diurutkan dari tinggi ke rendah"}, "yue_xiao_liang__desc": {"message": "Volume penjualan dalam 30 hari terakhir"}, "zhan_kai": {"message": "<PERSON><PERSON><PERSON>"}, "zhe_kou": {"message": "Diskon"}, "zhi_chi_yi_jian_dai_fa": {"message": "Pengiriman drop"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON> biaya kirim"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON><PERSON><PERSON> pesanan untuk produk ini (30 hari)"}, "zhu_ce_xing_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "zi_duan": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON><PERSON> k<PERSON>luruhan"}, "zong_he_fu_wu_fen__desc": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>han layanan pen<PERSON>al"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen_3": {"message": "Di bawah 4 Bintang"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 Bintang"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 <PERSON><PERSON>g"}, "zong_he_ti_yan_fen_5": {"message": "5 Bintang"}, "zong_ku_cun": {"message": "<PERSON><PERSON><PERSON>"}, "zong_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Tingkat respons dalam 30 hari terakhir dan 3 menit"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Dalam 30 hari te<PERSON>hir, k<PERSON><PERSON><PERSON> 48H <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Tingkat kinerja 48 jam dalam 30 hari terakhir"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Catatan perdagangan (30 hari)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Catatan perdagangan (30 hari)"}, "zui_jin_30D_jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON> 30 hari terakhir"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Tingkat Refuksi dalam 30 Hari Terakhir"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON> pesanan dalam 30 hari terakhir"}}