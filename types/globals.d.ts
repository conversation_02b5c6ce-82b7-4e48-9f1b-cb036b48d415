/**
 * 全局变量类型定义
 *
 * 这些变量通过 vite.define 在构建时注入
 * 定义在 scripts/extension-config-manager/wxt.config.base.ts 中
 */

// 开发/生产环境标识
declare const __IS_DEV__: boolean;
declare const __IS_PROD__: boolean;

// 浏览器类型判断
declare const __IS_CHROME__: boolean;
declare const __IS_EDGE__: boolean;
declare const __IS_FIREFOX__: boolean;
declare const __IS_BROWSER_360__: boolean;
declare const __IS_SAFARI__: boolean;
declare const __IS_ADSPOWER__: boolean;
declare const __IS_OPERA__: boolean;

// 扩展商店信息
declare const __EXT_WEBSTORE__: string;
declare const __EXT_WEBSTORE_CN__: string;

// 扩展基本信息
declare const __EXT_NAME__: string;
declare const __EXT_VERSION__: string;
declare const __EXT_MANIFEST_VERSION__: 2 | 3;
declare const __EXT_DEFAULT_LOCALE__: string;

// 渠道包信息
declare const __EXT_VARIANT_ID__: string;
declare const __EXT_VARIANT_NAME__: string;
declare const __EXT_VARIANT_TYPE__: string;
declare const __EXT_VARIANT_CHANNEL__: string;
declare const __EXT_VARIANT_TARGET__: string;

// 谷歌流量统计的 Measurement ID
declare const __EXT_MEASUREMENT_ID__: string;
