---
description: 
globs: 
alwaysApply: false
---
### 需求规格说明书 (SRS)

#### 1. 项目概述

**项目名称**：电商辅助工具箱浏览器插件矩阵 (Project "E-commerce Toolbox Matrix")

**项目背景**：本项目旨在为一系列功能相似但目标用户或分发渠道不同的电商辅助工具浏览器插件，建立一个统一、高效、可扩展的开发和构建框架。核心功能包括图搜集成、数据分析、产品信息复制导出、图片采集下载等。

**解决的问题**：随着支持的浏览器、插件版本（Manifest V2/V3）和分发变体（官方版、镜像版、离线版等）数量的增加，独立维护每个插件包变得极其困难、耗时且容易出错。本项目旨在通过自动化和标准化来解决这一问题。

#### 2. 核心目标

*   **可维护性 (Maintainability)**：所有插件共享一套核心代码库和构建逻辑，修改通用功能或修复 Bug 只需在一个地方进行。
*   **可扩展性 (Scalability)**：能够轻松地添加新的独立插件（如 `pinduoduo_helper`）、支持新的浏览器或创建新的分发变体，而无需重构整个系统。
*   **自动化 (Automation)**：构建、打包和发布流程应完全自动化。开发者只需关心插件的业务逻辑和核心配置，无需手动管理 `manifest.json`、语言包等文件。
*   **一致性 (Consistency)**：确保所有输出的插件包都遵循统一的命名规范、目录结构和构建标准。

#### 3. 功能需求

##### 3.1. 插件矩阵支持

*   系统必须支持在 `packages/extensions/` 目录下管理多个独立的插件项目。
*   每个插件项目都是自包含的，拥有自己的源代码、配置和资源文件。

##### 3.2. 构建目标支持 (渠道插件)

系统必须能够为每个插件生成多种构建目标（渠道插件），其组合规则如下：

*   **目标浏览器 (7种)**：`chrome`, `firefox`, `browser360`, `safari`, `adspower`, `opera`, `edge`
*   **Manifest 版本 (2种)**：`mv2`, `mv3`
*   **分发变体 (5种)**：
    *   `master`: 官方商店版
    *   `tm`: 镜像版
    *   `tmBeta`: 镜像 Beta 版
    *   `dba`: 自我分发版 (主要用于 Firefox)
    *   `offline`: 离线安装包版

##### 3.3. 配置管理

*   每个插件必须有一个单一的、人类可读的配置文件 (`extension.config.ts`) 作为其“事实来源”。
*   此配置文件应能定义插件的基础信息、所有变体共享的 `manifest.json` 模板、以及每个变体的特定配置。

##### 3.4. 国际化 (i18n) 支持

*   支持公共语言包 (`packages/shared/locales/`) 和插件专属语言包 (`packages/extensions/{name}/src/locales/`)。
*   构建系统必须能自动合并上述两种语言包。
*   必须能根据配置，自动生成两种格式的语言文件：
    1.  **Chrome Extension 格式**: 用于 `manifest.json` 和 `_locales` 目录，需支持 `placeholders` 自动生成。
    2.  **Vue-i18n 格式**: 用于插件前端界面，需将占位符转换为 `{var}` 风格。
*   支持根据变体配置，在语言包中选择性地覆盖特定文案。

##### 3.5. 命令行接口 (CLI)

*   **开发命令**:
    *   `pnpm dev {插件名字} {目标浏览器}-{manifest version}-{变体名字}`: 启动指定插件的指定渠道的开发模式。
    *   `pnpm dev {插件名字}`: 启动指定插件的开发模式，并默认使用 `chrome-mv3-master` 渠道。
*   **构建/发布命令**:
    *   `pnpm release`: 根据一个集中的配置文件 (`scripts/release-extensions.json`)，批量构建一个或多个插件的一个或多个渠道包，用于最终发布。

#### 4. 非功能需求

*   **技术栈**: WXT + TypeScript + Vue3 + Vue-i18n + Alova.js
*   **包管理器**: pnpm (利用其 workspace 特性)
*   **目录结构**: 必须严格遵循用户在初始需求中定义的目录结构。
