# 用户使用指南

本文档是为使用此脚手架进行插件开发的开发者提供的唯一参考。

## 1. 核心概念

在开始之前，请先了解以下核心术语：

*   **插件 (Extension)**：项目中的一个独立浏览器插件。
*   **插件渠道类型 (Variant)**：插件的一个特定版本，定义其渠道特性（如 `master`, `tm`, `offline`）。
*   **插件渠道包 (Variant Target)**：唯一标识一个插件的特定构建目标，格式为 `{webstore}-{mv}-{variant}`（如 `chrome-mv3-master`）。
*   **源配置文件 (extension.config.ts)**：开发者维护的唯一插件配置文件。
*   **最终配置文件 (extension.config.json)**：为每个插件渠道包生成的最终配置产物。
*   **语言包 (locales/messages)**：多语言资源文件夹或文件。

## 2. 日常开发工作流 (`pnpm dev`)

此工作流用于日常编码和测试，通过专注于单个插件插件渠道包并利用缓存和热重载来优化速度和快速迭代。

### 2.1. 启动开发环境

使用单个命令启动开发环境。开发模式必须指定插件名称和渠道包。

```
# 开发指定插件的渠道包
pnpm dev cookies_manager chrome-mv3-master

# 显示详细输出信息
pnpm dev cookies_manager chrome-mv3-master --verbose

# 强制重新生成配置（不使用缓存）
pnpm dev cookies_manager chrome-mv3-master --no-cache
```

**开发工作流概览：**

```
用户执行 pnpm dev <插件名> <渠道包>
      |
      V
  创建构建队列并验证参数
      |
      V
  确定缓存策略 (检查 Git 状态或使用 --no-cache)
      |
      +--- (使用缓存) ---> 检查现有配置文件
      |
      +--- (不使用缓存) ---> 重新生成配置和语言包文件
      |                      (输出到 .variants/{variantTarget}/)
      V
  设置环境变量并执行 WXT 开发服务器
      |
      V
  启动开发浏览器，加载插件
      |
      V
  实时热重载 (代码变更即时反映)
```

### 2.2. 配置插件

在开发过程中，您可以随时编辑 `packages/extensions/{插件名}/extension.config.ts` 来调整插件配置。

```typescript
import { defineExtensionConfig } from '../../../scripts/extension-config-manager';

export default defineExtensionConfig({
  name: 'my_extension',
  version: '1.0.0',
  manifestVersion: 3,
  defaultLocale: 'en',
  
  // 国际化配置
  i18n: {
    locales: [], // 空数组表示自动扫描 locales 目录
    includeKeys: [], // 包含的文案 key 模式
    excludeKeys: [], // 排除的文案 key 模式
  },
  
  // 插件渠道包配置
  variants: [
    {
      variantId: '1001',
      variantName: '主渠道',
      variant: 'master',
      webstore: 'chrome',
      webstoreId: 'your-extension-id',
      measurementId: 'G-XXXXXXXXXX',
    },
    // 更多插件渠道包...
  ],
});
```

### 2.3. 管理语言包

语言包分为共享和插件专属两种：
*   `packages/shared/locales/`：所有插件共享的语言包。
*   `packages/extensions/{插件名}/locales/`：插件专属的语言包。

#### 条件化文案

支持基于插件渠道包的动态文案，系统会根据构建目标自动选择最匹配的文案。

```json
{
  "GREETING": "Hello",
  "GREETING@chrome": "Hello Chrome User",
  "GREETING@chrome-mv3-master": "Hello Chrome MV3 Master User"
}
```

#### 语言包维护工具

项目提供了独立的语言包维护工具，用于简化翻译工作流。这些工具位于 `scripts/source-i18n-manager/` 目录中。

```
# 将 Excel 翻译文件转换为 JSON 语言包
node scripts/source-i18n-manager/cli.js excel2i18n

# 将 JSON 语言包导出为 Excel 文件
node scripts/source-i18n-manager/cli.js i18n2excel

# 批量更新语言包文件
node scripts/source-i18n-manager/cli.js updateI18n
```

**注意**: 这些工具独立于构建流程运行，主要用于翻译人员和语言包维护。

## 3. 发布工作流 (`pnpm release`)

这是一个正式的多阶段流程，用于创建官方版本化发布，并将其打包用于分发。

### 3.1. 阶段 1：版本控制与变更日志生成

**目标**：分析所有新提交，自动确定每个插件的正确新版本号，生成变更日志，并创建不可变的 Git 标签来标记发布点。此阶段修改 Git 历史。

**流程图：**

```
开发者提交代码 (pnpm commit)
      | (强制结构化提交信息，包含 Issue-ID 和 Applies-To)
      V
  更新 issue_status.json
      |
      V
  执行 pnpm release run --dry-run (预演发布)
      |
      +--- (检查计划和警告) ---+
      |                        |
      V                        V
  确认发布计划             修改代码/issue_status.json
      |
      V
  执行 pnpm release run (正式发布)
      |
      V
  系统自动更新 changelog.json
      |
      V
  系统自动创建 Git 提交 (chore(release): publish ...)
      |
      V
  系统自动创建 Git 标签 (extension-vX.Y.Z)
      |
      V
  开发者执行 git push --follow-tags (推送到远程仓库)
```

#### 步骤 1.1：提交代码 (关键步骤)

所有代码更改**必须**使用交互式 `pnpm commit` 命令提交。这会强制执行结构化的提交消息格式，要求您指定更改的 `类型`（如 `feat`、`fix`）、`Issue ID` 以及最重要的 `Applies-To:` 字段，该字段将提交链接到其影响的特定插件。

```
# 使用交互式提交（推荐）
pnpm commit
```

**提交类型说明**:
*   `feat`: 新功能 (将提升 `minor` 版本)
*   `fix`: 修复 (将提升 `patch` 版本)
*   `perf`: 性能优化 (将提升 `patch` 版本)
*   `docs`, `style`, `refactor`, `test`, `chore`: 不会触发版本更新

#### 步骤 1.2：更新 Issue 状态

发布前，请确保 `issue_status.json` 文件中的 Issue 状态已更新为 `completed`。这是防止发布未完成功能的质量门控。

```json
{
  "1#20250103-01": "completed",
  "1#20250103-02": "in-progress"
}
```

#### 步骤 1.3：执行发布

1.  **预演发布（安全检查）**
    在进行任何更改之前，运行预演来预览发布计划。
    ```
    pnpm release run --dry-run
    ```
    该命令会分析 Git 历史，显示建议的版本变更、相关提交以及任何警告。

2.  **正式发布**
    验证计划后，运行命令执行发布。
    ```
    pnpm release run
    ```
    系统会更新 `changelog.json`，创建 Git 提交和版本标签。

3.  **推送到远程**
    发布完成后，将新提交和标签推送到远程仓库。
    ```
    git push --follow-tags
    ```

### 3.2. 阶段 2：构建与打包发布产物

**目标**：创建最终的可安装 `.zip` 包用于分发。此阶段**不**修改 Git 历史；它只创建构建产物。

**流程图：**

```
开发者配置 release-extensions.json
      |
      V
  执行 pnpm release (构建命令)
      |
      V
  读取构建计划 (release-extensions.json)
      |
      V
  遍历每个插件渠道包
      |
      V
  生成配置 (不使用缓存，确保干净构建)
      |
      V
  执行 WXT 构建 (wxt build)
      |
      V
  生成 RELEASE.md (在最终包内)
      |
      V
  更新 Manifest 备份 (.manifest/ 目录)
      |
      V
  Manifest 权限守卫 (比较新旧 Manifest 权限，发出警告)
      |
      V
  收集构建统计信息
      |
      V
  所有构建完成后，打印构建摘要表
      |
      V
  构建产物位于 /release 目录
```

#### 步骤 2.1：开发者准备 (手动任务)

1.  **配置构建目标**
    在 `scripts/build/release-extensions.json` 文件中指定要构建的插件和插件渠道包。

#### 步骤 2.2：执行构建脚本 (自动化)

此过程由构建系统自动处理。

1.  **配置构建目标**
    在 `scripts/build/release-extensions.json` 文件中指定要构建的插件和插件渠道包。

2.  **执行构建**
    ```
    # 构建 release-extensions.json 中定义的所有插件
    pnpm build

    # 构建指定插件的所有已配置插件渠道包
    pnpm build --extension cookies_manager

    # 构建指定插件的特定插件渠道包
    pnpm build --extension cookies_manager --variantTargets chrome-mv3-master firefox-mv3-master

    # 显示详细构建信息
    pnpm build --verbose

    # 强制不使用缓存
    pnpm build --no-cache
    ```
    构建成功后，产物将位于项目根目录下的 `/release` 目录中。

## 4. 常用命令参考

### 开发命令
```
pnpm dev <插件名> <渠道包名>           # 开发模式（必须指定插件和渠道包）
pnpm dev <插件名> <渠道包名> --verbose # 显示详细输出
pnpm dev <插件名> <渠道包名> --no-cache # 强制重新生成配置
```

### 发布命令
```
pnpm commit                    # 交互式提交
pnpm release preview <插件名>  # 预览特定插件的待发布变更
pnpm release run              # 执行发布
pnpm release run --dry-run    # 预演发布
pnpm release run --include <插件名> # 强制发布特定插件
pnpm release run --set-version <插件名>@<版本号> # 手动指定版本
```

### 构建命令
```
pnpm build                                    # 构建所有在 release-extensions.json 中定义的插件
pnpm build --extension <插件名>               # 构建指定插件
pnpm build --extension <插件名> --variantTargets <渠道包1> <渠道包2> # 构建指定渠道包
pnpm build --verbose                         # 显示详细构建信息
pnpm build --no-cache                        # 强制不使用缓存
```

### 国际化命令
```
node scripts/source-i18n-manager/cli.js excel2i18n   # Excel → JSON
node scripts/source-i18n-manager/cli.js i18n2excel   # JSON → Excel
node scripts/source-i18n-manager/cli.js updateI18n   # 批量更新
```

### 配置管理命令
```
# 查看可用的插件和渠道包
tsx scripts/extension-config-manager/cli.ts list
tsx scripts/extension-config-manager/cli.ts list --verbose

# 清理配置缓存
tsx scripts/extension-config-manager/cli.ts clean-all
tsx scripts/extension-config-manager/cli.ts clean <插件名>
```

## 5. 常见问题

| 问题 | 解决方案 |
|---|---|
| 提交被拒绝 | 提交信息格式不符合规范，请始终使用 `pnpm commit` 进行提交。 |
| 配置缓存问题 | 开发时配置未更新，运行 `pnpm dev` 时添加 `--no-cache` 参数。 |
| 找不到插件 | 插件目录或配置文件缺失，请检查 `packages/extensions/` 下的插件目录和 `extension.config.ts`。 |
| 开发模式参数错误 | 开发模式必须指定插件名和渠道包，格式：`pnpm dev <插件名> <渠道包>`。 |
| 构建失败 | 检查配置文件语法，使用 `--verbose` 参数查看详细错误信息。 |
| 语言包问题 | 检查 `locales/` 目录下的 JSON 文件格式，确保语法正确。 |
| Issue 警告 | 发布时 `issue_status.json` 中有 Issue 未完成，请更新 Issue 状态为 `completed` 或使用 `pnpm release run --force` 忽略。 |
| 版本计算错误 | 提交类型 (`feat`/`fix`) 不正确，请检查提交历史，或使用 `--set-version` 手动指定。 |
| Git 标签冲突 | 本地已存在将要创建的版本标签，请使用 `git tag -d <tag>` 删除冲突标签。 |

## 6. WXT 配置 (`wxt.config.ts`)

项目采用单一的中央 `wxt.config.ts` 文件，位于项目根目录。该文件负责为所有插件动态加载配置。

您不再需要在每个插件的目录（`packages/extensions/{插件名}/`）下创建或维护单独的 `wxt.config.ts` 文件。所有特定于插件的构建配置都应在各自的 `extension.config.ts` 文件中定义，脚手架会处理后续的集成。

这种设计简化了项目结构，并确保了配置的一致性。
