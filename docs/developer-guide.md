# 开发者指南

本文档旨在为项目维护者和高级开发者提供深入的技术洞察，涵盖系统架构、核心模块实现、开发规范以及未来的改进方向。

## 1. 项目架构

### 1.1. 目录结构

`scripts/` 目录是脚手架的核心，其结构如下：

```
scripts/
├── extension-config-manager/    # 扩展配置管理：配置处理、验证、生成
│   ├── index.ts                # 主入口，提供核心 API
│   ├── config-processor.ts     # 配置处理器，负责合并和验证
│   ├── generator.ts            # 配置文件生成器
│   ├── i18n-processor.ts       # 国际化处理器
│   ├── utils.ts                # 工具函数集合
│   ├── constants.ts            # 常量定义
│   └── types.ts                # 类型定义
├── build/                      # 构建系统：开发/生产构建流程、缓存策略
│   ├── index.ts                # 构建流程主入口
│   ├── utils.ts                # 构建工具和缓存策略
│   ├── cli.ts                  # 构建命令行接口
│   ├── types.ts                # 类型定义
│   └── release-extensions.json # 发布配置
├── changelog-release/          # 变更日志发布系统：版本管理、变更日志生成、Git 标签管理
│   ├── changelog.ts            # 变更日志生成逻辑
│   ├── cli.ts                  # 命令行入口
│   ├── commitizen-adapter.mjs  # Commitizen 适配器
│   ├── git.ts                  # Git 操作工具
│   ├── issue-checker.ts        # Issue 状态检查
│   ├── parser.ts               # 提交信息解析
│   ├── preview.ts              # 预览功能
│   ├── run.ts                  # 发布流程主入口
│   └── types.ts                # 类型定义
├── helpers/                    # 共享工具：通用工具函数、类型、常量、日志、Git、权限处理
│   ├── index.ts                # 统一导出
│   ├── utils.ts                # 通用工具函数
│   ├── logger.ts               # 统一日志系统
│   ├── permissions.ts          # 权限处理
│   ├── constants.ts            # 常量定义
│   └── types.ts                # 类型定义
├── __test__/                   # 测试文件
├── tools/                      # 其他工具
└── cli.ts                      # 统一 CLI 入口 (尚未实现)
```

### 1.2. 核心模块职责与数据流

*   **`extension-config-manager/`**: 负责扩展配置的完整处理流程，包括配置加载、验证、合并、国际化处理和文件生成，是整个配置系统的核心。
*   **`build/`**: 封装开发/生产构建流程，处理缓存策略、构建队列管理，并提供命令行接口。
*   **`changelog-release/`**: 实现自动化版本管理、变更日志生成和 Git 标签管理，确保发布流程的规范化。
*   **`helpers/`**: 提供所有模块共享的通用工具函数、类型定义、常量、统一日志系统、Git 工具和权限处理。

**核心数据流概览：**

```
extension.config.ts (源配置)
      |
      V
extension-config-manager (解析、验证、合并、生成 ProcessedVariantConfig)
      |
      +--- config-processor (配置处理和合并)
      |
      +--- i18n-processor (语言包处理，生成 Vue/Chrome 格式消息)
      |
      +--- generator (生成配置文件和语言包到 .variants/ 目录)
      |
      V
build (编排 WXT 构建，管理缓存策略)
      |
      V
WXT (消费生成的配置和资源，执行打包)
```

## 2. 插件配置系统

### 2.1. `extension.config.ts` 文件结构与处理规则

`extension.config.ts` 是定义插件所有行为的唯一真实来源。

#### 配置定义与验证

*   **`defineExtensionConfig` 函数**: 所有 `extension.config.ts` 文件必须使用此函数定义配置，以获得 TypeScript 类型检查和 IDE 智能提示。
*   **验证机制**:
    *   **开发时**: TypeScript 类型系统和 IDE 提供第一层验证。
    *   **构建时**: 脚手架在处理配置时进行完整验证，包括必填字段检查、值有效性验证等，任何失败都将抛出明确错误并终止程序。

#### 顶层配置字段

*   `name` (string, 必填): 插件的唯一标识名，与 `packages/extensions/` 下的目录名一致。
*   `version` (string, 必填): 插件的版本号，所有插件渠道类型共享此版本。
*   `manifestVersion` (number, 可选): 默认的 Manifest 版本 (2 或 3)，可被渠道包配置覆盖。
*   `defaultLocale` (string, 可选): 默认的语言包代码，可被渠道包配置覆盖。
*   `manifest` (object, 可选): 全局共享的 `manifest.json` 基础配置，将与渠道包中的 `manifest` 配置进行深度合并。
*   `i18n` (object, 可选): 全局的 i18n 处理配置。
    *   `locales` (string[], 可选): **注意：此字段在代码中会被忽略，语言列表通过扫描目录自动获取。**
    *   `includeKeys` / `excludeKeys` (string[], 可选): 用于过滤语言包 key 的正则表达式数组。
    *   `chromeOnlyLocales` (string[], 可选): 仅生成 Chrome 语言包格式的语言代码列表。
    *   `chromeOnlyKeys` (string[], 可选): 在 Chrome 语言包中只包含这些 key 的正则表达式数组。
*   `variants` (array, 必填): 定义所有插件渠道类型的数组。

#### `variants` 对象结构

`variants` 数组中的每个对象定义了一个具体的插件渠道类型。

*   `variantId` (string, 必填): 插件渠道 ID，由后端提供。
*   `variantName` (string, 必填): 插件渠道名称，由后端提供。
*   `webstore` (string, 必填): 目标浏览器 (e.g., 'chrome', 'firefox')。
*   `variant` (string, 必填): 插件渠道类型 (e.g., 'master', 'tm', 'offline')。
*   `manifestVersion` / `defaultLocale` (可选): 便捷属性，用于覆盖顶层同名配置。
*   `manifest` (object, 可选): 特定于此插件渠道类型的 `manifest.json` 配置。

#### 通用配置处理规则

*   **继承与覆盖**: 渠道包的配置将覆盖顶层同名字段。
*   **`manifest` 对象深度合并**: `manifest` 对象进行深度合并。对于数组类型字段（如 `permissions`），合并行为是**连接并去重**。
*   **优先级规则**: 对于可在多个位置定义的配置，遵循“最具体者优先”原则。例如 `manifest_version` 的优先级：`variant.manifest.manifest_version` > `variant.manifestVersion` > `extension.manifest.manifest_version` > `extension.manifestVersion`。

### 2.2. I18n 语言包处理流水线 (构建时)

`i18n.messages` 对象的生成是一个动态的、针对**每一个插件渠道包**的解析与格式化过程，由 `i18n-processor.ts` 模块负责处理。

**I18n 处理流水线：**

```
原始语言包数据 (shared/locales + extension/locales)
      |
      V
  确定语言列表 (自动扫描目录，支持缓存)
      |
      V
  合并语言包 (shared 优先级低于 extension)
      |
      V
  过滤文案 Keys (根据 includeKeys/excludeKeys)
      |
      V
  解析条件化文案 (根据 variantTarget 优先级匹配)
      |
      V
  格式化输出与占位符转换
      |
      +--- (Vue I18n 格式: $xxx$ -> {xxx}) ---> Vue Messages
      |
      +--- (Chrome 格式: $xxx$ -> $1$, 自动生成 placeholders) ---> Chrome Messages
      |
      V
  最终 i18n.messages 对象 (包含 vueMessages 和 chromeMessages)
```

### 2.3. 配置处理流程与最终产物

脚手架的配置处理严格按照以下两个阶段进行：

**配置处理流程：**

```
阶段一：配置解析与合并
  - 输入: extension.config.ts 源文件
  - 处理:
    - 使用 defineExtensionConfig 加载和验证
    - 执行配置继承与合并
    - 解析 i18n 条件化文案
    - 生成完整的内存配置对象
  - 输出: 完全解析的配置对象 (内存中)
      |
      V
阶段二：文件生成与写入
  - 输入: 上一阶段的配置对象
  - 处理:
    - 为每个插件渠道包生成独立的 extension.config.json
    - 生成对应的 i18n 文件 (i18n.json 和 _locales/ 目录)
    - 写入到 .variants/{variantTarget}/ 目录
  - 输出: 文件系统中的配置和语言包文件
```


**最终产物 (`extension.config.json`)**:

生成的 `extension.config.json` 是一个扁平化的、已完全解析的配置对象，包含了所有继承、合并和 i18n 解析后的最终结果。每个插件渠道包的配置文件都是完全独立的。

关键字段包括：
- `variantTarget`: 渠道包标识符
- `i18n.locales`: 支持的语言列表
- `i18n.vueMessages`: Vue I18n 格式的语言包（用于前端）
- `i18n.chromeMessages`: Chrome 扩展格式的语言包（用于 manifest）
- `manifest`: 完整的 manifest.json 配置
- `paths`: 所有相关路径信息

## 3. 扩展配置管理器架构

扩展配置管理器是整个系统的核心，负责处理所有与扩展配置相关的逻辑。

### 3.1. 模块结构

*   **`index.ts`**: 提供公共 API，包括 `defineExtensionConfig`、`getProcessedExtensionConfig` 等核心函数
*   **`config-processor.ts`**: 核心配置处理器，负责配置的合并、验证和处理
*   **`i18n-processor.ts`**: 国际化处理器，负责语言包的扫描、合并和格式转换
*   **`generator.ts`**: 配置文件生成器，负责将处理后的配置写入文件系统
*   **`utils.ts`**: 工具函数集合，提供路径管理、权限处理、文件操作等功能
*   **`constants.ts`**: 常量定义，包括支持的浏览器、变体类型等
*   **`types.ts`**: 类型定义，定义所有相关的 TypeScript 类型

### 3.2. 核心处理流程

1. **配置定义**: 通过 `defineExtensionConfig` 函数处理原始配置
2. **配置验证**: 验证配置的完整性和有效性
3. **配置合并**: 合并全局配置和变体特定配置
4. **国际化处理**: 处理语言包，生成 Vue 和 Chrome 格式
5. **文件生成**: 生成最终的配置文件和语言包文件

### 3.3. 缓存机制

*   **文件 I/O 缓存**: 语言包文件读取使用缓存，避免重复读取
*   **配置缓存**: 生成的配置文件可作为缓存使用，提高构建效率
*   **智能缓存策略**: 根据文件修改状态决定是否使用缓存

## 4. 构建系统架构

构建系统负责协调整个构建流程，管理缓存策略，并与 WXT 集成。

### 4.1. 构建系统组件

*   **`index.ts`**: 构建工作流主入口，协调整个构建过程
*   **`utils.ts`**: 构建工具函数，包括队列创建、计划生成、缓存策略
*   **`cli.ts`**: 命令行接口，提供开发和生产模式的构建命令
*   **`types.ts`**: 构建相关的类型定义
*   **`release-extensions.json`**: 发布配置文件，定义要构建的扩展

### 4.2. 构建流程

**开发模式流程：**

```
pnpm dev <extension> [variantTarget]
      |
      V
  创建构建队列 (单个扩展，单个变体)
      |
      V
  生成构建计划 (检查缓存，决定是否重新生成配置)
      |
      V
  执行 WXT 开发服务器
      |
      V
  启动热重载和开发环境
```

**生产模式流程：**

```
pnpm build [options]
      |
      V
  创建构建队列 (多个扩展，多个变体)
      |
      V
  生成构建计划 (强制重新生成配置，确保一致性)
      |
      V
  逐个执行 WXT 构建
      |
      V
  生成构建统计和报告
```

### 4.3. WXT 集成

*   **单一的 WXT 配置**: 项目使用位于根目录的唯一 `wxt.config.ts` 文件。构建脚本会生成完整的 JSON 配置文件，并通过 `VARIANT_TARGET_CONFIG_PATH` 环境变量传递给 WXT。
*   **动态配置加载**: WXT 配置文件会读取环境变量，动态加载对应的配置文件，生成完整的构建配置。
*   **构建脚本编排**: `scripts/build/cli.ts` 负责：
    1. 调用扩展配置管理器生成配置和语言包文件
    2. 设置环境变量
    3. 执行 WXT 构建命令

### 4.4. 缓存策略

*   **缓存位置**: 生成的配置文件位于 `.variants/{variantTarget}/` 目录
*   **智能缓存**: 开发模式下会检查 Git 状态，决定是否使用缓存
*   **缓存验证**: 检查 `extension.config.json` 文件是否存在来判断缓存有效性
*   **强制刷新**: 支持 `--no-cache` 参数强制重新生成配置
*   **缓存清理**: 提供清理工具删除 `.variants` 目录

## 5. 开发原则与规范

*   **KISS (Keep It Simple, Stupid)**: 保持代码直接简洁，避免过度复杂化
*   **YAGNI (You Aren't Gonna Need It)**: 只实现当前需要的功能，避免过度工程化
*   **模块化设计**: 每个功能模块独立，职责清晰，通过 `index.ts` 统一导出接口
*   **类型安全**: 充分利用 TypeScript 类型系统，确保代码健壮性
*   **错误处理**: 提供清晰的错误信息和处理机制，确保程序在异常情况下能优雅地终止或恢复
*   **统一日志**: 使用 `scripts/helpers/logger.ts` 提供的统一日志系统，支持上下文和详细模式
*   **代码复用**: 尽可能复用现有工具函数和模块
*   **术语一致性**: 在代码和文档中保持术语的一致使用
*   **中文注释**: 使用 JSDoc 风格的中文注释，为复杂逻辑提供详细说明

## 6. 测试与调试

*   **调试配置生成**: 使用 `--verbose` 参数启用详细日志，检查 `.variants/{variantTarget}/extension.config.json`
*   **调试构建流程**: 使用 `pnpm dev <extension> --verbose` 查看详细的构建过程
*   **缓存调试**: 使用 `--no-cache` 参数强制重新生成配置，排除缓存问题
*   **配置验证**: 检查生成的配置文件结构和内容是否正确
*   **语言包调试**: 检查 `.variants/{variantTarget}/i18n.json` 和 `_locales/` 目录
*   **测试发布流程**: 使用 `pnpm release run --dry-run` 进行预演
*   **单元测试**: 检查 `__test__/` 目录下的测试用例

## 7. 贡献指南

1.  **创建功能分支**：从 `main` 分支创建新分支。
2.  **实现功能/修复 Bug**：编写代码，添加必要的类型定义和测试。
3.  **更新文档**：保持文档与代码同步，提供清晰的示例。
4.  **提交代码**：始终使用 `pnpm commit` 进行交互式提交，遵循提交规范。
5.  **创建 Pull Request**：提交 PR 到 `main` 分支，描述变更内容。

## 8. 已知问题与未来计划

以下是当前项目脚手架中尚未实现的功能和未来优化的方向：

### 8.1. 当前实现状态

*   **扩展配置管理器**: ✅ 已完成，提供完整的配置处理、验证、生成功能
*   **构建系统**: ✅ 已完成，支持开发和生产模式，包含缓存策略
*   **统一日志系统**: ✅ 已完成，提供统一的日志管理和输出格式
*   **国际化处理**: ✅ 已完成，支持多语言包处理和格式转换
*   **命令行接口**: ✅ 已完成，提供开发和构建命令

### 8.2. 尚未实现的功能

*   **统一 CLI 入口 (`scripts/cli.ts`)**: 目前 CLI 命令分散在各个模块中，计划实现统一入口
*   **`RELEASE.md` 文件生成**: 在最终打包产物中生成发布说明文件
*   **Manifest 权限守卫**: 完善 Manifest 权限变更检测和警告机制

### 8.2. 优化项目/未来工作

#### 8.2.1. 构建系统优化

**生产模式后处理工作** (`scripts/build/index.ts`)：
- 给每个插件都生成 `release/{extensionName}/{version}/RELEASED.md` 文件，内容为插件名，版本号，渠道包列表和更新的内容
- 给每个插件渠道包都生成 `packages/{extensionName}/.manifest/manifest.{variantTarget}.json` 文件，用于留底，权限校验和对比
- 统计构建结果
- 上传到 SVN/Git（如果需要）

#### 8.2.2. 国际化系统优化

**Excel 转换工具增强**：
- ✅ 已完成：对 `EXTENSION_NAME` (≤75) 和 `EXTENSION_DESCRIPTION` (≤132) 的长度校验
- ✅ 已完成：写入时将这两个字段排序到最前面

#### 8.2.3. 配置系统优化

**i18n.locales 配置行为**：
- ✅ 已完成：保持通过扫描目录获取语言列表的简单性
- ✅ 已完成：在检测到手动设置 `i18n.locales` 时发出警告，提示用户该设置将被忽略

#### 8.2.4. 代码质量改进

**统一 CLI 入口**：
- 实现 `scripts/cli.ts` 统一 CLI 入口，目前该文件为空
- 整合分散在各模块中的 CLI 命令

**代码规范化**：
- 移除未使用的变量和函数（当前 ESLint 配置为 'off'）
- 统一错误处理机制
- 完善类型定义

#### 8.2.5. 文档和工具链

**开发工具改进**：
- 完善测试覆盖率
- 改进调试工具和日志系统
- 优化开发者体验

**文档完善**：
- 补充 API 文档
- 添加更多使用示例
- 完善故障排除指南
