/**
 * @fileoverview 插件构建类型定义
 * @description 定义插件项目及渠道包（variant）构建相关的类型接口。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import type { ProcessedVariantConfig } from '../extension-config/types.js';

/**
 * 构建选项
 */
export interface BuildOptions {
  /** 构建模式 */
  mode: 'dev' | 'release';
}

/**
 * 构建目标 - 表示需要构建的单个插件及其渠道包
 */
export interface BuildTarget {
  /** 插件名称 */
  extensionName: string;
  /** 渠道包列表，空数组表示构建所有渠道包 */
  variantTargets: string[];
}

/**
 * 构建任务 - 表示经过配置处理后的单个构建项
 */
export interface BuildTask {
  /** 插件名称 */
  extensionName: string;
  /** 渠道包名称 */
  variantTarget: string;
  /** 处理后的配置对象 */
  config: ProcessedVariantConfig;
}

/**
 * 构建结果统计
 */
export interface BuildSummary {
  /** 总插件数 */
  totalExtensions: number;
  /** 总渠道包数 */
  totalVariantTargets: number;
  /** 构建开始时间 */
  startTime: Date;
  /** 构建结束时间 */
  endTime: Date;
  /** 构建用时（毫秒） */
  duration: number;
}

/**
 * Release配置文件格式
 */
export interface ReleaseConfig {
  /** 插件名称 */
  extensionName: string;
  /** 渠道包列表 */
  variantTargets: string[];
}
