/**
 * @fileoverview 插件构建主工作流
 * @description 负责插件项目及渠道包（variant）构建的执行、统计和后处理。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import { execa } from 'execa';
import type { Options as ExecaOptions } from 'execa';
import path from 'path';
import { createLogger, projectPaths } from '../helpers/index.js';
import { generateExtensionPaths } from '../extension-config/index.js';
import { validateBuildTargets, generateBuildTasks, loadReleaseConfig, checkManifestPermissions } from './utils.js';
import type { BuildOptions, BuildTarget, BuildTask, BuildSummary } from './types.js';

const logger = createLogger('BuildWorkflow');

/**
 * 插件构建主工作流入口
 *
 * 主要职责：
 * 1. 校验构建目标（插件名字、渠道包）
 * 2. 生成构建任务
 * 3. 执行构建任务
 * 4. 输出构建统计与日志
 *
 * @param targets - 构建目标数组
 * @param options - 构建选项
 */
export async function runBuildWorkflow(
  targets: BuildTarget[],
  options: BuildOptions,
): Promise<void> {
  const startTime = new Date();
  logger.info(`构建流程开始 [${options.mode}]`);

  try {
    // 1. 验证构建目标
    const validatedTargets = await validateBuildTargets(targets, options.mode);
    if (validatedTargets.length === 0) {
      logger.warn('没有需要构建的目标，退出。');
      return;
    }

    // 2. 生成构建任务
    const buildTasks = await generateBuildTasks(validatedTargets);
    logger.success(`构建任务创建完成: ${buildTasks.length} 个渠道包`);

    // 3. 执行构建任务
    await executeBuildTasks(buildTasks, options.mode);

    // 4. 完成构建
    await finalizeBuild(buildTasks, startTime, options.mode);

    logger.success('构建流程完成');
  } catch (error) {
    logger.error(`构建失败: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * 执行插件构建任务
 * @param buildTasks - 构建任务数组
 * @param mode - 构建模式（dev/release）
 */
async function executeBuildTasks(
  buildTasks: BuildTask[],
  mode: 'dev' | 'release',
): Promise<void> {
  logger.info('开始执行 WXT 构建');

  // 统一处理中断信号，跟踪所有子进程
  const childProcesses: any[] = [];
  const handleExit = () => {
    logger.info('收到中断信号，正在终止所有构建进程...');
    for (const cp of childProcesses) {
      if (cp && typeof cp.kill === 'function') {
        cp.kill('SIGTERM');
      }
    }
    logger.info('所有构建进程已终止');
    process.exit(0);
  };
  process.on('SIGINT', handleExit);
  process.on('SIGTERM', handleExit);

  try {
    if (mode === 'dev') {
      // 开发模式：只构建第一个任务
      const task = buildTasks[0];
      await executeWxtCommand(task, mode, childProcesses);
    } else {
      // 生产模式：逐个构建所有任务
      for (const task of buildTasks) {
        await executeWxtCommand(task, mode, childProcesses);
      }
    }
    logger.success('WXT 构建完成');
  } finally {
    process.off('SIGINT', handleExit);
    process.off('SIGTERM', handleExit);
  }
}

/**
 * 执行单个 WXT 构建命令
 */
async function executeWxtCommand(task: BuildTask, mode: 'dev' | 'release', childProcesses?: any[]): Promise<void> {
  const { extensionName, variantTarget, config } = task;

  logger.info(`构建 ${extensionName}:${variantTarget}`);

  // 动态生成路径信息
  const paths = generateExtensionPaths(extensionName, variantTarget, config.version);
  const variantTargetConfigPath = paths.extensionVariantTargetJSON;

  const command = 'pnpm';
  const args = [
    ...(mode === 'dev' ? ['wxt'] : ['wxt', 'build']),
    '-c',
    path.resolve(projectPaths.workspace, 'wxt.config.ts'),
    `--mv${config.manifestVersion}`,
  ];

  const options: ExecaOptions = {
    stdio: 'inherit',
    env: {
      VARIANT_TARGET: variantTarget,
      VARIANT_TARGET_CONFIG_PATH: variantTargetConfigPath,
    },
  };

  // 统一由上层处理信号和进程管理
  const childProcess = execa(command, args, options);
  if (Array.isArray(childProcesses)) {
    childProcesses.push(childProcess);
  }
  try {
    await childProcess;
  } catch (error: any) {
    // 忽略由于进程被终止导致的错误
    if (error?.signal === 'SIGTERM' || error?.signal === 'SIGINT') {
      logger.info('构建进程已停止');
      process.exit(0);
    }
    throw error;
  }
  logger.verbose(`完成构建: ${extensionName}:${variantTarget}`);
}

/**
 * 完成构建并输出统计信息
 */
async function finalizeBuild(buildTasks: BuildTask[], startTime: Date, mode: 'dev' | 'release'): Promise<void> {
  const endTime = new Date();
  const duration = endTime.getTime() - startTime.getTime();

  // 按插件分组统计
  const extensionGroups = new Map<string, {
    variantTargets: string[];
    version: string;
  }>();

  for (const task of buildTasks) {
    if (!extensionGroups.has(task.extensionName)) {
      extensionGroups.set(task.extensionName, {
        variantTargets: [],
        version: task.config.version,
      });
    }
    const group = extensionGroups.get(task.extensionName)!;
    group.variantTargets.push(task.variantTarget);
  }

  const summary: BuildSummary = {
    totalExtensions: extensionGroups.size,
    totalVariantTargets: buildTasks.length,
    startTime,
    endTime,
    duration,
  };

  // 输出详细的构建统计
  logger.info(
    `[构建完成] 共构建 ${summary.totalExtensions} 个插件，${summary.totalVariantTargets} 个渠道包，耗时 ${Math.round(duration / 1000)}s:`
  );

  for (const [extensionName, group] of extensionGroups) {
    logger.info(`- ${extensionName} v${group.version}`);
    for (const variantTarget of group.variantTargets) {
      logger.info(`  - ${variantTarget}`);
    }
  }

  // 生产模式下进行权限检查
  if (mode === 'release') {
    await checkManifestPermissions(buildTasks);
  }
}
