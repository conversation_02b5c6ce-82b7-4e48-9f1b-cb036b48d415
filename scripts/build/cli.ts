#!/usr/bin/env node
/**
 * @fileoverview 插件构建工具命令行接口
 * @description 提供插件项目及渠道包（variant）开发、生产模式的构建命令。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { Logger } from '../helpers/index.js';
import { runBuildWorkflow } from './build.js';
import { loadReleaseConfig } from './utils.js';
import type { BuildTarget } from './types.js';

/**
 * 命令行主入口函数
 */
yargs(hideBin(process.argv))
  .scriptName('build')
  .usage('用法: $0 <command> [options]\n\n浏览器插件构建工具')
  .option('verbose', {
    alias: 'V',
    type: 'boolean',
    describe: '显示详细输出信息',
    default: false,
    global: true,
  })
  .command(
    'dev <extension> [variantTarget]',
    '开发模式构建指定插件和渠道包',
    (yargs) => {
      yargs
        .positional('extension', {
          describe: '插件名（如 cookies_manager）',
          type: 'string',
          demandOption: true,
        })
        .positional('variantTarget', {
          describe: '渠道包名（如 chrome-mv3-master）',
          type: 'string',
          demandOption: false,
          default: 'chrome-mv3-master',
        });
    },
    async (argv) => {
      const extensionName = argv.extension as string;
      const variantTarget = (argv.variantTarget as string) || 'chrome-mv3-master';
      const verbose = argv.verbose as boolean;

      Logger.setVerbose(verbose);

      const buildTargets: BuildTarget[] = [
        {
          extensionName,
          variantTargets: [variantTarget],
        },
      ];

      await runBuildWorkflow(buildTargets, { mode: 'dev' });
    },
  )
  .command(
    'build <extension> [variantTargets..]',
    '生产模式构建指定插件',
    (yargs) => {
      yargs
        .positional('extension', {
          describe: '插件名（如 cookies_manager）',
          type: 'string',
          demandOption: true,
        })
        .positional('variantTargets', {
          describe: '渠道包列表，不指定则构建所有渠道包',
          type: 'string',
          array: true,
          default: [],
        });
    },
    async (argv) => {
      const extensionName = argv.extension as string;
      const variantTargets = (argv.variantTargets as string[]) || [];
      const verbose = argv.verbose as boolean;

      Logger.setVerbose(verbose);

      const buildTargets: BuildTarget[] = [
        {
          extensionName,
          variantTargets,
        },
      ];

      await runBuildWorkflow(buildTargets, { mode: 'release' });
    },
  )
  .command(
    'release',
    '生产模式批量构建（从 release-extensions.json 读取配置）',
    (yargs) => {
      // release 命令不需要额外参数
    },
    async (argv) => {
      const verbose = argv.verbose as boolean;

      Logger.setVerbose(verbose);

      // 从配置文件读取构建列表
      const buildTargets = await loadReleaseConfig();

      await runBuildWorkflow(buildTargets, { mode: 'release' });
    },
  )
  .example('$0 dev cookies_manager', '开发模式构建 cookies_manager 插件（默认渠道包）')
  .example('$0 dev cookies_manager chrome-mv3-tm', '开发模式构建指定渠道包')
  .example('$0 build cookies_manager', '生产模式构建指定插件的所有渠道包')
  .example('$0 build cookies_manager chrome-mv3-master chrome-mv3-tm', '生产模式构建指定渠道包')
  .example('$0 release', '生产模式构建所有插件（从配置文件读取）')
  .demandCommand(1, '请指定一个命令（dev、build、release）')
  .strict()
  .help()
  .version('3.0.0')
  .epilog(
    `说明：
  • dev 命令用于开发模式，支持单插件单渠道包构建
  • build 命令用于生产模式，支持单插件多渠道包构建
  • release 命令用于生产模式，支持多插件批量构建
  • 使用 --verbose 可以查看详细的构建过程信息`,
  )
  .parse();
