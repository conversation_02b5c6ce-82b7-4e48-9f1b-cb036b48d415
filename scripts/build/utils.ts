/**
 * @fileoverview 插件构建工具函数集合
 * @description 提供插件项目及渠道包（variant）构建相关的工具函数，包括校验、配置处理、manifest 联动等。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import fs from 'fs-extra';
import path from 'path';
import {
  listExtensionVariants,
  loadExtensionConfigs,
  writeExtensionFiles,
  generateExtensionJson,
  generateExtensionPaths,
} from '../extension-config/index.js';
import { createLogger, projectPaths } from '../helpers/index.js';
import {
  checkManifestPermissions as _checkManifestPermissions,
  compareManifestPermissions as _compareManifestPermissions,
} from '../manifest/utils.js';
import type { BuildTarget, BuildTask, ReleaseConfig } from './types.js';

const logger = createLogger('BuildUtils');

/**
 * 校验插件构建目标（插件名字、渠道包）
 *
 * 主要职责：
 * 1. 校验目标数组非空
 * 2. 开发模式下只允许单插件单渠道包
 * 3. 校验插件名字和渠道包是否存在
 * 4. 生产模式下可自动补全所有渠道包
 *
 * @param targets - 构建目标数组
 * @param mode - 构建模式（dev/release）
 * @returns 校验通过的构建目标数组
 * @throws 校验失败时抛出异常
 */
export async function validateBuildTargets(
  targets: BuildTarget[],
  mode: 'dev' | 'release',
): Promise<BuildTarget[]> {
  if (!targets || targets.length === 0) {
    throw new Error('构建目标不能为空');
  }

  // 开发模式限制：只能构建单个插件和单个渠道包
  if (mode === 'dev') {
    if (targets.length !== 1) {
      throw new Error('开发模式下只允许构建一个插件');
    }

    const target = targets[0];
    if (!target.variantTargets || target.variantTargets.length === 0) {
      // 开发模式下默认使用 chrome-mv3-master
      target.variantTargets = ['chrome-mv3-master'];
    } else if (target.variantTargets.length !== 1) {
      throw new Error('开发模式下只允许构建一个渠道包');
    }
  }

  // 验证插件名和渠道包是否存在
  for (const target of targets) {
    const { extensionName } = target;
    let { variantTargets } = target;

    // 获取所有可用的插件和变体信息
    const { extensions: availableExtensions } = await listExtensionVariants([extensionName]);

    // 验证插件名是否存在
    if (!availableExtensions[extensionName]) {
      throw new Error(`插件不存在: ${extensionName}`);
    }

    // 处理空的 variantTargets（只在生产模式下允许）
    if (!variantTargets || variantTargets.length === 0) {
      if (mode === 'release') {
        // 生产模式：如果没有指定渠道包，则使用所有可用的渠道包
        variantTargets = availableExtensions[extensionName];
        target.variantTargets = variantTargets;
      }
    } else {
      // 验证指定的渠道包是否存在
      const availableVariants = availableExtensions[extensionName];
      for (const variantTarget of variantTargets) {
        if (!availableVariants.includes(variantTarget)) {
          throw new Error(`插件 ${extensionName} 的渠道包不存在: ${variantTarget}`);
        }
      }
    }
  }

  logger.info(`构建目标验证通过: ${targets.length} 个插件`);
  return targets;
}

/**
 * 生成插件构建任务列表
 *
 * 主要职责：
 * 1. 加载插件配置
 * 2. 生成所有渠道包的配置文件（写入 .variants 目录）
 * 3. 返回构建任务数组
 *
 * @param targets - 构建目标数组
 * @returns 构建任务数组
 * @throws 插件配置未找到时抛出异常
 */
export async function generateBuildTasks(targets: BuildTarget[]): Promise<BuildTask[]> {
  const tasks: BuildTask[] = [];

  for (const target of targets) {
    const { extensionName, variantTargets } = target;

    // 加载所有插件配置
    const allExtensionConfigs = await loadExtensionConfigs([extensionName]);

    logger.verbose(`处理插件: ${extensionName}`);

    const extensionConfigs = allExtensionConfigs[extensionName];
    if (!extensionConfigs) {
      throw new Error(`插件配置未找到: ${extensionName}`);
    }

    // 生成配置文件（写入到 .variants 目录）
    const variantsToGenerate =
      variantTargets.length > 0 ? variantTargets : Object.keys(extensionConfigs);

    for (const variantTarget of variantsToGenerate) {
      const config = extensionConfigs[variantTarget];
      if (config) {
        // 为配置添加路径信息
        const paths = generateExtensionPaths(extensionName, variantTarget, config.version);

        // 为每个变体生成并写入文件（包含 paths 信息）
        const extensionJson = { ...generateExtensionJson(config), paths };
        await writeExtensionFiles(extensionName, variantTarget, extensionJson);
      }
    }

    logger.info(`[${extensionName}] 生成 ${variantsToGenerate.length} 个渠道包的配置文件`);

    // 创建构建任务
    for (const variantTarget of variantsToGenerate) {
      const config = extensionConfigs[variantTarget];
      if (config) {
        tasks.push({
          extensionName,
          variantTarget,
          config,
        });
      }
    }
  }

  logger.info(`构建任务生成完成: ${tasks.length} 个渠道包`);
  return tasks;
}

/**
 * 读取 release-extensions.json 配置文件，获取发布构建目标
 * @returns 发布构建目标数组
 * @throws 文件不存在或格式错误时抛出异常
 */
export async function loadReleaseConfig(): Promise<BuildTarget[]> {
  const releaseConfigPath = path.resolve(projectPaths.workspace, 'release-extensions.json');

  if (!(await fs.pathExists(releaseConfigPath))) {
    throw new Error(
      `未找到 release-extensions.json: ${path.relative(projectPaths.workspace, releaseConfigPath)}`,
    );
  }

  const releaseConfigs: ReleaseConfig[] = await fs.readJson(releaseConfigPath);

  return releaseConfigs;
}

/**
 * 检查所有构建任务的 manifest 权限变更
 *
 * 主要职责：
 * 1. 对比每个渠道包的 manifest 权限变更
 * 2. 输出权限变更警告或提示
 * 3. 缓存最新 manifest
 *
 * @param tasks - 构建任务数组（需包含插件名字、渠道包、版本号）
 */
export async function checkManifestPermissions(
  tasks: {
    extensionName: string;
    variantTarget: string;
    config: {
      version: string;
    };
  }[],
): Promise<void> {
  try {
    logger.info('开始检查 manifest 权限变更...');

    for (const task of tasks) {
      const { extensionName, variantTarget, config } = task;
      const paths = generateExtensionPaths(extensionName, variantTarget, config.version);
      const outputManifestPath = path.join(paths.extensionVariantTargetOutput, 'manifest.json');
      const cachedManifestPath = paths.extensionVariantTargetManifest;
      if (!(await fs.pathExists(outputManifestPath))) {
        logger.warn(`未找到输出的 manifest 文件: ${extensionName}:${variantTarget}`);
        continue;
      }
      const outputManifest = await fs.readJson(outputManifestPath);
      let cachedManifest = null;
      if (await fs.pathExists(cachedManifestPath)) {
        cachedManifest = await fs.readJson(cachedManifestPath);
      }
      // 只传递参数，日志交由内部处理
      if (cachedManifest) {
        _compareManifestPermissions(outputManifest, cachedManifest, extensionName, variantTarget);
      } else {
        _checkManifestPermissions(outputManifest, extensionName, variantTarget);
      }
      await fs.ensureDir(path.dirname(cachedManifestPath));
      await fs.writeJson(cachedManifestPath, outputManifest, { spaces: 2 });
    }
    logger.success('Manifest 权限检查完成');
  } catch (error) {
    logger.error(
      `Manifest 权限检查失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
