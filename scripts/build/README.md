# build 模块 PRD 文档

## 一、背景与目标

在多扩展、多平台的开发场景下，自动化构建流程对于保障产物一致性、提升开发效率至关重要。本模块旨在提供统一、可扩展的构建工具链，支持多变体、多平台、多语言的扩展自动化打包、产物输出与流程集成。

## 二、设计原则

1. **自动化与可配置**：支持声明式构建配置，自动化产物生成。
2. **多平台适配**：兼容 Chrome/Edge/Firefox/Opera 等主流平台构建差异。
3. **流程可插拔**：支持自定义构建流程与钩子，便于扩展。
4. **类型安全**：全流程 TypeScript 类型约束。

## 三、核心功能

1. **统一构建入口**：一键构建所有扩展及其变体。
2. **多平台产物输出**：自动生成各平台/变体的构建产物。
3. **流程钩子与扩展**：支持自定义构建前后处理逻辑。
4. **类型与工具支持**：丰富的类型定义与工具函数，便于集成。

## 四、目录结构与职责

| 文件/目录      | 说明                         |
|---------------|------------------------------|
| build.ts      | 构建主流程与入口              |
| cli.ts        | 命令行入口，参数解析          |
| types.ts      | 类型定义与约束                |
| utils.ts      | 构建相关工具函数              |

## 五、核心流程说明

1. **构建入口流程**
   - 通过 `cli.ts` 解析命令行参数，确定构建目标与参数。
   - 调用 `build.ts` 主流程，加载所有扩展配置。

2. **产物生成流程**
   - 针对每个扩展及其变体，自动生成对应的构建产物（如 zip 包、dist 目录等）。
   - 支持多平台差异化产物输出。

3. **流程钩子与扩展**
   - 支持在构建前后插入自定义处理逻辑（如校验、产物后处理等）。

## 六、主要接口说明

### buildAllExtensions(options?): Promise<void>
> 一键构建所有扩展及其变体。

### buildSingleExtension(extensionName, options?): Promise<void>
> 构建单个扩展及其所有变体。

### registerBuildHook(hook: BuildHook): void
> 注册自定义构建流程钩子。

## 七、使用示例

```ts
import { buildAllExtensions } from 'scripts/build';

await buildAllExtensions({ platform: 'chrome', mode: 'release' });
```

## 八、常见问题 FAQ

**Q: 如何新增构建流程钩子？**
A: 实现 BuildHook 接口，通过 registerBuildHook 注册。

**Q: 如何支持新平台构建？**
A: 在 types.ts 和 build.ts 中补充平台类型与处理逻辑。

## 九、维护与扩展约定

1. 类型定义集中于 types.ts，变更需同步文档。
2. 新增构建流程需补充注释与用例。
3. 目录结构如有调整需同步通知相关团队。

---
如有更多问题或建议，请联系维护人或提交 issue。
