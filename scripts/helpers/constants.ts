import { SUPPORTED_LOCALES as WXT_SUPPORTED_LOCALES } from '@wxt-dev/i18n/build';
import type { ManifestVersionType, VariantType, WebstoreType } from './types.js';

// #region --- 语言支持 ---

export const SUPPORTED_LOCALES = WXT_SUPPORTED_LOCALES;

// #endregion

// #region --- Webstore 相关常量 ---

export const SUPPORT_WEBSTORE = {
  chrome: 'chrome',
  firefox: 'firefox',
  browser360: 'browser360',
  safari: 'safari',
  adspower: 'adspower',
  opera: 'opera',
  edge: 'edge',
} as const;

export const SUPPORT_WEBSTORES: WebstoreType[] = Object.values(SUPPORT_WEBSTORE) as WebstoreType[];

/**
 * 浏览器商店/中文简称
 * 参考：https://easydoc.net/doc/91904722/fvS6AHYM/9TYy2p86
 */
export const WEBSTORE_CN: Record<WebstoreType, string> = {
  chrome: 'e-c',
  firefox: 'e-f',
  opera: 'e-o',
  browser360: 'e-360',
  safari: 'e-s',
  adspower: 'e-ads',
  edge: 'e-edge',
};

/**
 * 各浏览器商店的插件自动更新地址
 * 参考：https://learn.microsoft.com/zh-cn/microsoft-edge/extensions-chromium/publish/auto-update
 */
export const WEBSTORES_UPDATE_URL: Record<WebstoreType | 'aliprice', string> = {
  chrome: 'https://clients2.google.com/service/update2/crx',
  opera: 'https://clients2.google.com/service/update2/crx',
  browser360: 'http://upext.chrome.360.cn/intf.php?method=ExtUpdate.query',
  firefox: '',
  safari: '',
  adspower: '',
  edge: '',
  aliprice: 'https://www.aliprice.com/extension_page/{extensionName}/updates.json',
};

/**
 * 需要通过 AMO（Firefox Add-ons）发布的浏览器商店
 */
export const AMO_REQUIRED_WEBSTORES: Partial<Record<WebstoreType, WebstoreType>> = {
  firefox: 'firefox',
  browser360: 'browser360',
};

/**
 * 各浏览器商店的基础 URL
 */
export const EXTENSION_BASE_URL: Record<WebstoreType, string> = {
  chrome: 'chrome-extension://__MSG_@@extension_id__',
  opera: 'chrome-extension://__MSG_@@extension_id__',
  browser360: 'chrome-extension://__MSG_@@extension_id__',
  firefox: 'moz-extension://__MSG_@@extension_id__',
  safari: 'chrome-extension://__MSG_@@extension_id__',
  adspower: 'chrome-extension://__MSG_@@extension_id__',
  edge: 'chrome-extension://__MSG_@@extension_id__',
};

// #endregion

// #region --- Manifest 相关常量 ---

export const SUPPORT_MANIFEST_VERSIONS: ManifestVersionType[] = [2, 3];

export const SUPPORT_MV = {
  mv2: 'mv2',
  mv3: 'mv3',
} as const;

export const SUPPORT_MVS = Object.values(SUPPORT_MV) as string[];

// #endregion

// #region --- Variant 相关常量 ---

export const SUPPORT_VARIANT = {
  /**
   * 插件主版本
   */
  master: 'master',
  /**
   * 插件商标版本：去除所有阿里巴巴相关商标，针对海外商店如 1688、AliExpress、Taobao、Alibaba
   * alias for trademark
   */
  tm: 'tm',
  /**
   * 与 `tm` 相同功能，但为 beta 版本
   */
  tmBeta: 'tmBeta',
  /**
   * 分发版本：非插件主版本，而是由 AliPrice 直接分发的版本，如 Firefox
   * alias for distribute by aliprice
   */
  dba: 'dba',
  /**
   * 离线版本
   */
  offline: 'offline',
} as const;

export const SUPPORT_VARIANTS: VariantType[] = Object.values(SUPPORT_VARIANT) as VariantType[];

// #endregion

// #region --- I18n 相关常量 ---

/**
 * 默认仅生成到 Chrome 的语言
 * 这些语言仅会出现在 `_locales/xx/messages.json` 中，只包含 `EXTENSION_NAME` 和 `EXTENSION_DESCRIPTION` 等关键 key
 */
export const DEFAULT_CHROME_LOCALES_ONLY = ['en_US', 'en_GB', 'pt_BR', 'es_419'];

/**
 * Chrome 格式中默认包含的 key
 * 这些 key 会出现在 `_locales/xx/messages.json` 中
 */
export const DEFAULT_CHROME_MESSAGES_ONLY = [
  'EXTENSION_NAME',
  'EXTENSION_DESCRIPTION',
  '/^context_menu_.*/',
];

// #endregion

// #region --- 权限相关常量 ---

/**
 * 常用的浏览器扩展权限
 */
export const COMMON_PERMISSIONS = {
  contextMenus: 'contextMenus',
  cookies: 'cookies',
  storage: 'storage',
  notifications: 'notifications',
  alarms: 'alarms',
  tabs: 'tabs',
  activeTab: 'activeTab',
  scripting: 'scripting',
  unlimitedStorage: 'unlimitedStorage',
  identity: 'identity',
  webRequest: 'webRequest',
  webRequestBlocking: 'webRequestBlocking',
  webNavigation: 'webNavigation',
  declarativeNetRequest: 'declarativeNetRequest',
} as const;

/**
 * 基础默认权限组合
 */
export const BASIC_DEFAULT_PERMISSIONS = [
  COMMON_PERMISSIONS.contextMenus,
  COMMON_PERMISSIONS.cookies,
  COMMON_PERMISSIONS.storage,
  COMMON_PERMISSIONS.notifications,
  COMMON_PERMISSIONS.alarms,
];

/**
 * 常用主机权限
 */
export const COMMON_HOST_PERMISSIONS = {
  allUrls: '<all_urls>',
  allProtocolUrls: '*://*/*',
  allHttp: ['http://*/*', 'https://*/*'],
} as const;

// #endregion
