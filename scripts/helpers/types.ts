import type { Browser } from 'wxt/browser';

export type BrowserManifest = Browser.runtime.Manifest;

/**
 * @description 插件支持的浏览器类型
 */
export type WebstoreType =
  | 'chrome'
  | 'firefox'
  | 'browser360'
  | 'safari'
  | 'adspower'
  | 'opera'
  | 'edge';

/**
 * @description 来源插件还是网站
 */
export type WebstoreCNType =
  | 'e-c'
  | 'e-f'
  | 'e-o'
  | 'e-360'
  | 'e-s'
  | 'e-ads'
  | 'e-edge'
  | WebstoreType;

/**
 * @description 插件的渠道类型
 */
export type VariantType = 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';

/**
 * @description 插件的渠道类型
 */
export type VariantChannel = WebstoreType | `${WebstoreType}_offline`;

/**
 * @description Manifest 版本类型
 */
export type ManifestVersionType = 2 | 3;

/**
 * @description 配置合并选项
 */
export interface DeepMergeOptions {
  /** @description 是否深度合并对象 */
  deepMerge?: boolean;
  /** @description 是否覆盖数组（false 表示合并数组） */
  overrideArrays?: boolean;
  /** @description 是否保留源对象不变 */
  immutable?: boolean;
}

/**
 * @description 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * @description 插件渠道包的结构化信息
 */
export interface VariantInfo {
  webstore: WebstoreType;
  mv: string;
  variant: VariantType;
  target: string;
  'webstore-variant': string;
}

/**
 * @description Chrome manifest 中使用的消息格式
 */
export interface ChromeMessage {
  message: string;
  description?: string;
  placeholders?: Record<string, { content: string; example?: string }>;
}

/**
 * @description 语言包消息类型
 */
export type LocaleMessages = Record<string, string>;
