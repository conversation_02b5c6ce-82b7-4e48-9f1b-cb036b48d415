# manifest 模块 PRD 文档

## 一、背景与目标

浏览器扩展 manifest 文件（manifest.json）是扩展的核心描述文件，不同平台、不同 manifest 版本、不同变体对字段和权限有差异。手动维护 manifest 易出错，难以适配多平台和多变体需求。

本模块旨在自动化生成、校验、合并和管理多平台、多变体的 manifest 文件，提升扩展开发效率和安全性。

## 二、设计原则

1. **平台适配**：支持 Chrome/Edge/Firefox/Opera 等主流平台 manifest 差异。
2. **自动化合并**：支持多层级（全局/变体/平台）manifest 合并与字段补全。
3. **权限安全**：内置权限校验与变更检测，防止高危权限误用。
4. **类型安全**：所有 manifest 字段均有类型约束，支持 IDE 智能提示。
5. **可扩展性**：便于新增平台、权限、字段等。

## 三、核心功能

1. **manifest 生成与合并**：根据扩展配置自动生成各平台/变体的 manifest 文件，支持多层级合并与字段补全。
2. **manifest 校验**：校验 manifest 字段合法性，输出详细错误与警告。
3. **权限管理与变更检测**：检测高危权限、不可选权限，支持权限变更对比与警告输出。
4. **工具与类型支持**：丰富的工具函数与类型定义，便于二次开发和集成。

## 四、目录结构与职责

| 文件/目录      | 说明                         |
|---------------|------------------------------|
| constants.ts  | manifest 相关常量             |
| index.ts      | 模块主入口，统一导出         |
| permissions.ts| 权限相关处理与常量           |
| processor.ts  | manifest 生成与合并核心逻辑   |
| types.ts      | 类型定义与约束               |
| utils.ts      | manifest 工具函数             |

## 五、核心流程说明

1. **manifest 生成流程**
   - 通过 `processManifestData(options)` 合并 baseManifest 与 overrideManifest
   - 自动补全 version、manifest_version、default_locale 等必需字段
   - 产出标准化 manifest 对象

2. **manifest 校验流程**
   - 通过工具函数校验字段合法性，输出错误与警告
   - 检查必需字段、类型、平台兼容性等

3. **权限管理与变更检测流程**
   - `checkManifestPermissions` 检查 manifest 权限，输出高危/不可选权限警告
   - `compareManifestPermissions` 对比新旧 manifest 权限变更，输出变更明细与警告

## 六、主要接口说明

### processManifestData(options: ManifestProcessOptions): ProcessedManifestData
> 合并并生成标准化 manifest 对象，自动补全必需字段。

### cleanManifest(manifest: Partial<BrowserManifest>): Partial<BrowserManifest>
> 递归清理 manifest 对象中的空值字段。

### checkManifestPermissions(manifestJson, extensionName?, variantTarget?): { hasWarnings, warnings }
> 检查 manifest 权限，输出高危/不可选权限警告。

### compareManifestPermissions(newManifestJson, oldManifestJson, extensionName?, variantTarget?): { hasChanges, addedPermissions, removedPermissions, warnings }
> 对比新旧 manifest 权限变更，输出变更明细与警告。

## 七、使用示例

```ts
import { processManifestData, checkManifestPermissions } from 'scripts/manifest';

const manifest = processManifestData({
  extensionName: 'my-extension',
  version: '1.0.0',
  manifestVersion: 3,
  defaultLocale: 'en',
  variantInfo: { ... },
  baseManifest: { ... },
  overrideManifest: { ... },
});

const { hasWarnings, warnings } = checkManifestPermissions(manifest, 'my-extension', 'chrome-mv3-release');
if (hasWarnings) {
  warnings.forEach(w => console.warn(w));
}
```

## 八、常见问题 FAQ

**Q: 如何支持新的 manifest 字段或平台？**
A: 在 types.ts 和 constants.ts 中补充类型与常量，processor.ts 处理合并逻辑。

**Q: 权限变更如何检测？**
A: 使用 compareManifestPermissions 对比新旧 manifest 权限，自动输出变更明细。

**Q: manifest 校验报错如何排查？**
A: 校验函数会详细输出字段与原因，建议先检查类型与必填项。

## 九、维护与扩展约定

1. 类型定义集中于 types.ts，变更需同步文档。
2. 新增 manifest 字段需补充类型、常量与合并逻辑。
3. 权限相关变更需同步 permissions.ts 与下游依赖。
4. 目录结构如有调整需同步通知相关团队。

---
如有更多问题或建议，请联系维护人或提交 issue。
