/**
 * @fileoverview 插件 manifest 配置类型定义
 * @description 定义插件 manifest 处理相关的类型接口。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import type { WebstoreType, ManifestVersionType, BrowserManifest } from '../helpers/types.js';

// #region --- 基础类型 ---

/**
 * manifest 处理选项
 * @property extensionName 插件名字
 * @property version 插件版本号
 * @property manifestVersion manifest 版本号
 * @property defaultLocale 默认语言（语言包 key）
 * @property variantInfo 渠道包相关信息
 * @property baseManifest 基础 manifest 配置
 * @property overrideManifest 覆盖 manifest 配置
 */
export interface ManifestProcessOptions {
  /**
   * 扩展名称
   */
  extensionName: string;

  /**
   * 版本号
   */
  version: string;

  /**
   * manifest 版本
   */
  manifestVersion: ManifestVersionType;

  /**
   * 默认语言
   */
  defaultLocale: string;

  /**
   * variant 信息
   */
  variantInfo: {
    variantId: string;
    variantName: string;
    variantChannel: string;
    webstore: WebstoreType;
    variantTarget: string;
  };

  /**
   * 基础 manifest 配置
   */
  baseManifest?: Partial<BrowserManifest>;

  /**
   * 覆盖 manifest 配置
   */
  overrideManifest?: Partial<BrowserManifest>;

}

export type ProcessedManifestData = BrowserManifest;

// #endregion
