/**
 * @fileoverview 插件 manifest 配置模块统一导出
 * @description 提供插件 manifest 相关的统一 API 接口，包括类型、常量、权限、工具函数、核心处理器等。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

// 类型定义
export type { ManifestProcessOptions, ProcessedManifestData } from './types.js';

// 常量定义
export {
  SUPPORT_MANIFEST_VERSIONS,
  SUPPORT_MV,
  SUPPORT_MVS,
  WEBSTORES_UPDATE_URL,
  EXTENSION_BASE_URL,
  COMMON_PERMISSIONS,
  BASIC_DEFAULT_PERMISSIONS,
  COMMON_HOST_PERMISSIONS,
} from './constants.js';

// 权限处理
export * from './permissions.js';

// 工具函数
export { 
  cleanManifest, 
  checkManifestPermissions, 
  compareManifestPermissions,
} from './utils.js';

// 核心处理器
export { processManifestData } from './processor.js';
