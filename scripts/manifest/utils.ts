/**
 * @fileoverview 插件 manifest 配置专用工具函数
 * @description 提供插件 manifest 处理相关的工具函数，包括清理、权限检查等。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 */

import { createLogger } from '../helpers/index.js';
import { BrowserManifest } from '../helpers/types.js';
import { PERMISSIONS_WILL_CAUSE_WARNING_DISPLAYED, PERMISSIONS_CAN_NOT_BE_OPTIONAL } from './permissions.js';

const logger = createLogger('ManifestUtils');

/**
 * 清理 manifest 对象中的空值字段
 * @param manifest - 要清理的 manifest 对象
 * @returns 清理后的 manifest 对象
 */
export function cleanManifest(manifest: Partial<BrowserManifest>): Partial<BrowserManifest> {
  const cleaned: Partial<BrowserManifest> = {};

  for (const [key, value] of Object.entries(manifest)) {
    if (value !== null && value !== undefined && value !== '') {
      // 对于数组，只保留非空数组
      if (Array.isArray(value)) {
        if (value.length > 0) {
          cleaned[key as keyof BrowserManifest] = value;
        }
      } else if (typeof value === 'object') {
        // 对于对象，递归清理
        const cleanedObj = cleanManifest(value as Partial<BrowserManifest>);
        if (Object.keys(cleanedObj).length > 0) {
          cleaned[key as keyof BrowserManifest] = cleanedObj;
        }
      } else {
        cleaned[key as keyof BrowserManifest] = value;
      }
    }
  }

  return cleaned;
}

/**
 * 检查单个 manifest 的权限
 */
export function checkManifestPermissions(manifestJson: any, extensionName?: string, variantTarget?: string): {
  hasWarnings: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  const permissions: string[] = Array.isArray(manifestJson.permissions) ? manifestJson.permissions : [];

  // 检查是否有会显示警告的权限
  const warningPermissions = permissions.filter((p: string) => 
    PERMISSIONS_WILL_CAUSE_WARNING_DISPLAYED.includes(p)
  );
  if (warningPermissions.length > 0) {
    warnings.push(`以下权限将显示安装警告: ${warningPermissions.join(', ')}`);
  }

  // 检查是否有不能设为可选的权限
  const nonOptionalPermissions = permissions.filter((p: string) => 
    PERMISSIONS_CAN_NOT_BE_OPTIONAL.includes(p)
  );
  if (nonOptionalPermissions.length > 0) {
    warnings.push(`以下权限不能设为可选权限: ${nonOptionalPermissions.join(', ')}`);
  }

  if (warnings.length > 0 && extensionName && variantTarget) {
    logger.info(`权限检查 ${extensionName}:${variantTarget}:`);
    for (const warning of warnings) {
      logger.warn(`  ⚠️  ${warning}`);
    }
  }

  return {
    hasWarnings: warnings.length > 0,
    warnings,
  };
}

/**
 * 对比两个 manifest 的权限变更
 */
export function compareManifestPermissions(newManifestJson: any, oldManifestJson: any, extensionName?: string, variantTarget?: string): {
  hasChanges: boolean;
  hasWarnings: boolean;
  addedPermissions: string[];
  removedPermissions: string[];
  warnings: string[];
} {
  const newPermissions: string[] = Array.isArray(newManifestJson.permissions) ? newManifestJson.permissions : [];
  const oldPermissions: string[] = Array.isArray(oldManifestJson.permissions) ? oldManifestJson.permissions : [];
  const addedPermissions: string[] = newPermissions.filter(p => !oldPermissions.includes(p));
  const removedPermissions: string[] = oldPermissions.filter(p => !newPermissions.includes(p));
  const warnings: string[] = [];
  const hasChanges = addedPermissions.length > 0 || removedPermissions.length > 0;

  if (addedPermissions.length > 0) {
    // 检查新增权限中是否有会显示警告的权限
    const warningPermissions = addedPermissions.filter(p => 
      PERMISSIONS_WILL_CAUSE_WARNING_DISPLAYED.includes(p)
    );
    if (warningPermissions.length > 0) {
      warnings.push(`新增权限中以下将显示安装警告: ${warningPermissions.join(', ')}`);
    }

    // 检查新增权限中是否有不能设为可选的权限
    const nonOptionalPermissions = addedPermissions.filter(p => 
      PERMISSIONS_CAN_NOT_BE_OPTIONAL.includes(p)
    );
    if (nonOptionalPermissions.length > 0) {
      warnings.push(`新增权限中以下不能设为可选权限: ${nonOptionalPermissions.join(', ')}`);
    }
  }

  if (hasChanges && extensionName && variantTarget) {
    logger.info(`权限变更检测 ${extensionName}:${variantTarget}:`);
    if (addedPermissions.length > 0) {
      logger.info(`  新增权限: ${addedPermissions.join(', ')}`);
    }
    if (removedPermissions.length > 0) {
      logger.info(`  移除权限: ${removedPermissions.join(', ')}`);
    }
    if (warnings.length > 0) {
      for (const warning of warnings) {
        logger.warn(`  ⚠️  ${warning}`);
      }
    }
  }

  return {
    hasChanges,
    hasWarnings: warnings.length > 0,
    addedPermissions,
    removedPermissions,
    warnings,
  };
}


