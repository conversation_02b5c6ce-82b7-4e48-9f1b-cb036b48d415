/**
 * Git 操作封装
 * 
 * 封装所有 Git 相关操作，包括获取提交、标签、分支信息等
 */

import { execa } from 'execa';
import { existsSync } from 'node:fs';
import { join } from 'node:path';
import { Logger } from '../helpers/logger';
import { GIT_COMMANDS, ERROR_MESSAGES } from './constants';
import type { GitCommit, GitTag } from './types';

const logger = Logger.create('Git');

/**
 * 检查是否为 Git 仓库
 */
export async function isGitRepo(cwd: string = process.cwd()): Promise<boolean> {
  try {
    const gitDir = join(cwd, '.git');
    if (!existsSync(gitDir)) {
      return false;
    }
    
    await execa('git', ['rev-parse', '--git-dir'], { cwd });
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取当前分支名称
 */
export async function getCurrentBranch(cwd: string = process.cwd()): Promise<string> {
  try {
    const { stdout } = await execa('git', ['rev-parse', '--abbrev-ref', 'HEAD'], { cwd });
    return stdout.trim();
  } catch (error) {
    logger.error('获取当前分支失败', error);
    throw new Error(ERROR_MESSAGES.gitCommandFailed);
  }
}

/**
 * 检查是否有未提交的更改
 */
export async function hasUncommittedChanges(cwd: string = process.cwd()): Promise<boolean> {
  try {
    const { stdout } = await execa('git', ['status', '--porcelain'], { cwd });
    return stdout.trim().length > 0;
  } catch (error) {
    logger.error('检查未提交更改失败', error);
    throw new Error(ERROR_MESSAGES.gitCommandFailed);
  }
}

/**
 * 获取所有标签
 */
export async function getAllTags(cwd: string = process.cwd()): Promise<GitTag[]> {
  try {
    const { stdout } = await execa('git', ['tag', '-l', '--sort=-version:refname'], { cwd });
    
    if (!stdout.trim()) {
      return [];
    }
    
    const tagNames = stdout.trim().split('\n');
    const tags: GitTag[] = [];
    
    for (const name of tagNames) {
      try {
        // 获取标签详细信息
        const { stdout: info } = await execa('git', ['show', '-s', '--format=%H|%at|%s', name], { cwd });
        const [commit, timestamp, message] = info.trim().split('|');
        
        tags.push({
          name,
          commit,
          date: new Date(parseInt(timestamp) * 1000),
          message: message || '',
        });
      } catch (error) {
        logger.warn(`获取标签 ${name} 详情失败`, error);
      }
    }
    
    return tags;
  } catch (error) {
    logger.error('获取标签列表失败', error);
    return [];
  }
}

/**
 * 获取最新标签
 */
export async function getLatestTag(cwd: string = process.cwd()): Promise<GitTag | null> {
  try {
    const { stdout } = await execa('git', ['describe', '--tags', '--abbrev=0'], { cwd });
    const tagName = stdout.trim();
    
    if (!tagName) {
      return null;
    }
    
    // 获取标签详细信息
    const { stdout: info } = await execa('git', ['show', '-s', '--format=%H|%at|%s', tagName], { cwd });
    const [commit, timestamp, message] = info.trim().split('|');
    
    return {
      name: tagName,
      commit,
      date: new Date(parseInt(timestamp) * 1000),
      message: message || '',
    };
  } catch {
    logger.verbose('没有找到标签');
    return null;
  }
}

/**
 * 获取两个引用之间的提交列表
 */
export async function getCommitsBetween(
  from: string | null,
  to: string = 'HEAD',
  cwd: string = process.cwd()
): Promise<GitCommit[]> {
  try {
    // 构建范围参数
    const range = from ? `${from}..${to}` : to;
    
    // 使用自定义格式获取提交信息
    const { stdout } = await execa(
      'git',
      ['log', '--format=%H|%h|%an|%ae|%at|%B%x00', range],
      { cwd, maxBuffer: 10 * 1024 * 1024 }
    );
    
    if (!stdout.trim()) {
      return [];
    }
    
    // 按 null 字符分割每个提交
    const commitStrings = stdout.trim().split('\x00').filter(Boolean);
    const commits: GitCommit[] = [];
    
    for (const commitString of commitStrings) {
      const lines = commitString.trim().split('\n');
      const [hash, shortHash, author, email, timestamp] = lines[0].split('|');
      const message = lines.slice(1).join('\n').trim();
      
      commits.push({
        hash,
        shortHash,
        author,
        email,
        date: new Date(parseInt(timestamp) * 1000),
        message,
      });
    }
    
    return commits;
  } catch (error) {
    logger.error(`获取提交列表失败 (${from || 'start'}..${to})`, error);
    throw new Error(ERROR_MESSAGES.gitCommandFailed);
  }
}

/**
 * 获取从基准分支分叉后的所有提交
 */
export async function getCommitsSinceBranch(
  baseBranch: string,
  cwd: string = process.cwd()
): Promise<GitCommit[]> {
  try {
    // 获取分叉点
    const { stdout: mergeBase } = await execa(
      'git',
      ['merge-base', baseBranch, 'HEAD'],
      { cwd }
    );
    
    const forkPoint = mergeBase.trim();
    if (!forkPoint) {
      return [];
    }
    
    // 获取从分叉点到 HEAD 的提交
    return await getCommitsBetween(forkPoint, 'HEAD', cwd);
  } catch (error) {
    logger.error(`获取分支提交失败 (base: ${baseBranch})`, error);
    throw new Error(ERROR_MESSAGES.gitCommandFailed);
  }
}

/**
 * 获取指定数量的最近提交
 */
export async function getRecentCommits(
  limit: number = 10,
  cwd: string = process.cwd()
): Promise<GitCommit[]> {
  try {
    const { stdout } = await execa(
      'git',
      ['log', '--format=%H|%h|%an|%ae|%at|%B%x00', `-${limit}`],
      { cwd }
    );
    
    if (!stdout.trim()) {
      return [];
    }
    
    const commitStrings = stdout.trim().split('\x00').filter(Boolean);
    const commits: GitCommit[] = [];
    
    for (const commitString of commitStrings) {
      const lines = commitString.trim().split('\n');
      const [hash, shortHash, author, email, timestamp] = lines[0].split('|');
      const message = lines.slice(1).join('\n').trim();
      
      commits.push({
        hash,
        shortHash,
        author,
        email,
        date: new Date(parseInt(timestamp) * 1000),
        message,
      });
    }
    
    return commits;
  } catch (error) {
    logger.error('获取最近提交失败', error);
    throw new Error(ERROR_MESSAGES.gitCommandFailed);
  }
}

/**
 * 获取仓库根目录
 */
export async function getRepoRoot(cwd: string = process.cwd()): Promise<string> {
  try {
    const { stdout } = await execa('git', ['rev-parse', '--show-toplevel'], { cwd });
    return stdout.trim();
  } catch (error) {
    logger.error('获取仓库根目录失败', error);
    throw new Error(ERROR_MESSAGES.notGitRepo);
  }
}

/**
 * 获取远程仓库信息
 */
export async function getRemoteUrl(
  remote: string = 'origin',
  cwd: string = process.cwd()
): Promise<string | null> {
  try {
    const { stdout } = await execa('git', ['remote', 'get-url', remote], { cwd });
    return stdout.trim() || null;
  } catch {
    return null;
  }
}

/**
 * 检查引用是否存在
 */
export async function isValidRef(ref: string, cwd: string = process.cwd()): Promise<boolean> {
  try {
    await execa('git', ['rev-parse', '--verify', ref], { cwd });
    return true;
  } catch {
    return false;
  }
}