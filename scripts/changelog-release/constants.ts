/**
 * Changelog Release 模块常量定义
 * 
 * 定义常量，如 Commit 类型映射、默认配置、文件路径模板等
 */

import type { VersionBump } from './types';

/**
 * Conventional Commit 类型定义
 */
export const COMMIT_TYPES = {
  feat: { description: '新功能', changelog: true, bump: 'minor' as VersionBump },
  fix: { description: '修复', changelog: true, bump: 'patch' as VersionBump },
  docs: { description: '文档', changelog: false, bump: 'none' as VersionBump },
  style: { description: '格式', changelog: false, bump: 'none' as VersionBump },
  refactor: { description: '重构', changelog: true, bump: 'patch' as VersionBump },
  perf: { description: '性能', changelog: true, bump: 'patch' as VersionBump },
  test: { description: '测试', changelog: false, bump: 'none' as VersionBump },
  build: { description: '构建', changelog: false, bump: 'none' as VersionBump },
  ci: { description: 'CI', changelog: false, bump: 'none' as VersionBump },
  chore: { description: '杂项', changelog: false, bump: 'none' as VersionBump },
  revert: { description: '回滚', changelog: true, bump: 'patch' as VersionBump },
} as const;

/**
 * 提交类型列表
 */
export const VALID_COMMIT_TYPES = Object.keys(COMMIT_TYPES);

/**
 * 需要包含在 Changelog 中的提交类型
 */
export const CHANGELOG_TYPES = Object.entries(COMMIT_TYPES)
  .filter(([, config]) => config.changelog)
  .map(([type]) => type);

/**
 * 破坏性变更标识
 */
export const BREAKING_CHANGE_KEYWORDS = [
  'BREAKING CHANGE',
  'BREAKING-CHANGE',
  'BREAKING CHANGES',
  'BREAKING-CHANGES',
];

/**
 * 自定义 Footer 键名
 */
export const CUSTOM_FOOTERS = {
  APPLIES_TO: 'Applies-To',
  ISSUE_ID: 'Issue-ID',
} as const;

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  /** 默认基准分支 */
  baseBranch: 'main',
  /** 默认文件编码 */
  encoding: 'utf-8' as BufferEncoding,
  /** JSON 缩进空格数 */
  jsonIndent: 2,
  /** 默认时区 */
  timezone: 'Asia/Shanghai',
} as const;

/**
 * 文件路径模板
 */
export const FILE_PATHS = {
  /** 扩展配置文件路径模板 */
  extensionConfig: 'packages/extensions/extension_dashboard/extensions/{name}.json',
  /** 发布配置文件名 */
  releaseConfig: 'release-extensions.json',
  /** 发布报告路径模板 */
  releaseReport: '.output/{extensionName}/{version}/RELEASE.md',
} as const;

/**
 * Git 命令
 */
export const GIT_COMMANDS = {
  /** 获取当前分支 */
  currentBranch: 'git rev-parse --abbrev-ref HEAD',
  /** 获取所有标签 */
  allTags: 'git tag -l --sort=-version:refname',
  /** 获取最新标签 */
  latestTag: 'git describe --tags --abbrev=0',
  /** 获取标签信息 */
  tagInfo: 'git show -s --format=%H|%at|%s {tag}',
  /** 获取提交列表 */
  commits: 'git log --format=%H|%h|%an|%ae|%at|%B%x00 {range}',
  /** 检查是否有未提交的更改 */
  hasChanges: 'git status --porcelain',
  /** 获取仓库根目录 */
  repoRoot: 'git rev-parse --show-toplevel',
} as const;

/**
 * Changelog 模板
 */
export const CHANGELOG_TEMPLATES = {
  /** 版本标题模板 */
  versionTitle: '## [{version}] - {date}',
  /** 类型分组标题模板 */
  typeTitle: '### {emoji} {title}',
  /** 变更项模板 */
  changeItem: '- {description} ({commits}){issues}',
  /** 破坏性变更标题 */
  breakingTitle: '### ⚠️ BREAKING CHANGES',
  /** 破坏性变更项模板 */
  breakingItem: '- {description}',
} as const;

/**
 * 类型图标映射
 */
export const TYPE_EMOJIS = {
  feat: '✨',
  fix: '🐛',
  docs: '📝',
  style: '💄',
  refactor: '♻️',
  perf: '⚡',
  test: '✅',
  build: '📦',
  ci: '👷',
  chore: '🔧',
  revert: '⏪',
  breaking: '⚠️',
} as const;

/**
 * 正则表达式
 */
export const REGEX_PATTERNS = {
  /** Issue ID 格式 - 匹配完整的 issue id，包括可选的子需求 ID */
  issueId: /(\d+)?#\d{8}-\d{2}/g,
  /** 版本号格式 */
  version: /^\d+\.\d+\.\d+$/,
  /** 标签名称格式 */
  tagName: /^v?\d+\.\d+\.\d+/,
  /** Commit 哈希格式 */
  commitHash: /^[a-f0-9]{40}$/i,
  /** 短哈希格式 */
  shortHash: /^[a-f0-9]{7}$/i,
} as const;

/**
 * 错误消息
 */
export const ERROR_MESSAGES = {
  notGitRepo: '当前目录不是 Git 仓库',
  noCommits: '没有找到需要处理的提交',
  invalidVersion: '无效的版本号格式',
  fileNotFound: '文件不存在',
  parseError: '解析失败',
  writeError: '写入文件失败',
  gitCommandFailed: 'Git 命令执行失败',
} as const;

/**
 * 日志级别配置
 */
export const LOG_LEVELS = {
  silent: 0,
  error: 1,
  warn: 2,
  info: 3,
  debug: 4,
  trace: 5,
} as const;

