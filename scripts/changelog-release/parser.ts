/**
 * Commit 解析和版本计算
 * 
 * 负责解析 Conventional Commits、提取元数据、计算语义化版本号
 */

import {
  CommitParser
} from 'conventional-commits-parser';
import { valid, inc } from 'semver';
import { Logger } from '../helpers/logger';
import {
  COMMIT_TYPES,
  BREAKING_CHANGE_KEYWORDS,
  CUSTOM_FOOTERS,
  REGEX_PATTERNS
} from './constants';
import type {
  GitCommit,
  ParsedCommit,
  VersionBump
} from './types';

const logger = Logger.create('Parser');

/**
 * 解析单个 Git 提交
 * 
 * @description
 * 将原始的 Git 提交信息解析为结构化的 ParsedCommit 对象。
 * 使用 Conventional Commits 规范解析提交信息，并提取额外的元数据。
 * 
 * 提交格式示例：
 * ```
 * feat(core): add new feature
 * 
 * This is the body explaining the change
 * 
 * Applies-To: extension1, extension2
 * Issue-ID: #20240101-01
 * BREAKING CHANGE: This breaks the API
 * ```
 * 
 * @param commit - 原始的 Git 提交信息
 * @returns 解析后的提交对象，如果提交格式无效则返回 null
 */
export function parseGitCommit(commit: GitCommit): ParsedCommit | null {
  try {
    // 使用 conventional-commits-parser 解析
    const parser = new CommitParser({
      headerPattern: /^(\w+)(?:\(([^)]+)\))?: (.+)$/,
      headerCorrespondence: ['type', 'scope', 'subject'],
      noteKeywords: BREAKING_CHANGE_KEYWORDS,
    });
    
    const parsed = parser.parse(commit.message);
    
    if (!parsed) {
      return null;
    }
    
    // 验证提交类型
    const type = parsed.type?.toLowerCase();
    if (!type || !COMMIT_TYPES[type as keyof typeof COMMIT_TYPES]) {
      logger.verbose(`跳过无效提交类型: ${type} (${commit.shortHash})`);
      return null;
    }
    
    // 提取自定义元数据
    const footers = extractFooters(commit.message);
    const appliesTo = extractAppliesTo(footers);
    const issueIds = extractIssueIds(footers);
    
    // 检查是否有破坏性变更
    const breaking = hasBreakingChange(parsed, footers);
    const breakingNotes = extractBreakingNotes(parsed, footers);
    
    return {
      ...commit,
      type,
      scope: parsed.scope || undefined,
      subject: parsed.subject || '',
      body: parsed.body || undefined,
      breaking,
      breakingNotes: breakingNotes.length > 0 ? breakingNotes : undefined,
      appliesTo,
      issueIds,
      footers,
    };
  } catch (error) {
    logger.warn(`解析提交失败: ${commit.shortHash}`, error);
    return null;
  }
}

/**
 * 批量解析提交
 */
export function parseCommits(commits: GitCommit[]): ParsedCommit[] {
  const parsed: ParsedCommit[] = [];
  
  for (const commit of commits) {
    const parsedCommit = parseGitCommit(commit);
    if (parsedCommit) {
      parsed.push(parsedCommit);
    }
  }
  
  logger.info(`解析了 ${parsed.length}/${commits.length} 个有效提交`);
  return parsed;
}

/**
 * 提取 Footer 信息
 * 
 * @description
 * 从提交信息中提取 footer 部分的键值对。
 * Footer 是提交信息最后的元数据部分，格式为 "Key: Value" 或 "Key #comment"。
 * 
 * @example
 * ```
 * feat: add new feature
 * 
 * This is the body of the commit
 * 
 * Applies-To: extension1, extension2
 * Issue-ID: #20240101-01
 * BREAKING CHANGE: This breaks the API
 * ```
 * 
 * @param message - 完整的提交信息
 * @returns 提取的 footer 键值对
 */
function extractFooters(message: string): Record<string, string> {
  const footers: Record<string, string> = {};
  const lines = message.split('\n');
  
  // 从后往前查找 footer 部分
  // Footer 部分位于提交信息的最后，由空行与正文分隔
  let inFooter = false;
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i].trim();
    
    // 空行表示 footer 部分结束（因为是从后往前遍历）
    if (!line && inFooter) {
      break;
    }
    
    // 匹配 footer 格式: 
    // 1. "Key: Value" - 标准格式
    // 2. "Key #comment" - 破坏性变更格式
    const footerMatch = line.match(/^([\w-]+):\s*(.+)$/) || line.match(/^([\w-]+)\s+#(.+)$/);
    if (footerMatch) {
      inFooter = true;
      const [, key, value] = footerMatch;
      footers[key] = value.trim();
    }
  }
  
  return footers;
}

/**
 * 提取 Applies-To 信息
 */
function extractAppliesTo(footers: Record<string, string>): string[] {
  const appliesTo = footers[CUSTOM_FOOTERS.APPLIES_TO];
  if (!appliesTo) {
    return [];
  }
  
  return appliesTo
    .split(',')
    .map(s => s.trim())
    .filter(Boolean);
}

/**
 * 提取 Issue-ID 信息
 */
function extractIssueIds(footers: Record<string, string>): string[] {
  const issueId = footers[CUSTOM_FOOTERS.ISSUE_ID];
  if (!issueId) {
    return [];
  }
  
  // 使用正则表达式提取所有 issue ids
  const ids: string[] = [];
  const matches = issueId.matchAll(REGEX_PATTERNS.issueId);
  
  for (const match of matches) {
    ids.push(match[0]);
  }
  
  return ids;
}

/**
 * 检查是否有破坏性变更
 */
function hasBreakingChange(parsed: any, footers: Record<string, string>): boolean {
  // 检查提交信息中的 BREAKING CHANGE
  if (parsed.notes && parsed.notes.length > 0) {
    return parsed.notes.some((note: any) => 
      BREAKING_CHANGE_KEYWORDS.includes(note.title.toUpperCase())
    );
  }
  
  // 检查 footer 中的 BREAKING CHANGE
  return Object.keys(footers).some(key => 
    BREAKING_CHANGE_KEYWORDS.includes(key.toUpperCase().replace(/[-\s]/g, ' '))
  );
}

/**
 * 提取破坏性变更说明
 */
function extractBreakingNotes(parsed: any, footers: Record<string, string>): string[] {
  const notes: string[] = [];
  
  // 从 notes 中提取
  if (parsed.notes && parsed.notes.length > 0) {
    for (const note of parsed.notes) {
      if (BREAKING_CHANGE_KEYWORDS.includes(note.title.toUpperCase())) {
        notes.push(note.text);
      }
    }
  }
  
  // 从 footer 中提取
  for (const [key, value] of Object.entries(footers)) {
    if (BREAKING_CHANGE_KEYWORDS.includes(key.toUpperCase().replace(/[-\s]/g, ' '))) {
      notes.push(value);
    }
  }
  
  return notes;
}

/**
 * 计算版本变更类型
 * 
 * @description
 * 根据提交列表计算应该进行的版本号变更类型。
 * 遵循语义化版本规范 (Semantic Versioning)：
 * - major: 破坏性变更，不兼容的 API 修改
 * - minor: 新功能，向后兼容的功能性新增
 * - patch: 修复问题，向后兼容的问题修正
 * - none: 无需变更版本
 * 
 * 优先级规则（从高到低）：
 * 1. 如果有任何破坏性变更 → major
 * 2. 如果有 feat 类型提交 → minor
 * 3. 如果有 fix/perf/refactor/revert 类型提交 → patch
 * 4. 其他情况 → none
 * 
 * @param commits - 已解析的提交列表
 * @returns 版本变更类型
 */
export function calculateVersionBump(commits: ParsedCommit[]): VersionBump {
  // 如果有破坏性变更，则为 major
  if (commits.some(c => c.breaking)) {
    return 'major';
  }
  
  // 根据提交类型判断
  let bump: VersionBump = 'none';
  
  for (const commit of commits) {
    const commitType = COMMIT_TYPES[commit.type as keyof typeof COMMIT_TYPES];
    if (!commitType) continue;
    
    const commitBump = commitType.bump;
    
    // 更新到更高级别的变更
    if (commitBump === 'major' || bump === 'major') {
      bump = 'major';
    } else if (commitBump === 'minor' || bump === 'minor') {
      bump = 'minor';
    } else if (commitBump === 'patch' && bump === 'none') {
      bump = 'patch';
    }
  }
  
  return bump;
}

/**
 * 计算下一个版本号
 */
export function calculateNextVersion(currentVersion: string, bump: VersionBump): string | null {
  if (bump === 'none') {
    return currentVersion;
  }
  
  if (!valid(currentVersion)) {
    logger.error(`无效的版本号: ${currentVersion}`);
    return null;
  }
  
  const nextVersion = inc(currentVersion, bump);
  if (!nextVersion) {
    logger.error(`计算新版本失败: ${currentVersion} + ${bump}`);
    return null;
  }
  
  return nextVersion;
}

/**
 * 将提交按扩展分组
 */
export function groupCommitsByExtension(
  commits: ParsedCommit[], 
  extensionNames: string[]
): Record<string, ParsedCommit[]> {
  const groups: Record<string, ParsedCommit[]> = {};
  
  // 初始化所有扩展的空数组
  for (const name of extensionNames) {
    groups[name] = [];
  }
  
  // 分配提交到对应的扩展
  for (const commit of commits) {
    if (commit.appliesTo.length === 0) {
      logger.verbose(`提交 ${commit.shortHash} 未指定 Applies-To`);
      continue;
    }
    
    for (const extension of commit.appliesTo) {
      if (extensionNames.includes(extension)) {
        groups[extension].push(commit);
      } else {
        logger.warn(`未知的扩展名: ${extension} (${commit.shortHash})`);
      }
    }
  }
  
  // 过滤掉没有提交的扩展
  const filteredGroups: Record<string, ParsedCommit[]> = {};
  for (const [extension, commits] of Object.entries(groups)) {
    if (commits.length > 0) {
      filteredGroups[extension] = commits;
      logger.info(`扩展 ${extension} 有 ${commits.length} 个相关提交`);
    }
  }
  
  return filteredGroups;
}

/**
 * 将提交按类型分组
 */
export function groupCommitsByType(commits: ParsedCommit[]): Record<string, ParsedCommit[]> {
  const groups: Record<string, ParsedCommit[]> = {};
  
  for (const commit of commits) {
    if (!groups[commit.type]) {
      groups[commit.type] = [];
    }
    groups[commit.type].push(commit);
  }
  
  return groups;
}

/**
 * 过滤需要包含在 Changelog 中的提交
 */
export function filterChangelogCommits(commits: ParsedCommit[]): ParsedCommit[] {
  return commits.filter(commit => {
    const commitType = COMMIT_TYPES[commit.type as keyof typeof COMMIT_TYPES];
    return commitType?.changelog === true;
  });
}
