/**
 * Changelog Release 模块类型定义
 * 
 * 定义所有核心数据结构，被模块内部和外部工具共同使用
 */

import type { SemVer } from 'semver';

/**
 * Git 提交信息
 */
export interface GitCommit {
  /** 提交哈希 */
  hash: string;
  /** 简短哈希 (前7位) */
  shortHash: string;
  /** 作者名称 */
  author: string;
  /** 作者邮箱 */
  email: string;
  /** 提交日期 */
  date: Date;
  /** 原始提交信息 */
  message: string;
}

/**
 * 解析后的 Conventional Commit
 */
export interface ParsedCommit extends GitCommit {
  /** 提交类型 (feat, fix, docs, etc.) */
  type: string;
  /** 作用域 */
  scope?: string;
  /** 主题描述 */
  subject: string;
  /** 正文内容 */
  body?: string;
  /** 是否包含破坏性变更 */
  breaking: boolean;
  /** 破坏性变更说明 */
  breakingNotes?: string[];
  /** 适用的插件列表 */
  appliesTo: string[];
  /** 关联的 Issue ID 列表 */
  issueIds: string[];
  /** 原始的 footer 内容 */
  footers: Record<string, string>;
}

/**
 * 版本变更类型
 */
export type VersionBump = 'major' | 'minor' | 'patch' | 'none';

/**
 * 插件配置文件结构
 */
export interface ExtensionConfig {
  /** 插件名称 */
  name: string;
  /** 当前版本 */
  version: string;
  /** 插件描述 */
  description?: string;
  /** 更新日志 */
  changelog?: ChangelogEntry[];
  /** 下一版本预览信息 */
  next?: {
    /** 预计版本号 */
    version: string;
    /** 预计更新日志 */
    changelog: ChangelogEntry[];
  };
  /** 其他配置项 */
  [key: string]: any;
}

/**
 * 更新日志条目
 */
export interface ChangelogEntry {
  /** 版本号 */
  version: string;
  /** 发布日期 */
  date: string;
  /** 变更列表 */
  changes: ChangeItem[];
}

/**
 * 变更项
 */
export interface ChangeItem {
  /** 变更类型 */
  type: string;
  /** 作用域 */
  scope?: string;
  /** 描述 */
  description: string;
  /** 是否为破坏性变更 */
  breaking?: boolean;
  /** 相关提交 */
  commits: string[];
  /** 关联的 Issue */
  issues?: string[];
}

/**
 * 发布信息
 */
export interface ReleaseInfo {
  /** 插件名称 */
  extensionName: string;
  /** 当前版本 */
  currentVersion: string;
  /** 新版本 */
  newVersion: string;
  /** 版本变更类型 */
  bump: VersionBump;
  /** 相关提交列表 */
  commits: ParsedCommit[];
  /** 更新日志 */
  changelog: ChangelogEntry;
}

/**
 * 发布扩展配置 (release-extensions.json)
 */
export interface ReleaseExtensionsConfig {
  /** 生成时间 */
  generatedAt: string;
  /** 发布的扩展列表 */
  extensions: ReleaseExtensionItem[];
}

/**
 * 发布扩展项
 */
export interface ReleaseExtensionItem {
  /** 扩展名称 */
  name: string;
  /** 版本号 */
  version: string;
  /** 是否需要构建 */
  build: boolean;
  /** 构建目标平台 */
  platforms?: string[];
}

/**
 * CLI 选项基类
 */
export interface BaseOptions {
  /** 基准分支，默认 'main' */
  baseBranch?: string;
  /** 详细日志输出 */
  verbose?: boolean;
  /** 指定处理的扩展列表 */
  extensions?: string[];
  /** 工作目录 */
  cwd?: string;
}

/**
 * Preview 命令选项
 */
export interface PreviewOptions extends BaseOptions {
  /** 是否跳过未关联的提交 */
  skipUnassigned?: boolean;
}

/**
 * Release 命令选项
 */
export interface ReleaseOptions extends BaseOptions {
  /** 模拟运行，不写入文件 */
  dryRun?: boolean;
  /** 是否创建发布报告 */
  createReport?: boolean;
}

/**
 * Status 命令选项
 */
export interface StatusOptions extends BaseOptions {
  /** 显示详细信息 */
  detailed?: boolean;
}

/**
 * Preview 执行结果
 */
export interface PreviewResult {
  /** 处理成功 */
  success: boolean;
  /** 更新的扩展列表 */
  updatedExtensions: string[];
  /** 发布信息映射 */
  releases: Record<string, ReleaseInfo>;
  /** 错误信息 */
  errors?: string[];
}

/**
 * Release 执行结果
 */
export interface ReleaseResult extends PreviewResult {
  /** 生成的文件列表 */
  generatedFiles: string[];
  /** 发布配置文件路径 */
  releaseConfigPath?: string;
  /** 发布报告路径映射 */
  reportPaths?: Record<string, string>;
}

/**
 * Status 执行结果
 */
export interface StatusResult {
  /** Git 仓库状态 */
  isGitRepo: boolean;
  /** 当前分支 */
  currentBranch?: string;
  /** 是否有未提交的更改 */
  hasUncommittedChanges?: boolean;
  /** 最新标签 */
  latestTag?: string;
  /** 待处理的提交数 */
  pendingCommits?: number;
  /** 受影响的扩展 */
  affectedExtensions?: string[];
}

/**
 * Git 标签信息
 */
export interface GitTag {
  /** 标签名称 */
  name: string;
  /** 提交哈希 */
  commit: string;
  /** 标签日期 */
  date: Date;
  /** 标签消息 */
  message?: string;
}

/**
 * 提交分组结果
 */
export interface CommitGroup {
  /** 扩展名称 */
  extension: string;
  /** 提交列表 */
  commits: ParsedCommit[];
  /** 版本变更类型 */
  bump: VersionBump;
}

/**
 * 文件操作选项
 */
export interface FileOperationOptions {
  /** 是否格式化输出 */
  format?: boolean;
  /** 文件编码 */
  encoding?: BufferEncoding;
}