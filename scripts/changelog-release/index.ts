/**
 * Changelog Release 核心流程编排
 * 
 * 整合各功能模块，实现 preview 和 release 的核心工作流
 */

import { Logger } from '../helpers/logger';
import { 
  isGitRepo, 
  getCurrentBranch, 
  hasUncommittedChanges,
  getLatestTag,
  getCommitsBetween,
  getCommitsSinceBranch,
  getAllTags
} from './git';
import { 
  parseCommits, 
  groupCommitsByExtension,
  calculateVersionBump,
  calculateNextVersion,
  filterChangelogCommits
} from './parser';
import { 
  readExtensionConfig,
  readExtensionConfigs,
  generateChangelogEntry,
  updateExtensionNext,
  updateExtensionRelease,
  generateReleaseConfig,
  generateReleaseReport
} from './generator';
import { 
  getRelativePath, 
  formatCommitList,
  shouldRelease,
  generateReleaseSummary,
  sortExtensionNames,
  filterValidExtensions
} from './utils';
import { DEFAULT_CONFIG, ERROR_MESSAGES } from './constants';
import type { 
  PreviewOptions, 
  ReleaseOptions, 
  StatusOptions,
  PreviewResult,
  ReleaseResult,
  StatusResult,
  ReleaseInfo,
  ParsedCommit,
  ExtensionConfig
} from './types';

const logger = Logger.create('ChangelogRelease');

/**
 * 生成预览
 */
export async function generatePreview(options: PreviewOptions = {}): Promise<PreviewResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    extensions: targetExtensions,
    skipUnassigned = false,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info('开始生成 Changelog 预览...');
  
  try {
    // 1. 检查 Git 仓库状态
    if (!await isGitRepo(cwd)) {
      throw new Error(ERROR_MESSAGES.notGitRepo);
    }
    
    const currentBranch = await getCurrentBranch(cwd);
    logger.info(`当前分支: ${currentBranch}`);
    
    if (await hasUncommittedChanges(cwd)) {
      logger.warn('检测到未提交的更改');
    }
    
    // 2. 获取提交范围
    const latestTag = await getLatestTag(cwd);
    let commits;
    
    if (latestTag) {
      logger.info(`最新标签: ${latestTag.name}`);
      commits = await getCommitsBetween(latestTag.commit, 'HEAD', cwd);
    } else {
      logger.info(`没有找到标签，从 ${baseBranch} 分支获取提交`);
      commits = await getCommitsSinceBranch(baseBranch, cwd);
    }
    
    if (commits.length === 0) {
      logger.warn(ERROR_MESSAGES.noCommits);
      return {
        success: true,
        updatedExtensions: [],
        releases: {}
      };
    }
    
    logger.info(`找到 ${commits.length} 个提交`);
    
    // 3. 解析提交
    const parsedCommits = parseCommits(commits);
    
    // 4. 获取扩展列表
    let extensionNames: string[];
    if (targetExtensions && targetExtensions.length > 0) {
      extensionNames = targetExtensions;
    } else {
      // 从解析的提交中提取所有涉及的扩展
      const mentionedExtensions = new Set<string>();
      for (const commit of parsedCommits) {
        for (const ext of commit.appliesTo) {
          mentionedExtensions.add(ext);
        }
      }
      extensionNames = Array.from(mentionedExtensions);
    }
    
    if (extensionNames.length === 0) {
      logger.warn('没有找到需要更新的扩展');
      return {
        success: true,
        updatedExtensions: [],
        releases: {}
      };
    }
    
    // 5. 读取扩展配置
    const configs = await readExtensionConfigs(extensionNames);
    extensionNames = filterValidExtensions(extensionNames, configs);
    
    // 6. 按扩展分组提交
    const commitGroups = groupCommitsByExtension(parsedCommits, extensionNames);
    
    // 7. 处理每个扩展
    const releases: Record<string, ReleaseInfo> = {};
    const updatedExtensions: string[] = [];
    const errors: string[] = [];
    
    for (const [extensionName, extensionCommits] of Object.entries(commitGroups)) {
      try {
        logger.group(`处理扩展: ${extensionName}`);
        
        // 过滤需要包含在 changelog 中的提交
        const changelogCommits = filterChangelogCommits(extensionCommits);
        
        if (!shouldRelease(changelogCommits)) {
          logger.info('没有需要发布的变更，跳过');
          continue;
        }
        
        const config = configs[extensionName];
        const currentVersion = config.version;
        
        // 计算版本号
        const bump = calculateVersionBump(changelogCommits);
        const newVersion = calculateNextVersion(currentVersion, bump);
        
        if (!newVersion) {
          throw new Error(`计算版本号失败: ${currentVersion} + ${bump}`);
        }
        
        logger.info(`版本变更: ${currentVersion} → ${newVersion} (${bump})`);
        
        // 生成 changelog
        const changelog = generateChangelogEntry(newVersion, changelogCommits);
        
        // 创建发布信息
        const releaseInfo: ReleaseInfo = {
          extensionName,
          currentVersion,
          newVersion,
          bump,
          commits: changelogCommits,
          changelog
        };
        
        // 更新 next 字段
        await updateExtensionNext(extensionName, releaseInfo);
        
        releases[extensionName] = releaseInfo;
        updatedExtensions.push(extensionName);
        
        logger.success(`更新成功: ${extensionName}`);
      } catch (error) {
        const errorMsg = `处理扩展 ${extensionName} 失败: ${error}`;
        logger.error(errorMsg);
        errors.push(errorMsg);
      } finally {
        logger.groupEnd();
      }
    }
    
    // 8. 输出摘要
    logger.group('预览摘要');
    logger.info(`更新了 ${updatedExtensions.length} 个扩展:`);
    for (const name of sortExtensionNames(updatedExtensions)) {
      const info = releases[name];
      logger.info(`  - ${generateReleaseSummary(name, info.currentVersion, info.newVersion, info.commits.length)}`);
    }
    logger.groupEnd();
    
    return {
      success: errors.length === 0,
      updatedExtensions,
      releases,
      errors: errors.length > 0 ? errors : undefined
    };
    
  } catch (error) {
    logger.error('生成预览失败', error);
    return {
      success: false,
      updatedExtensions: [],
      releases: {},
      errors: [String(error)]
    };
  }
}

/**
 * 生成发布
 */
export async function generateRelease(options: ReleaseOptions = {}): Promise<ReleaseResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    extensions: targetExtensions,
    dryRun = false,
    createReport = true,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info(`开始生成发布配置${dryRun ? ' (模拟运行)' : ''}...`);
  
  try {
    // 1. 执行与 preview 相同的分析流程
    const previewResult = await generatePreview({
      baseBranch,
      verbose,
      extensions: targetExtensions,
      cwd
    });
    
    if (!previewResult.success) {
      return {
        ...previewResult,
        generatedFiles: []
      };
    }
    
    const { releases, updatedExtensions } = previewResult;
    
    if (updatedExtensions.length === 0) {
      logger.warn('没有需要发布的扩展');
      return {
        ...previewResult,
        generatedFiles: []
      };
    }
    
    const generatedFiles: string[] = [];
    const reportPaths: Record<string, string> = {};
    
    // 2. 更新扩展配置文件
    for (const extensionName of updatedExtensions) {
      const releaseInfo = releases[extensionName];
      
      if (!dryRun) {
        await updateExtensionRelease(extensionName, releaseInfo);
        generatedFiles.push(getRelativePath(getExtensionConfigPath(extensionName)));
      }
      
      logger.success(`${dryRun ? '[模拟] ' : ''}更新扩展配置: ${extensionName}`);
    }
    
    // 3. 生成 release-extensions.json
    if (!dryRun) {
      await generateReleaseConfig(releases);
      generatedFiles.push('release-extensions.json');
    }
    logger.success(`${dryRun ? '[模拟] ' : ''}生成发布配置: release-extensions.json`);
    
    // 4. 生成发布报告
    if (createReport) {
      for (const extensionName of updatedExtensions) {
        const releaseInfo = releases[extensionName];
        
        if (!dryRun) {
          const reportPath = await generateReleaseReport(extensionName, releaseInfo);
          reportPaths[extensionName] = getRelativePath(reportPath);
          generatedFiles.push(reportPaths[extensionName]);
        }
        
        logger.success(`${dryRun ? '[模拟] ' : ''}生成发布报告: ${extensionName}`);
      }
    }
    
    // 5. 输出摘要
    logger.group('发布摘要');
    logger.info(`${dryRun ? '[模拟] ' : ''}生成了 ${generatedFiles.length} 个文件:`);
    for (const file of generatedFiles) {
      logger.info(`  - ${file}`);
    }
    logger.groupEnd();
    
    if (!dryRun) {
      logger.warn('请手动提交这些更改并创建相应的 Git 标签');
    }
    
    return {
      ...previewResult,
      generatedFiles,
      releaseConfigPath: 'release-extensions.json',
      reportPaths: Object.keys(reportPaths).length > 0 ? reportPaths : undefined
    };
    
  } catch (error) {
    logger.error('生成发布失败', error);
    return {
      success: false,
      updatedExtensions: [],
      releases: {},
      generatedFiles: [],
      errors: [String(error)]
    };
  }
}

/**
 * 获取状态
 */
export async function getStatus(options: StatusOptions = {}): Promise<StatusResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    detailed = false,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info('检查仓库状态...');
  
  try {
    // 检查是否为 Git 仓库
    const isRepo = await isGitRepo(cwd);
    if (!isRepo) {
      return {
        isGitRepo: false
      };
    }
    
    // 获取基本信息
    const currentBranch = await getCurrentBranch(cwd);
    const hasChanges = await hasUncommittedChanges(cwd);
    const latestTag = await getLatestTag(cwd);
    
    // 获取待处理的提交
    let pendingCommits = 0;
    let affectedExtensions: string[] = [];
    
    if (latestTag) {
      const commits = await getCommitsBetween(latestTag.commit, 'HEAD', cwd);
      pendingCommits = commits.length;
      
      if (detailed && commits.length > 0) {
        const parsedCommits = parseCommits(commits);
        const extensions = new Set<string>();
        
        for (const commit of parsedCommits) {
          for (const ext of commit.appliesTo) {
            extensions.add(ext);
          }
        }
        
        affectedExtensions = Array.from(extensions);
      }
    }
    
    // 输出状态信息
    logger.info(`Git 仓库: ✓`);
    logger.info(`当前分支: ${currentBranch}`);
    logger.info(`未提交更改: ${hasChanges ? '是' : '否'}`);
    logger.info(`最新标签: ${latestTag?.name || '无'}`);
    logger.info(`待处理提交: ${pendingCommits}`);
    
    if (detailed && affectedExtensions.length > 0) {
      logger.info(`受影响的扩展: ${affectedExtensions.join(', ')}`);
    }
    
    return {
      isGitRepo: true,
      currentBranch,
      hasUncommittedChanges: hasChanges,
      latestTag: latestTag?.name,
      pendingCommits,
      affectedExtensions: affectedExtensions.length > 0 ? affectedExtensions : undefined
    };
    
  } catch (error) {
    logger.error('获取状态失败', error);
    return {
      isGitRepo: true
    };
  }
}

// 辅助函数：获取扩展配置路径
function getExtensionConfigPath(extensionName: string): string {
  return join(process.cwd(), 'packages/extensions/extension_dashboard/extensions', `${extensionName}.json`);
}

// 重新导出类型，供外部使用
export type {
  PreviewOptions,
  ReleaseOptions,
  StatusOptions,
  PreviewResult,
  ReleaseResult,
  StatusResult,
  ExtensionConfig,
  ReleaseInfo,
  ParsedCommit,
  ChangelogEntry,
  VersionBump
} from './types';

// 导出常量，供外部工具使用
export { COMMIT_TYPES, CUSTOM_FOOTERS } from './constants';

// 导出工具函数，供外部工具使用
export { 
  isValidVersion,
  formatCommit
} from './utils';

// 需要导入 join
import { join } from 'node:path';