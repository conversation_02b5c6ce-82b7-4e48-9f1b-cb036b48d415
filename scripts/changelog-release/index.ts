/**
 * Changelog Release 核心流程编排
 * 
 * 整合各功能模块，实现 preview 和 release 的核心工作流
 */

import { Logger } from '../helpers/logger';
import { 
  isGitRepo, 
  getCurrentBranch, 
  hasUncommittedChanges,
  getLatestTag,
  getCommitsBetween,
  getCommitsSinceBranch,
  getAllTags
} from './git';
import { 
  parseCommits, 
  groupCommitsByExtension,
  calculateVersionBump,
  calculateNextVersion,
  filterChangelogCommits
} from './parser';
import { 
  readExtensionConfig,
  readExtensionConfigs,
  generateChangelogEntry,
  updateExtensionNext,
  updateExtensionRelease,
  generateReleaseConfig,
  generateReleaseReport
} from './generator';
import { 
  getRelativePath, 
  formatCommitList,
  shouldRelease,
  generateReleaseSummary,
  sortExtensionNames,
  filterValidExtensions
} from './utils';
import { DEFAULT_CONFIG, ERROR_MESSAGES } from './constants';
import type { 
  PreviewOptions, 
  ReleaseOptions, 
  StatusOptions,
  PreviewResult,
  ReleaseResult,
  StatusResult,
  ReleaseInfo,
  ParsedCommit,
  ExtensionConfig
} from './types';

const logger = Logger.create('ChangelogRelease');

/**
 * 验证 Git 仓库状态
 */
async function validateGitRepository(cwd: string): Promise<void> {
  if (!await isGitRepo(cwd)) {
    throw new Error(ERROR_MESSAGES.notGitRepo);
  }
  
  const currentBranch = await getCurrentBranch(cwd);
  logger.info(`当前分支: ${currentBranch}`);
  
  if (await hasUncommittedChanges(cwd)) {
    logger.warn('检测到未提交的更改');
  }
}

/**
 * 获取需要处理的提交
 */
async function fetchCommits(baseBranch: string, cwd: string): Promise<GitCommit[]> {
  const latestTag = await getLatestTag(cwd);
  let commits;
  
  if (latestTag) {
    logger.info(`最新标签: ${latestTag.name}`);
    commits = await getCommitsBetween(latestTag.commit, 'HEAD', cwd);
  } else {
    logger.info(`没有找到标签，从 ${baseBranch} 分支获取提交`);
    commits = await getCommitsSinceBranch(baseBranch, cwd);
  }
  
  if (commits.length === 0) {
    logger.warn(ERROR_MESSAGES.noCommits);
  } else {
    logger.info(`找到 ${commits.length} 个提交`);
  }
  
  return commits;
}

/**
 * 提取扩展名称列表
 */
function extractExtensionNames(
  parsedCommits: ParsedCommit[], 
  targetExtensions?: string[]
): string[] {
  if (targetExtensions && targetExtensions.length > 0) {
    return targetExtensions;
  }
  
  // 从解析的提交中提取所有涉及的扩展
  const mentionedExtensions = new Set<string>();
  for (const commit of parsedCommits) {
    for (const ext of commit.appliesTo) {
      mentionedExtensions.add(ext);
    }
  }
  
  return Array.from(mentionedExtensions);
}

/**
 * 处理单个扩展的发布信息
 */
async function processExtensionRelease(
  extensionName: string,
  extensionCommits: ParsedCommit[],
  config: ExtensionConfig
): Promise<ReleaseInfo> {
  logger.group(`处理扩展: ${extensionName}`);
  
  try {
    // 过滤需要包含在 changelog 中的提交
    const changelogCommits = filterChangelogCommits(extensionCommits);
    
    if (!shouldRelease(changelogCommits)) {
      logger.info('没有需要发布的变更，跳过');
      throw new Error('No releasable changes');
    }
    
    const currentVersion = config.version;
    
    // 计算版本号
    const bump = calculateVersionBump(changelogCommits);
    const newVersion = calculateNextVersion(currentVersion, bump);
    
    if (!newVersion) {
      throw new Error(`计算版本号失败: ${currentVersion} + ${bump}`);
    }
    
    logger.info(`版本变更: ${currentVersion} → ${newVersion} (${bump})`);
    
    // 生成 changelog
    const changelog = generateChangelogEntry(newVersion, changelogCommits);
    
    // 创建发布信息
    const releaseInfo: ReleaseInfo = {
      extensionName,
      currentVersion,
      newVersion,
      bump,
      commits: changelogCommits,
      changelog
    };
    
    // 更新 next 字段
    await updateExtensionNext(extensionName, releaseInfo);
    
    logger.success(`更新成功: ${extensionName}`);
    return releaseInfo;
  } finally {
    logger.groupEnd();
  }
}

/**
 * 输出预览摘要
 */
function printPreviewSummary(
  updatedExtensions: string[], 
  releases: Record<string, ReleaseInfo>
): void {
  logger.group('预览摘要');
  logger.info(`更新了 ${updatedExtensions.length} 个扩展:`);
  for (const name of sortExtensionNames(updatedExtensions)) {
    const info = releases[name];
    logger.info(`  - ${generateReleaseSummary(name, info.currentVersion, info.newVersion, info.commits.length)}`);
  }
  logger.groupEnd();
}

/**
 * 更新所有扩展配置文件
 */
async function updateExtensionConfigs(
  updatedExtensions: string[],
  releases: Record<string, ReleaseInfo>,
  dryRun: boolean
): Promise<string[]> {
  const generatedFiles: string[] = [];
  
  for (const extensionName of updatedExtensions) {
    const releaseInfo = releases[extensionName];
    
    if (!dryRun) {
      await updateExtensionRelease(extensionName, releaseInfo);
      generatedFiles.push(getRelativePath(getExtensionConfigPath(extensionName)));
    }
    
    logger.success(`${dryRun ? '[模拟] ' : ''}更新扩展配置: ${extensionName}`);
  }
  
  return generatedFiles;
}

/**
 * 生成所有发布报告
 */
async function generateReleaseReports(
  updatedExtensions: string[],
  releases: Record<string, ReleaseInfo>,
  dryRun: boolean
): Promise<{ files: string[], paths: Record<string, string> }> {
  const generatedFiles: string[] = [];
  const reportPaths: Record<string, string> = {};
  
  for (const extensionName of updatedExtensions) {
    const releaseInfo = releases[extensionName];
    
    if (!dryRun) {
      const reportPath = await generateReleaseReport(extensionName, releaseInfo);
      const relativePath = getRelativePath(reportPath);
      reportPaths[extensionName] = relativePath;
      generatedFiles.push(relativePath);
    }
    
    logger.success(`${dryRun ? '[模拟] ' : ''}生成发布报告: ${extensionName}`);
  }
  
  return { files: generatedFiles, paths: reportPaths };
}

/**
 * 输出发布摘要
 */
function printReleaseSummary(generatedFiles: string[], dryRun: boolean): void {
  logger.group('发布摘要');
  logger.info(`${dryRun ? '[模拟] ' : ''}生成了 ${generatedFiles.length} 个文件:`);
  for (const file of generatedFiles) {
    logger.info(`  - ${file}`);
  }
  logger.groupEnd();
  
  if (!dryRun) {
    logger.warn('请手动提交这些更改并创建相应的 Git 标签');
  }
}

/**
 * 生成预览
 */
export async function generatePreview(options: PreviewOptions = {}): Promise<PreviewResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    extensions: targetExtensions,
    skipUnassigned = false,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info('开始生成 Changelog 预览...');
  
  try {
    // 1. 验证 Git 仓库
    await validateGitRepository(cwd);
    
    // 2. 获取提交
    const commits = await fetchCommits(baseBranch, cwd);
    if (commits.length === 0) {
      return {
        success: true,
        updatedExtensions: [],
        releases: {}
      };
    }
    
    // 3. 解析提交
    const parsedCommits = parseCommits(commits);
    
    // 4. 获取扩展列表
    const extensionNames = extractExtensionNames(parsedCommits, targetExtensions);
    if (extensionNames.length === 0) {
      logger.warn('没有找到需要更新的扩展');
      return {
        success: true,
        updatedExtensions: [],
        releases: {}
      };
    }
    
    // 5. 读取扩展配置
    const configs = await readExtensionConfigs(extensionNames);
    const validExtensions = filterValidExtensions(extensionNames, configs);
    
    // 6. 按扩展分组提交
    const commitGroups = groupCommitsByExtension(parsedCommits, validExtensions);
    
    // 7. 处理每个扩展
    const releases: Record<string, ReleaseInfo> = {};
    const updatedExtensions: string[] = [];
    const errors: string[] = [];
    
    for (const [extensionName, extensionCommits] of Object.entries(commitGroups)) {
      try {
        const config = configs[extensionName];
        const releaseInfo = await processExtensionRelease(extensionName, extensionCommits, config);
        releases[extensionName] = releaseInfo;
        updatedExtensions.push(extensionName);
      } catch (error) {
        if (error.message !== 'No releasable changes') {
          const errorMsg = `处理扩展 ${extensionName} 失败: ${error}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }
    }
    
    // 8. 输出摘要
    printPreviewSummary(updatedExtensions, releases);
    
    return {
      success: errors.length === 0,
      updatedExtensions,
      releases,
      errors: errors.length > 0 ? errors : undefined
    };
    
  } catch (error) {
    logger.error('生成预览失败', error);
    return {
      success: false,
      updatedExtensions: [],
      releases: {},
      errors: [String(error)]
    };
  }
}

/**
 * 生成发布
 */
export async function generateRelease(options: ReleaseOptions = {}): Promise<ReleaseResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    extensions: targetExtensions,
    dryRun = false,
    createReport = true,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info(`开始生成发布配置${dryRun ? ' (模拟运行)' : ''}...`);
  
  try {
    // 1. 执行预览分析
    const previewResult = await generatePreview({
      baseBranch,
      verbose,
      extensions: targetExtensions,
      cwd
    });
    
    if (!previewResult.success || previewResult.updatedExtensions.length === 0) {
      logger.warn('没有需要发布的扩展');
      return {
        ...previewResult,
        generatedFiles: []
      };
    }
    
    const { releases, updatedExtensions } = previewResult;
    let generatedFiles: string[] = [];
    
    // 2. 更新扩展配置文件
    const configFiles = await updateExtensionConfigs(updatedExtensions, releases, dryRun);
    generatedFiles = generatedFiles.concat(configFiles);
    
    // 3. 生成 release-extensions.json
    if (!dryRun) {
      await generateReleaseConfig(releases);
      generatedFiles.push('release-extensions.json');
    }
    logger.success(`${dryRun ? '[模拟] ' : ''}生成发布配置: release-extensions.json`);
    
    // 4. 生成发布报告
    let reportPaths: Record<string, string> | undefined;
    if (createReport) {
      const reports = await generateReleaseReports(updatedExtensions, releases, dryRun);
      generatedFiles = generatedFiles.concat(reports.files);
      reportPaths = Object.keys(reports.paths).length > 0 ? reports.paths : undefined;
    }
    
    // 5. 输出摘要
    printReleaseSummary(generatedFiles, dryRun);
    
    return {
      ...previewResult,
      generatedFiles,
      releaseConfigPath: 'release-extensions.json',
      reportPaths
    };
    
  } catch (error) {
    logger.error('生成发布失败', error);
    return {
      success: false,
      updatedExtensions: [],
      releases: {},
      generatedFiles: [],
      errors: [String(error)]
    };
  }
}

/**
 * 获取状态
 */
export async function getStatus(options: StatusOptions = {}): Promise<StatusResult> {
  const {
    baseBranch = DEFAULT_CONFIG.baseBranch,
    verbose = false,
    detailed = false,
    cwd = process.cwd()
  } = options;
  
  if (verbose) {
    Logger.setVerbose(true);
  }
  
  logger.info('检查仓库状态...');
  
  try {
    // 检查是否为 Git 仓库
    const isRepo = await isGitRepo(cwd);
    if (!isRepo) {
      return {
        isGitRepo: false
      };
    }
    
    // 获取基本信息
    const currentBranch = await getCurrentBranch(cwd);
    const hasChanges = await hasUncommittedChanges(cwd);
    const latestTag = await getLatestTag(cwd);
    
    // 获取待处理的提交
    let pendingCommits = 0;
    let affectedExtensions: string[] = [];
    
    if (latestTag) {
      const commits = await getCommitsBetween(latestTag.commit, 'HEAD', cwd);
      pendingCommits = commits.length;
      
      if (detailed && commits.length > 0) {
        const parsedCommits = parseCommits(commits);
        const extensions = new Set<string>();
        
        for (const commit of parsedCommits) {
          for (const ext of commit.appliesTo) {
            extensions.add(ext);
          }
        }
        
        affectedExtensions = Array.from(extensions);
      }
    }
    
    // 输出状态信息
    logger.info(`Git 仓库: ✓`);
    logger.info(`当前分支: ${currentBranch}`);
    logger.info(`未提交更改: ${hasChanges ? '是' : '否'}`);
    logger.info(`最新标签: ${latestTag?.name || '无'}`);
    logger.info(`待处理提交: ${pendingCommits}`);
    
    if (detailed && affectedExtensions.length > 0) {
      logger.info(`受影响的扩展: ${affectedExtensions.join(', ')}`);
    }
    
    return {
      isGitRepo: true,
      currentBranch,
      hasUncommittedChanges: hasChanges,
      latestTag: latestTag?.name,
      pendingCommits,
      affectedExtensions: affectedExtensions.length > 0 ? affectedExtensions : undefined
    };
    
  } catch (error) {
    logger.error('获取状态失败', error);
    return {
      isGitRepo: true
    };
  }
}

// 辅助函数：获取扩展配置路径
function getExtensionConfigPath(extensionName: string): string {
  return join(process.cwd(), 'packages/extensions/extension_dashboard/extensions', `${extensionName}.json`);
}

// 重新导出类型，供外部使用
export type {
  PreviewOptions,
  ReleaseOptions,
  StatusOptions,
  PreviewResult,
  ReleaseResult,
  StatusResult,
  ExtensionConfig,
  ReleaseInfo,
  ParsedCommit,
  ChangelogEntry,
  VersionBump
} from './types';

// 导出常量，供外部工具使用
export { COMMIT_TYPES, CUSTOM_FOOTERS } from './constants';

// 导出工具函数，供外部工具使用
export { 
  isValidVersion,
  formatCommit
} from './utils';

// 需要导入 join
import { join } from 'node:path';