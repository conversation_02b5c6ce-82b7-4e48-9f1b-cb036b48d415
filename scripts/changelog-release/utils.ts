/**
 * Changelog Release 模块工具函数
 * 
 * 提供辅助功能，被模块内部和外部工具共同使用
 */

import { join, relative } from 'node:path';
import { projectPaths } from '../helpers/utils';
import { REGEX_PATTERNS } from './constants';
import type { ParsedCommit, ExtensionConfig } from './types';

/**
 * 获取扩展配置文件的完整路径
 */
export function getExtensionConfigPath(extensionName: string): string {
  return join(
    projectPaths.workspace,
    'packages/extensions/extension_dashboard/extensions',
    `${extensionName}.json`
  );
}

/**
 * 获取发布报告的完整路径
 */
export function getReleaseReportPath(extensionName: string, version: string): string {
  return join(
    projectPaths.workspace,
    '.output',
    extensionName,
    version,
    'RELEASE.md'
  );
}

/**
 * 获取发布配置文件的完整路径
 */
export function getReleaseConfigPath(): string {
  return join(projectPaths.workspace, 'release-extensions.json');
}

/**
 * 验证版本号格式
 */
export function isValidVersion(version: string): boolean {
  return REGEX_PATTERNS.version.test(version);
}

/**
 * 验证标签名称格式
 */
export function isValidTag(tag: string): boolean {
  return REGEX_PATTERNS.tagName.test(tag);
}

/**
 * 验证 commit 哈希格式
 */
export function isValidCommitHash(hash: string): boolean {
  return REGEX_PATTERNS.commitHash.test(hash) || REGEX_PATTERNS.shortHash.test(hash);
}

/**
 * 格式化提交信息用于显示
 */
export function formatCommit(commit: ParsedCommit): string {
  const scope = commit.scope ? `(${commit.scope})` : '';
  return `${commit.type}${scope}: ${commit.subject}`;
}

/**
 * 格式化提交列表用于显示
 */
export function formatCommitList(commits: ParsedCommit[], indent: string = ''): string[] {
  return commits.map(commit => {
    const formatted = formatCommit(commit);
    const hash = commit.shortHash;
    return `${indent}${hash} - ${formatted}`;
  });
}

/**
 * 从标签名称中提取版本号
 */
export function extractVersionFromTag(tag: string): string | null {
  const match = tag.match(REGEX_PATTERNS.tagName);
  if (match) {
    return tag.startsWith('v') ? tag.substring(1) : tag;
  }
  return null;
}

/**
 * 生成标签名称
 */
export function generateTagName(version: string, prefix: string = 'v'): string {
  return `${prefix}${version}`;
}

/**
 * 按扩展名称排序
 */
export function sortExtensionNames(names: string[]): string[] {
  return names.sort((a, b) => a.localeCompare(b));
}

/**
 * 过滤出有效的扩展名称
 */
export function filterValidExtensions(
  names: string[], 
  configs: Record<string, ExtensionConfig>
): string[] {
  return names.filter(name => {
    const config = configs[name];
    return config && config.version && isValidVersion(config.version);
  });
}

/**
 * 获取相对路径（用于日志显示）
 */
export function getRelativePath(absolutePath: string): string {
  return relative(projectPaths.workspace, absolutePath);
}


/**
 * 检查是否需要发布
 */
export function shouldRelease(commits: ParsedCommit[]): boolean {
  // 如果没有提交，不需要发布
  if (commits.length === 0) {
    return false;
  }
  
  // 如果有任何需要包含在 changelog 中的提交，就需要发布
  return commits.some(commit => {
    const commitType = commit.type;
    return ['feat', 'fix', 'perf', 'refactor', 'revert'].includes(commitType);
  });
}

/**
 * 合并多个提交的 Issue ID
 */
export function mergeIssueIds(commits: ParsedCommit[]): string[] {
  const issueSet = new Set<string>();
  
  for (const commit of commits) {
    for (const issueId of commit.issueIds) {
      issueSet.add(issueId);
    }
  }
  
  return Array.from(issueSet).sort();
}

/**
 * 统计提交类型
 */
export function countCommitTypes(commits: ParsedCommit[]): Record<string, number> {
  const counts: Record<string, number> = {};
  
  for (const commit of commits) {
    counts[commit.type] = (counts[commit.type] || 0) + 1;
  }
  
  return counts;
}

/**
 * 生成发布摘要
 */
export function generateReleaseSummary(
  extensionName: string,
  currentVersion: string,
  newVersion: string,
  commitCount: number
): string {
  return `${extensionName}: ${currentVersion} → ${newVersion} (${commitCount} commits)`;
}