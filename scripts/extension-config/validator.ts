/**
 * @fileoverview 插件配置验证逻辑
 * @description 提供插件项目及渠道包（variant）配置的验证工具函数和规则。
 *
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import {
  SUPPORT_WEBSTORES,
  SUPPORT_VARIANTS,
  SUPPORT_MANIFEST_VERSIONS,
} from '../helpers/constants.js';
import type { ValidationResult } from '../helpers/types.js';
import type { UserExtensionConfig, VariantConfig } from './types.js';

/**
 * 校验插件名字是否合法
 * @param name - 插件名字
 * @returns 校验结果
 */
export function validateExtensionName(name: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!name?.trim()) {
    errors.push('Extension name is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验插件版本号是否合法（基本格式）
 * @param version - 插件版本号
 * @returns 校验结果
 */
export function validateVersion(version: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!version?.trim()) {
    errors.push('Version is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验渠道包（variant）ID 是否符合规范
 * @param variantId - 渠道包唯一 ID
 * @returns 校验结果
 */
export function validateVariantId(variantId: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!variantId?.trim()) {
    errors.push('Variant ID is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验语言包代码（locale）是否合法
 * @param locale - 语言包代码
 * @returns 校验结果
 */
export function validateLocale(locale: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!locale?.trim()) {
    errors.push('Locale is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验 URL 格式是否合法
 * @param url - 待校验的 URL
 * @param fieldName - 字段名（用于错误提示）
 * @returns 校验结果
 */
export function validateUrl(url: string, fieldName: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (url?.trim()) {
    try {
      new URL(url);
    } catch {
      errors.push(`${fieldName} must be a valid URL`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验正则表达式模式（仅检查非空）
 * @param pattern - 正则表达式字符串
 * @param fieldName - 字段名（用于错误提示）
 * @returns 校验结果
 */
export function validateRegexPattern(pattern: string, fieldName: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 简单验证，只检查是否为空
  if (!pattern?.trim()) {
    errors.push(`${fieldName} pattern cannot be empty`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验完整的插件项目配置（基础字段、渠道包数组等）
 * @param config - 用户输入的插件项目配置
 * @returns 校验结果
 */
export function validateFullExtensionConfig(config: UserExtensionConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 只验证必要字段
  const nameValidation = validateExtensionName(config.name);
  errors.push(...nameValidation.errors);

  const versionValidation = validateVersion(config.version);
  errors.push(...versionValidation.errors);

  // 验证 variants
  if (!config.variants || !Array.isArray(config.variants) || config.variants.length === 0) {
    errors.push('variants is required and must be a non-empty array');
  } else {
    for (let i = 0; i < config.variants.length; i++) {
      const variantValidation = validateFullVariantConfig(config.variants[i], i);
      errors.push(...variantValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 校验完整的渠道包（variant）配置（基础字段、类型、平台等）
 * @param variant - 渠道包配置
 * @param index - 可选，渠道包在数组中的下标（用于错误提示）
 * @returns 校验结果
 */
export function validateFullVariantConfig(variant: VariantConfig, index?: number): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const prefix = index !== undefined ? `Variant[${index}]` : 'Variant';

  // 只验证必要字段
  if (!variant.variantId?.trim()) {
    errors.push(`${prefix}: variantId is required`);
  }

  if (!variant.variantName?.trim()) {
    errors.push(`${prefix}: variantName is required`);
  }

  if (!variant.variantType?.trim()) {
    errors.push(`${prefix}: variantType is required`);
  } else if (!SUPPORT_VARIANTS.includes(variant.variantType)) {
    errors.push(`${prefix}: Unsupported variantType: ${variant.variantType}`);
  }

  if (!variant.webstore?.trim()) {
    errors.push(`${prefix}: webstore is required`);
  } else if (!SUPPORT_WEBSTORES.includes(variant.webstore)) {
    errors.push(`${prefix}: Unsupported webstore: ${variant.webstore}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
