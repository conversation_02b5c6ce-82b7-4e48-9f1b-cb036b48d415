
/**
 * @fileoverview 插件配置核心处理器
 * @description 实现 defineExtensionConfig 主处理器，负责插件项目配置的基础处理、验证与渠道包（variant）解析。
 * 
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import {
  WEBSTORE_CN,
  DEFAULT_CHROME_LOCALES_ONLY,
  DEFAULT_CHROME_MESSAGES_ONLY,
} from '../helpers/constants.js';
import { createLogger, deepMerge } from '../helpers/index.js';
import type {
  WebstoreType,
  WebstoreCNType,
  VariantType,
  ManifestVersionType,
  VariantChannel,
} from '../helpers/types.js';
import type {
  UserExtensionConfig,
  UserI18nConfig,
  VariantConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  ProcessedI18nConfig,
  BrowserManifest,
} from './types.js';
import { validateFullExtensionConfig, validateFullVariantConfig } from './validator.js';

const logger = createLogger('ExtensionConfigProcessor');


// #region --- 字段自动填充 ---


/**
 * 生成渠道包唯一标识符 variantTarget
 * @param webstore - 分发平台标识（如 chrome、edge 等）
 * @param variantType - 渠道包类型（如 release、beta、offline 等）
 * @param manifestVersion - manifest 版本号
 * @returns 渠道包唯一标识符，格式: `{webstore}-mv{manifestVersion}-{variantType}`
 */
function generateVariantTarget(
  webstore: WebstoreType,
  variantType: VariantType,
  manifestVersion: ManifestVersionType,
): string {
  return `${webstore}-mv${manifestVersion}-${variantType}`;
}


/**
 * 生成渠道包的渠道标识 variantChannel
 * @param webstore - 分发平台标识
 * @param variantType - 渠道包类型
 * @returns 渠道包渠道标识，offline 类型为 `{webstore}_offline`，否则为 `{webstore}`
 */
function generateVariantChannel(webstore: WebstoreType, variantType: VariantType): VariantChannel {
  return variantType === 'offline' ? `${webstore}_offline` : webstore;
}


/**
 * 生成分发平台的中文标识 webstoreCN
 * @param webstore - 分发平台标识
 * @returns 中文平台名
 */
function generateWebstoreCN(webstore: WebstoreType): WebstoreCNType {
  return (WEBSTORE_CN[webstore] || webstore) as WebstoreCNType;
}

// #endregion


// #region --- 配置解析与合并 ---


/**
 * 解析 manifest 版本号（优先级：渠道包 > 插件项目 > 默认 3）
 * @param config - 插件项目配置
 * @param variant - 渠道包配置
 * @returns manifest 版本号
 */
function resolveManifestVersion(
  config: UserExtensionConfig,
  variant: VariantConfig,
): ManifestVersionType {
  return variant.manifestVersion ?? config.manifestVersion ?? 3;
}


/**
 * 解析默认语言（优先级：渠道包 > 插件项目 > 默认 'en'）
 * @param config - 插件项目配置
 * @param variant - 渠道包配置
 * @returns 默认语言（语言包 key）
 */
function resolveDefaultLocale(config: UserExtensionConfig, variant: VariantConfig): string {
  return variant.defaultLocale ?? config.defaultLocale ?? 'en';
}


/**
 * 解析 Google Analytics Measurement ID（优先级：渠道包 > 插件项目）
 * @param config - 插件项目配置
 * @param variant - 渠道包配置
 * @returns Measurement ID 或 undefined
 */
function resolveMeasurementId(
  config: UserExtensionConfig,
  variant: VariantConfig,
): string | undefined {
  return variant.measurementId ?? config.measurementId;
}

// #endregion


// #region --- 主处理器 ---


/**
 * 插件配置主处理器
 *
 * 主要职责：
 * 1. 校验插件项目配置（基础校验、警告输出）
 * 2. 处理每个渠道包（variant）：
 *    - 合并全局与渠道包配置
 *    - 自动填充渠道包相关字段（variantChannel, webstoreCN, variantTarget 等）
 *    - 校验渠道包配置
 *    - 生成最终渠道包配置对象
 * 3. 检查渠道包唯一性（variantTarget 不重复）
 * 4. 返回处理后的插件配置对象
 *
 * @param config - 用户输入的插件项目配置
 * @returns 处理后的插件配置对象（包含所有有效渠道包）
 * @throws 配置校验失败或无有效渠道包时抛出异常
 */
export function defineExtensionConfig(config: UserExtensionConfig): ProcessedExtensionConfig {
  logger.info(`开始处理插件配置: ${config.name} v${config.version}`);

  // 1. 插件项目配置基础校验
  const globalValidation = validateFullExtensionConfig(config);
  if (!globalValidation.isValid) {
    const errorMessage = `插件配置验证失败:\n${globalValidation.errors.join('\n')}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // 输出全局警告信息
  if (globalValidation.warnings.length > 0) {
    for (const warning of globalValidation.warnings) {
      logger.warn(warning);
    }
  }

  // 存储所有已处理的渠道包配置
  const processedVariants: ProcessedVariantConfig[] = [];
  // 用于检测渠道包唯一性
  const allVariantTargets = new Set<string>();

  // 2. 处理每个渠道包（variant）
  for (let i = 0; i < config.variants.length; i++) {
    const variant = config.variants[i];

    try {
      // --- 渠道包字段解析与自动填充 ---
      // 1. 解析 manifest 版本号
      const manifestVersion = resolveManifestVersion(config, variant);
      // 2. 生成渠道包唯一标识符
      const variantTarget = generateVariantTarget(
        variant.webstore,
        variant.variantType,
        manifestVersion,
      );

      // 检查渠道包唯一性（variantTarget 不可重复）
      if (allVariantTargets.has(variantTarget)) {
        const errorMessage = `发现重复的渠道包 variantTarget: ${variantTarget}`;
        logger.error(errorMessage);
        throw new Error(errorMessage);
      }
      allVariantTargets.add(variantTarget);

      // 构建处理后的渠道包配置对象
      const processedVariant: ProcessedVariantConfig = {
        // 插件名字与版本
        name: config.name,
        version: config.version,

        // manifest 相关字段
        manifestVersion,
        defaultLocale: resolveDefaultLocale(config, variant), // 默认语言（语言包 key）
        measurementId: resolveMeasurementId(config, variant),

        // 渠道包核心字段
        variantId: variant.variantId,
        variantName: variant.variantName,
        variantType: variant.variantType,
        variantChannel:
          variant.variantChannel || generateVariantChannel(variant.webstore, variant.variantType),
        variantTarget,
        webstore: variant.webstore,
        webstoreCN: variant.webstoreCN || generateWebstoreCN(variant.webstore),
        webstoreId: variant.webstoreId || '',
        webstoreUrl: variant.webstoreUrl || '',

        // 语言包配置（合并全局与渠道包 i18n 字段，locales 由 i18n 模块后续填充）
        i18n: {
          ...deepMerge(
            (config.i18n || {}) as Record<string, unknown>,
            (variant.i18n || {}) as Record<string, unknown>,
          ),
          locales: [], // 语言包数组，后续由 i18n 模块扫描填充
        },

        // manifest 配置（合并全局与渠道包 manifest 字段）
        manifest: deepMerge(config.manifest || {}, variant.manifest || {}),
      };

      // --- 渠道包配置校验 ---
      const variantValidation = validateFullVariantConfig(variant, i);
      if (!variantValidation.isValid) {
        logger.error(`渠道包 [${i}] 校验失败:\n${variantValidation.errors.join('\n')}`);
        continue; // 跳过无效渠道包
      }

      // 输出渠道包警告信息
      if (variantValidation.warnings.length > 0) {
        for (const warning of variantValidation.warnings) {
          logger.warn(warning);
        }
      }

      processedVariants.push(processedVariant);
      logger.verbose(`成功处理渠道包: ${variantTarget}`);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error(`处理渠道包 [${i}] 时出错: ${message}`);
      continue; // 跳过有错误的渠道包
    }
  }


  // 3. 检查是否有有效渠道包
  if (processedVariants.length === 0) {
    throw new Error(`没有有效的渠道包配置被处理: ${config.name} v${config.version}`);
  }

  logger.success(
    `插件配置处理完成: ${config.name}, 成功处理 ${processedVariants.length} 个渠道包`,
  );

  // 4. 返回最终插件配置对象
  return {
    name: config.name,
    version: config.version,
    variants: processedVariants,
  };
}


// #endregion
