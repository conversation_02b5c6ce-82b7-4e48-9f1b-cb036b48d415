/**
 * @fileoverview 插件渠道包文件写入器
 * @description 负责生成和写入 .variants 目录下的 extension.json、语言包、文案等相关文件。
 *
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import fs from 'fs-extra';
import path from 'path';
import { createLogger } from '../helpers/index.js';
import { processI18nData } from '../i18n/index.js';
import { processManifestData } from '../manifest/index.js';
import type { ProcessedVariantConfig, ExtensionJsonContent } from './types.js';
import { generateExtensionPaths } from './utils.js';

const logger = createLogger('ExtensionFileWriter');

// #region --- 文件生成 ---

/**
 * 生成 extension.json 文件内容
 * 组合渠道包（variant）、语言包（i18n）、manifest 等数据，供后续写入磁盘。
 * @param variant - 单个渠道包（variant）完整配置
 * @returns extension.json 文件内容对象
 */

export function generateExtensionJson(variant: ProcessedVariantConfig): ExtensionJsonContent {
  logger.info(`生成 extension.json 内容: ${variant.variantTarget}`);

  // 1. 处理 i18n 数据
  const i18nData = processI18nData({
    extensionName: variant.name,
    variantTarget: variant.variantTarget,
    includes: variant.i18n?.includes || [],
    excludes: variant.i18n?.excludes || [],
    chromeLocalesOnly: variant.i18n?.chromeLocalesOnly || [],
    chromeMessagesOnly: variant.i18n?.chromeMessagesOnly || [],
  });

  // 2. 处理 manifest 数据
  const manifestData = processManifestData({
    extensionName: variant.name,
    version: variant.version,
    manifestVersion: variant.manifestVersion,
    defaultLocale: variant.defaultLocale,
    variantInfo: {
      variantId: variant.variantId,
      variantName: variant.variantName,
      variantChannel: variant.variantChannel,
      webstore: variant.webstore,
      variantTarget: variant.variantTarget,
    },
    baseManifest: variant.manifest,
  });

  // 3. 组合完整的 extension.json 内容
  const extensionJson: ExtensionJsonContent = {
    ...variant,
    i18n: {
      ...variant.i18n,
      locales: i18nData.locales,
      chromeI18nData: i18nData.chromeI18nData,
      vueI18nData: i18nData.vueI18nData,
      ...i18nData.filterConfig,
    },
    manifest: manifestData,
  };

  logger.success(`extension.json 内容生成完成: ${variant.variantTarget}`);
  return extensionJson;
}

/**
 * 写入插件渠道包相关文件到磁盘
 * 包括 extension.json、i18n.json 及 Chrome 语言包目录：
 *   _locales/xx/messages.json
 * @param extensionName 插件名字
 * @param variantTarget 渠道包唯一标识符
 * @param content extension.json 文件内容对象
 */
export async function writeExtensionFiles(
  extensionName: string,
  variantTarget: string,
  content: ExtensionJsonContent,
): Promise<void> {
  logger.info(`开始写入扩展文件: ${extensionName} - ${variantTarget}`);

  try {
    const extensionPaths = generateExtensionPaths(extensionName, variantTarget, content.version);

    // 1. 确定输出目录
    if (await fs.exists(extensionPaths.extensionVariantTargetRoot)) {
      await fs.remove(extensionPaths.extensionVariantTargetRoot);
    }
    await fs.ensureDir(extensionPaths.extensionVariantTargetRoot);

    // 2. 写入 extension.json
    const extensionJsonPath = extensionPaths.extensionVariantTargetJSON;
    await fs.writeJson(
      extensionJsonPath,
      {
        ...content,
        i18n: {
          ...content.i18n,
          chromeI18nData: undefined,
          vueI18nData: undefined,
        },
      },
      { spaces: 2 },
    );
    logger.verbose(`写入 extension.json: ${extensionJsonPath}`);

    // 3. 写入 i18n.json (Vue i18n 格式)
    const i18nJsonPath = extensionPaths.extensionVariantTargetI18n;
    await fs.writeJson(i18nJsonPath, content.i18n?.vueI18nData || {}, { spaces: 2 });
    logger.verbose(`写入 i18n.json: ${i18nJsonPath}`);

    // 4. 写入 _locales/*/messages.json (Chrome 格式)
    const localesDir = extensionPaths.extensionVariantTargetLocales;
    for (const [locale, messages] of Object.entries(content.i18n?.chromeI18nData || {})) {
      const localeDir = path.join(localesDir, locale);
      await fs.ensureDir(localeDir);

      const messagesJsonPath = path.join(localeDir, 'messages.json');
      await fs.writeJson(messagesJsonPath, messages, { spaces: 2 });
      logger.verbose(`写入 Chrome messages.json: ${messagesJsonPath}`);
    }

    logger.success(`扩展文件写入完成: ${extensionName} - ${variantTarget}`);
  } catch (error) {
    const errorMessage = `写入扩展文件失败: ${extensionName} - ${variantTarget}`;
    logger.error(errorMessage, error);
    throw new Error(errorMessage);
  }
}

// #endregion
