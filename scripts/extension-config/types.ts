/**
 * @fileoverview 插件配置类型定义
 * @description 定义插件项目、渠道包（variant）、语言包等相关类型接口。
 *
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import type {
  WebstoreType,
  WebstoreCNType,
  VariantType,
  VariantChannel,
  ManifestVersionType,
  ValidationResult,
  ChromeMessage,
  VariantInfo,
  BrowserManifest,
} from '../helpers/types.js';

// 从 helpers 导入基础类型，避免重复定义
export type {
  BrowserManifest,
  WebstoreType,
  WebstoreCNType,
  VariantType,
  VariantChannel,
  ManifestVersionType,
  ValidationResult,
  ChromeMessage,
  VariantInfo,
};

// #region --- 用户配置类型 (extension.config.ts) ---

/**
 * 用户在 extension.config.ts 中定义的语言包（i18n）配置
 */
export interface UserI18nConfig {
  /**
   * [只读] 插件支持的语言列表。由脚本自动扫描填充，用户无需填写。
   */
  readonly locales?: string[];

  /**
   * [可选] 包含的文案 key (支持正则字符串)。如果定义，则只有匹配的文案会被保留。
   */
  includes?: string[];

  /**
   * [可选] 排除的文案 key (支持正则字符串)。
   */
  excludes?: string[];

  /**
   * [可选] 仅生成到 `_locales` 目录的语言。这些语言的文案不会出现在给 vue-i18n 的产物中。
   * 默认值: ['en_US', 'en_GB', 'pt_BR', 'es_419']
   */
  chromeLocalesOnly?: string[];

  /**
   * [可选] 仅包含在 `_locales` 目录中的文案 key (支持正则字符串)。在 `includes`/`excludes` 过滤后生效。
   * 默认值: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*']
   */
  chromeMessagesOnly?: string[];
}

/**
 * 用户在 extension.config.ts 中定义的渠道包（variant）配置
 */
export interface VariantConfig {
  /**
   * [必填] 渠道包的唯一 ID。
   */
  variantId: string;

  /**
   * [必填] 渠道包的用户友好名称。
   */
  variantName: string;

  /**
   * [必填] 渠道包类型。
   * 可选值: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline'
   */
  variantType: VariantType;

  /**
   * [必填] 目标浏览器商店的简称。
   * 可选值: 'chrome' | 'firefox' | 'edge' | 'opera' | 'browser360' | 'safari' | 'adspower'
   */
  webstore: WebstoreType;

  /**
   * [可选] 渠道代码名称。如果留空，脚本将自动填充。
   * 自动填充规则: 如果 `variantType` 是 'offline', 格式为 `{webstore}_offline`，否则为 `{webstore}`。
   */
  variantChannel?: VariantChannel;

  /**
   * [只读] 渠道包的最终构建目标标识符。由脚本自动生成，用户无需填写。
   * 格式: `{webstore}-mv{manifestVersion}-{variantType}`
   */
  readonly variantTarget?: string;

  /**
   * [可选] 目标浏览器的内部代码名称。如果留空，脚本将自动填充。
   * 自动填充规则 (示例):
   * - 'firefox' -> 'e-f'
   * - 'opera'   -> 'e-o'
   * - 'edge'    -> 'e-edge'
   * - 'chrome' (默认) -> 'e-c'
   */
  webstoreCN?: WebstoreCNType;

  /**
   * [可选] 插件在对应商店的 ID。
   */
  webstoreId?: string;

  /**
   * [可选] 插件在对应商店的 URL。
   */
  webstoreUrl?: string;

  /**
   * [可选] 覆盖上层的 manifest 版本号 (e.g., 3)。
   */
  manifestVersion?: ManifestVersionType;

  /**
   * [可选] 覆盖上层的插件默认语言环境 (e.g., 'en')。
   */
  defaultLocale?: string;

  /**
   * [可选] 覆盖上层的 Google Analytics Measurement ID。
   */
  measurementId?: string;

  /**
   * [可选] 覆盖上层的国际化 (i18n) 处理配置。
   */
  i18n?: UserI18nConfig;

  /**
   * [可选] 覆盖上层的 manifest.json 配置骨架。
   * 注意: version, manifest_version, default_locale, name, description 字段会被脚本自动处理，用户填写无效。
   */
  manifest?: Partial<BrowserManifest>;
}

/**
 * 用户在 extension.config.ts 中定义的插件项目配置
 */
export interface UserExtensionConfig {
  /**
   * [必填] 插件项目名称，与 `packages/extensions/` 下的文件夹名一致。
   */
  name: string;

  /**
   * [必填] 插件的版本号，遵循 SemVer 规范。
   */
  version: string;

  /**
   * [可选] 默认的 manifest 版本号 (e.g., 3)。可被 variant 覆盖。
   */
  manifestVersion?: ManifestVersionType;

  /**
   * [可选] 插件的默认语言环境 (e.g., 'en')。可被 variant 覆盖。
   */
  defaultLocale?: string;

  /**
   * [可选] Google Analytics Measurement ID。可被 variant 覆盖。
   */
  measurementId?: string;

  /**
   * [可选] 国际化 (i18n) 处理配置。
   */
  i18n?: UserI18nConfig;

  /**
   * [可选] 标准的 manifest.json 配置骨架。所有 variant 都会继承此配置。
   * 注意: version, manifest_version, default_locale, name, description 字段会被脚本自动处理，用户填写无效。
   */
  manifest?: Partial<BrowserManifest>;

  /**
   * [必填] 渠道包 (变体) 定义数组。
   */
  variants: VariantConfig[];
}

// #endregion

// #region --- 处理后的配置类型 ---

/**
 * 处理后的语言包（i18n）配置
 */
export interface ProcessedI18nConfig {
  locales: string[];
  includes?: string[];
  excludes?: string[];
  chromeLocalesOnly?: string[];
  chromeMessagesOnly?: string[];
  chromeI18nData: Record<string, Record<string, { message: string }>>;
  vueI18nData: Record<string, Record<string, string>>;
}

/**
 * 经过处理后的所有插件项目相关路径
 */
export interface ProcessedPaths {
  workspace: string;
  packages: string;
  extensions: string;
  shared: string;
  sharedLocales: string;
  output: string;
  scripts: string;
  extensionRoot: string;
  extensionRawConfig: string;
  extensionRawLocales: string;
  extensionVariantsRoot: string;
  extensionVariantTargetRoot: string;
  extensionVariantTargetJSON: string;
  extensionVariantTargetI18n: string;
  extensionVariantTargetLocales: string;
  extensionManifestsRoot: string;
  extensionVariantTargetManifest: string;
  extensionVariantTargetOutput: string;
}

/**
 * 单个渠道包（variant）的完整配置结构。
 * 继承全局配置，并应用自身特定配置。
 * 所有可选字段都已被填充或赋予了确定的默认值。
 */
export interface ProcessedVariantConfig {
  // --- 从全局继承或自身定义的元数据 ---
  name: string;
  version: string;

  // --- 被规范化和填充的 Variant 核心字段 ---
  variantId: string;
  variantName: string;
  variantType: VariantType;
  variantChannel: VariantChannel; // [已填充]
  variantTarget: string; // [已填充]
  webstore: WebstoreType;
  webstoreCN: WebstoreCNType; // [已填充]
  webstoreId?: string;
  webstoreUrl?: string;

  // --- 被规范化和填充的插件核心配置 ---
  manifestVersion: ManifestVersionType; // [已确定]
  defaultLocale: string; // [已确定]
  measurementId?: string;

  // --- 完整的 i18n 配置 ---
  i18n?: UserI18nConfig;

  // --- 完整的、合并后的 manifest 对象 ---
  manifest?: Partial<BrowserManifest>;

  // --- 路径信息 ---
  // paths?: ProcessedPaths;
}

/**
 * 经过 defineExtensionConfig 处理后的插件项目配置对象结构。
 */
export interface ProcessedExtensionConfig {
  /**
   * 插件项目名称，直接从用户配置中获取。
   */
  name: string;

  /**
   * 插件的版本号，直接从用户配置中获取。
   */
  version: string;

  /**
   * 一个处理过的渠道包 (变体) 对象数组。
   * 每个对象都是一个独立的、完整的配置单元。
   */
  variants: ProcessedVariantConfig[];
}

// #endregion

// #region --- 文件内容类型 ---

/**
 * extension.json 文件内容类型（包含完整语言包数据）
 */
export interface ExtensionJsonContent extends ProcessedVariantConfig {
  i18n: ProcessedI18nConfig;
}

// #endregion
