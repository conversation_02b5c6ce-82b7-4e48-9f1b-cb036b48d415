/**
 * @fileoverview 插件配置专用工具函数
 * @description 提供插件项目及渠道包（variant）配置处理相关的工具函数。
 *
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import fs from 'fs-extra';
import { glob } from 'glob';
import path from 'path';
import { pathToFileURL } from 'url';
import { projectPaths, createLogger } from '../helpers/index.js';
import type { ProcessedVariantConfig, ProcessedPaths } from './types.js';

const logger = createLogger('ExtensionConfigUtils');

// 缓存已加载的扩展配置
const extensionConfigCache = new Map<string, Record<string, ProcessedVariantConfig>>();

// #region --- 路径生成工具 ---

/**
 * 生成特定插件项目和渠道包（variant）的所有相关路径
 * @param extensionName 插件名字
 * @param variantTarget 渠道包唯一标识符
 * @param version 插件版本号
 * @returns 包含所有路径的对象
 */
export function generateExtensionPaths(
  extensionName: string,
  variantTarget: string,
  version: string,
): ProcessedPaths {
  const extensionRoot = path.join(projectPaths.extensions, extensionName);

  return {
    // 基础路径
    workspace: projectPaths.workspace,
    packages: projectPaths.packages,
    extensions: projectPaths.extensions,
    shared: projectPaths.shared,
    sharedLocales: projectPaths.sharedLocales,
    output: projectPaths.output,
    scripts: projectPaths.scripts,

    // 插件相关路径
    extensionRoot,
    extensionRawConfig: path.join(extensionRoot, 'extension.config.ts'),
    extensionRawLocales: path.join(extensionRoot, 'locales'),
    extensionVariantsRoot: path.join(extensionRoot, '.variants'),
    extensionManifestsRoot: path.join(extensionRoot, '.manifests'),

    // 变体相关路径
    extensionVariantTargetRoot: path.join(extensionRoot, '.variants', variantTarget),
    extensionVariantTargetJSON: path.join(
      extensionRoot,
      '.variants',
      variantTarget,
      'extension.json',
    ),
    extensionVariantTargetI18n: path.join(extensionRoot, '.variants', variantTarget, 'i18n.json'),
    extensionVariantTargetLocales: path.join(
      extensionRoot,
      '.variants',
      variantTarget,
      'public',
      '_locales',
    ),
    extensionVariantTargetManifest: path.join(
      extensionRoot,
      '.manifests',
      `manifest.${variantTarget}.json`,
    ),
    extensionVariantTargetOutput: path.join(
      projectPaths.output,
      extensionName,
      version,
      variantTarget,
    ),
  };
}

// #endregion

// #region --- 文件扫描工具 ---

/**
 * 扫描并列出所有插件项目名字
 * @param extensionNames 可选的插件名字过滤列表
 * @returns 插件名字数组
 */
export function listExtensions(extensionNames?: string[]): string[] {
  const foundExtensions: string[] = [];

  try {
    const configFiles = glob.sync(path.join(projectPaths.extensions, '**/extension.config.ts'));

    for (const configFile of configFiles) {
      const extensionName = path.basename(path.dirname(configFile));

      // 如果指定了过滤列表，只返回在列表中的插件
      if (extensionNames && extensionNames.length > 0) {
        if (extensionNames.includes(extensionName)) {
          foundExtensions.push(extensionName);
        }
      } else {
        foundExtensions.push(extensionName);
      }
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`扫描插件目录失败: ${message}`);
  }

  return [...new Set(foundExtensions)].sort();
}

/**
 * 获取所有可用插件项目及其渠道包（variant）列表
 * @param extensionNames 可选的插件名字过滤列表
 * @returns 插件与渠道包映射对象
 */
export async function listExtensionVariants(extensionNames?: string[]): Promise<{ extensions: Record<string, string[]> }> {
  const extensionConfigs = await loadExtensionConfigs(extensionNames);
  const extensions: Record<string, string[]> = {};

  for (const [extensionName, variants] of Object.entries(extensionConfigs)) {
    extensions[extensionName] = Object.keys(variants);
  }

  return { extensions };
}

/**
 * 加载并处理插件项目的配置文件
 * @param extensionNames 可选的插件名字列表
 * @returns 插件项目及其渠道包（variant）信息对象
 */
export async function loadExtensionConfigs(
  extensionNames?: string[],
): Promise<Record<string, Record<string, ProcessedVariantConfig>>> {
  const targetExtensions = listExtensions(extensionNames);
  const extensions: Record<string, Record<string, ProcessedVariantConfig>> = {};

  for (const extensionName of targetExtensions) {
    // 检查缓存
    if (extensionConfigCache.has(extensionName)) {
      logger.verbose(`[${extensionName}] 使用缓存的配置`);
      extensions[extensionName] = extensionConfigCache.get(extensionName)!;
      continue;
    }

    try {
      const configPath = path.join(projectPaths.extensions, extensionName, 'extension.config.ts');
      const configModule = await import(pathToFileURL(configPath).href);

      // 假设配置文件已经通过 defineExtensionConfig 处理
      const processedConfig = configModule.default;

      if (!processedConfig || typeof processedConfig !== 'object') {
        logger.warn(`[${extensionName}] 配置文件没有默认导出或格式不正确`);
        continue;
      }

      // 如果是处理后的配置，转换为 variantTarget 索引的格式
      if ('variants' in processedConfig && Array.isArray(processedConfig.variants)) {
        const variantsByTarget: Record<string, ProcessedVariantConfig> = {};

        for (const variant of processedConfig.variants) {
          variantsByTarget[variant.variantTarget] = variant;
        }

        // 缓存配置
        extensionConfigCache.set(extensionName, variantsByTarget);
        extensions[extensionName] = variantsByTarget;
        
        logger.verbose(`[${extensionName}] 配置已加载并缓存`);
      } else {
        logger.warn(`[${extensionName}] 配置格式不正确，应该包含 variants 数组`);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.warn(`[${extensionName}] 配置加载失败: ${message}`);
    }
  }

  return extensions;
}

/**
 * 清除插件配置缓存
 * @param extensionNames 可选的插件名字列表，如果不提供则清除所有缓存
 */
export function clearExtensionConfigCache(extensionNames?: string[]): void {
  if (!extensionNames || extensionNames.length === 0) {
    // 清除所有缓存
    const cacheSize = extensionConfigCache.size;
    extensionConfigCache.clear();
    logger.verbose(`已清除所有扩展配置缓存 (${cacheSize} 个)`);
  } else {
    // 清除指定插件的缓存
    let clearedCount = 0;
    for (const extensionName of extensionNames) {
      if (extensionConfigCache.delete(extensionName)) {
        clearedCount++;
        logger.verbose(`已清除 ${extensionName} 的配置缓存`);
      }
    }
    logger.verbose(`已清除 ${clearedCount} 个扩展配置缓存`);
  }
}

// #endregion

// #region --- 清理工具 ---

/**
 * 清理插件项目的 .variants 目录
 * @param extensionNames 可选的插件名字列表
 */
export async function cleanExtensionVariants(extensionNames?: string[]): Promise<void> {
  const targetExtensions = listExtensions(extensionNames);

  for (const extensionName of targetExtensions) {
    const variantsDir = path.join(projectPaths.extensions, extensionName, '.variants');

    try {
      if (await fs.pathExists(variantsDir)) {
        await fs.remove(variantsDir);
        logger.info(`已清理: ${extensionName}/.variants`);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error(`清理 ${extensionName}/.variants 失败: ${message}`);
    }
  }
}

// #endregion
