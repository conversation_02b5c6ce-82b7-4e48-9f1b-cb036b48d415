# extension-config 模块 PRD 文档

## 一、背景与目标

在多平台浏览器扩展开发中，配置管理的复杂度极高：
- 需支持多平台（Chrome/Edge/Firefox/Opera 等）manifest 差异化
- 多变体（variant）并行开发与灰度发布
- 国际化（i18n）与多语言包自动合并
- 配置的校验、生成、缓存、批量写入、清理等自动化

本模块旨在提供一套高内聚、低耦合、可扩展的扩展配置管理方案，支撑大规模插件矩阵的自动化构建与运维。

## 二、设计原则

1. **声明式配置**：所有扩展配置均采用 TypeScript 类型约束，支持类型推导与 IDE 智能提示。
2. **自动化流程**：配置的生成、校验、写入、清理等全流程自动化，减少人工干预。
3. **高可维护性**：核心逻辑分层，工具函数、类型、校验、处理器职责单一，便于扩展和维护。
4. **平台适配**：支持多平台 manifest 差异，自动生成各平台/变体的配置产物。
5. **国际化友好**：内置 i18n 处理，支持多语言包合并与裁剪。

## 三、核心功能

1. **扩展配置生成**：根据 `extension.config.ts` 声明式配置，自动生成标准化的扩展配置对象。
2. **配置校验**：多层级校验（全局/变体/字段级），保证配置合法性，错误与警告分级输出。
3. **批量写入**：支持将配置、i18n、manifest 等产物批量写入到指定目录结构。
4. **缓存与清理**：配置缓存加速批量处理，支持一键清理所有变体产物。
5. **工具与类型支持**：丰富的工具函数与类型定义，便于二次开发和集成。


## 四、目录结构与职责

| 文件/目录           | 说明                         |
|---------------------|------------------------------|
| file-writer.ts      | 配置与产物写入逻辑           |
| index.ts            | 模块主入口，导出核心能力     |
| processor.ts        | 配置解析与合并核心处理器     |
| types.ts            | 类型定义与约束               |
| utils.ts            | 通用工具函数                 |
| validator.ts        | 配置校验规则与工具           |

## 五、核心流程说明

1. **配置声明与加载流程**
   - 开发者在 `packages/extensions/xxx/extension.config.ts` 中声明扩展配置（支持多变体、平台、i18n 等字段）。
   - 通过 `loadExtensionConfigs([extensionNames])` 批量加载所有扩展配置，自动缓存加速后续处理。

2. **配置解析与校验流程**
   - 使用 `defineExtensionConfig(config)` 对单个扩展配置进行解析、字段补全与合并。
   - 自动校验全局与变体级配置的合法性，输出详细错误与警告。
   - 校验内容包括：扩展名、版本、变体 ID、locale、URL、正则等字段。

3. **产物生成与写入流程**
   - 针对每个变体，调用 `generateExtensionJson(variant)` 组装完整 extension.json 内容（含 i18n、manifest）。
   - 通过 `writeExtensionFiles(extensionName, variantTarget, content)` 批量写入 extension.json、i18n.json、_locales/messages.json 等产物到 .variants 目录。

4. **缓存与清理流程**
   - 所有已加载配置对象自动缓存，提升多扩展批量处理性能。
   - 通过 `clearExtensionConfigCache([extensionNames])` 可清理指定扩展或全部缓存。
   - 使用 `cleanExtensionVariants([extensionNames])` 一键清理所有扩展的 .variants 产物目录，便于重构或重新生成。

## 六、主要接口说明

### defineExtensionConfig(config: UserExtensionConfig): ProcessedExtensionConfig
> 解析并校验扩展声明式配置，自动补全缺省字段，返回标准化配置对象。

### generateExtensionJson(variant: ProcessedVariantConfig): ExtensionJsonContent
> 组装单个变体的完整配置（含 i18n、manifest），用于产物写入。

### writeExtensionFiles(extensionName, variantTarget, content): Promise<void>
> 批量写入 extension.json、i18n.json、_locales/messages.json 等产物。

### listExtensions([extensionNames]): string[]
> 扫描所有扩展目录，返回扩展名列表，可选过滤。

### loadExtensionConfigs([extensionNames]): Promise<Record<string, Record<string, ProcessedVariantConfig>>>
> 批量加载并缓存扩展配置。

### cleanExtensionVariants([extensionNames]): Promise<void>
> 清理指定扩展的 .variants 产物目录。

## 七、使用示例

```ts
import { defineExtensionConfig, writeExtensionFiles } from 'scripts/extension-config';

const config = defineExtensionConfig({
  name: 'my-extension',
  version: '1.0.0',
  variants: [
    { webstore: 'chrome', variantType: 'release', ... },
    { webstore: 'edge', variantType: 'beta', ... },
  ],
});

// 生成并写入所有变体产物
for (const variant of config.variants) {
  const content = generateExtensionJson(variant);
  await writeExtensionFiles(config.name, variant.variantTarget, content);
}
```

## 八、常见问题 FAQ

**Q: 如何支持新的平台或变体？**
A: 在 helpers/constants.ts 中补充平台/变体类型，并在 config 中声明即可。

**Q: 配置校验报错如何排查？**
A: 校验错误会详细输出字段与原因，建议先检查类型与必填项，必要时可参考 validator.ts 规则。

**Q: 如何批量清理所有变体产物？**
A: 调用 cleanExtensionVariants()，支持传入扩展名过滤。

## 九、维护与扩展约定

1. 所有类型定义集中于 types.ts，变更需同步文档与下游依赖。
2. 工具函数需具备单元测试或用例说明。
3. 复杂流程需补充详细 JSDoc 注释与代码块注释。
4. 产物目录结构如有调整需同步通知相关团队。

---
如有更多问题或建议，请联系维护人或提交 issue。
