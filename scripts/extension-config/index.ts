/**
 * @fileoverview 插件配置管理器的公共 API 入口
 * @description 提供插件项目及渠道包（variant）配置处理的核心 API，包括配置定义、处理、生成、校验等功能。
 *
 * 术语说明：
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 插件：指 browser extension 项目。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

// #region --- 类型导出 ---

export * from './types.js';

// #endregion

// #region --- 核心 API ---

export { defineExtensionConfig } from './processor.js';

export { generateExtensionJson, writeExtensionFiles } from './file-writer.js';

export {
  validateFullExtensionConfig,
  validateFullVariantConfig,
  validateExtensionName,
  validateVersion,
  validateVariantId,
  validateLocale,
  validateUrl,
  validateRegexPattern,
} from './validator.js';

export {
  generateExtensionPaths,
  listExtensions,
  listExtensionVariants,
  loadExtensionConfigs,
  cleanExtensionVariants,
  clearExtensionConfigCache,
} from './utils.js';

// #endregion
