/**
 * @fileoverview 插件语言包（i18n）生成器
 * @description 负责生成插件项目及渠道包（variant）相关的语言包（i18n）文件内容。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import { createLogger } from '../helpers/index.js';
import type { ProcessedI18nData } from './types.js';

const logger = createLogger('I18nGenerator');
