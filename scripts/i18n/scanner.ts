/**
 * @fileoverview 插件语言包（i18n）扫描器
 * @description 负责扫描和缓存插件项目及渠道包（variant）相关的语言包（locales）文件。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import fs from 'fs-extra';
import path from 'path';
import { createLogger, projectPaths } from '../helpers/index.js';
import { findLocaleMessageFiles, getLocaleFromPath } from './utils.js';

const logger = createLogger('I18nScanner');

// #region --- 文件缓存 ---

/**
 * 语言包文件缓存，避免重复读取相同文件
 */
const localeFileCache = new Map<string, Record<string, unknown>>();

/**
 * 读取语言包文件并缓存结果
 * @param filePath 语言包文件路径
 * @returns 语言包数据对象
 */
export function readLocaleFile(filePath: string): Record<string, unknown> {
  if (localeFileCache.has(filePath)) {
    return localeFileCache.get(filePath)!;
  }

  try {
    const data = fs.readJsonSync(filePath);
    localeFileCache.set(filePath, data);
    return data;
  } catch (error) {
    logger.error(`读取或解析语言包文件失败: ${filePath}`, error);
    return {};
  }
}

/**
 * 清空语言包文件缓存
 */
export function clearLocaleFileCache(): void {
  localeFileCache.clear();
}

// #endregion

// #region --- 语言包扫描 ---

/**
 * 获取共享语言包文件列表
 */
export function getSharedLocaleFiles(): string[] {
  return findLocaleMessageFiles(projectPaths.sharedLocales);
}

/**
 * 获取指定扩展的语言包文件列表
 */
export function getExtensionLocaleFiles(extensionName: string): string[] {
  return findLocaleMessageFiles(path.join(projectPaths.extensions, extensionName, 'locales'));
}

/**
 * 获取指定扩展的所有可用语言列表
 */
export function getAvailableLocales(extensionName: string): string[] {
  const files = getExtensionLocaleFiles(extensionName);
  const locales = files
    .map(getLocaleFromPath)
    .filter((locale): locale is string => locale !== null);
  return [...new Set(locales)].sort();
}

/**
 * 合并指定语言的共享和扩展语言包
 */
export function mergeMessagesForLocale(
  locale: string,
  extensionName: string,
): Record<string, unknown> {
  const sharedFiles = getSharedLocaleFiles();
  const extensionFiles = getExtensionLocaleFiles(extensionName);

  const sharedFile = sharedFiles.find((file) => getLocaleFromPath(file) === locale);
  const extensionFile = extensionFiles.find((file) => getLocaleFromPath(file) === locale);

  const messages = [sharedFile, extensionFile]
    .filter((filePath): filePath is string => !!filePath)
    .map(readLocaleFile);

  return messages.reduce((acc, current) => ({ ...acc, ...current }), {});
}

// #endregion
