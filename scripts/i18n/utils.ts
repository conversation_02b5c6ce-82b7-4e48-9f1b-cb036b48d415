import path from 'path';
import fs from 'fs-extra';
import { projectPaths } from '../helpers/utils.js';

/**
 * 获取语言包（locales）目录的绝对路径
 * @param p - 路径参数
 * @returns 绝对路径
 */
export const getLocalesDir = (p: string): string => {
  if (p === 'locales') {
    return projectPaths.sharedLocales;
  }
  if (!/[\\/\\]+/.test(p)) {
    return path.join(projectPaths.extensions, p, 'locales');
  }
  return p;
};

/**
 * 查找指定目录下的语言包（locales）文件
 * @param localesDir - 语言包目录路径
 * @returns 语言包文件路径数组
 */
export function findLocaleMessageFiles(localesDir: string): string[] {
  if (!fs.existsSync(localesDir)) {
    return [];
  }

  const files: string[] = [];
  const entries = fs.readdirSync(localesDir);

  for (const entry of entries) {
    const fullPath = path.join(localesDir, entry);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 检查子目录中的 messages.json
      const messagesFile = path.join(fullPath, 'messages.json');
      if (fs.existsSync(messagesFile)) {
        files.push(messagesFile);
      }
    } else if (entry.endsWith('.json')) {
      // 直接的 JSON 文件
      files.push(fullPath);
    }
  }

  return files.sort();
}

/**
 * 从文件路径中提取语言代码
 * @param filePath - 语言包（locales）文件路径
 * @returns 语言代码，如果无法解析则返回 null
 */
export function getLocaleFromPath(filePath: string): string | null {
  const basename = path.basename(filePath, '.json');
  
  // 如果文件名是 messages.json，则从父目录名获取语言代码
  if (basename === 'messages') {
    const parentDir = path.basename(path.dirname(filePath));
    return parentDir || null;
  }
  
  // 如果文件名本身就是语言代码
  if (/^[a-z]{2}(_[A-Z]{2})?$/.test(basename)) {
    return basename;
  }
  
  return null;
}
