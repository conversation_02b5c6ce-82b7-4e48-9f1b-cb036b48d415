# i18n 模块 PRD 文档

## 一、背景与目标

多语言国际化（i18n）是扩展面向全球用户的基础能力。手动维护多语言包易出错、难以同步。本模块旨在自动化扫描、合并、校验和生成多语言资源，提升多扩展、多平台的国际化效率和一致性。

## 二、设计原则

1. **自动化与声明式**：支持自动扫描与声明式配置，减少人工维护。
2. **多平台兼容**：兼容 Chrome/Edge/Firefox 等平台 i18n 格式差异。
3. **类型安全**：全流程 TypeScript 类型约束。
4. **可扩展性**：便于新增语言、定制处理流程。

## 三、核心功能

1. **多语言包扫描与合并**：自动扫描扩展源码与共享目录，合并多语言资源。
2. **i18n 产物生成**：自动生成 Chrome _locales、Vue i18n、i18n.json 等产物。
3. **多语言校验**：校验多语言包完整性与一致性，输出错误与警告。
4. **类型与工具支持**：丰富的类型定义与工具函数，便于集成和二次开发。

## 四、目录结构与职责

| 文件/目录      | 说明                         |
|---------------|------------------------------|
| constants.ts  | i18n 相关常量                 |
| generator.ts  | 多语言包生成与合并逻辑         |
| index.ts      | 模块主入口，统一导出           |
| processor.ts  | i18n 处理核心流程              |
| scanner.ts    | 多语言包扫描与校验             |
| types.ts      | 类型定义与约束                 |
| utils.ts      | i18n 工具函数                  |
| tools/        | 相关辅助工具                   |

## 五、核心流程说明

1. **多语言包扫描与合并流程**
   - 通过 `scanner.ts` 自动扫描扩展源码与共享目录下的多语言资源。
   - 使用 `generator.ts` 合并多语言包，生成标准化 i18n 数据结构。

2. **i18n 产物生成流程**
   - 通过 `processor.ts` 生成 Chrome _locales、Vue i18n、i18n.json 等产物。
   - 支持多平台差异化产物输出。

3. **多语言校验流程**
   - 校验多语言包的完整性、一致性，输出缺失或冗余项警告。

## 六、主要接口说明

### scanI18nResources(options?): Promise<I18nScanResult>
> 自动扫描并返回所有多语言资源。

### generateI18nData(options): I18nData
> 合并并生成标准化多语言数据结构。

### processI18nData(options): ProcessedI18nData
> 生成各平台/产物所需的 i18n 数据。

## 七、使用示例

```ts
import { scanI18nResources, generateI18nData } from 'scripts/i18n';

const scanResult = await scanI18nResources({ extensions: ['my-extension'] });
const i18nData = generateI18nData({ scanResult });
```

## 八、常见问题 FAQ

**Q: 如何新增支持的语言？**
A: 在 shared/locales/ 目录下新增对应语言包，并在 constants.ts 中注册。

**Q: i18n 校验报错如何排查？**
A: 校验函数会详细输出缺失或冗余项，建议先检查语言包内容。

## 九、维护与扩展约定

1. 类型定义集中于 types.ts，变更需同步文档。
2. 新增语言需同步 shared/locales/ 目录与常量。
3. 目录结构如有调整需同步通知相关团队。

---
如有更多问题或建议，请联系维护人或提交 issue。
