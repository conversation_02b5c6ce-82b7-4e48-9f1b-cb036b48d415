/**
 * @fileoverview 插件语言包（i18n）处理器
 * @description 独立实现插件项目及渠道包（variant）相关的语言包（i18n）数据处理核心逻辑。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import { isEmpty } from 'lodash-es';
import {
  createLogger,
  DEFAULT_CHROME_LOCALES_ONLY,
  DEFAULT_CHROME_MESSAGES_ONLY,
} from '../helpers/index.js';
import { matchKey, parseVariantTarget } from '../helpers/utils.js';
import type { ChromeMessage } from '../helpers/types.js';
import type { I18nProcessOptions, ProcessedI18nData, LocaleMessages } from './types.js';
import { getAvailableLocales, mergeMessagesForLocale } from './scanner.js';

const logger = createLogger('I18nProcessor');

// #region --- 条件文案与占位符处理 ---

/**
 * 占位符正则表达式
 */
const PLACEHOLDER_REGEX = /\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g;

/**
 * 判断是否为条件消息
 */
function isConditionalMessage(
  value: unknown,
): value is Record<string, unknown> & { message: string } {
  if (typeof value !== 'object' || value === null || !('message' in value)) {
    return false;
  }
  const standardFields = new Set(['message', 'description', 'placeholders']);
  return Object.keys(value).some((key) => !standardFields.has(key));
}

/**
 * 解析条件消息
 * 保留现有的优先级逻辑：
 * webstore-manifestVersion-variantType > webstore-variant > webstore > message
 */
function resolveConditionalMessage(
  message: Record<string, unknown>,
  variantTarget: string,
): string {
  if (typeof message === 'string') return message;
  if (!isConditionalMessage(message)) {
    return typeof message.message === 'string' ? message.message : '';
  }

  const variantInfo = parseVariantTarget(variantTarget);
  if (message[variantTarget]) {
    return message[variantTarget] as string;
  }
  if (message[`${variantInfo.webstore}-${variantInfo.variantType}`]) {
    return message[`${variantInfo.webstore}-${variantInfo.variantType}`] as string;
  }
  if (message[variantInfo.webstore]) {
    return message[variantInfo.webstore] as string;
  }

  return typeof message.message === 'string' ? message.message : '';
}

/**
 * 格式化为 Vue i18n 格式
 * 将 $placeholder$ 格式转换为 {placeholder} 格式
 */
function formatForVue(message: string): string {
  return message.replace(PLACEHOLDER_REGEX, '{$1}');
}

/**
 * 格式化为 Chrome 格式
 * 保留现有的占位符处理逻辑
 */
function formatForChrome(message: string): ChromeMessage {
  const chromeMessage: ChromeMessage = { message };
  const placeholders: Record<string, { content: string }> = {};
  const matches = Array.from(message.matchAll(PLACEHOLDER_REGEX));

  if (matches.length > 0) {
    const seen = new Set<string>();
    let index = 1;
    for (const match of matches) {
      const placeholderName = match[1];
      if (!seen.has(placeholderName)) {
        placeholders[placeholderName] = { content: `$${index++}` };
        seen.add(placeholderName);
      }
    }
    if (!isEmpty(placeholders)) {
      chromeMessage.placeholders = placeholders;
    }
  }

  return chromeMessage;
}

// #endregion

// #region --- 主处理器 ---

/**
 * 处理 i18n 数据
 * 基于现有的 generateI18n 函数，保留所有核心算法
 */
export function processI18nData(options: I18nProcessOptions): ProcessedI18nData {
  const {
    extensionName,
    variantTarget,
    includes,
    excludes,
    chromeLocalesOnly = [],
    chromeMessagesOnly = [],
  } = options;

  // 优化：为空时设置默认值
  const effectiveChromeLocalesOnly = chromeLocalesOnly.length === 0 ? DEFAULT_CHROME_LOCALES_ONLY : chromeLocalesOnly;
  const effectiveChromeMessagesOnly = chromeMessagesOnly.length === 0 ? DEFAULT_CHROME_MESSAGES_ONLY : chromeMessagesOnly;

  logger.info(`开始处理 i18n 数据: ${extensionName} - ${variantTarget}`);

  const allLocales = getAvailableLocales(extensionName);

  const result: ProcessedI18nData = {
    locales: allLocales.filter((locale) => !effectiveChromeLocalesOnly.includes(locale)),
    vueI18nData: {},
    chromeI18nData: {},
    filterConfig: {
      includes,
      excludes,
      chromeLocalesOnly: effectiveChromeLocalesOnly,
      chromeMessagesOnly: effectiveChromeMessagesOnly,
    },
  };

  // 处理每个语言
  for (const locale of allLocales) {
    const mergedMessages = mergeMessagesForLocale(locale, extensionName);
    const vueLocaleMessages: LocaleMessages = {};
    const chromeLocaleMessages: Record<string, ChromeMessage> = {};

    // 处理每个消息
    for (const key in mergedMessages) {
      const messageValue = mergedMessages[key];
      const resolvedMsg = resolveConditionalMessage(
        messageValue as Record<string, unknown>,
        variantTarget,
      );

      // 填充 Vue 消息
      if (result.locales.includes(locale) && matchKey(key, includes, excludes)) {
        vueLocaleMessages[key] = formatForVue(resolvedMsg);
      }

      // 填充 Chrome 消息
      if (matchKey(key, effectiveChromeMessagesOnly)) {
        chromeLocaleMessages[key] = formatForChrome(resolvedMsg);
      }
    }

    result.vueI18nData[locale] = isEmpty(vueLocaleMessages) ? {} : vueLocaleMessages;
    result.chromeI18nData[locale] = isEmpty(chromeLocaleMessages) ? {} : chromeLocaleMessages;
  }

  logger.success(`i18n 数据处理完成: ${extensionName} - ${variantTarget}`);
  logger.verbose(
    `处理了 ${allLocales.length} 个语言，生成了 ${Object.keys(result.vueI18nData).length} 个 Vue 语言包和 ${Object.keys(result.chromeI18nData).length} 个 Chrome 语言包`,
  );

  return result;
}

// #endregion
