import { isEmpty } from 'lodash-es';
import {
  TRANSLATE_EXCEL_FILE_PATH,
  convertExcelToJsonPlaceholders,
  getExistingLocales,
  jsonFileExists,
  logger,
  readExcelFile,
  validateTranslationEntries,
  writeAllJsonFiles,
} from './utils.js';
import type { Excel2I18nOptions } from './types.js';

/**
 * 将 Excel 翻译文件转换为多个 i18n JSON 文件
 */
export async function excel2json(
  localesDir: string,
  options: Excel2I18nOptions = {},
): Promise<void> {
  const excelPath = TRANSLATE_EXCEL_FILE_PATH;
  const startTime = Date.now();
  let hasErrors = false;

  try {
    logger.info('开始 Excel 转 JSON 处理');

    const opts = {
      autoAddNewLocale: false,
      sheetName: 'translate',
      ...options,
    };

    // 读取 Excel 文件
    const excelData = await readExcelFile(excelPath, opts.sheetName);
    if (!excelData) {
      throw new Error('无法读取 Excel 文件');
    }

    const { header, rows } = excelData;

    // 获取现有的语言列表
    const existingLocales = await getExistingLocales(localesDir);
    const allLocales = Array.from(header.localeColumns.keys());

    logger.info(`Excel 中发现 ${allLocales.length} 种语言: ${allLocales.join(', ')}`);
    logger.info(`目标目录中现有 ${existingLocales.length} 种语言: ${existingLocales.join(', ')}`);

    // 准备翻译条目进行批量校验
    const translationEntries: Array<{
      messageKey: string;
      requirement: string;
      translation: string;
      locale: string;
      rowNumber: number;
    }> = [];

    // 收集所有翻译条目
    for (const row of rows) {
      for (const [locale, translation] of Object.entries(row.translations)) {
        if (typeof translation === 'string' && translation.trim()) {
          translationEntries.push({
            messageKey: row.messageKey,
            requirement: row.requirement,
            translation,
            locale,
            rowNumber: row.rowNumber,
          });
        }
      }
    }

    // 批量校验
    logger.info(`开始校验 ${translationEntries.length} 个翻译条目`);
    const validationResult = validateTranslationEntries(translationEntries, rows);

    // 输出校验错误和警告
    for (const invalidEntry of validationResult.invalidEntries) {
      if (invalidEntry.validationResult.error) {
        logger.error(invalidEntry.validationResult.error);
        hasErrors = true;
      } else if (invalidEntry.validationResult.warning) {
        logger.warn(invalidEntry.validationResult.warning);
      }
    }

    // 按语言组织有效的翻译数据
    const localeData: Record<string, Record<string, string>> = {};

    for (const entry of validationResult.validEntries) {
      const { locale, messageKey, translation } = entry;

      // 检查是否需要处理这个语言
      const shouldProcess =
        opts.autoAddNewLocale ||
        existingLocales.includes(locale) ||
        jsonFileExists(localesDir, locale);

      if (!shouldProcess) {
        logger.warn(`跳过语言 ${locale}：文件不存在且 autoAddNewLocale 为 false`);
        continue;
      }

      if (!localeData[locale]) {
        localeData[locale] = {};
      }

      // 转换占位符格式：Excel -> JSON
      const convertedTranslation = convertExcelToJsonPlaceholders(translation);
      localeData[locale][messageKey] = convertedTranslation;
    }

    // 转换为嵌套格式并写入文件
    const nestedLocaleData: Record<string, Record<string, unknown>> = {};
    for (const [locale, flatData] of Object.entries(localeData)) {
      nestedLocaleData[locale] = {};
      for (const [key, value] of Object.entries(flatData)) {
        const [messageKey, messageKeyFlag] = key.split('.');

        if (isEmpty(messageKeyFlag)) {
          nestedLocaleData[locale][messageKey] = {
            ...(nestedLocaleData[locale][messageKey] || {}),
            message: value,
          };
        } else {
          nestedLocaleData[locale][messageKey] = {
            ...(nestedLocaleData[locale][messageKey] || {}),
            [messageKeyFlag]: value,
          };
        }
      }
    }

    const writtenCount = await writeAllJsonFiles(localesDir, nestedLocaleData);

    const duration = Date.now() - startTime;

    // 输出处理结果
    logger.success(`处理完成：`);
    logger.info(`- 处理语言: ${Object.keys(localeData).length}`);
    logger.info(`- 写入文件: ${writtenCount}`);
    logger.info(`- 有效翻译: ${validationResult.summary.valid}`);
    logger.info(`- 跳过条目: ${validationResult.summary.invalid}`);
    logger.info(`- 耗时: ${duration}ms`);
  } catch (error) {
    logger.error(
      `Excel 转 JSON 处理失败: ${error instanceof Error ? error.message : String(error)}`,
    );
    hasErrors = true;
  }

  // 如果有错误，抛出异常
  if (hasErrors) {
    throw new Error('Excel 转 JSON 处理失败');
  }
}
