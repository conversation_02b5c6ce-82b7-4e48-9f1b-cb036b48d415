import ExcelJS from 'exceljs';
import fs from 'fs-extra';
import path from 'path';
import { Logger } from '../../helpers/logger.js';
import { projectPaths } from '../../helpers/utils.js';
import type { ExcelData, FlatMessageEntry, NestedObject } from './types.js';

Logger.setVerbose(true);
const logger = Logger.create('I18nUtils');

// 常量定义
export const TRANSLATE_EXCEL_FILE_PATH = path.resolve(
  projectPaths.scripts,
  './source-i18n-manager/messages.xlsx',
);
export const TEMPLATE_TRANSLATE_EXCEL_FILE_PATH = path.resolve(
  projectPaths.scripts,
  './source-i18n-manager/messages.template.xlsx',
);
export const TRANSLATE_SHEET_NAME = 'translate';
export const TEMPLATE_COLUMNS = [
  'message key',
  'Group Notes',
  'Notes',
  'Requirement',
  'am',
  'ar',
  'bg',
  'bn',
  'ca',
  'cs',
  'da',
  'de',
  'el',
  'en',
  'en_GB',
  'en_US',
  'es',
  'es_419',
  'et',
  'fa',
  'fi',
  'fil',
  'fr',
  'gu',
  'he',
  'hi',
  'hr',
  'hu',
  'id',
  'it',
  'ja',
  'kn',
  'ko',
  'lt',
  'lv',
  'ml',
  'mr',
  'ms',
  'my',
  'ne',
  'nl',
  'no',
  'pl',
  'pt_BR',
  'pt_PT',
  'ro',
  'ru',
  'si',
  'sk',
  'sl',
  'sr',
  'sv',
  'sw',
  'ta',
  'te',
  'th',
  'tr',
  'uk',
  'vi',
  'zh_CN',
  'zh_TW',
];

// 占位符相关工具函数
export function extractPlaceholders(text: string): string[] {
  if (typeof text !== 'string') return [];
  const matches = text.match(/\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g);
  return matches ? matches.map((m) => m.slice(1, -1)) : [];
}

export function convertExcelToJsonPlaceholders(text: string): string {
  if (typeof text !== 'string') return '';
  return text.replace(/\$PLACEHOLDER_([a-zA-Z_][a-zA-Z0-9_]*)\$/g, '$$$1$$');
}

export function convertJsonToExcelPlaceholders(text: string): string {
  if (typeof text !== 'string') return '';
  return text.replace(/\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g, `$PLACEHOLDER_$1$`);
}

// 文件系统工具函数
export async function getExistingLocales(localesDir: string): Promise<string[]> {
  try {
    if (!(await fs.pathExists(localesDir))) {
      return [];
    }
    const files = await fs.readdir(localesDir);
    return files
      .filter((file) => file.endsWith('.json'))
      .map((file) => path.basename(file, '.json'))
      .sort();
  } catch (error) {
    return [];
  }
}

export async function readJson(filePath: string): Promise<NestedObject | null> {
  try {
    if (!(await fs.pathExists(filePath))) {
      return null;
    }
    return await fs.readJson(filePath);
  } catch (error) {
    return null;
  }
}

export async function writeJson(filePath: string, data: NestedObject): Promise<boolean> {
  try {
    await fs.ensureDir(path.dirname(filePath));
    await fs.writeJson(filePath, data, { spaces: 2 });
    return true;
  } catch (error) {
    return false;
  }
}

export async function backupFile(filePath: string, suffix = '.bak'): Promise<boolean> {
  try {
    if (!(await fs.pathExists(filePath))) {
      return false;
    }
    const backupPath = `${filePath}${suffix}`;
    await fs.copy(filePath, backupPath);
    return true;
  } catch (error) {
    return false;
  }
}

export function jsonFileExists(localesDir: string, locale: string): boolean {
  const filePath = path.join(localesDir, `${locale}.json`);
  return fs.pathExistsSync(filePath);
}

// 键名过滤工具函数
function matchesPatterns(key: string, patterns: string[]): boolean {
  if (patterns.length === 0) return false;
  return patterns.some((pattern) => {
    try {
      const regex = new RegExp(pattern);
      return regex.test(key);
    } catch {
      return key === pattern;
    }
  });
}

export function filterMessageKeys(
  baseKeys: string[],
  includeKeys: string[] = [],
  excludeKeys: string[] = [],
): string[] {
  let filteredKeys = baseKeys;
  if (includeKeys.length > 0) {
    filteredKeys = filteredKeys.filter((key) => matchesPatterns(key, includeKeys));
  }
  if (excludeKeys.length > 0) {
    filteredKeys = filteredKeys.filter((key) => !matchesPatterns(key, excludeKeys));
  }
  return filteredKeys;
}

// 数据处理工具函数
export function collectKeys(obj: Record<string, unknown>, prefix: string, keys: Set<string>): void {
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      collectKeys(value as Record<string, unknown>, fullKey, keys);
    } else if (typeof value === 'string') {
      keys.add(fullKey);
    }
  }
}

export function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
  const keys = path.split('.');
  let current: unknown = obj;
  for (const key of keys) {
    if (typeof current === 'object' && current !== null && key in current) {
      current = (current as Record<string, unknown>)[key];
    } else {
      return undefined;
    }
  }
  return current;
}

export function getBaseKeys(flatEntries: FlatMessageEntry[]): string[] {
  const baseKeys = new Set<string>();
  for (const entry of flatEntries) {
    baseKeys.add(entry.baseKey);
  }
  return Array.from(baseKeys).sort();
}

export function convertToFlatEntries(
  localeData: Record<string, Record<string, unknown>>,
): FlatMessageEntry[] {
  const flatEntries: FlatMessageEntry[] = [];
  const allKeys = new Set<string>();

  // 收集所有可能的 key
  for (const data of Object.values(localeData)) {
    collectKeys(data, '', allKeys);
  }

  // 为每个 key 创建条目
  for (const key of allKeys) {
    const translations: Record<string, string> = {};

    for (const [locale, data] of Object.entries(localeData)) {
      const value = getNestedValue(data, key);
      if (typeof value === 'string') {
        translations[locale] = value;
      }
    }

    flatEntries.push({
      flatKey: /\.message$/.test(key) ? key.replace(/\.message$/, '') : key,
      baseKey: key.split('.')[0],
      translations,
    });
  }

  return flatEntries;
}

// 校验相关函数
function validateTranslationEntry(entry: {
  messageKey: string;
  translation: string;
  locale: string;
  rowNumber: number;
}): { valid: boolean; error?: string; warning?: string } {
  if (!entry.messageKey.trim()) {
    return { valid: false, error: `第 ${entry.rowNumber} 行：message key 不能为空` };
  }
  if (!entry.translation.trim()) {
    return { valid: false, warning: `第 ${entry.rowNumber} 行：${entry.locale} 语言的翻译为空` };
  }

  // 特定字段的长度校验
  if (entry.messageKey === 'EXTENSION_NAME' && entry.translation.length > 75) {
    return {
      valid: false,
      error: `第 ${entry.rowNumber} 行：EXTENSION_NAME 长度超限: ${entry.translation.length}/75 字符 - "${entry.translation}"`,
    };
  }
  if (entry.messageKey === 'EXTENSION_DESCRIPTION' && entry.translation.length > 132) {
    return {
      valid: false,
      error: `第 ${entry.rowNumber} 行：EXTENSION_DESCRIPTION 长度超限: ${entry.translation.length}/132 字符 - "${entry.translation}"`,
    };
  }

  return { valid: true };
}

function validatePlaceholders(
  translation: string,
  source: string,
  locale: string,
  messageKey: string,
): { valid: boolean; error?: string; warning?: string } {
  const srcPlaceholders = extractPlaceholders(convertExcelToJsonPlaceholders(source));
  const tgtPlaceholders = extractPlaceholders(convertExcelToJsonPlaceholders(translation));
  const srcSet = new Set(srcPlaceholders);
  const tgtSet = new Set(tgtPlaceholders);
  if (
    srcSet.size !== tgtSet.size ||
    srcPlaceholders.sort().join(',') !== tgtPlaceholders.sort().join(',')
  ) {
    return {
      valid: false,
      error: `占位符不一致: key=${messageKey}, ${source}=${JSON.stringify(srcPlaceholders)}, ${locale}=${JSON.stringify(tgtPlaceholders)}`,
    };
  }
  return { valid: true };
}

export function validateTranslationEntries(
  entries: Array<{
    messageKey: string;
    requirement: string;
    translation: string;
    locale: string;
    rowNumber: number;
  }>,
  allRows?: Array<{
    messageKey: string;
    requirement: string;
    translations: Record<string, string>;
  }>,
) {
  const validEntries: typeof entries = [];
  const invalidEntries: Array<
    { validationResult: ReturnType<typeof validateTranslationEntry> } & (typeof entries)[0]
  > = [];
  let errors = 0;
  let warnings = 0;
  for (const entry of entries) {
    let validationResult = { valid: true, error: '', warning: '' } as ReturnType<
      typeof validateTranslationEntry
    >;

    // 译文非空校验
    validationResult = validateTranslationEntry(entry);

    // 占位符一致性校验
    if (validationResult.valid && allRows) {
      const source = allRows.find((r) => r.messageKey === entry.messageKey)?.requirement || '';
      const placeholderResult = validatePlaceholders(
        entry.translation,
        source,
        entry.locale,
        entry.messageKey,
      );
      if (!placeholderResult.valid) {
        validationResult = placeholderResult;
      }
    }

    if (validationResult.valid) {
      validEntries.push(entry);
    } else {
      invalidEntries.push({ ...entry, validationResult });
      if (validationResult.error) errors++;
      if (validationResult.warning) warnings++;
    }
  }
  return {
    validEntries,
    invalidEntries,
    summary: {
      valid: validEntries.length,
      invalid: invalidEntries.length,
      errors,
      warnings,
    },
  };
}

// Excel 相关函数
export async function readExcelFile(
  excelPath: string,
  sheetName: string,
): Promise<ExcelData | null> {
  try {
    if (!(await fs.pathExists(excelPath))) {
      logger.error(`Excel 文件不存在: ${excelPath}`);
      return null;
    }

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(excelPath);

    const worksheet = workbook.getWorksheet(sheetName);
    if (!worksheet) {
      logger.error(`工作表 "${sheetName}" 不存在`);
      return null;
    }

    const rows: ExcelData['rows'] = [];
    const header: ExcelData['header'] = {
      messageKeyColumn: -1,
      requirementColumn: -1,
      localeColumns: new Map(),
    };

    // 解析表头（严格按照 TEMPLATE_COLUMNS 顺序）
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((_cell, colNumber) => {
      const templateCol = TEMPLATE_COLUMNS[colNumber - 1];
      if (!templateCol) return;
      if (templateCol === 'message key') {
        header.messageKeyColumn = colNumber;
      } else if (templateCol === 'Requirement') {
        header.requirementColumn = colNumber;
      } else if (
        templateCol !== 'Group Notes' &&
        templateCol !== 'Notes' &&
        templateCol.length >= 2
      ) {
        header.localeColumns.set(templateCol, colNumber);
      }
    });

    if (header.messageKeyColumn === -1) {
      logger.error('未找到 "message key" 列');
      return null;
    }

    // 解析数据行
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // 跳过表头

      const messageKey = String(row.getCell(header.messageKeyColumn).value || '').trim();
      if (!messageKey) return; // 跳过空行

      const requirement = String(row.getCell(header.requirementColumn).value || '').trim();

      const translations: Record<string, string> = {};
      for (const [locale, colIndex] of header.localeColumns) {
        const translation = String(row.getCell(colIndex).value || '').trim();
        translations[locale] = translation;
      }

      rows.push({
        messageKey,
        requirement,
        translations,
        rowNumber,
      });
    });

    return { header, rows };
  } catch (error) {
    logger.error(`读取 Excel 文件失败: ${error instanceof Error ? error.message : String(error)}`);
    return null;
  }
}

export async function writeExcelFileFromTemplate(
  templatePath: string,
  templateSheetName: string,
  outputPath: string,
  data: { headers: string[]; rows: (string | number)[][] },
  sheetName: string,
  duplicateSheetAction: 'overwrite' | 'append',
): Promise<boolean> {
  try {
    // 读取模板文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(templatePath);

    const templateSheet = workbook.getWorksheet(templateSheetName);
    if (!templateSheet) {
      logger.error(`模板工作表 "${templateSheetName}" 不存在`);
      return false;
    }

    // 检查目标工作表是否存在
    let targetSheet = workbook.getWorksheet(sheetName);
    if (targetSheet) {
      if (duplicateSheetAction === 'overwrite') {
        workbook.removeWorksheet(targetSheet.id);
        targetSheet = undefined;
      } else {
        // append 模式，使用唯一名称
        let counter = 1;
        let newSheetName = `${sheetName}_${counter}`;
        while (workbook.getWorksheet(newSheetName)) {
          counter++;
          newSheetName = `${sheetName}_${counter}`;
        }
        sheetName = newSheetName;
      }
    }

    // 复制模板工作表
    if (!targetSheet) {
      targetSheet = workbook.addWorksheet(sheetName);

      // 复制模板的表头和格式
      templateSheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) {
          // 复制表头
          const newRow = targetSheet!.getRow(rowNumber);
          row.eachCell((cell, colNumber) => {
            newRow.getCell(colNumber).value = cell.value;
            // 复制样式
            newRow.getCell(colNumber).style = { ...cell.style };
          });
          return;
        }
      });
    }

    // 写入数据行
    data.rows.forEach((rowData, index) => {
      const rowNumber = index + 2; // 从第二行开始（第一行是表头）
      const row = targetSheet!.getRow(rowNumber);

      rowData.forEach((cellValue, colIndex) => {
        row.getCell(colIndex + 1).value = cellValue;
      });
    });

    // 保存文件
    await workbook.xlsx.writeFile(outputPath);
    return true;
  } catch (error) {
    logger.error(`写入 Excel 文件失败: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

// JSON 相关函数
export async function readAllJsonFiles(
  localesDir: string,
): Promise<Record<string, Record<string, unknown>>> {
  const localeData: Record<string, Record<string, unknown>> = {};

  try {
    if (!(await fs.pathExists(localesDir))) {
      logger.warn(`语言包目录不存在: ${localesDir}`);
      return localeData;
    }

    const locales = await getExistingLocales(localesDir);

    for (const locale of locales) {
      const filePath = path.join(localesDir, `${locale}.json`);
      const data = await readJson(filePath);

      if (data) {
        localeData[locale] = data;
        logger.verbose(`读取文件: ${path.relative(projectPaths.workspace, filePath)}`);
      } else {
        logger.warn(`读取文件失败: ${path.relative(projectPaths.workspace, filePath)}`);
      }
    }
  } catch (error) {
    logger.error(`读取语言包目录失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  return localeData;
}

export async function writeAllJsonFiles(
  localesDir: string,
  nestedLocaleData: Record<string, Record<string, unknown>>,
): Promise<number> {
  let writtenCount = 0;

  try {
    await fs.ensureDir(localesDir);

    for (const [locale, data] of Object.entries(nestedLocaleData)) {
      const filePath = path.join(localesDir, `${locale}.json`);
      const oldData = (await readJson(filePath)) || {};
      const mergedData = { ...oldData, ...data };

      // 对合并后的数据进行排序，EXTENSION_NAME 和 EXTENSION_DESCRIPTION 置顶
      const sortedData = sortJsonData(mergedData);

      if (await writeJson(filePath, sortedData)) {
        writtenCount++;
        logger.verbose(`写入文件: ${path.relative(projectPaths.workspace, filePath)}`);
      }
    }
  } catch (error) {
    logger.error(`写入 JSON 文件失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  return writtenCount;
}

// Excel 模板构建函数
export function buildRowFromTemplate(
  entry: FlatMessageEntry,
  sourceLocale: string,
  localeData: Record<string, Record<string, unknown>>,
): (string | number)[] {
  const row: (string | number)[] = [];

  for (const column of TEMPLATE_COLUMNS) {
    switch (column) {
      case 'message key':
        row.push(entry.flatKey);
        break;
      case 'Group Notes':
      case 'Notes':
        row.push(''); // 空值
        break;
      case 'Requirement': {
        const requirementText = getRequirementText(entry, sourceLocale, localeData);
        row.push(convertJsonToExcelPlaceholders(requirementText));
        break;
      }
      default: {
        // 语言列
        const translation = entry.translations[column] || '';
        const convertedTranslation = translation ? convertJsonToExcelPlaceholders(translation) : '';
        row.push(convertedTranslation);
        break;
      }
    }
  }

  return row;
}

function getRequirementText(
  entry: FlatMessageEntry,
  sourceLocale: string,
  localeData: Record<string, Record<string, unknown>>,
): string {
  // 首先尝试从当前条目获取源语言翻译
  if (entry.translations[sourceLocale]) {
    return entry.translations[sourceLocale];
  }

  // 如果当前条目没有源语言翻译，尝试从基础条目获取
  if (entry.conditionPath) {
    const baseEntry = Object.values(localeData[sourceLocale] || {}).find((value: unknown) => {
      if (typeof value === 'object' && value !== null && 'message' in value) {
        return true;
      }
      return false;
    });

    if (
      baseEntry &&
      typeof baseEntry === 'object' &&
      baseEntry !== null &&
      'message' in baseEntry
    ) {
      return (baseEntry as { message?: string }).message || '';
    }
  }

  // 最后尝试使用任何可用的翻译
  const availableTranslations = Object.values(entry.translations).filter(
    (v): v is string => typeof v === 'string' && Boolean(v),
  );
  if (availableTranslations.length > 0) {
    return availableTranslations[0];
  }

  return '';
}

function sortJsonData(data: Record<string, unknown>): Record<string, unknown> {
  const sortedData: Record<string, unknown> = {};

  // 优先添加 EXTENSION_NAME 和 EXTENSION_DESCRIPTION
  if (data.EXTENSION_NAME !== undefined) {
    sortedData.EXTENSION_NAME = data.EXTENSION_NAME;
  }
  if (data.EXTENSION_DESCRIPTION !== undefined) {
    sortedData.EXTENSION_DESCRIPTION = data.EXTENSION_DESCRIPTION;
  }

  // 添加其他字段，按字母顺序排序
  const otherKeys = Object.keys(data)
    .filter((key) => key !== 'EXTENSION_NAME' && key !== 'EXTENSION_DESCRIPTION')
    .sort();

  for (const key of otherKeys) {
    sortedData[key] = data[key];
  }

  return sortedData;
}

export { logger };
