/**
 * i18n 管理工具主入口文件
 *
 * 提供 Excel 和 JSON 语言包转换、批量更新等功能
 */

import { getLocalesDir } from '../utils.js';
import { excel2json } from './excel2json.js';
import { json2excel } from './json2excel.js';
import { updateI18n, commonTransforms } from './updateI18n.js';

/**
 * 主函数 - 执行 i18n 工具操作
 */
// excel 转换为 i18n
async function excel2jsonMain() {
  await excel2json(getLocalesDir('locales'), {
    autoAddNewLocale: false, // 是否自动添加新语言
    sheetName: 'translate-2025-06-30', // Excel 工作表名称
  });
}

// i18n 转换为 excel
async function json2excelMain() {
  await json2excel(getLocalesDir('locales'), {
    sourceLocale: 'en',
    includeKeys: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', 'context_menu_screenshot_search'],
    excludeKeys: [],
    duplicateSheetAction: 'overwrite',
  });
}

// 批量更新 i18n 文件
async function updateI18nMain() {
  await updateI18n(getLocalesDir('locales'), (key, value, locale) => {
    // return commonTransforms.updateValue('EXTENSION_NAME', '')(key, value, locale);
    return [key, value];
  });
}

// excel2jsonMain();
// json2excelMain();
// updateI18nMain();
