import fs from 'fs-extra';
import path from 'path';
import {
  TRANSLATE_EXCEL_FILE_PATH,
  TEMPLATE_TRANSLATE_EXCEL_FILE_PATH,
  buildRowFromTemplate,
  convertToFlatEntries,
  filterMessageKeys,
  getBaseKeys,
  logger,
  readAllJsonFiles,
  writeExcelFileFromTemplate,
} from './utils.js';
import type { I18n2ExcelOptions } from './types.js';

/**
 * 将多个 i18n JSON 文件聚合到一个 Excel 文件中
 */
export async function json2excel(
  localesDir: string,
  options: I18n2ExcelOptions = {},
): Promise<void> {
  const startTime = Date.now();
  let hasErrors = false;

  try {
    logger.info('开始 JSON 转 Excel 处理');

    // 检查目标 Excel 文件是否存在，如果不存在则从模板复制
    if (!(await fs.pathExists(TRANSLATE_EXCEL_FILE_PATH))) {
      logger.info(
        `目标 Excel 文件不存在，从模板复制: ${TEMPLATE_TRANSLATE_EXCEL_FILE_PATH} -> ${TRANSLATE_EXCEL_FILE_PATH}`,
      );

      if (!(await fs.pathExists(TEMPLATE_TRANSLATE_EXCEL_FILE_PATH))) {
        throw new Error(`模板 Excel 文件不存在: ${TEMPLATE_TRANSLATE_EXCEL_FILE_PATH}`);
      }

      // 确保目标目录存在
      await fs.ensureDir(path.dirname(TRANSLATE_EXCEL_FILE_PATH));

      // 复制模板文件
      await fs.copy(TEMPLATE_TRANSLATE_EXCEL_FILE_PATH, TRANSLATE_EXCEL_FILE_PATH);
      logger.success(`已从模板复制 Excel 文件`);
    }

    const excelPath = TRANSLATE_EXCEL_FILE_PATH;

    // 设置默认选项
    const opts = {
      sourceLocale: 'en',
      sheetName: `translate-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`,
      includeKeys: [],
      excludeKeys: [],
      duplicateSheetAction: 'overwrite' as const,
      ...options,
    };

    // 读取所有 JSON 文件
    const localeData = await readAllJsonFiles(localesDir);
    const locales = Object.keys(localeData).sort();

    if (locales.length === 0) {
      throw new Error('没有找到有效的 JSON 语言包文件');
    }

    logger.info(`发现 ${locales.length} 种语言: ${locales.join(', ')}`);

    // 转换为扁平化条目
    const flatEntries = convertToFlatEntries(localeData);
    const allBaseKeys = getBaseKeys(flatEntries);

    // 应用 message key 过滤
    const filteredBaseKeys = filterMessageKeys(allBaseKeys, opts.includeKeys, opts.excludeKeys);

    logger.info(`提取到 ${flatEntries.length} 个文案，${allBaseKeys.length} 个基础 key`);
    if (opts.includeKeys.length > 0 || opts.excludeKeys.length > 0) {
      logger.info(`过滤后保留 ${filteredBaseKeys.length} 个基础 key`);
    }

    // 构建数据行
    const rows: (string | number)[][] = [];

    // 按基础 key 分组处理（只处理过滤后的 keys）
    for (const baseKey of filteredBaseKeys) {
      const relatedEntries = flatEntries.filter((entry) => entry.baseKey === baseKey);

      // 按条件路径排序：基础消息在前，然后按字母顺序
      relatedEntries.sort((a, b) => {
        if (!a.conditionPath && !b.conditionPath) return 0;
        if (!a.conditionPath) return -1;
        if (!b.conditionPath) return 1;
        return a.conditionPath.localeCompare(b.conditionPath);
      });

      for (const entry of relatedEntries) {
        const row = buildRowFromTemplate(entry, opts.sourceLocale, localeData);
        rows.push(row);
      }
    }

    // 使用模板文件写入 Excel 文件
    const success = await writeExcelFileFromTemplate(
      excelPath,
      'template',
      excelPath,
      { headers: [], rows }, // headers 会从模板复制，这里传空数组
      opts.sheetName,
      opts.duplicateSheetAction,
    );

    if (!success) {
      throw new Error('写入 Excel 文件失败');
    }

    const duration = Date.now() - startTime;

    // 输出处理结果
    logger.success(`处理完成：`);
    logger.info(`- 处理语言: ${locales.length}`);
    logger.info(`- 文案: ${flatEntries.length}`);
    logger.info(`- Excel 行数: ${rows.length}`);
    logger.info(`- 输出文件: ${excelPath}`);
    logger.info(`- 耗时: ${duration}ms`);
  } catch (error) {
    logger.error(
      `JSON 转 Excel 处理失败: ${error instanceof Error ? error.message : String(error)}`,
    );
    hasErrors = true;
  }

  // 如果有错误，抛出异常
  if (hasErrors) {
    throw new Error('JSON 转 Excel 处理失败');
  }
}
