# i18n/tools 子模块文档

## 一、定位与目标

i18n/tools 目录用于沉淀和维护多语言相关的辅助工具脚本、批处理脚本、迁移工具等，支撑多扩展、多平台的国际化自动化运维和批量处理需求。

## 二、适用场景

- 多语言包的批量生成、格式转换、内容校验
- 语言包迁移、合并、去重、同步
- 语言包内容的统计、分析、自动补全
- 其他与 i18n 相关的自动化脚本

## 三、目录结构与职责

| 文件/目录      | 说明                         |
|---------------|------------------------------|
| xxx-tool.ts   | 具体工具脚本（如批量合并等）  |
| ...           | 其他辅助工具                  |

> 注：每个工具脚本建议包含命令行参数说明、使用示例、输入输出格式说明。

## 四、开发与维护约定

1. 所有工具脚本需补充详细注释和用法说明。
2. 推荐支持命令行参数，便于自动化集成。
3. 工具脚本如有通用逻辑可抽取为 utils 复用。
4. 新增工具需同步更新本 README 文档。

## 五、使用示例

```sh
# 以 node 方式运行批量合并工具
node scripts/i18n/tools/merge-locales-tool.js --src shared/locales --out merged.json
```

---
如有更多问题或建议，请联系维护人或提交 issue。
