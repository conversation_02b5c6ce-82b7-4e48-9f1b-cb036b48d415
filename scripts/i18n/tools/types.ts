/**
 * i18n 管理工具的公共类型定义
 *
 * 包含了 Excel 和 JSON 语言包转换、批量更新等功能所需的类型定义
 */

/**
 * 嵌套对象类型，用于表示 JSON 数据结构
 */
export type NestedObject = Record<string, unknown>;

/**
 * 转换函数类型，用于 updateI18n 批量更新操作
 *
 * 转换函数接收一个键值对，返回新的键值对或 null（表示删除）
 *
 * @param key - 消息键名
 * @param value - 消息值
 * @param locale - 语言代码（可选）
 * @returns 转换后的键值对 [newKey, newValue]，或 null/undefined 表示删除该键
 */
export type TransformFunction = (
  key: string,
  value: unknown,
  locale?: string,
) => [string, unknown] | null | undefined;

/**
 * updateI18n 函数的选项配置
 * @param includeLocales - 包含的语言列表，为空表示包含所有语言
 * @param excludeLocales - 排除的语言列表
 * @param backup - 是否备份原文件，默认为 false
 * @param backupSuffix - 备份文件后缀，默认为 '.bak'
 */
export interface UpdateI18nOptions {
  /** 包含的语言列表，为空表示包含所有语言 */
  includeLocales?: string[];
  /** 排除的语言列表 */
  excludeLocales?: string[];
  /** 是否备份原文件，默认为 false */
  backup?: boolean;
  /** 备份文件后缀，默认为 '.bak' */
  backupSuffix?: string;
}

/**
 * Excel 转 i18n JSON 的选项配置
 * @param autoAddNewLocale - 是否自动添加新语言文件，默认为 false
 * @param sheetName - Excel 工作表名称，默认为 'translate'
 */
export interface Excel2I18nOptions {
  /** 是否自动添加新语言文件，默认为 false */
  autoAddNewLocale?: boolean;
  /** Excel 工作表名称，默认为 'translate' */
  sheetName?: string;
}

/**
 * i18n JSON 转 Excel 的选项配置
 * @param sourceLocale - 源语言代码，用于 Requirement 列，默认为 'en'
 * @param sheetName - Excel 工作表名称，默认为 'translate-{YYYYMMDD
 * @param includeKeys - 包含的消息键模式列表，为空表示包含所有
 * @param excludeKeys - 排除的消息键模式列表
 * @param duplicateSheetAction - 工作表重名时的处理方式：覆盖或追加
 *   默认为 'overwrite'
 */
export interface I18n2ExcelOptions {
  /** 源语言代码，用于 Requirement 列，默认为 'en' */
  sourceLocale?: string;
  /** Excel 工作表名称，默认为 'translate-{YYYYMMDD}' */
  sheetName?: string;
  /** 包含的消息键模式列表，为空表示包含所有 */
  includeKeys?: string[];
  /** 排除的消息键模式列表 */
  excludeKeys?: string[];
  /** 工作表重名时的处理方式：覆盖或追加，默认为 'overwrite' */
  duplicateSheetAction?: 'overwrite' | 'append';
}

/**
 * 扁平化的消息条目，用于 Excel 和 JSON 之间的转换
 * 包含了消息键名、各语言的翻译文本等信息
 * @field flatKey - 扁平化的键名（去除 .message 后缀）
 * @field baseKey - 基础键名（第一级键名）
 * @field conditionPath - 条件路径（用于变体消息）
 * @field translations - 各语言的翻译文本
 */
export interface FlatMessageEntry {
  /** 扁平化的键名（去除 .message 后缀） */
  flatKey: string;
  /** 基础键名（第一级键名） */
  baseKey: string;
  /** 条件路径（用于变体消息） */
  conditionPath?: string;
  /** 各语言的翻译文本 */
  translations: Record<string, string>;
}

/**
 * Excel 文件解析后的数据结构
 * 包含表头信息和数据行列表
 * @field header - 表头信息，包括消息键列、需求列和语言列索
 * @field header.messageKeyColumn - 消息键列的索引
 * @field header.requirementColumn - 需求列的索引
 * @field header.localeColumns - 语言列的映射表：语言代码 -> 列索引
 *   用于快速查找各语言的翻译文本所在列
 * @field rows - 数据行列表，每行包含消息键名、需求文本和各语言的翻译文本
 * @field rows[].messageKey - 消息键名
 * @field rows[].requirement - 需求文本
 * @field rows[].translations - 各语言的翻译文本
 * @field rows[].rowNumber - Excel 行号
 */
export interface ExcelData {
  /** 表头信息 */
  header: {
    /** 消息键列的索引 */
    messageKeyColumn: number;
    /** 需求列的索引 */
    requirementColumn: number;
    /** 语言列的映射表：语言代码 -> 列索引 */
    localeColumns: Map<string, number>;
  };
  /** 数据行列表 */
  rows: Array<{
    /** 消息键名 */
    messageKey: string;
    /** 需求文本 */
    requirement: string;
    /** 各语言的翻译文本 */
    translations: Record<string, string>;
    /** Excel 行号 */
    rowNumber: number;
  }>;
}
