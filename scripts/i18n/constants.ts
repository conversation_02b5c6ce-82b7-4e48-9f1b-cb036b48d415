/**
 * @fileoverview 插件语言包（i18n）专用常量
 * @description 定义插件项目及渠道包（variant）相关的语言包（i18n）处理专用常量。
 */

// 重新导出通用常量，避免跨模块依赖
export {
  DEFAULT_CHROME_LOCALES_ONLY,
  DEFAULT_CHROME_MESSAGES_ONLY,
  SUPPORTED_LOCALES,
} from '../helpers/constants.js';

/**
 * 占位符正则表达式，用于 i18n 消息中的占位符处理
 */
export const PLACEHOLDER_REGEX = /\$\{(\w+)\}/g;

/**
 * 条件消息前缀，用于标识需要特殊处理的消息
 */
export const CONDITIONAL_MESSAGE_PREFIX = '@conditional:';

/**
 * 语言包文件缓存的默认过期时间 (毫秒)
 */
export const LOCALE_FILE_CACHE_TTL = 5 * 60 * 1000; // 5分钟

/**
 * 支持的语言包文件格式
 */
export const SUPPORTED_LOCALE_FILE_FORMATS = ['.json'] as const;
