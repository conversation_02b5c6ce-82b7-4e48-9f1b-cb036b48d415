/**
 * @fileoverview 插件语言包（i18n）模块统一导出
 * @description 提供插件项目及渠道包（variant）相关的语言包（i18n）API 接口。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

// 类型定义
export type {
  LocaleMessages,
  I18nProcessOptions,
  ProcessedI18nData,
  ChromeMessage,
} from './types.js';

// 常量定义
export {
  DEFAULT_CHROME_LOCALES_ONLY,
  DEFAULT_CHROME_MESSAGES_ONLY,
  SUPPORTED_LOCALES,
  PLACEHOLDER_REGEX,
  CONDITIONAL_MESSAGE_PREFIX,
  LOCALE_FILE_CACHE_TTL,
  SUPPORTED_LOCALE_FILE_FORMATS,
} from './constants.js';

// 语言包扫描
export {
  readLocaleFile,
  clearLocaleFileCache,
  getSharedLocaleFiles,
  getExtensionLocaleFiles,
  getAvailableLocales,
  mergeMessagesForLocale,
} from './scanner.js';

// 核心处理器
export {
  processI18nData,
} from './processor.js';


// 工具函数
export {
  getLocalesDir,
  findLocaleMessageFiles,
  getLocaleFromPath,
} from './utils.js';
