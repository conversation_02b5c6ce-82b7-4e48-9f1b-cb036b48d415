/**
 * @fileoverview 插件语言包（i18n）类型定义
 * @description 定义插件项目及渠道包（variant）相关的语言包（i18n）处理类型接口。
 *
 * 术语说明：
 * - 插件：指 browser extension 项目。
 * - 渠道包（variant/variants）：指插件的不同分发渠道或构建包。
 * - 插件名字（extension name）：指插件项目的名称。
 * - 语言包（locales）：指多语言资源包。
 * - 文案（message key）：指语言包中的文案 key。
 */

import type { ChromeMessage, LocaleMessages } from '../helpers/types.js';

// 重新导出通用类型，避免重复定义
export type { ChromeMessage, LocaleMessages };

// #region --- 基础类型 ---

/**
 * i18n 处理选项
 */
export interface I18nProcessOptions {
  /**
   * 插件名称
   */
  extensionName: string;

  /**
   * variant 目标标识
   */
  variantTarget: string;

  /**
   * 包含的文案 key (支持正则字符串)
   */
  includes?: string[];

  /**
   * 排除的文案 key (支持正则字符串)
   */
  excludes?: string[];

  /**
   * 仅生成到 Chrome 的语言列表
   */
  chromeLocalesOnly?: string[];

  /**
   * 仅包含在 Chrome 中的文案 key (支持正则字符串)
   */
  chromeMessagesOnly?: string[];
}

/**
 * 处理后的 i18n 数据
 */
export interface ProcessedI18nData {
  /**
   * 所有可用的语言列表
   */
  locales: string[];

  /**
   * Vue i18n 格式的语言包数据
   */
  vueI18nData: Record<string, LocaleMessages>;

  /**
   * Chrome 格式的语言包数据
   */
  chromeI18nData: Record<string, Record<string, ChromeMessage>>;

  /**
   * 过滤配置（用于参考）
   */
  filterConfig: {
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[];
    chromeMessagesOnly: string[];
  };
}

// #endregion
