# 浏览器扩展开发脚手架

本项目提供了一个完整的浏览器扩展开发脚手架，支持多浏览器、多渠道、多语言的扩展开发与发布。通过统一的配置管理和自动化构建流程，简化扩展开发的复杂性。

## 核心特性

*   🚀 **统一配置管理**：单一配置文件支持多个渠道包的配置
*   🌍 **多语言支持**：自动处理语言包，支持条件化文案
*   ⚡ **智能缓存**：开发模式下的智能缓存策略，提高构建效率
*   🔧 **开发友好**：热重载、详细日志、类型安全
*   📦 **生产就绪**：完整的构建和发布流程

## 核心概念

*   **扩展 (Extension)**：项目中的一个独立浏览器扩展
*   **变体 (Variant)**：扩展的一个特定版本，定义其渠道特性
*   **渠道包 (Variant Target)**：唯一标识，格式为 `{webstore}-{mv}-{variant}` (例如 `chrome-mv3-master`)
*   **源配置文件 (extension.config.ts)**：开发者维护的唯一配置源文件
*   **最终配置文件 (extension.config.json)**：为每个渠道包生成的最终配置产物
*   **语言包 (locales)**：多语言资源文件，支持共享和扩展专属

## 快速开始

### 1. 安装依赖
```powershell
pnpm install
```

### 2. 查看可用扩展
```powershell
# 查看所有扩展目录
ls packages/extensions/

# 或使用内置工具查看详细信息
tsx scripts/extension-config-manager/cli.ts list --verbose
```

### 3. 启动开发环境
```powershell
# 开发指定扩展的渠道包（必须指定扩展名和渠道包）
pnpm dev cookies_manager chrome-mv3-master

# 显示详细输出
pnpm dev cookies_manager chrome-mv3-master --verbose

# 强制重新生成配置
pnpm dev cookies_manager chrome-mv3-master --no-cache
```

### 4. 生产构建
```powershell
# 构建所有配置的扩展
pnpm build

# 构建指定扩展
pnpm build --extension cookies_manager

# 显示详细构建信息
pnpm build --verbose
```

## 项目结构

```
├── packages/
│   ├── extensions/          # 扩展源码目录
│   │   └── {扩展名}/
│   │       ├── extension.config.ts  # 扩展配置文件
│   │       ├── locales/            # 扩展专属语言包
│   │       └── ...                 # 扩展源码
│   └── shared/
│       └── locales/         # 共享语言包
├── scripts/                 # 构建脚本和工具
│   ├── extension-config-manager/  # 扩展配置管理
│   ├── build/              # 构建系统
│   ├── helpers/            # 共享工具
│   └── ...
└── docs/                   # 文档
```

## 更多文档

*   **[用户使用指南](./docs/user-guide.md)**：了解如何使用脚手架进行日常开发和发布
*   **[开发者指南](./docs/developer-guide.md)**：深入理解项目架构、配置系统和扩展开发

## 主要命令

| 命令 | 描述 |
|------|------|
| `pnpm dev <扩展> <渠道包>` | 启动开发模式 |
| `pnpm build` | 生产模式构建 |
| `pnpm commit` | 交互式提交 |
| `pnpm release run` | 执行发布流程 |

更多命令请参考 [用户使用指南](./docs/user-guide.md#4-常用命令参考)。
